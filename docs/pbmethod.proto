package com.proto;

option java_package = "com.proto";
option java_outer_classname = "GGProto";

message RequestData {
    optional string session = 1;
    repeated Action actions = 2;
}

message ResponseData {
    repeated Action actions = 2;
}

message ListAction {
    repeated Action aAction = 1;
}

message Action {
    optional int32 service = 1;
    optional bytes data = 2;
}

message CommonVector {
    repeated int64 aNumber = 1;
    repeated string aString = 2;
}

message ListCommonVector {
    repeated CommonVector aVector = 1;
}

message ProtoStorage {
    optional CommonVector hero = 1;
    optional CommonVector bomb = 2;
    optional CommonVector item = 3;
    optional CommonVector material = 4;
    optional CommonVector avatar = 5;
    optional CommonVector pet = 6;
    optional CommonVector petFood = 7;
    optional CommonVector accessories = 8;
	optional CommonVector stoneUfo = 9;
}

message SmsSyntax {
    required int32 serviceNumber = 1;
    required string serviceCode = 2;
    optional string tag = 3;
}

//Init info
message InitInfoResponse {
    optional SmsSyntax smsDK = 1;
    optional SmsSyntax smsRecoverPwd = 2;
    optional SmsSyntax smsTang = 3;
    required bool hasError = 4;
    optional string message = 5;
}

//Avatar
message Avatar {
    required int32 id = 1;
    required string name = 2;
    required string image = 3;
    required int32 price = 4;
}

//Item
message Item {
    required int32 id = 1;
    optional string name = 2;
    optional string desc = 3;
    required int32 quantity = 4;
    optional int32 expired = 5;
    optional int32 price = 6;
    //
    optional int32 posX = 7;
    optional int32 posY = 8;
}

message ListAvatarResponse {
    repeated Avatar lstAvatar = 1;
    repeated Item lstItem = 2;
}

//Update version
message Version {
    required string versionRcmUpdate = 1;
    required string versionForceUpdate = 2;
    required string msgRcmUpdate = 3;
    required string msgForceUpdate = 4;
    required string wap = 5;
}

//Login message send to client
message ProtoLoginResponse {
    optional int32 seedCode = 1;
    optional ProtoUser user = 2;
}

message ProtoMessage {
    optional int32 id = 1;
    optional string title = 2;
    optional string message = 3;
    repeated int32 bonus = 4;
    optional bool msgType = 5;
    optional int32 receive = 6;
    optional int64 time = 7;
}

message ProtoMailList {
    repeated ProtoUser aReqFriend = 1;
    repeated ProtoInviteBattle aInviteBattle = 2;
    repeated ProtoMessage aMsg = 3;
}

message ProtoInviteBattle {
    optional int64 userId = 1;
    optional string name = 2;
    optional int32 teamId = 3;
}

message ProtoListUser {
    repeated ProtoUser aUser = 1;
}

message ProtoListClan {
    repeated ProtoClan aClan = 1;
    optional ProtoClan myClan = 2;
    optional int32 myClanRank = 3;
}

message ProtoClan {
    optional int32 id = 1;
    optional string name = 2;
    optional string slogan = 3;
    optional int32 avatar = 4;
    optional int32 level = 5;
    optional int64 exp = 6;
    optional int32 position = 7;
    optional int32 member = 8;
    repeated int32 joinRule = 9;
    repeated int64 myInfo = 10;
    optional int64 trophy = 11;
}

//User
message ProtoUser {
    optional int64 id = 1;
    optional string username = 2;
    optional string name = 3;
    optional int64 gem = 4;
    optional int64 gold = 5;
    repeated int32 avatar = 6;
    optional int64 online = 7;
    optional int64 exp = 8;
    optional int32 lvl = 9;
    optional int32 unreadMail = 10;
    optional int64 trophy = 11;
    optional int64 rank = 12;
    repeated int32 settings = 13;
    optional string facebookId = 14;
    optional int32 atomic = 15;
    repeated int32 energy = 16;
    optional int64 medal = 17;
    repeated int64 infor = 18;
    repeated int32 winRate = 19;
    repeated int32 clan = 20;
    optional string clanName = 21;
}

message ProtoListChat {
    repeated ProtoChat aChat = 1;
}

message ProtoChat {
    optional int64 userId = 1;
    optional string name = 2;
    optional string avatar = 3;
    optional int64 time = 4;
    optional string message = 5;
    optional int32 vip = 6;
    optional int32 channel = 7;
    optional int32 msgType = 8;
    optional int32 msgId = 9;
}

message ProtoJoinGame {
    repeated ProtoPlayerInfo aPlayer = 1;
    optional int32 mapId = 2;
}

message ProtoListPlayerInfo {
    repeated ProtoPlayerInfo aPlayer = 1;
}

message Pos {
    optional float x = 1;
    optional float y = 2;
}

message MapPos {
    optional int32 x = 1;
    optional int32 y = 2;
}

message Square {
    optional int32 type = 1;
    optional int32 item = 2;
    optional MapPos mPos = 3;
}

message Boom {
    optional MapPos mPos = 1;
    optional Pos pos = 2;
    optional float timeStart = 3;
    optional float timeExplosive = 4;
    optional float deltaTimeWait = 5;
    optional float deltaTimeExplosive = 6;
    optional float deltaTimeExist = 7;
    optional float lenght = 8;
    optional float stepSpeed = 9;
    optional int32 status = 10;
    optional MapPos posLeft = 11;
    optional MapPos posRight = 12;
    optional MapPos posUp = 13;
    optional MapPos posDown = 14;
}

message ProtoInput {
    optional float time = 1;
    optional int64 seq = 2;
    repeated int32 inputKeys = 3;
}

message ProtoStartGame {
    optional int32 mapId = 1;
    optional int64 serverTime = 2;
}

// id cho bomb, do dai bomb chuyen thanh square, an item id
message ProtoGetItem {
    optional int64 playerId = 1;
    optional int64 itemId = 2;
    repeated float values = 3;
}

message ProtoAction {
    optional int32 service = 1;
    optional bytes data = 2;
}

message ProtoListAction {
    optional ProtoAction actions = 1;
}

message ProtoItem {
    optional int32 imageId = 1;
    optional int32 id = 2;
    optional MapPos mPos = 3;
    optional bool add = 4;
}

//
message ProtoPlayerInfo {
    optional int64 id = 1;
    optional string name = 2;
    repeated int32 avatar = 3;
    optional int32 team = 4;
    repeated int32 aItem = 5;
    repeated float aPoint = 6;
    optional int32 rank = 7;
}

message ProtoPlayerResult {
    optional int32 team = 1;
    optional ProtoUser user = 2;
    repeated int64 bonus = 3;
    repeated int64 exBonus = 4;
    optional int32 status = 5;
}

message ProtoEndgame {
    repeated ProtoPlayerResult aResult = 1;
    optional int32 teamWin = 2;
    optional int32 type = 3;
    optional int64 timeCreated = 4;
    optional int32 star = 5;
    optional int32 gameId = 6;
}

message ProtoMonsterPos {
    optional int64 id = 1;
    optional float posX = 2;
    optional float posY = 3;
}

message ProtoListMonsterState {
    repeated ProtoMonsterState aMonsterState = 1;
}
//
message ProtoUnitPos {
    optional int32 id = 1;
    optional float posX = 2;
    optional float posY = 3;
    optional int64 lastInputSeq = 4;
}

message ProtoUnitJumpPos {
    optional int32 id = 1;
    optional float posX = 2;
    optional float posY = 3;
    optional int32 type = 4;
}

message ProtoListUnitJumpPos {
    repeated ProtoUnitJumpPos aJump = 1;
}

message ProtoUnitAdd {
    optional int32 id = 1;
    optional int32 type = 2;
    repeated int32 avatar = 3;
    optional Pos pos = 4;
    optional bool add = 5;
    optional ProtoPlayerInfo playerInfo = 6;
}

message ProtoPlayerState {
    optional int64 id = 1;
    repeated int32 status = 2;
    repeated float info = 3;
    //optional float boomLenght = 8;
    //optional int32 numberBoom = 9;
}

message ProtoMonsterState {
    optional int64 id = 1;
    repeated int32 status = 2;
    repeated float info = 3;
}

message ProtoSquare {
    optional int32 id = 1;
    optional int32 type = 2; // di duoc hay ko
    optional int32 imageId = 3;
    optional Pos pos = 4;
    optional bool add = 5;
}

message ProtoListBoom {
    repeated ProtoBoom aBoom = 1;
}

message ProtoListPlayerState {
    repeated ProtoPlayerState aPlayerState = 1;
}

message ProtoUnitUpdate {
    optional int32 type = 1;
    repeated bytes data = 2;
}

message ProtoBoom {
    optional int32 id = 1;
    repeated int32 bombLength = 2;
    optional int32 imageId = 3;
    optional int32 status = 4;
    optional int32 playerUnitId = 5;
    optional MapPos mPos = 6;
}

message ProtoInitTable {
    optional int32 mapId = 1;
    repeated ProtoUnitAdd aUnitAdd = 2;
    repeated ProtoUnitAdd aItemAdd = 3;
    repeated ProtoUnitUpdate aUnitUpdate = 4;
    optional int32 mode = 5;
}

message ProtoTableState {
    optional float serverTime = 1;
    repeated ProtoUnitPos aUnitPos = 2;
    repeated ProtoUnitAdd aUnitAdd = 3;
    repeated ProtoUnitUpdate aUnitUpdate = 4;
}

message ProtoTeamStatus {
    optional int32 type = 1;
    optional int32 id = 2;
    optional int32 status = 3;
    optional int64 hostId = 4;
    repeated ProtoUser aUser = 5;
    optional int32 curMap = 6;
}

message ProtoShop {
    optional int32 type = 1;
    optional int32 priceType = 2;
    optional int64 price = 3;
    optional string saleoff = 4;
    optional int32 imageId = 5;
    optional int32 number = 6;
    optional int32 vipRequired = 7;
}

message ProtoShopList {
    repeated ProtoShop aShop = 1;
    optional int64 timeout = 2;
    optional int32 feeRefresh = 3;
    optional int32 shopType = 4;
}

message ProtoTopBossDetail {
    optional int32 id = 1;
    optional string hostName = 2;
    optional int32 time = 3;
    repeated ProtoUser aUser = 4;
}

message ProtoTopListBossDetail {
    repeated ProtoTopBossDetail aTop = 1;

}

message ProtoTopPlayer {
    optional int32 type = 1;
    optional int64 nextReset = 2;
    repeated int32 myRank = 3;
    repeated ProtoUser aUser = 4;
    repeated ProtoUser preSession = 5;
}

message ProtoListTopupHistory {
    repeated ProtoTopupHistory history = 1;
}

message ProtoTopupHistory {
    optional string timeCreated = 1;
    optional string type = 2;
    optional string money = 3;
    optional int32 oldGem = 4;
    optional int32 addGem = 5;
}

message ProtoListEndgame {
    repeated ProtoEndgame aEndgame = 1;
}

message ProtoGameStatus {
    optional int32 status = 1;
    optional int32 numberAtk = 2;
    optional string strTime = 3;
    repeated ProtoUser aUser = 4;
    optional int32 fee = 5;
    optional ListCommonVector info = 6;
    optional int32 cownDown = 7;
}
