package grep.database;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

import grep.helper.Filer;
import org.hibernate.SessionFactory;
import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
import org.hibernate.cfg.Configuration;
import org.hibernate.service.ServiceRegistry;

import java.util.Arrays;
import java.util.List;

/**
 * Hibernate Utility class with a convenient method to get Session Factory object.
 *
 * <AUTHOR>
 */
public class HibernateUtil {

    private static SessionFactory sessionFactory;
    private static ServiceRegistry serviceRegistry;

    private static SessionFactory mainFactory;
    private static ServiceRegistry mainRegistry;

    public static void init(String user, String pass, String db, String dbMain, String host) {
        user = decrypt(user);
        pass = decrypt(pass);
        if (true) {
            user = "root";
            pass = "metabomb";
        }
        System.out.println("user = " + user);
        System.out.println("pass = " + pass);
        System.out.println("host = " + host);
        System.out.println("db = " + db);
        System.out.println("dbMain = " + dbMain);
        if (db != null) {
            try {
                Configuration configuration = new Configuration().configure("hibernate.cfg.xml");
                configuration.setProperty("hibernate.connection.username", user);
                configuration.setProperty("hibernate.connection.password", pass);
                String connectionUrl = String.format("*****************************************************************************", host, db);
                System.out.println(connectionUrl);
                System.out.println(configuration.getProperties().get("hibernate.physical_naming_strategy"));
                configuration.setProperty("hibernate.connection.url", connectionUrl);
                //                configuration.addAnnotatedClass(ConfigLocalEntity.class)
                //                        .addAnnotatedClass(MapEntity.class).addAnnotatedClass(ConfigEntity.class);
                List<String> packages = Arrays.asList("com.bem.dao.mapping");
                for (String aPackage : packages) {
                    Class[] allClasses = Filer.getClasses(aPackage, true);
                    for (Class allClass : allClasses) {
                        try {
                            String simpleName = allClass.getSimpleName();
                            //                            if (simpleName.contains("Entity") || simpleName.contains("ConfigLanguage") || simpleName.contains("ConfigLanguage")) {
                            configuration.addAnnotatedClass(allClass);
                            //                        }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                }
                serviceRegistry = new StandardServiceRegistryBuilder().applySettings(configuration.getProperties()).build();
                sessionFactory = configuration.buildSessionFactory(serviceRegistry);
            } catch (Exception ex) {
                // Log the exception.
                ex.printStackTrace();
                throw new ExceptionInInitializerError(ex);
            }
        }
        //        if (dbMain != null) {
        //            try {
        //                Configuration configuration = new Configuration().configure("hibernateMain.cfg.xml");
        //                configuration.setProperty("hibernate.connection.username", user);
        //                configuration.setProperty("hibernate.connection.password", pass);
        //                String connectionUrl = String.format("*****************************************************************************", host, dbMain);
        //                System.out.println(connectionUrl);
        //                configuration.setProperty("hibernate.connection.url", connectionUrl);
        //                mainRegistry = new StandardServiceRegistryBuilder().applySettings(configuration.getProperties()).build();
        //                sessionFactory = configuration.buildSessionFactory(mainRegistry);
        //            } catch (Throwable ex) {
        //                // Log the exception.
        //                System.err.println("Initial SessionFactory creation failed." + ex);
        //                throw new ExceptionInInitializerError(ex);
        //            }
        //        }
    }

    public static SessionFactory getSessionFactory() {
        return sessionFactory;
    }

    public static SessionFactory getMainSessionFactory() {
        return mainFactory;
    }

    static String decrypt(String value) {
        int code = 10, codeFrom = 32, codeRange = 125 - 32;
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < value.length(); i++) {
            int n = value.charAt(i) - codeFrom - code;
            n = n >= 0 ? n : n + codeRange;
            buffer.append((char) (n + codeFrom));
        }
        return buffer.toString();
    }

    static String encrypt(String value) {
        int code = 10, codeFrom = 32, codeRange = 125 - 32;
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < value.length(); i++) {
            int n = value.charAt(i) - codeFrom + code;
            n = n > codeRange ? n % codeRange : n;
            buffer.append((char) (n + codeFrom));
        }
        return buffer.toString();
    }

    public static void main(String[] args) {
        String a = "zyuo|";
        String b = "coukA?xB=ylBZyuo|%";
        System.out.println(a);
        System.out.println(b);
        System.out.println(decrypt(a));
        System.out.println(decrypt(b));
        System.out.println(encrypt("dsonserver"));
        System.out.println(encrypt("wO5icApi7O134uQInOJekutAw4F83O"));
    }
}
