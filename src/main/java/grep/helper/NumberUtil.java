package grep.helper;

import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by vieth_000 on 12/27/2016.
 */
public class NumberUtil {

    public static long billion = 1_000_000_000;
    public static long million = 1_000_000;
    public static long thousand = 1_000;
    public static long hundred = 100;

    static SecureRandom rand = new SecureRandom();

    public static void main(String[] args) {
        System.out.println("rand = " + rand);
    }

    static DecimalFormat formatter = new DecimalFormat("#,###");

    public static String formatNumber(long value) {
        String result = formatter.format(value);
        return result.replace(",", ".");
    }


    /**
     * Kiểm tra trong mảng có phần tử nào lặp hay không
     *
     * @param values
     * @return
     */
    public static boolean hasDuplicate(List<Long> values, long... ignoreValues) {
        long ignore = ignoreValues.length == 0 ? -999999 : ignoreValues[0];
        for (int i = 0; i < values.size(); i++) {
            if (values.get(i) == ignore) continue;
            for (int j = i + 1; j < values.size(); j++) {
                if (values.get(i) == values.get(j)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static String joiningListInt(List<Integer> aInt) {
        return aInt.stream().map(Object::toString).collect(Collectors.joining(","));
    }


    public static String joiningListLong(List<Long> aLong) {
        return aLong.stream().map(Object::toString).collect(Collectors.joining(","));
    }

    public static boolean isNumber(String value) {
        try {
            Long.parseLong(value);
            return true;
        } catch (Exception ex) {
        }
        return false;
    }

    public static boolean isIntNumber(String value) {
        try {
            Integer.parseInt(value);
            return true;
        } catch (Exception ex) {
        }
        return false;
    }

    public static float randomPercent() {
        return rand.nextFloat() * 100;
    }

    /**
     * Ngẫu nhiên các số tự nhiên giữa 2 số bất kì
     *
     * @param n1
     * @param n2
     * @return n1<= number < = n2
     */
    public static int getRandom(int n1, int n2) {
        return rand.nextInt(Math.abs(n1 - n2) + 1) + Math.min(n1, n2);
    }

    /**
     * Ngẫu nhiên các số tự nhiên từ 0 tới n
     *
     * @param n
     */
    public static int getRandom(int n) {
        return rand.nextInt(n);
    }

    public static int min(int... number) {
        if (number.length == 0) return 0;
        int min = number[0];
        for (int i = 1; i < number.length; i++) {
            min = Math.min(min, number[i]);
        }
        return min;
    }

    public static int max(int... number) {
        if (number.length == 0) return 0;
        int max = number[0];
        for (int i = 1; i < number.length; i++) {
            max = Math.max(max, number[i]);
        }
        return max;
    }

    public static List<Long> randomDisticList(int size, int number) {
        List<Long> ret = new ArrayList<>();
        if (size <= number) {
            for (int i = 0; i < size; i++) {
                ret.add((long) i);
            }
        } else {
            while (ret.size() < number) {
                long num = rand.nextInt(size);
                if (!ret.contains(num)) {
                    ret.add(num);
                }
            }
        }
        return ret;
    }

    public static List<Integer> randomDisticInList(List<Integer> lst, int number) {
        List<Integer> ret = new ArrayList<>();
        if (lst.size() <= number) {
            return lst;
        } else {
            while (ret.size() < number) {
                int num = rand.nextInt(lst.size());
                if (!ret.contains(lst.get(num))) {
                    ret.add(lst.get(num));
                }
            }
        }
        return ret;
    }

    public static List<Long> genListLong(int size, Long value) {
        List<Long> lst = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            lst.add(value);
        }
        return lst;
    }

    public static List<Integer> genListInt(int size, int value) {
        List<Integer> lst = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            lst.add(value);
        }
        return lst;
    }

    public static int randomInList(List<Integer> lst) {
        int rand = getRandom(lst.size());
        return lst.get(rand);
    }

    public static boolean isHitRandom(int percent) {
        return rand.nextInt(100) < percent;
    }

    public static <T> T getRandomInList(List<T> aT) {
        return aT.get(getRandom(aT.size()));
    }

}
