package grep.log;

import grep.database.DBJPA;
import grep.helper.GUtil;
import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;

/**
 * Created by vieth_000 on 6/19/2017.
 */
public class Logs {
    static HashSet<String> mError = new HashSet<>();
    static String curDate = "";
    static boolean realServer = true;
    private static String[][] ignoreErr = {{"CCUCounter", "Duplicate entry"}};

    public static void init(boolean realServer) {
        Logs.realServer = realServer;
    }

    public static void warn(String msg) {
        slib_Logger.root().warn(msg);
    }

    public static void error(String debug, Exception ex) {
        slib_Logger.root().error(debug, ex);
    }

    public static void error(Exception ex) {
        slib_Logger.root().error("", ex);
        sendErrorNotify(GUtil.exToString(ex));
    }

    public static void event(String msg) {
        slib_Logger.event().info(msg);
    }

    public static void error(String msg) {
        slib_Logger.root().error(msg);
        sendErrorNotify(msg);
    }

    public static void apiErr(String msg) {
        slib_Logger.api().error(msg);
    }

    public static void slow(String msg) {
        LoggerFactory.getLogger("SLOW").info(msg);
    }

    public static void sendErrorNotify(String msg) {
        //        try {
        //            if (mCache.get(msg) == null) {
        //                String[] ignore = Arrays.stream(ignoreErr).filter(values -> {
        //                    for (String value : values) {
        //                        if (!msg.contains(value)) return false;
        //                    }
        //                    return true;
        //                }).findFirst().orElse(null);
        //                if (ignore == null) {
        //                    mCache.add(msg, "");
        //                    Filer.append("logs/errorUnique.log", String.format("%s -> %s", DateTime.getFullDate(new Date()), msg));
        //                    Telegram.sendError(msg);
        //                    //
        ////                    MailNotify.teleNotify(String.format("%s -> %s", DateTime.getFullDate(new Date()), msg));
        //                }
        //            }
        //        } catch (Exception ex) {
        //            Logs.error(ex);
        //        }
    }

    public static void debug(String msg) {
        slib_Logger.root().debug(msg);
    }

    public static void info(String msg) {
        slib_Logger.root().info(msg);
    }

    //    public static String getLogPath(int serverId, String eventName) {
    //        String strDate = DateTime.getDateyyyyMMdd(new Date());
    //        return String.format("%s/%s/%s.%s.%s.log", Constans.FILE_PATH_EVENT_HISTORY, strDate, eventName, serverId, Calendar.getInstance().get(Calendar.HOUR_OF_DAY));
    //    }

    public static void save(Object obj) {
        Session session = null;
        try {
            session = DBJPA.getEntityManager().unwrap(Session.class);
            session.beginTransaction();
            session.save(obj);
            session.getTransaction().commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            slib_Logger.root().error(GUtil.exToString(ex));
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
    }

    public static Logger getMailLogger() {
        return LoggerFactory.getLogger("MAIL");
    }
}
