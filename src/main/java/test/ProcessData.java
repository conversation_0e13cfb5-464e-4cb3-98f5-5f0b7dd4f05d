package test;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;

import java.text.SimpleDateFormat;
import java.util.EventObject;
import java.util.List;

public class ProcessData {

    public static String value = "[{\"status\":2,\"id\":\"0\",\"time1\":12,\"time2\":18,\"time3\":21,\"dateBegin\":\"07/03/2017 14:00:00\",\"dateEnd\":\"14/03/2017 14:00:00\",\"name\":\"Free Strength!.\",\"title\":\"Free Strength!.\",\"desc\":[\"Receive 15 strength daily\"],\"required\":[8],\"award\":[[15,15]],\"image\":101},{\"status\":2,\"id\":\"1\",\"dateBegin\":\"07/03/2017 14:00:00\",\"dateEnd\":\"14/03/2017 14:00:00\",\"name\":\"Level up award\",\"title\":\"Level up award\",\"desc\":[\"Level 2\",\"Level 3\",\"Level 4\",\"Level 5\",\"Level 6\",\"Level 7\",\"Level 8\",\"Level 9\",\"Level 10\",\"Level 11\",\"Level 12\",\"Level 13\",\"Level 14\",\"Level 15\",\"Level 16\",\"Level 17\",\"Level 18\",\"Level 19\",\"Level 20\",\"Level 21\",\"Level 22\",\"Level 23\",\"Level 24\",\"Level 25\",\"Level 26\",\"Level 27\",\"Level 28\",\"Level 29\",\"Level 30\"],\"required\":[2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30],\"award\":[[1,1000],[1,1200,9,1,10],[1,1440],[1,1700,3,9,5],[1,2000],[1,2400,3,3,5,3,4,5],[1,2900],[1,3500],[1,4200,7,8,1],[1,5000],[1,6000],[1,7200,9,5,10],[1,8600],[1,10300,3,10,5],[1,12400],[1,14900,3,3,10,3,4,10],[1,17900],[1,21500],[1,25800,7,8,1],[1,31000],[1,37200],[1,44600,9,10,10],[1,53500],[1,64200,3,10,10],[1,77000],[1,92400,3,3,15,3,4,15],[1,110900],[1,133100],[1,159700,7,10,1]],\"image\":101},{\"status\":2,\"id\":\"2\",\"dateBegin\":\"07/03/2017 14:00:00\",\"dateEnd\":\"14/03/2017 14:00:00\",\"name\":\"Complete Stage\",\"title\":\"Complete Stage\",\"desc\":[\"Forest 1\",\"Forest 2\",\"Forest 3\",\"Forest 4\",\"Forest 5\",\"Forest 6\",\"Forest 7\",\"Forest 8\",\"Forest 9\",\"Forest 10\",\"Iceberg 1\",\"Iceberg 2\",\"Iceberg 3\",\"Iceberg 4\",\"Iceberg 5\",\"Iceberg 6\",\"Iceberg 7\",\"Iceberg 8\",\"Iceberg 9\",\"Iceberg 10\",\"Desert 1\",\"Desert 2\",\"Desert 3\",\"Desert 4\",\"Desert 5\",\"Desert 6\",\"Desert 7\",\"Desert 8\",\"Desert 9\",\"Desert 10\",\"Candy 1\",\"Candy 2\",\"Candy 3\",\"Candy 4\",\"Candy 5\",\"Candy 6\",\"Candy 7\",\"Candy 8\",\"Candy 9\",\"Candy 10\",\"Lava 1\",\"Lava 2\",\"Lava 3\",\"Lava 4\",\"Lava 5\",\"Lava 6\",\"Lava 7\",\"Lava 8\",\"Lava 9\",\"Lava 10\"],\"required\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],\"award\":[[1,1000],[1,2000],[9,1,5],[9,2,5],[9,3,5],[1,1000,3,9,5],[1,2000,3,9,5],[1,3000,3,9,5],[1,4000,3,9,5],[3,3,5,3,4,5],[1,10000],[1,15000],[9,4,5],[9,5,5],[9,6,5],[1,1000,3,10,5],[1,2000,3,10,5],[1,3000,3,10,5],[1,4000,3,10,5],[3,3,10,3,4,10],[1,20000],[1,25000],[9,7,5],[9,8,10],[9,9,10],[1,1000,3,11,5],[1,2000,3,11,5],[1,3000,3,11,5],[1,4000,3,11,5],[3,3,15,3,4,15],[1,30000],[1,35000],[9,10,5],[9,11,5],[9,12,5],[1,1000,3,11,5],[1,2000,3,11,5],[1,3000,3,11,5],[1,4000,3,11,5],[3,3,20,3,4,20],[1,40000],[1,45000],[9,13,5],[9,14,5],[9,15,5],[1,1000,3,11,5],[1,2000,3,11,5],[1,3000,3,11,5],[1,4000,3,11,5],[3,3,25,3,4,25]],\"image\":101},{\"status\":0,\"id\":\"3\",\"dateBegin\":\"07/03/2017 14:00:00\",\"dateEnd\":\"14/06/2017 14:00:00\",\"name\":\"Collect Star\",\"title\":\"Collect Star\",\"desc\":[\"30 star\",\"60 star\",\"90 star\",\"120 star\",\"150 star\"],\"required\":[30,60,90,120,150],\"award\":[[3,8,2],[3,8,5],[3,8,10],[3,8,15],[3,8,20]],\"image\":101},{\"status\":0,\"id\":\"4\",\"dateBegin\":\"07/03/2017 14:00:00\",\"dateEnd\":\"14/06/2017 14:00:00\",\"name\":\"Win Rank Match\",\"title\":\"Win Rank Match\",\"desc\":[\"5 win\",\"10 win\",\"20 win\",\"50 win\",\"100 win\",\"200 win\",\"350 win\",\"500 win\",\"750 win\",\"1000 win\",\"2000 win\"],\"required\":[5,10,20,50,100,200,350,500,750,1000,2000],\"award\":[[1,5000],[1,8000],[1,12800],[1,20500],[1,32800],[1,52500],[1,84000],[1,134400],[1,215000],[1,344000],[1,550400]],\"image\":101},{\"status\":0,\"id\":\"5\",\"dateBegin\":\"07/03/2017 14:00:00\",\"dateEnd\":\"14/06/2017 14:00:00\",\"name\":\"High Rank\",\"title\":\"High Rank\",\"desc\":[\"Rank 2\",\"Rank 3\",\"Rank 4\",\"Rank 5\",\"Rank 6\",\"Rank 7\",\"Rank 8\",\"Rank 9\",\"Rank 10\",\"Rank 11\",\"Rank 12\",\"Rank 13\",\"Rank 14\",\"Rank 15\",\"Rank 16\",\"Rank 17\",\"Rank 18\",\"Rank 19\",\"Rank 20\",\"Rank 21\",\"Rank 22\",\"Rank 23\",\"Rank 24\",\"Rank 25\",\"Rank 26\",\"Rank 27\"],\"required\":[23,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],\"award\":[[3,3,1,3,4,1],[3,3,2,3,4,2],[3,3,3,3,4,3],[3,3,4,3,4,4],[3,3,5,3,4,5],[3,3,10,3,4,10],[3,3,11,3,4,11],[3,3,12,3,4,12],[3,3,13,3,4,13],[3,3,14,3,4,14],[3,3,20,3,4,20],[3,3,21,3,4,21],[3,3,22,3,4,22],[3,3,23,3,4,23],[3,3,24,3,4,24],[3,3,30,3,4,30],[3,3,31,3,4,31],[3,3,32,3,4,32],[3,3,33,3,4,33],[3,3,34,3,4,34],[3,3,40,3,4,40],[3,3,41,3,4,41],[3,3,42,3,4,42],[3,3,43,3,4,43],[3,3,44,3,4,44],[3,3,50,3,4,50]],\"image\":101},{\"status\":1,\"id\":\"6\",\"dateBegin\":\"18/08/2017 10:30:00\",\"dateEnd\":\"21/08/2017 23:59:59\",\"name\":\"Win with Lady Bird\",\"title\":\"Win with Lady Bird\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[4,5,1,1,100000],[4,2,1,1,200000],[4,4,1,1,500000],[4,7,1,1,1000000],[4,10,1,1,5000000],[4,8,1,1,7000000],[4,9,1,1,10000000]],\"image\":101},{\"status\":1,\"id\":\"7\",\"dateBegin\":\"25/08/2017 17:20:00\",\"dateEnd\":\"28/08/2017 23:59:59\",\"name\":\"Win with Young\",\"title\":\"Win with Young\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[4,12,1,1,100000],[4,13,1,1,200000],[4,14,1,1,500000],[4,16,1,1,1000000],[4,19,1,1,5000000],[4,18,1,1,7000000],[4,20,1,1,10000000]],\"image\":101},{\"status\":1,\"id\":\"8\",\"dateBegin\":\"13/07/2017 14:00:00\",\"dateEnd\":\"16/07/2017 23:59:59\",\"name\":\"Win with Curling Wet\",\"title\":\"Win with Curling Wet\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[4,25,1,1,100000],[4,24,1,1,200000],[4,22,1,1,500000],[4,26,1,1,1000000],[4,28,1,1,5000000],[4,29,1,1,7000000],[4,61,1,1,10000000]],\"image\":101},{\"status\":1,\"id\":\"9\",\"dateBegin\":\"28/07/2017 10:00:00\",\"dateEnd\":\"30/07/2017 23:59:59\",\"name\":\"Win with Fatty Bald\",\"title\":\"Win with Fatty Bald\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[4,32,1,1,100000],[4,34,1,1,200000],[4,35,1,1,500000],[4,38,1,1,1000000],[4,37,1,1,5000000],[4,39,1,1,7000000],[4,62,1,1,10000000]],\"image\":101},{\"status\":1,\"id\":\"10\",\"dateBegin\":\"02/08/2017 00:00:00\",\"dateEnd\":\"05/08/2017 23:59:59\",\"name\":\"Win with Shorten\",\"title\":\"Win with Shorten\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[4,42,1,1,100000],[4,43,1,1,200000],[4,45,1,1,500000],[4,46,1,1,1000000],[4,48,1,1,5000000],[4,50,1,1,7000000],[4,60,1,1,10000000]],\"image\":101},{\"status\":1,\"id\":\"11\",\"dateBegin\":\"10/08/2017 00:00:00\",\"dateEnd\":\"10/08/2017 23:59:59\",\"name\":\"Win with Mermaid\",\"title\":\"Win with Mermaid\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[4,52,1,1,100000],[4,53,1,1,200000],[4,55,1,1,500000],[4,56,1,1,1000000],[4,54,1,1,5000000],[4,57,1,1,7000000],[4,58,1,1,10000000]],\"image\":100},{\"status\":1,\"id\":\"12\",\"dateBegin\":\"11/05/2017 00:00:00\",\"dateEnd\":\"11/11/2017 23:59:59\",\"name\":\"Win with Squirrel\",\"title\":\"Win with Squirrel\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[1,1000000],[1,5000000],[1,10000000],[1,20000000],[1,35000000],[1,70000000],[1,100000000]],\"image\":100},{\"status\":1,\"id\":\"57\",\"dateBegin\":\"31/08/2017 00:00:00\",\"dateEnd\":\"4/09/2017 23:59:59\",\"name\":\"Win with Pet Fighting Buffalo\",\"title\":\"Win with Pet Fighting Buffalo\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[1,100000,9,30,1],[1,200000,9,30,2],[1,500000,9,30,3],[1,1000000,9,30,4],[1,5000000,9,30,5,27,10,1],[1,7000000,9,41,1,27,10,1],[1,10000000,9,41,2,27,10,1]],\"image\":100},{\"status\":1,\"id\":\"58\",\"dateBegin\":\"10/09/2017 00:00:00\",\"dateEnd\":\"14/09/2017 23:59:59\",\"name\":\"Win with Pet Fira Kylin\",\"title\":\"Win with Pet Fira Kylin\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[1,1000000,9,31,1],[1,2000000,9,31,2],[1,3000000,9,31,3],[1,5000000,9,31,4],[1,7000000,9,31,5,27,10,1],[1,12000000,9,42,1,27,10,1],[1,20000000,9,42,2,27,10,1]],\"image\":100},{\"status\":1,\"id\":\"60\",\"dateBegin\":\"18/09/2017 00:00:00\",\"dateEnd\":\"21/09/2017 23:59:59\",\"name\":\"Win with Pet Eager Pig\",\"title\":\"Win with Pet Eager Pig\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[1,1000000,9,33,1],[1,2000000,9,33,2],[1,3000000,9,33,3],[1,5000000,9,33,4],[1,7000000,9,33,5,27,10,1],[1,12000000,9,44,1,27,10,1],[1,20000000,9,44,2,27,10,1]],\"image\":100},{\"status\":1,\"id\":\"61\",\"dateBegin\":\"29/11/2017 00:00:00\",\"dateEnd\":\"2/12/2017 23:59:59\",\"name\":\"Win with Pet HP Dragon\",\"title\":\"Win with Pet HP Dragon\",\"desc\":[\"5 Win\",\"20 Win\",\"50 Win\",\"100 Win\",\"250 Win\",\"400 Win\",\"600 Win\"],\"required\":[5,20,50,100,250,400,600],\"award\":[[1,1000000,9,34,1],[1,2000000,9,34,2],[1,3000000,9,34,3],[1,5000000,9,34,4],[1,7000000,9,34,5,27,10,1],[1,12000000,9,45,1,27,10,1],[1,20000000,9,45,2,27,10,1]],\"image\":100},{\"status\":0,\"id\":\"100\",\"dateBegin\":\"05/06/2017 00:00:00\",\"dateEnd\":\"20/06/2017 23:59:59\",\"name\":\"Đua Top Thám Hiểm\",\"title\":\"Đua Top Thám Hiểm\",\"desc\":[\"Nội Dung:Phần thưởng cho Top 50:\\n<b><color=green>-Top 1</color></b> :  Bomb bể cá, Rương vàng x40, Chìa khoá x60, Kim cương x3000  \\n<b><color=green>-Top 2-20</color></b> : Bomb Người Dơi, Rương vàng x30, Chìa khoá x40, Kim cương x2000  \\n<b><color=green>-Top 21-50</color></b>  Bomb Bí Ngô, Rương vàng 15, Chìa khoá 25, Kim cương x1000  \\nQuà sẽ được phát qua thư từ ngày 21/6\"],\"required\":[1],\"award\":[[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10]],\"image\":101},{\"status\":2,\"id\":\"102\",\"dateBegin\":\"07/03/2017 14:00:00\",\"dateEnd\":\"14/06/2017 14:00:00\",\"name\":\"Daily Vip Bonus!\",\"title\":\"Daily Vip Bonus!\",\"desc\":[\"Vip 0!\",\"Vip 1!\",\"Vip 2!\",\"Vip 3!\",\"Vip 4!\",\"Vip 5!\",\"Vip 6!\",\"Vip 7!\",\"Vip 8!\",\"Vip 9!\",\"Vip 10!\",\"Vip 11!\",\"Vip 12!\",\"Vip 13!\",\"Vip 14!\",\"Vip 15!\"],\"required\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15],\"award\":[[1,5000,3,18,10,3,3,1,3,4,1,6,50],[1,7000,3,3,1,3,4,1,6,55],[1,9000,3,3,2,3,4,2,6,60],[1,11000,3,3,3,3,4,3,6,65],[1,13000,3,3,4,3,4,4,6,70],[1,15000,3,3,5,3,4,5,6,75],[1,17000,3,3,6,3,4,6,6,80],[1,19000,3,3,7,3,4,7,6,85],[1,21000,3,3,8,3,4,8,6,90],[1,23000,3,3,9,3,4,9,6,95],[1,25000,3,3,10,3,4,10,6,100],[1,27000,3,3,11,3,4,11,6,105],[1,29000,3,3,12,3,4,12,6,110],[1,31000,3,3,13,3,4,13,6,115],[1,33000,3,3,14,3,4,14,6,120],[1,35000,3,3,15,3,4,15,6,125]],\"image\":101},{\"status\":1,\"id\":103,\"dateBegin\":\"05/01/2025 00:00:00\",\"dateEnd\":\"12/01/2025 23:59:59\",\"name\":\"Nạp tích lũy \",\"title\":\"Nạp tích lũy\",\"desc\":[\"Nạp 100 kim cương!\",\"Nạp 300 kim cương!\",\"Nạp 600 kim cương!\",\"Nạp 1000 kim cương!\",\"Nạp 1500 kim cương!\",\"Nạp 2000 kim cương!\",\"Nạp 3500 kim cương!\",\"Nạp 5000 kim cương!\",\"Nạp 8000 kim cương!\",\"Nạp 12000 kim cương!\"],\"required\":[100,300,600,1000,1500,2000,3500,5000,8000,12000],\"award\":[[1,1000000,15,50,9,47,5,3,9,5,3,108,100],[1,2000000,15,80,9,47,10,3,9,10,3,108,100],[3,25,1,20,14,1,1,3000000,15,120,3,9,15,9,47,15,3,108,100],[10,10,50,20,19,1,1,5000000,3,7,1,15,150,3,9,20,9,47,20,3,108,100],[3,26,1,1,7000000,3,7,1,15,200,3,18,50,3,9,25,9,47,30,3,108,100],[8,25,2,27,5,1,20,10,1,1,12000000,3,7,1,15,250,9,46,2,8,5,2,8,10,2,8,15,2,8,20,2,3,108,100],[8,25,3,27,5,2,20,5,1,1,20000000,3,7,1,15,300,9,46,2,8,5,3,8,10,3,8,15,3,8,20,3,3,18,100,3,108,100],[1,30000000,4,85,1,27,5,3,9,46,5,3,12,20,15,350,8,25,5,8,5,5,8,10,5,8,15,5,8,20,5,3,18,200,3,108,100],[1,50000000,4,98,1,3,28,20,8,10,7,8,15,7,8,20,7,8,25,7,8,5,1,9,46,7,3,13,20,15,450,3,18,300,27,5,5,3,108,100],[1,80000000,7,56,1,3,28,30,8,10,10,8,15,10,8,20,10,8,25,10,8,5,1,9,46,10,3,14,30,15,500,3,18,400,27,5,8,3,108,100]],\"image\":\"103\"},{\"status\":1,\"id\":\"104\",\"dateBegin\":\"6/1/2018 0:00:00\",\"dateEnd\":\"7/1/2018 23:59:59\",\"name\":\"Sự kiện liên thắng\",\"title\":\"Đạt chuỗi thắng liên tiếp để nhận thưởng\",\"desc\":[\"5 liên thắng!\",\"10 liên thắng!\",\"20 liên thắng!\",\"30 liên thắng!\",\"40 liên thắng!\",\"50 liên thắng!\",\"80 liên thắng!\",\"100 liên thắng!\",\"150 liên thắng!\",\"200 liên thắng\"],\"required\":[5,10,20,30,40,50,80,100,150,200],\"award\":[[1,100000],[1,300000],[1,500000],[1,1000000],[1,3000000],[1,5000000],[1,10000000],[1,20000000],[1,50000000],[1,100000000]],\"image\":101},{\"status\":1,\"id\":\"105\",\"dateBegin\":\"05/11/2024 00:00:00\",\"dateEnd\":\"04/11/2027 23:59:59\",\"name\":\"Each level receive 1 point\",\"title\":\"\",\"desc\":[\"1 point!\",\"3 point!\",\"5 point!\",\"7 point!\",\"10 point!\",\"15 point!\",\"25 point!\",\"30 point\",\"32 point\",\"35 point\",\"40 point\"],\"required\":[1,3,5,7,10,15,25,30,32,35,40],\"award\":[[1,2000000,3,3,20],[1,4000000,3,3,30,10,3,20],[1,6000000,3,3,40,10,7,20],[1,10000000,3,3,40,10,8,20],[1,14000000,9,47,40,10,4,20],[1,24000000,20,14,1,10,10,20],[1,40000000,9,46,15,10,11,20],[1,60000000,2,1000],[1,60000000,15,500],[27,5,20,27,10,20],[20,5,1,20,10,1,1,60000000]],\"image\":\"101\"},{\"status\":0,\"id\":\"106\",\"dateBegin\":\"20/07/2017 16:00:00\",\"dateEnd\":\"19/07/2017 23:59:59\",\"name\":\"Mỗi 20 kim cương khi quay Slot Machine nhận 1 điểm tích lũy\",\"title\":\"\",\"desc\":[\"1 điểm!\"],\"required\":[1],\"award\":[[3,18,10]],\"image\":\"102\"},{\"status\":1,\"id\":\"107\",\"dateBegin\":\"03/01/2025 00:00:00\",\"dateEnd\":\"03/01/2025 23:59:59\",\"name\":\"Khi đánh thắng đấu trường và thám hiểm sẽ có thể nhận được Kẹo\",\"title\":\"\",\"desc\":[\"5 Kẹo\",\"15 Kẹo\",\"75 Kẹo\",\"150 Kẹo\",\"450 Kẹo\",\"750 Kẹo\",\"1500 Kẹo\",\"2500 Kẹo\"],\"required\":[5,15,75,150,450,750,1500,2500],\"award\":[[27,4,2,3,12,15,1,2000000],[27,4,3,3,12,20,4,38,1,1,5000000],[27,4,5,4,57,1,3,12,30,1,8000000],[27,4,7,7,29,1,1,10000000,3,12,40],[27,4,10,1,15000000,4,76,1,3,13,10],[27,10,5,1,20000000,3,13,15,4,75,1],[27,5,4,1,40000000,3,13,20,4,86,1],[27,5,5,7,54,1,3,25,1,1,50000000,3,14,10]],\"image\":\"100\"},{\"status\":0,\"id\":\"101\",\"dateBegin\":\"06/06/2016 00:00:00\",\"dateEnd\":\"20/06/2016 23:59:59\",\"name\":\"Đua Top Cup\",\"title\":\"Đua Top Cup\",\"desc\":[\"Nội Dung:Phần thưởng cho Top 50:\\n<b><color=green>-Top 1</color></b> :  Bomb bể cá, Rương vàng x40, Chìa khoá x60, Kim cương x3000  \\n<b><color=green>-Top 2-20</color></b> : Bomb Người Dơi, Rương vàng x30, Chìa khoá x40, Kim cương x2000  \\n<b><color=green>-Top 21-50</color></b>  Bomb Bí Ngô, Rương vàng 15, Chìa khoá 25, Kim cương x1000  \\nQuà sẽ được phát qua thư từ ngày 21/6\"],\"required\":[1],\"award\":[[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10],[11,41,10]],\"image\":101},{\"status\":-1,\"id\":\"3000\",\"dateBegin\":\"17/10/2017 00:00:00\",\"dateEnd\":\"17/10/2017 23:59:59\",\"name\":\"Nạp tích lũy\",\"title\":\"Nạp tích lũy\",\"desc\":[\"Nạp 100 kim cương!\",\"Nạp 300 kim cương!\",\"Nạp 600 kim cương!\",\"Nạp 1000 kim cương!\",\"Nạp 1500 kim cương!\",\"Nạp 2000 kim cương!\",\"Nạp 3500 kim cương!\",\"Nạp 5000 kim cương!\"],\"required\":[100,300,600,1000,1500,2000,3500,5000],\"award\":[[1,1000000,15,50,9,47,5],[1,2000000,15,80,9,47,10],[20,9,1,1,3000000,15,120,9,47,15],[20,4,1,1,5000000,3,7,1,15,150,9,47,20],[1,7000000,3,7,1,15,200,9,47,30],[27,5,1,20,5,1,1,12000000,3,7,1,15,250,9,46,1,8,5,2,8,10,2,8,15,2,8,20,2,8,25,2],[27,5,2,20,10,1,1,20000000,3,7,1,15,300,9,46,2,8,5,3,8,10,3,8,15,3,8,20,3,8,25,3],[4,76,1,27,5,3,1,30000000,3,7,1,15,350,9,46,3,8,5,5,8,10,5,8,15,5,8,20,5,8,25,5]],\"image\":103}]";

    public static void main(String[] args) throws Exception {
        JsonArray arr = GsonUtil.parseJsonArray(value);
        for (JsonElement jsonElement : arr) {
            JsonObject obj = jsonElement.getAsJsonObject();
            EventObject event = new Gson().fromJson (obj.toString(), EventObject.class);
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            event.dateBegin = sdf2.format(sdf.parse(event.dateBegin));
            event.dateEnd = sdf2.format(sdf.parse(event.dateEnd));
            System.out.println(event.toSQL());
        }
    }

    public class EventObject {
        int status, id, time1,time2,time3, image;
        String dateBegin, dateEnd;
        String name, title;
        List<String> desc;
        List<Integer> required;
        List<List<Integer>> award;

        public String toSQL() {
            String sql = "insert into boom.config_event(id,name,title,status, date_begin, date_end, description, required, award,image) values('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s');"
                    .formatted(id, name, title, status,dateBegin,dateEnd, StringHelper.toDBString(desc),StringHelper.toDBString(required),StringHelper.toDBString(award),image);
            return sql;
        }
    }

}
