package com.cache;

import com.bem.config.CfgCluster;
import com.bem.config.CfgRedis;
import com.bem.config.CfgServer;
import com.bem.util.Util;
import com.k2tek.common.slib_Logger;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

public class JCache {
    public static String moneyChannel = "MoneyChannel";
    public static String gameChannel = "server";
    public static String serviceChannel = "ServiceChannel1";
    public final static int EXPIRE_1M = 60;
    public final static int EXPIRE_1H = 60 * 60;
    public final static int EXPIRE_1D = 60 * 60 * 24;
    public static final String PREFIX = CfgServer.SERVER_ID + ":";

    JedisPool pool;
    static JCache instance;

    public static JCache getInstance() {
        if (instance == null) {
            instance = new JCache();
        }
        return instance;
    }

    public JCache() {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(200);
        config.setMaxIdle(5);
        pool = new JedisPool(config, "172.31.10.3");

        CfgCluster.config.serverIds.forEach(serverId -> {
            new Thread(() -> {
                while (true) {
                    try {
                        String channelName = CfgCluster.isRealServer() ? "server" + serverId : "test" + serverId;
                        System.out.println(String.format("Subscribing to \"%s\"", channelName));
                        Jedis jedis = pool.getResource();
                        jedis.subscribe(new Subscriber(serverId), channelName);
                        pool.returnResource(jedis);
                        getLogger().info("Subscription ended.");
                        Thread.sleep(3000);
                    } catch (Exception ex) {
                        getLogger().error(Util.exToString(ex));
                    }
                }
            }).start();
        });
    }

    public JedisPool getJedisPool() {
        return pool;
    }

    public void reloadTopPlayer() {
        JSONObject obj = new JSONObject();
        publishGameChannel(JService.RELOAD_TOPPLAYER + "@" + obj.toString());
    }

    public void kickUser(String username) {
        JSONObject obj = new JSONObject();
        obj.put("id", CfgServer.SERVER_ID);
        obj.put("newUser", username);
        publishGameChannel(JService.KICK_USER + "@" + obj.toString());
    }

    public void kickUserSpecUser(int serverId, String username) {
        JSONObject obj = new JSONObject();
        obj.put("id", serverId);
        obj.put("newUser", username);
        publishGameChannel(JService.KICK_USER_SPEC + "@" + obj.toString());
    }

    public void kickLagUser(String username) {
        JSONObject obj = new JSONObject();
        obj.put("id", CfgServer.SERVER_ID);
        obj.put("newUser", username);
        publishGameChannel(JService.KICK_LAG_USER + "@" + obj.toString());
    }

    public void updateBalance(String username, long balance) {
        setBalance(username, balance);
        // JSONObject mapObject = new JSONObject();
        // mapObject.put("id", Xerver.SERVER_ID);
        // mapObject.put("newUser", username);
        // mapObject.put("balance", balance);
        // publishGameChannel(JedisService.UPDATE_BALANCE + "@" + mapObject.toString());
    }

    public int exists(String key) {
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            return jedis.exists(key) ? 1 : 0;
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            returnResource(jedis);
        }
        return -1;
    }

    public void returnResource(redis.clients.jedis.Jedis jedis) {
        try {
            if (jedis != null) {
                pool.returnResource(jedis);
            }
        } catch (Exception ex) {
        }
    }

    public void setBalanceLogout(String username, long balance) {
        redis.clients.jedis.Jedis jedis = pool.getResource();
        try {
            String tmp = jedis.set(JService.KEY_BALANCE_LOGOUT + username, String.valueOf(balance));
            jedis.expire(JService.KEY_BALANCE_LOGOUT + username, 60 * 2);
            if (!tmp.equalsIgnoreCase("ok")) {
                warn("setBalanceLogout -> " + username + " -> " + balance + " -> " + tmp);
            }
            jedis.expire(JService.KEY_BALANCE + username, 60 * 30);
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
    }

    public void increaseBalance(String username, long addBalance) {
        redis.clients.jedis.Jedis jedis = pool.getResource();
        try {
            long tmp = jedis.incrBy(JService.KEY_BALANCE + username, addBalance);
            if (tmp < 0) {
                warn("increaseBalance -> " + username + " -> " + addBalance + " -> " + tmp);
            }
            jedis.expire(JService.KEY_BALANCE + username, 60 * 30);
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
    }

    public void setBalance(String username, long balance) {
        redis.clients.jedis.Jedis jedis = pool.getResource();
        try {
            String tmp = jedis.set(JService.KEY_BALANCE + username, String.valueOf(balance));
            if (!tmp.equalsIgnoreCase("ok")) {
                warn("setBalance -> " + username + " -> " + balance + " -> " + tmp);
            }
            jedis.expire(JService.KEY_BALANCE + username, 60 * 30);
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
    }

    public void clearBalanceCache(String username) {
        redis.clients.jedis.Jedis jedis = pool.getResource();
        try {
            jedis.del(JService.KEY_BALANCE + username);
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
    }

    public long getBalance(String username) {
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            String value = jedis.get(JService.KEY_BALANCE + username);
            return Long.parseLong(value);
        } catch (Exception ex) {
            //ex.printStackTrace();
            // getLogger().error(Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return -1;
    }

    public long getBalanceLogout(String username) {
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            String value = jedis.get(JService.KEY_BALANCE_LOGOUT + username);
            return Long.parseLong(value);
        } catch (Exception ex) {
            //ex.printStackTrace();
            // getLogger().error(Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return -1;
    }

    // public boolean test(String msg) {
    // Jedis jedis = pool.getResource();
    // try {
    // debug("======================================");
    // debug(jedis.publish(moneyChannel, msg));
    // debug(jedis.publish("abc", msg));
    // debug("======================================");
    // } catch (Exception ex) {
    // } finally {
    // pool.returnResource(jedis);
    // }
    // return false;
    // }
    public boolean publishGameChannel(String msg) {
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            if (jedis.publish(gameChannel, msg) > 0) {
                return true;
            }
            return false;
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error("publishGameChannel=" + Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return false;
    }

    public boolean publishServiceChannel(String msg) {
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            if (jedis.publish(CfgRedis.serviceChannel, msg) > 0) {
                return true;
            }
            return true;
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error("publishServiceChannel=" + Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return false;
    }

    public boolean publishMoneyChannel(String msg) {
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            if (jedis.publish(CfgRedis.moneyChannel, msg) > 0) {
                return true;
            }
            return false;
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error("publishMoneyChannel=" + Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return false;
    }

    public Integer getIntValue(String key) {
        key = PREFIX + key;
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            return Integer.parseInt(jedis.get(key));
        } catch (Exception ex) {
            //ex.printStackTrace();
            // getLogger().error("getIntValue=" + Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return null;
    }

    public long getLongValue(String key) {
        key = PREFIX + key;
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            return Long.parseLong(jedis.get(key));
        } catch (Exception ex) {
            //ex.printStackTrace();
            // getLogger().error("getLongValue=" + Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return -1;
    }

    public String getValue(String key) {
        key = PREFIX + key;
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            return jedis.get(key);
        } catch (Exception ex) {
            //ex.printStackTrace();
            // getLogger().error("getValue=" + Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return null;
    }

    public boolean setValue(String key, String value) {
        key = PREFIX + key;
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            String tmp = jedis.set(key, value);
            if (!tmp.equalsIgnoreCase("ok")) {
                warn("setValue -> " + key + " -> " + value);
            }
            jedis.expire(key, EXPIRE_1D);
            return true;
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error("setValue=" + Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return false;
    }

    public boolean setValue(String key, String value, int expireTimeSec) {
        key = PREFIX + key;
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            String tmp = jedis.set(key, value);
            if (!tmp.equalsIgnoreCase("ok")) {
                warn("setValue -> " + key + " -> " + value);
            }
            jedis.expire(key, expireTimeSec);
            return true;
        } catch (Exception ex) {
            //ex.printStackTrace();
            getLogger().error("setValue=" + Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
        return false;
    }

    public boolean removeValue(String key) {
        key = PREFIX + key;
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            jedis.del(key);
            return true;
        } catch (Exception ex) {
            //ex.printStackTrace();
        } finally {
            pool.returnResource(jedis);
        }
        return false;
    }

    public static Logger getLogger() {
        return slib_Logger.root();
    }

    public static void warn(String msg) {
        slib_Logger.root().warn(msg);
    }
}
