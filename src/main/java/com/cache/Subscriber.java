package com.cache;

import com.bem.config.CfgAchievement;
import com.bem.config.CfgNotify;
import com.bem.config.CfgServer;
import com.bem.config.CfgVip;
import com.bem.dao.SystemDAO;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.UserEntity;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.UserInfo;
import com.bem.object.UserMoneys;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.k2tek.IAction;
import com.k2tek.Xerver;
import com.k2tek.common.Logs;
import com.k2tek.common.slib_Logger;
import grep.database.Database2;
import grep.database.HibernateUtil;
import io.netty.channel.Channel;
import net.sf.json.JSONObject;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.slf4j.Logger;
import redis.clients.jedis.JedisPubSub;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Subscriber extends JedisPubSub {
    int serverId;

    public Subscriber(int serverId) {
        this.serverId = serverId;
    }

    @Override
    public void onMessage(String channel, String message) {
        int pos = message.indexOf("@");
        try {
            int service = Integer.parseInt(message.substring(0, pos));
            message = message.substring(pos + 1);
            // if (CfgServer.debug) {
            Logs.api(String.format("Message received. Channel: {%s}, Service: {%s}, Msg: {%s}", channel, service, message));
            // }
            switch (service) {
                case JService.KOIN:
                    napSMS(message);
                    break;
                case JService.KICK_USER:
                    kickUser(message);
                    break;
                case JService.KICK_USER_SPEC:
                    kickUserSpec(message);
                    break;
                case JService.UPDATE_BALANCE:
                    // updateBalance(message);
                    break;
                case JService.NOTIFY_MESSAGE:
                    notifyMessage(message);
                    break;
                case JService.KICK_LAG_USER:
//                    kickLagUser(message);
                    break;
                case JService.ADMIN_RELOAD_CONFIG:
                    reloadConfig();
                    break;
                case JService.ADMIN_SENDMSG:
                    sendMsgToUser(message);
                    break;
                case JService.MAIL_TO_USER:
                    sendMsgToUser(message);
                    break;
                case JService.UPDATE_MESSENGER:
                    //ChatHandler.getInstance().updateAdmin(message);
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
    }

    public static void main(String[] rags) throws Exception {
        //{"type":2,"userId":2,"username":"nhdang1990","message":"Bạn đã nạp 500 GEM vào t/k [nhdang1990] thành công.\nSố GEM trước khi nạp: 173915, sau khi nạp: 174415.","money":100000,"gem":500,"gemInit":500,"totalGem":79405,"totalGemInit":79405,"cp":"TEST","firstNap":false}
//        Config.load(Xerver.DEFAULT_CONFIG_FILE);
//        Xerver.initGameConfig();
//        String msg = "{\"type\":2,\"userId\":6172,\"username\":\"linobonbon\",\"message\":\"Bạn đã nạp 3000 GEM vào t/k 'linobonbon' thành công.\\nSố GEM trước khi nạp: 6300, sau khi nạp: 9300.\",\"money\":500000,\"gem\":3000,\"gemInit\":3000,\"totalGem\":6000,\"totalGemInit\":6000,\"cp\":\"TEST\",\"firstNap\":false}";
//        new Subscriber().napSMS(msg);
    }

    public void napSMS(String message) {
        SystemDAO dao = new SystemDAO();
        JSONObject params = JSONObject.fromObject(message);
        long userId = params.getLong("userId");
        long gem = params.getLong("gem");
        long gemNap = params.getLong("gemInit");
        long totalgem = params.getLong("totalGem");
        int type = params.getInt("type");// 1sms , 2 card , 3iapios , 4 iap androd, 102 thẻ tháng
        String msg = params.getString("message");
        String username = params.getString("username");
        long money = params.getInt("money");
        String cp = params.getString("cp").toLowerCase();
        boolean firstNap = params.getBoolean("firstNap");
//        System.out.println("message--->" + message);
        UserDAO uDAO = new UserDAO();
        UserEntity userEntity = uDAO.getUserMain(userId, serverId);
        userId = userEntity.getId();
        Channel channel = Online.getChannel(userId);
        UserInfo user = UserMonitor.getUser(userId);
//        if(userId!=13){
//            return;
//        }
//        else{
//            System.out.println("pubs");
//        }
        if (user != null) {
            user.getDbUser().addGem(gem);
            user.getDbUser().setGemNap(user.getDbUser().getGemNap() + gemNap);
            try {
                if (CfgAchievement.getAchievement(CfgAchievement.NAP, user.getDbUser()).inTime()) {
                    if (user.getDbUser().getSumGemIntEvent() < 0) {
                        user.getDbUser().setSumGemIntEvent(0);
                    }
                    user.getDbUser().setSumGemIntEvent(user.getDbUser().getSumGemIntEvent() + gemNap);
                }
            } catch (Exception ex) {

            }
            int oldVip = user.getDbUser().getVip();
            user.getUData().getInitUInt(user.getDbUser());
            int newVip = user.getDbUser().getVip();
            if (oldVip != newVip)
                Database2.update("user", Arrays.asList("vip", String.valueOf(newVip)), Arrays.asList("id", String.valueOf(userId)));
            while (newVip > oldVip) {
                dao.sendMail(userId, "Phần thưởng nạp lên vip " + newVip, CfgVip.config.get(newVip).gift.toString());
                newVip--;
            }
            user.getDbUser().addSymBol(user);
            List<Long> aLong = new ArrayList<Long>();
            aLong.add(user.getDbUser().getGold());
            aLong.add(user.getDbUser().getGem());
            UserMoneys moneys = user.getUData().getUserMoneys();
            aLong.add(moneys.getValue(UserMoneys.MEDAL_BOSS));
            aLong.add(moneys.getValue(UserMoneys.MEDAL));
            aLong.add(moneys.getValue(UserMoneys.CLAN));
            Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(null, Arrays.asList(msg)), IAction.MSG_POPUP, System.currentTimeMillis());
            Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(CfgNotify.loginNotify(user), null), IAction.LOGIN_NOTIFY, System.currentTimeMillis());
            Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(aLong, null), IAction.SYNC_MONEYS, System.currentTimeMillis());
            Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(Arrays.asList(money), Arrays.asList(System.currentTimeMillis() + "")), IAction.TRACKING_MONEY, System.currentTimeMillis());
            Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getDbUser().getVip(), user.getDbUser().getGemNap()), null), IAction.VIP_INFOR, System.currentTimeMillis());
        } else {
            String sTotalGemNap = Database2.getUniqueColumn("user", Arrays.asList("id", userId + ""), "gem_nap");
            try {
                long totalGemNap = Long.parseLong(sTotalGemNap);
                int oldVip = CfgVip.getVip(totalGemNap - gemNap);
                int newVip = CfgVip.getVip(totalGemNap);
                if (oldVip != newVip)
                    Database2.update("user", Arrays.asList("vip", String.valueOf(newVip)), Arrays.asList("id", String.valueOf(userId)));
                while (newVip > oldVip) {
                    dao.sendMail(userId, "Phần thưởng nạp lên vip " + newVip, CfgVip.config.get(newVip).gift.toString());
                    newVip--;
                }
            } catch (Exception ex) {
                Logs.error(Util.exToString(ex));
            }
        }
        if (firstNap) {
            dao.sendMail(userId, "Phần thưởng nạp thẻ lần đầu ", "[12,3,1,1,7,39,1,1,100000,9,15,15]");
        }
    }

    List<UserEntity> dbListUser(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user where id =" + userId).addEntity(UserEntity.class);
            return query.list();
        } catch (HibernateException he) {
            getLogger().error(Util.exToString(he));
        } finally {
            if (session != null) {
                session.close();
            }
        }
        return null;
    }

    private void sendMsgToUser(String messgae) {
        JSONObject obj = JSONObject.fromObject(messgae);
        long userId = obj.getLong("userId");
        Channel ch = Online.mChannel.get(userId);
        if (ch != null) {
            List<Long> vLong = CfgNotify.mailNotify(userId);
//            Util.sendProtoData(ch, CommonProto.getCommonLongVectorProto(vLong, null), IAction.LOGIN_NOTIFY, System.currentTimeMillis());

        }
    }

    private void reloadConfig() {
        Xerver.initGameConfig();
    }

    /**
     * @param message
     * @desc thông báo kết quả invite cho newUser
     * @sample {"type":1 ok/2 false,"username":"username"}
     */
    private void notifyMessage(String message) {
        JSONObject params = JSONObject.fromObject(message);
        String username = Util.getStringJson(params, "username");
        int type = Util.getIntJson(params, "type"); // 1:invite thành công, 2: invite thất bại
//        Channel channel = Online.getChannel(username);
//        if (channel != null) {
//            Util.sendProtoData(channel, CommonProto.getStringProto(type == 1 ? CfgDailyBonus.inviteMessage1 : CfgDailyBonus.inviteMessage2), BaseServiceMessage.MSG_SLIDER, System.currentTimeMillis());
//        }
    }

    private void kickUserSpec(String message) {
        JSONObject obj = JSONObject.fromObject(message);
        int serverId = obj.getInt("id");
        String username = obj.getString("newUser");
        if (serverId == CfgServer.SERVER_ID && username.length() > 0) {
//            Channel ch = Online.getChannel(username);
//            if (ch != null) {
//                Util.sendProtoData(ch, CommonProto.getStringProto(Lang.instance((String) ChUtil.get(ch, Constans.KEY_LOCALE)).get(Lang.common_duplicate_login)), BaseServiceMessage.DUPLICATE_LOGIN, System.currentTimeMillis());
//                Util.logoutUser(ch, true, false);
//            } else {
//                getLogger().warn("kickUserSpec false -> " + username);
//            }
//            JCache.getInstance().removeValue(JedisService.KEY_USERONLINE + username);
        }
        // String sessionId = JCache.getInstance().getValue(JedisService.KEY_SESSION + username);
        // if (sessionId != null) {
        // Online.addSession(username, sessionId);
        // }
    }

    private void kickUser(String message) {
        JSONObject obj = JSONObject.fromObject(message);
        int serverId = obj.getInt("id");
        String username = obj.getString("newUser");
        if (serverId != CfgServer.SERVER_ID && username.length() > 0) {
//            Channel ch = Online.getChannel(username);
//            if (ch != null) {
//                Util.sendProtoData(ch, CommonProto.getStringProto(Lang.instance((String) ChUtil.get(ch, Constans.KEY_LOCALE)).get(Lang.common_duplicate_login)), BaseServiceMessage.DUPLICATE_LOGIN, System.currentTimeMillis());
//                Util.logoutUser(ch, true, false);
//            }
            // else {
            // getLogger().warn("kickUser false -> " + username);
            // }
            // JCache.getInstance().removeValue(JedisService.KEY_USERONLINE + username);
        }
        // String sessionId = JCache.getInstance().getValue(JedisService.KEY_SESSION + username);
        // if (sessionId != null) {
        // Online.addSession(username, sessionId);
        // }
    }

    private void updateBalance(String message) {
        JSONObject obj = JSONObject.fromObject(message);
        int serverId = obj.getInt("id");
        String username = obj.getString("newUser");
        int balance = obj.getInt("balance");
        if (serverId != CfgServer.SERVER_ID && username.length() > 0 && balance >= 0) {
//            Channel ch = Online.getChannel(username);
//            if (ch != null) {
//                UserInfo newUser = Util.getUser(ch);
//                if (newUser != null) {
////                    newUser.setBalance(balance);
//                }
//            }
            // ProcessUserKoin.addUserProcess(username, balance);
        }
    }

    @Override
    public void onPMessage(String pattern, String channel, String message) {

    }

    @Override
    public void onSubscribe(String channel, int subscribedChannels) {

    }

    @Override
    public void onUnsubscribe(String channel, int subscribedChannels) {

    }

    @Override
    public void onPUnsubscribe(String pattern, int subscribedChannels) {

    }

    @Override
    public void onPSubscribe(String pattern, int subscribedChannels) {

    }

    public Logger getLogger() {
        return slib_Logger.root();
    }

    public Logger getZombieLogger() {
        return slib_Logger.root();
    }
}
