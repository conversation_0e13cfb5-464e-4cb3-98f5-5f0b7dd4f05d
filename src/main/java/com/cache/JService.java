package com.cache;

public class JService {
    public static final int KOIN = 1;
    public static final int KICK_USER = 2;
    public static final int UPDATE_BALANCE = 3;
    public static final int UPDATE_CONFIG = 4;
    public static final int UPDATE_GAME_RESULT = 5;
    public static final int KICK_LAG_USER = 6;
    public static final int KICK_USER_SPEC = 7;
    public static final int NOTIFY_MESSAGE = 100;
    public static final int CHECK_PROCESS_USER = 1000;
    public static final int ADMIN_RELOAD_CONFIG = 1002;
    public static final int ADMIN_SENDMSG = 1003;
    public static final int RELOAD_TOPPLAYER = 1004;
    public static final int UPDATE_MESSENGER = 1005;
    public static final int MAIL_TO_USER = 1006;


    public static final String KEY_LOGIN_TIME = "t";
    public static final String KEY_SESSION = "s";
    public static final String KEY_BALANCE = "bl";
    public static final String KEY_BALANCE_LOGOUT = "blo|";
    public static final String KEY_USERONLINE = "o";
    public static final String KEY_TOP_BOSS = "topboss:";
    public static final String KEY_TIME_TOP_BOSS = "timetopboss:";

    // clear cache
    public static final int SCHEDULE_CLEAR_CACHE = 50;

}
