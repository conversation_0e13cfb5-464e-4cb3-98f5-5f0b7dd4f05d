package com.cache;

import com.bem.boom.object.AchievementInfo;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgNewDropItem;
import com.bem.config.CfgServer;
import com.bem.dao.EventDAO;
import com.bem.dao.SystemDAO;
import com.bem.dao.mapping.ConfigEntity;
import com.bem.dao.mapping.ConfigEvent;
import com.bem.object.ITimer;
import com.bem.util.Util;
import com.k2tek.Xerver;
import com.k2tek.common.slib_Logger;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;

import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class ConfigEventReload implements ITimer {

    static ConfigEventReload instance;

    public static ConfigEventReload getInstance() {
        if (instance == null) {
            instance = new ConfigEventReload();
        }
        return instance;
    }

    EventDAO dao = new EventDAO();
    SystemDAO systemDAO = new SystemDAO();

    public void doExpireTurn(int turnId) {
        try {
            List<ConfigEvent> configEvents = dao.getListConfigEvent();
            if (configEvents != null && !configEvents.isEmpty()) {
                List<AchievementInfo> list = configEvents.stream().map(ConfigEvent::toAchievement).collect(Collectors.toList());;
                CfgAchievement.setNewData(list);
            }
            ConfigEntity configEntity = systemDAO.getConfig("config_newDropItem");
            if (configEntity != null)
                CfgNewDropItem.loadConfig(configEntity.getValue());
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
        timer();
    }

    void timer() {
        try {
            Xerver.timer(this, 0, 120);
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
    }


    public static void debug(String msg) {
        if (CfgServer.debug) {
            slib_Logger.root().debug(msg);
        }
    }

    protected Logger getLogger() {
        return slib_Logger.root();
    }
}
