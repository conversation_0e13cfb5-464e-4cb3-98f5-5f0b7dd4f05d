package com.cache;

import com.bem.config.CfgServer;
import com.k2tek.Config;
import com.k2tek.Xerver;
import net.spy.memcached.MemcachedClient;

import java.io.IOException;
import java.net.InetSocketAddress;

public class MCache {

    public static final int DEFAULT_SESSION_EXPIRE = 60 * 60 * 24;// 1 day
    public static final int EXPIRE_1D = 60 * 60 * 24;
    public static final int EXPIRE_WEEK = 7 * EXPIRE_1D;
    public static final int EXPIRE_3D = 60 * 60 * 24 * 3;
    public static final int EXPIRE_1H = 60 * 60;
    public static final int EXPIRE_2H = 60 * 60 * 2;
    public static final int EXPIRE_12H = 60 * 60 * 12;
    public static final int EXPIRE_30M = 60 * 30;
    public static final int EXPIRE_15M = 60 * 15;
    public static final int EXPIRE_10M = 60 * 10;
    public static final int EXPIRE_5M = 60 * 5;
    public static final int EXPIRE_1M = 60;
    public static final int EXPIRE_1S = 1;
    public static final String PREFIX = CfgServer.SERVER_ID + ":";
    private static MCache instance;
    private MemcachedClient memCache;

    private MCache() throws IOException {
        memCache = new MemcachedClient(new InetSocketAddress(Config.getMemcacheHost(), Integer.parseInt(Config.getMemcachePort())));
    }

    public static MCache getInstance() {
        if (instance == null) {
            try {
                instance = new MCache();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return instance;
    }

    public void set(String key, Object value, int expire) {
        memCache.set(PREFIX + key, expire, value);
    }

    public void set(String key, Object value) {
        memCache.set(PREFIX + key, DEFAULT_SESSION_EXPIRE, value);
    }

    public Object get(String key) {
        return memCache.get(PREFIX + key);
    }

    public Object getNormal(String key) {
        return memCache.get(key);
    }

    public void setNormal(String key, Object value) {
        memCache.set(key, DEFAULT_SESSION_EXPIRE, value);
    }

    public void setNormal(String key, Object value, int expire) {
        memCache.set(key, expire, value);
    }


    public Object getAndTouch(String key, int expire) {
        memCache.touch(PREFIX + key, expire);
        return get(key);
    }

    public Object getAndTouch(String key) {
        return getAndTouch(PREFIX + key, DEFAULT_SESSION_EXPIRE);
    }

    public Object touch(String key, int expire) {
        return memCache.touch(PREFIX + key, expire);
    }

    public void touch(String key) {
        memCache.touch(PREFIX + key, DEFAULT_SESSION_EXPIRE);
    }

    public Long getLong(String key) {
        try {
            return (Long) get(key);
        } catch (Exception ex) {
        }
        return null;
    }

    public Integer getInt(String key) {
        try {
            return (Integer) get(key);
        } catch (Exception ex) {
        }
        return null;
    }

    public void delete(String key) {
        memCache.delete(PREFIX + key);
    }

    public void deleteNormal(String key) {
        memCache.delete(key);
    }

    /**
     * Key cache
     */
    public static final String KEY_SHIELD = "SHIELD:";
    public static final String KEY_RANK_TROPHY = "RTROPHY:";
    public static final String KEY_MISSION = "MISSION1:";
    public static final String KEY_MISSION_NUMBER = "MISSION:";
}
