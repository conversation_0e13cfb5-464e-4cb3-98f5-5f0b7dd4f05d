package com.cache.job;

import com.bem.config.CfgCommon;
import com.google.gson.Gson;
import com.k2tek.Config;
import grep.database.HibernateUtil;
import net.sf.json.JSONObject;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by <PERSON><PERSON> on 9/15/2014.
 */
public class Analysis {
    static int serverId = 0;

    public static void main(String[] args) throws Exception {
        Config.load("config.xml");
        serverId = Config.getInt("config.server.id");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        int number = 1;
        if (args.length == 1) {
            number = Integer.parseInt(args[0]);
        }

        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, -number);

        String path = "logs/act";
        String filename = "act.log." + sdf.format(ca.getTime());
        if (args.length == 2) {
            filename = "act.log";
        }
        System.out.println(filename);
        if (args.length == 2) {
            new Analysis().processFile(path, filename, ca.getTime(), false);
        } else {
            new Analysis().processFile(path, filename, ca.getTime(), true);
        }

//        for (int i = 0; i < 7; i++) {
//            number = i + 1;
//            Calendar ca = Calendar.getInstance();
//            ca.add(Calendar.DATE, -number);
//
//            String filename = "logs/act.log." + sdf.format(ca.getTime());
//            System.out.println(filename);
//            new Analysis().processFile(filename, ca.getTime());
//        }

        new Analysis().clearBattleLogs();

        System.exit(0);
    }

    /**
     * @param usedgold - theo item shop trong db
     * @param minigame - quay, auto ai, dau truong
     */
    final static int GAME_QUAY = 0, AUTO_MAP = 1, GAME_HERO_SOUL = 2;

    public void processFile(String path, String filename, Date dateTime, boolean isSaveDB) {
        List<String> aName = new ArrayList<String>();
        File f = new File(path);
        String[] tmp1 = f.list();
        for (int i = 0; i < tmp1.length; i++) {
            if (tmp1[i].contains(filename)) {
                aName.add(tmp1[i]);
            }
        }

        int[] usedGold = new int[20];
        String[] aAction = {"game", "map", "game", "game", "game", "user", "game", "game"};
        String[] aDetail = {"quay", "auto_result", "soul", "kingdom", "boss", "mission", "jackpot", "congthanh"};
        int[] minigame = new int[aAction.length];

        String[] aBonusAction = {"other", "receive", "receive", "receive", "game", "receive", "receive", "receive"};
        String[] aBonusDetail = {"other", "quay", "tambao", "giftbox", "market", "achievement", "boss", "mission"};
        int[] bonusGold = new int[aBonusAction.length];

        String[] addSilverAction = {"other", "item", "receive", "receive", "receive", "receive"};
        String[] addSilverDetail = {"other", "sell", "quay", "tambao", "giftbox", "achievement"};
        long[] addSilver = new long[addSilverAction.length];

        String[] minusSilverAction = {"other", "item", "hero", "hero", "war"};
        String[] minusSilverDetail = {"other", "upgrade", "pvp", "feepvp", "upgrade"};
        long[] minusSilver = new long[minusSilverAction.length];

        for (int index = 0; index < aName.size(); index++) {
            FileInputStream inputStream = null;
            Scanner sc = null;
            try {
                inputStream = new FileInputStream(path + "/" + aName.get(index));
                sc = new Scanner(inputStream, "UTF-8");
                String line;
                while (sc.hasNextLine()) {
                    line = sc.nextLine();
                    String[] tmp = line.substring(line.indexOf("u") + 1).split(" ");
                    int userId = Integer.parseInt(tmp[0]);
                    String action = tmp[1];
                    if (tmp.length > 2) {
                        String detail = tmp[2];
                        try {
                            JSONObject obj = JSONObject.fromObject(tmp[3]);
                            if (obj.containsKey("addGold")) {
                                if (action.equals("useditem") && detail.equals("buy")) {
                                    int id = obj.getInt("id");
                                    if (id <= 20) {
                                        usedGold[id - 1] += obj.getInt("addGold");
                                    }
                                } else if (action.equals("game") && detail.equals("forge")) {
                                    usedGold[16 + obj.getInt("type")] += obj.getInt("addGold");
                                } else {
                                    boolean isFound = false;
                                    for (int i = 0; i < aAction.length; i++) {
                                        if (action.equals(aAction[i]) && detail.equals(aDetail[i])) {
                                            isFound = true;
                                            minigame[i] += obj.getInt("addGold");
                                            break;
                                        }
                                    }

                                    for (int i = 0; i < aBonusAction.length; i++) {
                                        if (action.equals(aBonusAction[i]) && detail.equals(aBonusDetail[i])) {
                                            isFound = true;
                                            bonusGold[i] += obj.getInt("addGold");
                                            break;
                                        }
                                    }
                                    if (!isFound) {
                                        bonusGold[0] += obj.getInt("addGold");
                                    }
                                }
                            } else if (obj.containsKey("addSilver")) {
                                long silver = obj.getLong("addSilver");
                                if (silver > 0) {
                                    boolean isFound = false;
                                    for (int i = 0; i < addSilverAction.length; i++) {
                                        if (action.equals(addSilverAction[i]) && detail.equals(addSilverDetail[i])) {
                                            addSilver[i] += silver;
                                            isFound = true;
                                            break;
                                        }
                                    }
                                    if (!isFound) {
                                        addSilver[0] += silver;
                                    }
                                } else if (silver < 0) {
                                    boolean isFound = false;
                                    for (int i = 0; i < minusSilverAction.length; i++) {
                                        if (action.equals(minusSilverAction[i]) && detail.equals(minusSilverDetail[i])) {
                                            minusSilver[i] += silver;
                                            isFound = true;
                                            break;
                                        }
                                    }
                                    if (!isFound) {
                                        minusSilver[0] += silver;
                                    }
                                }
                            }
                        } catch (Exception ex) {
                        }
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                try {
                    if (inputStream != null) {
                        inputStream.close();
                    }
                    if (sc != null) {
                        sc.close();
                    }
                } catch (Exception ex) {

                }
            }
        }

        Gson gson = new Gson();
        JSONObject obj = new JSONObject();

        JSONObject gold = new JSONObject();
        gold.put("shop", gson.toJson(usedGold));
        gold.put("game", gson.toJson(minigame));
        gold.put("bonus", gson.toJson(bonusGold));

        JSONObject silver = new JSONObject();
        silver.put("add", gson.toJson(addSilver));
        silver.put("minus", gson.toJson(minusSilver));

        obj.put("gold", gold);
        obj.put("silver", silver);

        System.out.println("Gold -> " + gold.toString());
        System.out.println("Silver -> " + silver.toString());

        if (isSaveDB) {
            saveDbLogs(obj.toString(), dateTime);
        }
    }

    void saveDbLogs(String logs, Date dateTime) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("insert " + CfgCommon.mainDb + " ana_daily_log(server_id,logs,date_created) values(?,?,?)");
            query.setInteger(0, serverId);
            query.setString(1, logs);
            query.setDate(2, dateTime);
            query.executeUpdate();
            session.getTransaction().commit();
        } catch (Exception he) {
            he.printStackTrace();
        } finally {
            closeSession(session);
        }
    }

    void clearBattleLogs() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            Calendar ca = Calendar.getInstance();
            ca.add(Calendar.DATE, -4);
            session.createSQLQuery("delete from user_activity_log where date(date_created) < '" + new SimpleDateFormat("yyyy-MM-dd").format(ca.getTime()) + "'").executeUpdate();
            session.getTransaction().commit();
        } catch (Exception he) {
            he.printStackTrace();
        } finally {
            closeSession(session);
        }
    }

    void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
}
