package com.cache.job;

import com.bem.config.CfgCommon;
import com.bem.config.CfgServer;
import grep.database.HibernateUtil;
import com.k2tek.Config;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by <PERSON><PERSON> on 9/15/2014.
 */
public class LogUserAction {
    static int serverId = 0;
    static String path = "";
    static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    public static void main(String[] args) throws Exception {
        Config.load("config.xml");
        serverId = Config.getInt("config.server.id");
        path = Config.getString("config.userlog.path") + "/" + sdf.format(new Date()) + "/" + serverId + "/";
        File f = new File(path);
        if (!f.exists()) {
            f.mkdirs();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
        String filename = "";
        if (args.length == 1) {
            filename = args[0];
        } else {
            Calendar ca = Calendar.getInstance();
            ca.add(Calendar.HOUR_OF_DAY, -1);
            filename = sdf.format(ca.getTime());
        }
        filename = "logs/act/act.log." + filename;
        new LogUserAction().processFile(filename);
        System.exit(0);
    }

    /**
     * @param usedgold - theo item shop trong db
     * @param minigame - quay, auto ai, dau truong
     */
    final static int GAME_QUAY = 0, AUTO_MAP = 1, GAME_HERO_SOUL = 2;

    public void processFile(String logFile) {
        FileInputStream inputStream = null;
        Scanner sc = null;
        Map<String, List<String>> mLog = new HashMap<String, List<String>>();
        try {
            inputStream = new FileInputStream(logFile);
            sc = new Scanner(inputStream, "UTF-8");
            String line;
            while (sc.hasNextLine()) {
                line = sc.nextLine();
                String userId = line.substring(line.indexOf("u") + 1);
                userId = userId.substring(0, userId.indexOf(" "));
                if (!mLog.containsKey(userId)) {
                    mLog.put(userId, new ArrayList<String>());
                }
                mLog.get(userId).add(line);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (sc != null) {
                    sc.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        Iterator<String> iter = mLog.keySet().iterator();
        while (iter.hasNext()) {
            String key = iter.next();
            List<String> data = mLog.get(key);
            try {
                PrintWriter out = new PrintWriter(new BufferedWriter(new FileWriter(path + key, true)));
                for (int i = 0; i < data.size(); i++) {
                    out.println(data.get(i));
                }
                out.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    void saveDbLogs(String logs, Date dateTime) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("insert " + CfgCommon.mainDb + "ana_daily_log(server_id,logs,date_created) values(?,?,?)");
            query.setInteger(0, serverId);
            query.setString(1, logs);
            query.setDate(2, dateTime);
            query.executeUpdate();
            session.getTransaction().commit();
        } catch (Exception he) {
            he.printStackTrace();
        } finally {
            closeSession(session);
        }
    }

    void clearBattleLogs() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            Calendar ca = Calendar.getInstance();
            ca.add(Calendar.DATE, -4);
            session.createSQLQuery("delete from user_activity_log where date(date_created) < '" + new SimpleDateFormat("yyyy-MM-dd").format(ca.getTime()) + "'").executeUpdate();
            session.getTransaction().commit();
        } catch (Exception he) {
            he.printStackTrace();
        } finally {
            closeSession(session);
        }
    }

    void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
}
