package com.cache;

import com.bem.config.CfgAchievement;
import com.bem.config.CfgCommon;
import com.bem.config.CfgRankTrophy;
import com.bem.config.CfgServer;
import com.bem.dao.SystemDAO;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.SystemMessageEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.UserEventEntity;
import com.bem.object.ITimer;
import com.bem.object.event.TopEventLoop;
import com.bem.object.event.TopTrophy;
import com.bem.object.event.TopWin;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.k2tek.Constans;
import com.k2tek.Xerver;
import com.k2tek.common.slib_Logger;
import grep.database.Database2;
import lombok.Data;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.xml.DOMConfigurator;
import org.slf4j.Logger;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.*;

@Data
public class MainCache implements ITimer {

    public static JedisPool pool;
    public static String KEY_TOP_MONEY = "cache:topmoney";
    public static String KEY_TOP_LEVEL = "cache:topLvlBoom";
    public static String KEY_TOP_VIP = "cache:topvip";
    public static String KEY_TOP_RANK = "cache:rank";
    public static String KEY_SYS_MSG = "cache:sysmsg";
    public static UserDAO uDAO = new UserDAO();
    public static String[] gameId;
    public static Gson gson = new Gson();

    static MainCache instance;

    public static MainCache getInstance() {
        if (instance == null) {
            instance = new MainCache();
        }
        return instance;
    }

    public MainCache() {
    }

    //region System message
    Map<Integer, List<SystemMessageEntity>> aMessage = new HashMap<>();
    //    Map<Integer,List<UserEntity>> aUserTrophy = new HashMap<>();
    Map<Integer, List<UserEntity>> aUserTrophy100 = new HashMap<>();
    Map<Integer, List<UserEntity>> aUserWin100 = new HashMap<>();
    Map<Integer, Map<Integer, List<UserEntity>>> aTopEventLoop = new HashMap<>();


    public void cacheTopTrophy() {
        debug("cache top trophy");
        for (int ii = 1; ii < 3; ii++) {
            int eventIndex = TopTrophy.getInstance().getEventIndex()[0];
            debug("");
            List<UserEventEntity> aEvent = Database2.getList("user_event", Arrays.asList("event_id", String.valueOf(Constans.EVENT_TOP_TROPHY), "event_index", String.valueOf(eventIndex), "server_id", String.valueOf(ii)), "order by value desc limit 0, 1000", UserEventEntity.class);
            if (aEvent != null) {
                String ids = "";
                debug("aEvent.size--->" + aEvent.size());
                for (UserEventEntity event : aEvent) {
                    ids += "," + event.getUserId();
                }
                if (ids.length() > 0) {
                    ids = ids.substring(1);
                    List<UserEntity> aUser = Database2.getList("user", new ArrayList<>(), "where id in (" + ids + ")", UserEntity.class);
                    for (int i = aUser.size() - 1; i >= 0; i--) {
                        for (int j = 0; j < aEvent.size(); j++) {
                            if (aEvent.get(j).getUserId() == aUser.get(i).getId()) {
                                aUser.get(i).setTrophy((int) aEvent.get(j).getValue());
                                aEvent.remove(j);
                                break;
                            }
                        }
                    }
                    aUser.sort((UserEntity u1, UserEntity u2) -> u2.getTrophy() - u1.getTrophy());
                    List<UserEntity> saUserTrophy100 = new ArrayList<>();
                    saUserTrophy100 = aUser.size() > 100 ? aUser.subList(0, 100) : aUser;
                    getRank(saUserTrophy100);
//                    aUserTrophy = aUser;
                    debug("saUserTrophy100.size = " + saUserTrophy100.size());
                    aUserTrophy100.put(ii, saUserTrophy100);
                    debug(String.valueOf(aUserTrophy100.size()));
                    debug("aUserTrophy = " + saUserTrophy100.size());
                } else {
                    aUserTrophy100.put(ii, new ArrayList<>());

                }
            } else {
                aUserTrophy100.put(ii, new ArrayList<>());
                debug("aEvent null");
            }
        }
    }

    public void cacheTopWin() {
        for (int ii = 1; ii < 3; ii++) {
            int eventIndex = TopWin.getInstance().getEventIndex()[0];
            List<UserEventEntity> aEvent = Database2.getList("user_event", Arrays.asList("event_id", String.valueOf(Constans.EVENT_TOP_WIN), "event_index", String.valueOf(eventIndex), "server_id", String.valueOf(ii)), "order by value desc limit 0, 100", UserEventEntity.class);
            if (aEvent != null) {
                String ids = "";
                for (UserEventEntity event : aEvent) {
                    ids += "," + event.getUserId();
                }
                if (ids.length() > 0) {
                    ids = ids.substring(1);
                    List<UserEntity> aUser = Database2.getList("user", new ArrayList<>(), "where id in (" + ids + ")", UserEntity.class);
                    for (int i = aUser.size() - 1; i >= 0; i--) {
                        for (int j = 0; j < aEvent.size(); j++) {
                            if (aEvent.get(j).getUserId() == aUser.get(i).getId()) {
                                aUser.get(i).setMaxwin((int) aEvent.get(j).getValue());
                                aEvent.remove(j);
                                break;
                            }
                        }
                    }
                    aUser.sort((UserEntity u1, UserEntity u2) -> u2.getMaxwin() - u1.getMaxwin());
                    List<UserEntity> saUserWin100 = new ArrayList<>();
                    saUserWin100 = aUser.size() > 100 ? aUser.subList(0, 100) : aUser;
                    aUserWin100.put(ii, saUserWin100);
                }
            }
        }
    }

    public void subCacheTopLoop(int eventId) {
        for (int ii = 1; ii < 3; ii++) {
            int eventIndex = TopEventLoop.getInstance(eventId).getEventIndex()[0];
            List<UserEventEntity> aEvent = Database2.getList("user_event", Arrays.asList("event_id", String.valueOf(eventId), "event_index", String.valueOf(eventIndex), "server_id", String.valueOf(ii)), "order by value desc limit 0, 100", UserEventEntity.class);
            if (aEvent != null) {
                String ids = "";
                for (UserEventEntity event : aEvent) {
                    ids += "," + event.getUserId();
                }
                if (ids.length() > 0) {
                    ids = ids.substring(1);
                    List<UserEntity> aUser = Database2.getList("user", new ArrayList<>(), "where id in (" + ids + ")", UserEntity.class);
                    for (int i = aUser.size() - 1; i >= 0; i--) {
                        for (int j = 0; j < aEvent.size(); j++) {
                            if (aEvent.get(j).getUserId() == aUser.get(i).getId()) {
                                aUser.get(i).setMaxwin((int) aEvent.get(j).getValue());
                                aEvent.remove(j);
                                break;
                            }
                        }
                    }
                    if (aUser == null) {
                        aUser = new ArrayList<>();
                    }
                    aUser.sort((UserEntity u1, UserEntity u2) -> u2.getMaxwin() - u1.getMaxwin());
                    Map<Integer, List<UserEntity>> saTopEventLoop = new HashMap<>();
                    saTopEventLoop.put(eventId, aUser.size() > 100 ? aUser.subList(0, 100) : aUser);
                    aTopEventLoop.put(ii, saTopEventLoop);
                }
            }
        }
    }

    public void cacheTopLoop() {
        int[] arr = {Constans.EVENT_ZOMBIE, Constans.EVENT_PET};
        int numberPet = 11;
        int[] arrHero = {Constans.EVENT_CHARACTER_LOOP};
        int numberHero = 6;

        for (int eventId = arr[0]; eventId < arr[arr.length - 1] + numberPet; eventId++) {
            try {
                subCacheTopLoop(eventId);
            } catch (Exception ex) {
                debug("eventIndex-->" + eventId);
                ex.printStackTrace();
            }
        }
        for (int eventId = arrHero[0]; eventId < arrHero[arrHero.length - 1] + numberHero; eventId++) {
            try {
                subCacheTopLoop(eventId);
            } catch (Exception ex) {
                debug("eventIndex-->" + eventId);
                ex.printStackTrace();
            }
        }
    }

    public void cacheTopCapDoiLoop() {
        try {
            subCacheTopLoop(Constans.EVENT_TOP_CAP_DOI);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    public static void getRank(List<UserEntity> luser) {
        int number1 = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).number;
        int number2 = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).number;
        int number3 = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).number;
        int index = 0;
        for (index = 0; index < luser.size(); index++) {
            if (number1 > 0 && luser.get(index).getTrophy() >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).trophy) {
                luser.get(index).setRankName(CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).name);
                luser.get(index).setRankId(CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).id);
                number1 = 0;
            } else if (number2 > 0 && luser.get(index).getTrophy() >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).trophy) {
                luser.get(index).setRankName(CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).name);
                luser.get(index).setRankId(CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).id);
                number2--;
            } else if (number3 > 0 && luser.get(index).getTrophy() >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).trophy) {
                luser.get(index).setRankName(CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).name);
                luser.get(index).setRankId(CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).id);
                number3--;
            } else {
                for (int i = CfgRankTrophy.data.size() - 4; i >= 0; i--) {
                    if (CfgRankTrophy.data.get(i).trophy <= luser.get(index).getTrophy()) {
                        luser.get(index).setRankName(CfgRankTrophy.data.get(i).name);
                        luser.get(index).setRankId(CfgRankTrophy.data.get(i).id);
                        break;
                    }
                }
            }
        }
    }

    public List<UserEntity> aUserTrophyTime = new ArrayList<UserEntity>();

    public static void sendAwardTopTrophyTime(List<UserEntity> users, int serverId) {
//        SystemDAO dao = new SystemDAO();
//        JSONArray[] arr = CfgAchievement.getAchievement(CfgAchievement.TOP_TROPHY, serverId).getABonus();
//        for (int i = 0; i < Math.min(50, Math.min(arr.length, users.size())); i++) {
//            dao.sendMail(users.get(i).getId(), "Phần thưởng đua tốp cúp theo thời gian. Top " + (i + 1), arr[i].toString());
//        }
    }

    public void cacheTopTrophyTime(int serverId) {
        if (CfgAchievement.inEvent(CfgAchievement.TOP_TROPHY, serverId)) {
            List<UserEventEntity> aEvent = Database2.getList("user_event", Arrays.asList("event_id", String.valueOf(Constans.EVENT_TOP_TROPHY_TIME), "server_id", String.valueOf(serverId)), "order by value desc limit 0, 100", UserEventEntity.class);
            if (aEvent != null) {
                String ids = "";
                for (UserEventEntity event : aEvent) {
                    ids += "," + event.getUserId();
                }
                if (ids.length() > 0) {
                    ids = ids.substring(1);
                    List<UserEntity> aUser = Database2.getList("user", new ArrayList<>(), "where id in (" + ids + ")", UserEntity.class);
                    for (int i = aUser.size() - 1; i >= 0; i--) {
                        for (int j = 0; j < aEvent.size(); j++) {
                            if (aEvent.get(j).getUserId() == aUser.get(i).getId()) {
                                aUser.get(i).setTrophy((int) aEvent.get(j).getValue());
                                aEvent.remove(j);
                                break;
                            }
                        }
                    }
                    aUser.sort((UserEntity u1, UserEntity u2) -> u2.getTrophy() - u1.getTrophy());
                    aUserTrophyTime = aUser;
                    debug("aUserTrophyTime = " + aUserTrophyTime.size());
                }
            }
        }
    }

    public void cacheSystemMessage() {
        Map<Integer, List<SystemMessageEntity>> mMessage = new HashMap<>();
//        for (int ii = 1; ii < 3; ii++) {
        //            List<SystemMessageEntity> tmp = Database2.getList(CfgCommon.mainDb + "system_message", new ArrayList<Object>(), "where server=0 or server='" + ii + "'", SystemMessageEntity.class);
        //            if (tmp != null) {
        //                mMessage.put(ii, tmp);
        //            }
        //        }
        //
        //        if (mMessage != null && mMessage.size() > 0) aMessage = mMessage;
    }
    //endregion

    public static void main(String[] args) throws Exception {
        DOMConfigurator.configure("log4j.xml");
        com.k2tek.Config.load(com.k2tek.Config.getConfigPath() + "config.xml");

        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(5);
        config.setMaxIdle(1);
        pool = new JedisPool(config, "local.db");
        initGameId();
//        try {
//            cacheTopLevel();
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//        try {
//            cacheTopMoney();
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//        try {
//            cacheVip();
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//        try {
//            cacheSystemMessage();
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
        try {
            cacheSetTopLevel();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        reloadTopPlayer();
        System.exit(0);
    }

    public static void reloadTopPlayer() {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(200);
        config.setMaxIdle(5);
        JedisPool pool = new JedisPool(config, "local.db");
        JSONObject obj = new JSONObject();
        redis.clients.jedis.Jedis jedis = null;
        try {
            jedis = pool.getResource();
            if (jedis.publish(JCache.gameChannel, JService.RELOAD_TOPPLAYER + "@" + obj.toString()) > 0) {
            }
        } catch (Exception ex) {
            debug(Util.exToString(ex));
        } finally {
            pool.returnResource(jedis);
        }
    }

    public static void initGameId() {
//        List<Config> aConfig = sDAO.getConfig();
//        for (Config config : aConfig) {
//            if (config.getId().getKey().equals("game_name")) {
//                JSONObject json = JSONObject.fromObject(config.getId().getValue());
//                JSONArray arr = json.getJSONArray("name");
//                gameId = new String[arr.size()];
//                for (int i = 0; i < arr.size(); i++) {
//                    JSONObject obj = arr.getJSONObject(i);
//                    gameId[i] = obj.getString("id");
//                }
////                UserInfor.gameId = gameId;
//                break;
//            } else if (config.getId().getKey().startsWith("api")) {
//                String api = config.getId().getKey();
//                api = api.substring(api.indexOf("_") + 1);
//                api = api.substring(0, 1).toUpperCase() + api.substring(1);
//                try {
//                    Method m = Class.forName("com.bem.util.Api" + api).getDeclaredMethod("loadConfig", String.class);
//                    m.invoke(null, config.getId().getValue());
//                } catch (Exception ex) {
//                    ex.printStackTrace();
//                }
//            }
//        }
    }

    public static void cacheVip() {
//        List<AuthUserVip> ids = sDAO.getListIDVip();
//        String listId = "";
//        int count = 0;
//        for (AuthUserVip uVip : ids) {
//            listId += "," + uVip.getAuthUserId();
//            if (++count == 10) {
//                break;
//            }
//        }
//        if (listId.length() > 1) {
//            listId = listId.substring(1);
//        }
//        List<User> listUser = uDAO.getListUser(listId);
//        if (listUser == null) {
//            listUser = new ArrayList<User>();
//        }
//        List<UserInfo> listUserInfo = new ArrayList<UserInfo>();
//        if (listUser != null) {
//            int star = 5;
//            for (int i = 0; i < listUser.size(); i++) {
//                User u = null;
//                for (User tmp : listUser) {
//                    if (tmp.getId() == ids.get(i).getAuthUserId()) {
//                        u = tmp;
//                        break;
//                    }
//                }
//                if (u != null) {
//                    UserInfo tmp = ApiServer.getUserInfor(u.getUsername(), false);
//                    UserInfo userInfo = new UserInfo();
////                    userInfor.setDbUser(u);
////                    userInfor.setBalance(tmp.getBalance());
////                    userInfor.setVipType(star);
////                    userInfor.setUserId(u.getId());
////                    userInfor.setUsername(u.getUsername());
////                    userInfor.setScreenName(u.getScreenName());
////                    userInfor.setUserStatus(u.getStatus());
////                    if (userInfor.getDbUser().getGender() != 1 && userInfor.getDbUser().getGender() != 2) {
////                        userInfor.getDbUser().setGender((byte) 1);
////                    }
//                    listUserInfo.add(userInfo);
//                    if (star > 0) {
//                        star--;
//                    }
//                }
//            }
//        }
//        setValue(KEY_TOP_VIP, gson.toJson(listUserInfo));
//        debug("Top KEY_TOP_VIP -> " + listUserInfo.size());
//
//        listId = "";
//        for (AuthUserVip uVip : ids) {
//            listId += "," + uVip.getAuthUserId();
//        }
//        if (listId.length() > 1) {
//            listId = listId.substring(1);
//        }
//        setValue(KEY_TOP_RANK, listId);
    }


    public static void cacheSetTopLevel() {
        List<UserEntity> userEntityTopRs = null; //uDAO.getTopLevelPlayerReset();
        if (userEntityTopRs != null) {// reset
            for (int i = 0; i < userEntityTopRs.size(); i++) {
                Database2.update("user", Arrays.asList("level", "35"), Arrays.asList("id", String.valueOf(userEntityTopRs.get(i).getId())));
            }
        }

        if (userEntityTopRs != null && !userEntityTopRs.isEmpty()) {
            int number = 100;
            List<Integer> rank = Arrays.asList(36, 37, 38, 39, 40, 41);
            List<Integer> rankNumber = Arrays.asList(50, 40, 7, 1, 1, 1);
            int sizeRank = rank.size();
            for (int i = 0; i < Math.min(number, userEntityTopRs.size()); i++) {
//                if(i==0){
                for (int j = sizeRank - 1; j >= 0; j--) {
//                        if(userEntityTopRs.get(i).getExp()>=CfgLevel.getexp(rank.get(j))&&rankNumber.get(j)>0){
//                            Database.update("newUser", Arrays.asList("level", String.valueOf(rank.get(j))), Arrays.asList("id", String.valueOf(userEntityTopRs.get(0).getId())));
//                            rankNumber.set(j,rankNumber.get(j)-1);
//                            if(rankNumber.get(j)>0) {
//                                sizeRank = j;
//                            }else {
//                                sizeRank= j-1;
//                            }
//                            break;
//                        }
                }
//                }else if(i==1){
//                    for (int j = sizeRank -1 ; j >= 0; j--) {
//                        if(userEntityTopRs.get(i).getExp()>=CfgLevel.getexp(rank.get(j))&&rankNumber.get(j)>0){
//                            Database.update("newUser", Arrays.asList("level", String.valueOf(rank.get(j))), Arrays.asList("id", String.valueOf(userEntityTopRs.get(0).getId())));
//                            rankNumber.set(j,rankNumber.get(j)-1);
//                            if(rankNumber.get(j)>0) {
//                                sizeRank = j;
//                            }else {
//                                sizeRank= j-1;
//                            }
//                            break;
//                        }
//                    }
//
//                }else if(i==2){

//                }else if(i>=3&&i<=9){
//
//                }else if(i>=10&&i<=49){
//
//                }else if(i>=50&&i<=99){
//
//                }

//                Database.update("newUser", Arrays.asList("level", "41"), Arrays.asList("id", String.valueOf(userEntityTopRs.get(0).getId())));
//                Database.update("newUser", Arrays.asList("level", "40"), Arrays.asList("id", String.valueOf(userEntityTopRs.get(1).getId())));
//                Database.update("newUser", Arrays.asList("level", "39"), Arrays.asList("id", String.valueOf(userEntityTopRs.get(2).getId())));
            }
            List<UserEntity> userEntityTop = null;//uDAO.getTopLevelPlayer();
            setValue(KEY_TOP_LEVEL, gson.toJson(userEntityTop));
            debug("Top level -> " + KEY_TOP_LEVEL + " -> size -> " + userEntityTop.size());
            debug("Top level -> " + KEY_TOP_LEVEL + " ->" + getValue(KEY_TOP_LEVEL));

        }
    }

    public static void cacheTopMoney() {
//        UserDAO dao = new UserDAO();
//        List<UserInfo> listUserInfo = ApiServer.getTopRichUser();
//        if (listUserInfo != null) {
//            String listId = "";
//            for (UserInfo userInfo : listUserInfo) {
////                listId += "," + userInfor.getUserId();
//            }
//            if (listId.length() > 1) {
//                listId = listId.substring(1);
//            }
//            List<User> listUser = dao.getListUser(listId);
//            if (listUser == null) {
//                listUser = new ArrayList<User>();
//            }
//            boolean existInDB;
//            for (UserInfo userInfo : listUserInfo) {
//                existInDB = false;
//                for (int i = 0; i < listUser.size(); i++) {
//                    User u = listUser.get(i);
//                    if (u.getUsername().equals(userInfo.getUsername())) {
//                        existInDB = true;
////                        userInfor.setDbUser(u);
////                        userInfor.setScreenName(u.getScreenName());
////                        userInfor.setUserStatus(u.getStatus());
////                        userInfor.setVipType(ApiServer.getVipTypeUser(userInfor.getUsername()));
//                        break;
//                    }
//                }
//                if (!existInDB) {
////                    userInfor.setDbUser(new User());
////                    userInfor.getDbUser().setGender((byte) 0);
//                }
//            }
//        }
//
//        for (UserInfo userInfo : listUserInfo) {
////            debug(userInfor.getUsername() + " -> " + userInfor.getScreenName());
//        }
//
//        setValue(KEY_TOP_MONEY, gson.toJson(listUserInfo));
//        debug("Top Money" + " -> " + KEY_TOP_MONEY + " -> " + listUserInfo.size());
    }

    public static String getValue(String key) {
        redis.clients.jedis.Jedis jedis = pool.getResource();
        try {
            return jedis.get(key);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pool.returnResource(jedis);
        }
        return null;
    }

    public static boolean setValue(String key, String value) {
        redis.clients.jedis.Jedis jedis = pool.getResource();
        try {
            jedis.set(key, value);
            jedis.expire(key, 12 * 3600);
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pool.returnResource(jedis);
        }
        return false;
    }

    public static boolean removeValue(String key) {
        redis.clients.jedis.Jedis jedis = pool.getResource();
        try {
            jedis.del(key);
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pool.returnResource(jedis);
        }
        return false;
    }

    // cache CCU
    public int rankCCU = 0, bossCCU = 0;

    public synchronized void addRankCCU(int value, String gameId) {
        int oldRank = rankCCU;
        rankCCU += value;
        slib_Logger.root().debug(String.format("%s %s %s, id=%s", oldRank, value, rankCCU, gameId));
    }

    public synchronized void addBossCCU(int value, String gameId, String detail) {
        int oldRank = bossCCU;
        bossCCU += value;
        slib_Logger.root().debug(String.format("%s %s %s, id=%s -> %s", oldRank, value, bossCCU, gameId, detail));
    }

    void cacheCCU() {
        Database2.rawSQL("INSERT INTO boom.cache_ccu(server_id, date_created, hours, online_rank, online_boss, online_times) values(" + CfgServer.SERVER_ID + ", curdate(), hour(now())," + rankCCU + "," + bossCCU + ",1) \n" +
                "ON DUPLICATE KEY UPDATE online_times=online_times+1,online_rank=(online_rank * online_times + " + rankCCU + ") / (online_times +1),online_boss=(online_boss * online_times + " + bossCCU + ") / (online_times +1)");
    }

    //region timer
    int counter = 0;

    public void doExpireTurn(int turnId) {
        try {
            { // per minute
                //if (CfgServer.cacheCCU) cacheCCU();
            }
            if (counter % 1 == 0) { // per 5 minutes;
                cacheSystemMessage();
                cacheTopTrophy();
                cacheTopLoop();
                cacheTopCapDoiLoop();
                cacheTopWin();
                for (int i = 1; i < 3; i++) {
                    cacheTopTrophyTime(i);
                }

            }
            if (counter % 60 == 0) { // per hour
                counter = 0;
            }
            counter++;
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
        timer();
    }

    void timer() {
        try {
            Xerver.timer(this, 0, 60);
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
    }

    //endregion
    public static void debug(int msg) {
        if (CfgServer.debug) {
            debug(String.valueOf(msg));
        }
    }

    public static void debug(String msg) {
        if (CfgServer.debug) {
            slib_Logger.root().debug(msg);
        }
    }

    protected Logger getLogger() {
        return slib_Logger.root();
    }
}
