/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.monitor;

import com.bem.config.EventText;
import com.bem.object.TaskSchedule;
import com.bem.util.Util;
import grep.database.HibernateUtil;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Random;

public class TextEventMonitor extends TaskSchedule {

	public TextEventMonitor(long jobInterval) {
		super(jobInterval);
		curHour = getCurHour();
	}

	boolean isRun = true;
	int winnerOfHour = 0, winnerFromOtherHour = 0, awardMinute = 0;
	List<String> winnerName = new ArrayList<String>();
	List<UserGetText> queue = new ArrayList<UserGetText>();
	Random ran = new Random();
	int curHour;

	// public TextEventMonitor() {
	// curHour = getCurHour();
	// }

	private int getCurHour() {
		return Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
	}

	private int getCurMinute() {
		return Calendar.getInstance().get(Calendar.MINUTE);
	}

	private int getAwardMinute() {
		return (int) (System.currentTimeMillis() % 50);
	}

	@Override
	protected void doJob() {
		// while (isRun) {
		/*
		String debug = "";
		try {
			while (queue.size() > 0) {
				int hour = getCurHour();
				if (hour != curHour) {
					if (winnerOfHour < EventText.numberAwardPerHour) {
						winnerFromOtherHour += EventText.numberAwardPerHour - winnerOfHour;
					}
					curHour = hour;
					awardMinute = getAwardMinute();
					winnerOfHour = 0;
					winnerName.clear();
				}
				if (!EventText.isDisableHour(curHour)) {
					UserGetText getText = queue.get(0);
					String username = getText.username;
					Channel ch = Online.getChannel(username);
					UserInfor newUser = null;
					if (ch != null) {
						newUser = Util.getUser(ch);
					}
					if (newUser != null) {
						if (!getText.awardText || (getText.awardText && getText.money < 25000)) {
							int result = ran.nextInt(EventText.text.size() - 1);
							if (result >= 0) {
								// result--;
								String text = EventText.text.get(result);
								String curText = newUser.getTextEvent();
								debug += text + " " + curText + " " + result;
								if (!curText.contains(text)) {
									curText += "," + text;
									if (curText.startsWith(",")) {
										curText = curText.substring(1);
									}
									boolean winner = EventText.text.size() == curText.split(",").length;
									if (winner) { // Trúng thưởng
										// if (!winnerName.contains(newUser.getUsername())) {
										// boolean enoughAward = false;
										// if (winnerOfHour < EventText.numberAwardPerHour) {
										// winnerOfHour++;
										// enoughAward = true;
										// } else if (winnerFromOtherHour > 0) {
										// winnerFromOtherHour--;
										// enoughAward = true;
										// }
										// // Trúng thưởng và còn giải
										// if (enoughAward) { // && awardMinute <= getCurMinute()
										if (updateTextEvent(newUser.getUserId(), "") == 1) {
											updateGroupCount(newUser.getUserId());
											winnerName.add(newUser.getUsername());
											newUser.setTextEvent("");
											Xerver.koin.addKoin(newUser, EventText.koin, "EVENT_TEXT");
											String msg = String.format(EventText.msg, newUser.getUsername(), String.valueOf(EventText.koin));
											Util.sendProtoData(ch, CommonProto.getStringProto(msg), BaseServiceMessage.MSG_SLIDER, System.currentTimeMillis());
											// Online.sendMessage(CommonProto.getCommonVectorProto(null, Arrays.asList("Admin", msg)), BaseServiceMessage.SPEAKER_GLOBAL);
										}
										// }
										// }
									} else { // Không trúng thưởng, update infor newUser
										if (updateTextEvent(newUser.getUserId(), curText) == 1) {
											getLogger().info(newUser.getUsername() + " -> " + text + " -> " + curText);
											newUser.setTextEvent(curText);
											Util.sendProtoData(ch, CommonProto.getStringProto("Bạn đã nhận được chữ " + text), BaseServiceMessage.ERROR, System.currentTimeMillis());
										}
									}
								} else {
									// Trùng với chữ đã nhận, thông báo cho newUser
									Util.sendProtoData(ch, CommonProto.getStringProto("Bạn đã nhận được chữ " + text), BaseServiceMessage.ERROR, System.currentTimeMillis());
								}
							}
						} else {
							int result = ran.nextInt(EventText.text.size());
							if (result >= 0) {
								String text = EventText.text.get(result);
								String curText = newUser.getTextEvent();
								if (!curText.contains(text)) {
									curText += "," + text;
									if (curText.startsWith(",")) {
										curText = curText.substring(1);
									}
									boolean winner = EventText.text.size() == curText.split(",").length;
									if (winner) { // Trúng thưởng
										winnerOfHour++;
										if (updateTextEvent(newUser.getUserId(), "") == 1) {
											newUser.setEventText(newUser.getEventText() + 1);
											updateGroupCount(newUser.getUserId());
											winnerName.add(newUser.getUsername());
											newUser.setTextEvent("");
											Xerver.koin.addKoin(newUser, EventText.koin, "EVENT_TEXT");
											String msg = String.format(EventText.msg, newUser.getUsername(), String.valueOf(EventText.koin));
											// Online.sendMessage(CommonProto.getStringProto(msg), BaseServiceMessage.SPEAKER_GLOBAL);
											Util.sendProtoData(ch, CommonProto.getStringProto(msg), BaseServiceMessage.MSG_SLIDER, System.currentTimeMillis());
										}
									} else { // Không trúng thưởng, update infor newUser
										if (updateTextEvent(newUser.getUserId(), curText) == 1) {
											getLogger().info(newUser.getUsername() + " -> " + text + " -> " + curText);
											newUser.setTextEvent(curText);
											Util.sendProtoData(ch, CommonProto.getStringProto("Bạn đã nhận được chữ " + text), BaseServiceMessage.ERROR, System.currentTimeMillis());
										}
									}
								} else {
									// Trùng với chữ đã nhận, thông báo cho newUser
									Util.sendProtoData(ch, CommonProto.getStringProto("Bạn đã nhận được chữ " + text), BaseServiceMessage.ERROR, System.currentTimeMillis());
								}
							}
						}
					}
				}
				queue.remove(0);
				Thread.yield();
				sleepTime(100);
			}
			// else {
			// getLogger().info("LoginTimesMonitorDB empty sleep " + SLEEP_TIME);
			// sleepTime(SLEEP_TIME);
			// }
			// Thread.yield();
		} catch (Exception ex) {
			getLogger().info(debug + " " + Util.exToString(ex));
			if (queue != null && queue.size() > 0) {
				queue.remove(0);
			} else {
				queue = new ArrayList<UserGetText>();
			}
		}
		// }
		*/
	}

	public void addUser(String username, boolean award, int money) {
		if (EventText.isInEvent()) {
			UserGetText text = new UserGetText();
			text.username = username;
			text.awardText = award;
			text.money = money;
			queue.add(text);
		}
	}

	public void addUser(String username) {
		if (EventText.isInEvent()) {
			UserGetText text = new UserGetText();
			text.username = username;
			text.awardText = false;
			queue.add(text);
		}
	}

	public void updateGroupCount(int userId) {
		Session session = null;
		try {
			session = HibernateUtil.getSessionFactory().openSession();
			session.beginTransaction();
			session.createSQLQuery("update user set event_text=event_text+1 where id=" + userId).executeUpdate();
			session.getTransaction().commit();
		} catch (Exception he) {
			getLogger().info(Util.exToString(he));
		} finally {
			try {
				session.close();
			} catch (Exception ex) {
				getLogger().info(Util.exToString(ex));
			}
		}
	}

	public int updateTextEvent(int userId, String text) {
		Session session = null;
		try {
			session = HibernateUtil.getSessionFactory().openSession();
			session.beginTransaction();
			SQLQuery query = session.createSQLQuery("update user_new_year set infor=? where id=" + userId);
			query.setString(0, text);
			int rowCount = query.executeUpdate();
			session.getTransaction().commit();
			if (rowCount > 0) {
				return 1;
			} else {
				return 0;
			}
		} catch (HibernateException he) {
			getLogger().info(Util.exToString(he));
		} finally {
			try {
				session.close();
			} catch (Exception ex) {
				getLogger().info(Util.exToString(ex));
			}
		}
		return -1;
	}

	private void sleepTime(long time) {
		try {
			Thread.sleep(time);
		} catch (Exception ex) {
		}
	}

	class UserGetText {
		String username;
		boolean awardText;
		int money;
	}

}
