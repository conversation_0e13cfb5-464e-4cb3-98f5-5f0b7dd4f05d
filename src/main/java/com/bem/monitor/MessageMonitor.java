/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.monitor;

import com.k2tek.Constans;
import com.bem.util.ChUtil;
import io.netty.channel.Channel;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MessageMonitor extends Thread {

    List<Channel> lChannel = new ArrayList<Channel>();
    JSONObject message;
    int service;

    public MessageMonitor(JSONObject message, int service) {
        this.message = message;
        this.service = service;
    }

    public void addChannel(List<Channel> lChannel) {
        for (int i = 0; i < lChannel.size(); i++) {
            this.lChannel.add(lChannel.get(i));
        }
    }

    public void addChannel(Channel channel) {
        this.lChannel.add(channel);
    }

    @Override
    public void run() {
        for (int i = 0; i < lChannel.size(); i++) {
            Integer tableId = (Integer) ChUtil.get(lChannel.get(i), Constans.KEY_TABLEID);
            if (tableId != null) {
//                Util.sendProtoData(lChannel.get(i), message.toString(), service, System.currentTimeMillis());
            }
        }
    }
}
