package com.bem.monitor;

import com.bem.object.ITimer;
import com.bem.util.Util;
import com.handler.game.*;
import com.k2tek.Config;
import com.k2tek.Xerver;
import com.k2tek.common.slib_Logger;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 5/16/2017.
 */
public class EventMonitor implements ITimer {

    static EventMonitor instance;
    List<AAHandler> aHandler = new ArrayList<>();

    public void init() {
        for (int i = 0; i < Config.lstServerId.size(); i++) {
            aHandler.add(BossGlobal.getInstance(Config.lstServerId.get(i)));
            aHandler.add(ClanBattle.getInstance(Config.lstServerId.get(i)));
//            aHandler.add(CheckEvent.getInstance());
//            aHandler.add(Arena.getInstance(Config.lstServerId.get(i)));
        }
        aHandler.add(CheckEvent.getInstance());

        new Thread(() -> doExpireTurn(0)).start();
    }

    public static EventMonitor getInstance() {
        if (instance == null) {
            instance = new EventMonitor();
        }
        return instance;
    }

    @Override
    public void doExpireTurn(int turnId) {
        aHandler.forEach(handler -> new Thread(() -> handler.checkStatus()).start());
        timer();
    }

    void timer() {
        try {
            Xerver.timer(this, 0, 3);
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
    }

    protected Logger getLogger() {
        return slib_Logger.root();
    }
}
