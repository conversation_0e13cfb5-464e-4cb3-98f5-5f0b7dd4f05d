package com.bem.monitor;

import com.bem.object.UserInfo;
import com.proto.GGProto;
import grep.helper.StringHelper;

import java.util.ArrayList;
import java.util.List;

public class ChatMonitor {

    public static List<GGProto.ProtoChat> aChat = new ArrayList<GGProto.ProtoChat>();

    /**
     * Global Chat
     */
    public static boolean addChat(UserInfo user, String msg, int channel) {
        msg = StringHelper.chatFormat(msg);
        if (msg.length() == 0) {
            return false;
        }

        GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
        builder.setTime(System.currentTimeMillis() / 1000);
        builder.setUserId(user.getId());
        builder.setName(user.getDbUser().getName());
        builder.setMessage(msg);
        builder.setAvatar(user.getDbUser().getMAvatar().getUserAvatar() + "");
        builder.setVip(user.getDbUser().getVip());
        builder.setChannel(channel);
        aChat.add(builder.build());

        if (aChat.size() > 250) {
            int tmp = aChat.size() - 250;
            for (int i = 0; i < tmp; i++) {
                aChat.remove(0);
            }
        }
        return true;
    }

    public static GGProto.ProtoListChat getChatHistory(long reqTime) {
        GGProto.ProtoListChat.Builder builder = GGProto.ProtoListChat.newBuilder();
        int count = 0;
        for (int i = aChat.size() - 1; i >= 0; i--) {
            if (aChat.get(i).getTime() > reqTime) {
                builder.addAChat(aChat.get(i));
                count++;
            }
            if (count >= 30) {
                break;
            }
        }
        return builder.build();
    }

    public static GGProto.ProtoListChat getHomeHistory() {
        GGProto.ProtoListChat.Builder builder = GGProto.ProtoListChat.newBuilder();
        int count = 0;
        for (int i = aChat.size() - 1; i >= 0; i--) {
            builder.addAChat(aChat.get(i));
            if (++count == 2) {
                break;
            }
        }
        return builder.build();
    }
//    public static List<Integer> getNumberChat(MyUser _user, User newUser) {
//        List<Integer> aCount = new ArrayList<Integer>();
//        boolean hasData = false;
//        // Global chat
//        int count = 0;
//        if (_user.getLastReqChat() < lastGlobalChat) {
//            for (int i = aChat.size() - 1; i > 0; i--) {
//                ChatObject obj = aChat.get(i);
//                if (_user.getLastReqChat() < obj.getTime()) {
//                    count++;
//                    hasData = true;
//                } else {
//                    break;
//                }
//                if (count == 50) {
//                    break;
//                }
//            }
//        }
//        aCount.add(count);
//
//        // Clan chat
//        int support = 0;
//        count = 0;
//        if (newUser.getClan() > 0 && _user.getLastReqClanChat() < lastClanChat) {
//            List<ChatObject> aChat = getClanChat(newUser.getClan());//clanChat.get(newUser.getClan());
//            for (int i = aChat.size() - 1; i > 0; i--) {
//                ChatObject obj = aChat.get(i);
//                if (_user.getLastReqClanChat() < obj.getTime()) {
//                    count++;
//                    hasData = true;
//                    if (obj.getItemId() == ChatObject.REQ_TROOP) {
//                        support = 1;
//                    }
//                } else {
//                    break;
//                }
//                if (count == 50) {
//                    break;
//                }
//            }
//        }
//        aCount.add(count);
//        aCount.add(support);
//
//        count = 0;
//        if (newUser.getClan() > 0 && _user.getLastReqClanChat() < lastServerChat) {
//            List<ChatObject> aChat = getServerChat();
//            for (int i = aChat.size() - 1; i > 0; i--) {
//                ChatObject obj = aChat.get(i);
//                if (_user.getLastReqClanChat() < obj.getTime()) {
//                    count++;
//                    hasData = true;
//                } else {
//                    break;
//                }
//                if (count == 50) {
//                    break;
//                }
//            }
//        }
//        aCount.add(count);
//        _user.setLastReqChat(System.currentTimeMillis());
//        _user.setLastReqClanChat(System.currentTimeMillis());
//        if (hasData) {
//            return aCount;
//        }
//        return null;
//    }
//
//
//    /**
//     * Clan chat
//     */
//    public static Map<Integer, List<ChatObject>> clanChat = new HashMap<Integer, List<ChatObject>>();
//    public static long lastClanChat = 0;
//
//    public static synchronized List<ChatObject> getClanChat(int clanId) {
//        List<ChatObject> tmp = clanChat.get(clanId);
//        if (tmp == null) {
//            tmp = new ArrayList<ChatObject>();
//            clanChat.put(clanId, tmp);
//        }
//        return tmp;
//    }
//
//    public static void addClanChat(User newUser, String msg, int type) {
//        lastClanChat = System.currentTimeMillis();
//        List<ChatObject> tmp = getClanChat(newUser.getClan());
//        int icon = getClanIcon(newUser.getClan());
//        tmp.add(new ChatObject(newUser, msg, lastClanChat, type, newUser.getClan(), icon));
//
//        if (tmp.size() > 200) {
//            int number = aChat.size() - 200;
//            for (int i = 0; i < number; i++) {
//                aChat.remove(0);
//            }
//        }
//    }
//
//    public static List<ChatObject> getClanChatHistory(int clanId, long reqTime) {
//        List<ChatObject> chatHistory = getClanChat(clanId);
//        List<ChatObject> tmp = new ArrayList<ChatObject>();
//        int count = 0;
//        for (int i = chatHistory.size() - 1; i >= 0; i--) {
//            if (chatHistory.get(i).getTime() > reqTime) {
//                tmp.add(chatHistory.get(i));
//                count++;
//            }
//            if (count >= 30) {
//                break;
//            }
//        }
//        return tmp;
//    }
//
//    public static ChatObject getClanReqTroop(int clanId, int userId) {
//        List<ChatObject> chatHistory = getClanChat(clanId);
//        for (int i = 0; i < chatHistory.size(); i++) {
//            if (chatHistory.get(i).getUserId() == userId && chatHistory.get(i).getItemId() == ChatObject.REQ_TROOP) {
//                return chatHistory.get(i);
//            }
//        }
//        return null;
//    }
//
//    // Server chat
//    public static List<ChatObject> ServerChat = new ArrayList<ChatObject>();
//    public static long lastServerChat = 0;
//    public static long lastRequest = 0;
//    public static final String KEY_SERVER_CHAT = "serverchat:";
//
//    public static void addServerChat(User newUser, String msg) {
//        lastServerChat = System.currentTimeMillis();
//        int icon = getClanIcon(newUser.getClan());
//        ChatObject chat = new ChatObject("[s" + CfgServer.ServerId + "]" + newUser.getScreenname(), msg, lastServerChat, newUser.getClan(), icon);
//        Gson gson = new Gson();
//        if (JedisCache.saveServerChat(KEY_SERVER_CHAT, gson.toJson(chat))) {
//            ServerChat.add(chat);
//        }
//    }
//
//    public static synchronized List<ChatObject> getServerChat() {
//        if (System.currentTimeMillis() - lastRequest > 2000) {
//            lastRequest = System.currentTimeMillis();
//            Set<String> tmp = JedisCache.getServerChat(KEY_SERVER_CHAT);
//            ServerChat = new ArrayList<ChatObject>();
//            Gson gson = new Gson();
//            if (tmp != null) {
//                for (String str : tmp) {
//                    ChatObject obj = gson.fromJson(str, ChatObject.class);
//                    ServerChat.add(obj);
//                }
//            }
//        }
//        return ServerChat;
//    }
//
//    public static List<ChatObject> getServerChatHistory(long reqTime) {
//        List<ChatObject> chatHistory = getServerChat();
//        List<ChatObject> tmp = new ArrayList<ChatObject>();
//        int count = 0;
//        for (int i = chatHistory.size() - 1; i >= 0; i--) {
//            if (chatHistory.get(i).getTime() > reqTime) {
//                tmp.add(chatHistory.get(i));
//                count++;
//            } else {
//                break;
//            }
//            if (count >= 30) {
//                break;
//            }
//        }
//        return tmp;
//    }
//
//    public static Map<Integer, Integer> ClanIcon = new HashMap<Integer, Integer>();
//
//    public static int DateRequestIcon = -1;
//
//    public static int getClanIcon(int clanId) {
//        Calendar ca = Calendar.getInstance();
//        if (DateRequestIcon != ca.get(Calendar.DAY_OF_YEAR)) {
//            ClanIcon = new HashMap<Integer, Integer>();
//            DateRequestIcon = ca.get(Calendar.DAY_OF_YEAR);
//        }
//
//        Integer Icon = ClanIcon.get(clanId);
//        if (Icon == null) {
//            ClanDAO cDao = new ClanDAO();
//            Icon = cDao.getClanIcon(clanId);
//            ClanIcon.put(clanId, Icon);
//        }
//        return Icon == null ? -1 : Icon;
//    }

}
