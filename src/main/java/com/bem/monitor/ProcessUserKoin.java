package com.bem.monitor;

import java.util.*;

public class ProcessUserKoin {

	public static List<String> remains = new ArrayList<String>();
	public static Map<String, Integer> userBalances = new HashMap<String, Integer>();

	public static void addUserProcess(String username, int balance) {
		putBalance(username, balance);
		if (!remains.contains(username)) {
			remains.add(username);
		}

		if (remains.size() > 6000) {
			String key = remains.get(0);
			remains.remove(0);
			userBalances.remove(key);
		}
	}

	public static void clear() {
		remains.clear();
		userBalances.clear();
	}

	public static void removeUserProcess(int index) {
		remains.remove(index);
	}

	public static void removeUserProcess(String username) {
		remains.remove(username);
		userBalances.remove(username);
	}

	public static boolean stillRemain(String username) {
		return remains.contains(username);
	}

	public static void putBalance(String username, int balance) {
		userBalances.put(username, new Integer(balance));
	}

	public static int getBalance(String username) {
		try {
			return userBalances.get(username);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return -1;
	}

}
