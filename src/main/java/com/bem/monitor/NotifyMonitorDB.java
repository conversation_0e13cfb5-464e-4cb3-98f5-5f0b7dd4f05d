/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.monitor;

import com.bem.object.TaskSchedule;
import com.bem.util.Util;
import com.k2tek.common.slib_Logger;
import grep.database.HibernateUtil;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 */
public class NotifyMonitorDB extends TaskSchedule {


	public NotifyMonitorDB(long jobInterval) {
		super(jobInterval);
		// TODO Auto-generated constructor stub
	}

	private List<NotifyUser> notifies = new ArrayList<NotifyUser>();
	private boolean isRun = true;
	private int count = 0;

	@Override
	protected void doJob() {
		// while (isRun) {
		while (notifies.size() > 0) {
			NotifyUser notify = notifies.get(0);
			boolean success = false;
			if (notify.notifyType.equals("friend")) { // Notify friend
				success = updateNotifyFriend(notify);
			} else { // Notify message
				success = updateNotifyMessage(notify);
			}
			if (success) {
				notifies.remove(0);
				count = 0;
			} else {
				if (++count == 2) {
					getLogger().info("Cant process NotifyMonitorDB remove result");
					notifies.remove(0);
				}
				getLogger().info("Fail process NotifyMonitorDB-> sleep " + 3000);
				sleepTime(3000);
			}
			Thread.yield();
			sleepTime(100);
		}
		// else {
		// getLogger().info("NotifyMonitorDB empty sleep 60s");
		// sleepTime(SLEEP_TIME);
		// }
		// Thread.yield();
		// }
	}

	private boolean updateNotifyFriend(NotifyUser notify) {
		Session session = null;
		try {
			session = HibernateUtil.getSessionFactory().openSession();
			session.beginTransaction();
			String sql = "update user set notify_friend=? where id=?";
			SQLQuery query = session.createSQLQuery(sql);
			query.setInteger(0, notify.value);
			query.setInteger(1, notify.userId);
			query.executeUpdate();
			return true;
		} catch (Exception he) {
			getLogger().error(Util.exToString(he));
		} finally {
			session.getTransaction().commit();
			session.close();
		}
		return false;
	}

	private boolean updateNotifyMessage(NotifyUser notify) {
		Session session = null;
		try {
			session = HibernateUtil.getSessionFactory().openSession();
			session.beginTransaction();
			String sql = "update user set notify_message=? where id=?";
			SQLQuery query = session.createSQLQuery(sql);
			query.setInteger(0, notify.value);
			query.setInteger(1, notify.userId);
			query.executeUpdate();
			return true;
		} catch (HibernateException he) {
			getLogger().error(Util.exToString(he));
		} finally {
			session.getTransaction().commit();
			session.close();
		}
		return false;
	}

	private void sleepTime(long time) {
		try {
			Thread.sleep(time);
		} catch (Exception ex) {
		}
	}

	public void addNotifyUser(int userId, String notifyType, int value) {
		notifies.add(new NotifyUser(userId, notifyType, value));
	}

	private class NotifyUser {

		int userId, value;
		String notifyType;

		public NotifyUser(int userId, String notifyType, int value) {
			super();
			this.userId = userId;
			this.notifyType = notifyType;
			this.value = value;
		}

	}

	public Logger getLogger() {
		return slib_Logger.quay();
	}

}
