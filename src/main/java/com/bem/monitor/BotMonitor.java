package com.bem.monitor;

import com.bem.object.*;
import com.bem.util.Util;
import com.k2tek.IAction;
import com.k2tek.Xerver;
import com.k2tek.common.slib_Logger;
import io.netty.channel.Channel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 3/7/2017.
 */
public class BotMonitor implements ITimer {
    public static int fakeId = -1;
    public static Map<String, Integer> mBotId = new HashMap<String, Integer>(); // tat ca bot

    public static Map<String, BotBomb> mBot = new HashMap<String, BotBomb>(); // tat ca bot
    public static List<BotBomb> idleBot = new ArrayList<BotBomb>(); // so bot dang ranh
    public static List<BotBomb> busyBot = new ArrayList<BotBomb>(); // so bot dang ranh

    public static List<CommonData> aCheck = new ArrayList<CommonData>();

    public static synchronized int getBotId() {
        return --fakeId;
    }

    public static synchronized void addBot(Channel channel, UserInfo user) {
        if (mBot.containsKey(user.getAuthUser().getUdid())) { // logout if duplicate
            BotBomb bot = mBot.get(user.getAuthUser().getUdid());
//            Util.sendProtoData(bot.getChannel(), null, IAction.DUPLICATE_LOGIN, System.currentTimeMillis());
//            Online.logoutChannel(bot.getChannel());
            removeBot(user.getAuthUser().getUdid());
        }
        // fake bot id
        Integer botId = mBotId.get(user.getAuthUser().getUdid());
        if (botId == null) {
            botId = getBotId();
            mBotId.put(user.getAuthUser().getUdid(), botId);
        }
        user.getAuthUser().setId(botId);
        user.getDbUser().setId(botId);

        //
        BotBomb bot = new BotBomb(channel, user);
        idleBot.add(bot);
        mBot.put(user.getAuthUser().getUdid(), bot);
        Online.addChannel(botId, channel);
        //
        CommonData data = new CommonData();
        data.addStr(user.getAuthUser().getUdid());
        data.addLong(System.currentTimeMillis());
        aCheck.add(data);
        System.out.println("idleBot = " + idleBot.size());
    }

    public static synchronized List<BotBomb> getBot(int number) {
        if (idleBot.size() >= number) {
            List<BotBomb> retBot = new ArrayList<BotBomb>();
            for (int i = 0; i < number; i++) {
                BotBomb bot = idleBot.get(0);
                idleBot.remove(0);
                busyBot.add(bot);
                retBot.add(bot);
            }
            return retBot;
        }
        return null;
    }

    public static synchronized void returnBot(BotBomb bot) {
        if (busyBot.contains(bot)) {
            busyBot.remove(bot);
            idleBot.add(bot);
            mBot.put(bot.getUser().getAuthUser().getUdid(), bot);
            slib_Logger.root().debug("add = " + bot.getUser().getAuthUser().getUdid());
        }
    }

    public static void closeBot(BotBomb bot) {
        slib_Logger.root().debug("close = " + bot.getUser().getAuthUser().getUdid());
        bot.getChannel().close();
    }

    public static synchronized void removeBot(String udid) {
        System.out.println("removeBot() = " + udid);
        BotBomb bot = mBot.get(udid);
        if (bot != null) {
            slib_Logger.root().debug("remove = " + bot.getUser().getAuthUser().getUdid());
            mBot.remove(udid);
            idleBot.remove(bot);
            busyBot.remove(bot);
        }
    }

    public void doExpireTurn(int turnId) {
        timer();
    }

    public void timer() {
        try {
            Xerver.mCounter.get(3).addQueue(new TurnInfor(this, 0, 3));
        } catch (Exception ex) {
            slib_Logger.root().warn(Xerver.mCounter.get(3) + " " + this + " " + Util.exToString(ex));
        }
    }
}
