package com.bem.monitor;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.Arrays;

import io.netty.channel.Channel;
import net.sf.json.JSONObject;

import com.bem.object.UserInfo;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.k2tek.Xerver;
import com.k2tek.service.BaseServiceMessage;

public class UserPaymentMonitor {

	String allowIP = "127.0.0.1,localhost,*************";
	boolean isRun = true;

	public void startUDPServer() {
		new Thread(new Runnable() {

			public void run() {
				try {
					DatagramSocket serverSocket = new DatagramSocket(9876);
					byte[] receiveData = new byte[1024];
					// byte[] sendData = new byte[1024];
					while (isRun) {
						DatagramPacket receivePacket = new DatagramPacket(receiveData, receiveData.length);
						serverSocket.receive(receivePacket);
						String sentence = new String(receivePacket.getData());
						InetAddress IPAddress = receivePacket.getAddress();
						if (allowIP.contains(IPAddress.getHostAddress())) {
							try {
								JSONObject params = JSONObject.fromObject(sentence);
								String username = Util.getStringJson(params, "username");
								int koinAdded = Util.getIntJson(params, "koinAdded");
								String type = Util.getStringJson(params, "type"); // 1:sms, 2:the cao
								if (koinAdded == -1 || username == "" || type == "") {
								} else {
									UserInfo user = null;
//									Channel channel = Online.getChannel(username);
//									if (channel != null) {
//										newUser = Util.getUser(channel);
//										if (newUser != null) {
////											newUser.setBalance(newUser.getBalance() + koinAdded);
//											Util.sendProtoData(channel, CommonProto.getCommonVectorProto(null, Arrays.asList("Admin", "Bạn đã nạp koin thành công, tài khoản cộng thêm " + koinAdded + " koin.")), BaseServiceMessage.SPEAKER_GLOBAL, System.currentTimeMillis());
//										}
//									}
									int money = Util.getIntJson(params, "money");
									Xerver.textEvent.addUser(username, true, money);
								}
							} catch (Exception ex) {
								ex.printStackTrace();
							}
						}
						Thread.yield();
					}
					serverSocket.close();
				} catch (Exception ex) {
					ex.printStackTrace();
				}
			}
		}).start();

	}
}
