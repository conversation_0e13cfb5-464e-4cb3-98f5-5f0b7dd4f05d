package com.bem.monitor;

import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.net.tcp.RequestDecoder;
import com.k2tek.net.RequestMessage;
import com.k2tek.common.slib_Logger;
import com.proto.GGProto;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 4/19/2017.
 */
public class ServerConnector {

    static ServerConnector instance;

    public static ServerConnector getInstance() {
        if (instance == null) {
            instance = new ServerConnector();
        }
        return instance;
    }

    Map<String, Connector> mServer = new HashMap<String, Connector>();

    public void init() {
//        List<String> aHost = Arrays.asList("localhost");
//        List<Integer> aPort = Arrays.asList(6664);
//        for (int i = 0; i < aHost.size(); i++) {
//            mServer.put("1", new Connector(aHost.get(i), aPort.get(i)));
//        }
//        mServer.forEach((key, value) -> {
//            value.connect();
//            value.keepConnection();
//        });
    }

    public void sendResult(String server, String message) {
        Connector connect = mServer.get(server);
        if (connect != null) {
            connect.send(message);
        }
    }

    class Connector {
        String host;
        int port;
        Channel channel;

        public Connector(String host, int port) {
            this.host = host;
            this.port = port;
        }

        void send(String message) {
            try {
                byte[] data = message == null ? new byte[0] : message.getBytes();
                ByteBuf buffer = Unpooled.buffer();
                buffer.writeBytes(Constans.STRING_MAGIC.getBytes()); // K2
                buffer.writeInt(data.length);
                buffer.writeBytes(data);
                channel.writeAndFlush(buffer).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
            } catch (Exception ex) {
                slib_Logger.root().error(Util.exToString(ex));
            }
        }

        public void connect() {
            new Thread(() -> {
                try {
                    EventLoopGroup group = new NioEventLoopGroup();
                    try {
                        Bootstrap b = new Bootstrap();
                        b.group(group)
                                .channel(NioSocketChannel.class)
                                .option(ChannelOption.TCP_NODELAY, true)
                                .handler(new ChannelInitializer<SocketChannel>() {
                                    @Override
                                    public void initChannel(SocketChannel ch) throws Exception {
                                        ChannelPipeline p = ch.pipeline();
                                        p.addLast("decoder", new RequestDecoder());
                                        p.addLast(new ConnectorHandler());
                                        // p.addLast(new EchoClientHandler());
                                    }
                                });

                        // Start the client.
                        ChannelFuture f = b.connect(host, port).sync();
                        channel = f.channel();
                        // Wait until the connection is closed.
                        Thread.sleep(1000);
                        f.channel().closeFuture().sync();
                    } finally {
                        // Shut down the event loop to terminate all threads.
                        group.shutdownGracefully();
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }).start();
        }

        void keepConnection() {
            new Thread(() -> {
                while (true) {
                    try {
                        Thread.sleep(3000);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    try {
                        if (channel != null && !channel.isWritable()) {
                            try {
                                channel.disconnect();
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            try {
                                channel.close();
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            channel = null;
                            Thread.sleep(3000);
                            connect();
                        } else if (channel == null) {
                            connect();
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }).start();
        }
    }

    class ConnectorHandler extends ChannelDuplexHandler {

        byte[] IDLE_DATA = new byte[]{75, 52, 0, 0, 0, 2, 49, 64};

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            if (msg instanceof RequestMessage) {
                RequestMessage req = (RequestMessage) msg;
                GGProto.ResponseData response = CommonProto.parseResponseData(req.getBody());
                if (response != null) {
                    List<GGProto.Action> actions = response.getActionsList();
                    for (GGProto.Action action : actions) {
                        if (action.getService() == IAction.PING_IDLE) {
                            byte[] data = "".getBytes();
                            ByteBuf buffer = Unpooled.buffer();
                            buffer.writeBytes(Constans.STRING_MAGIC.getBytes()); // K2
                            buffer.writeInt(data.length);
                            buffer.writeBytes(data);
                            ctx.channel().writeAndFlush(buffer).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
                        }
                    }
                }
//                Object returnObject = resp.getResponse();
//                if (returnObject != null) {
//                    ctx.channel().writeAndFlush(returnObject).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
//                }
            } else {
                ctx.channel().close();
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            super.exceptionCaught(ctx, cause);
            ctx.channel().close();
        }

        @Override
        public void close(ChannelHandlerContext ctx, ChannelPromise promise) throws Exception {
            super.close(ctx, promise);
        }

        @Override
        public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
            super.channelRegistered(ctx);
        }
    }
}
