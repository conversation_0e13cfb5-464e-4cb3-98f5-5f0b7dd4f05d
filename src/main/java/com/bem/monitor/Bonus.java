package com.bem.monitor;

import com.bem.boom.Resources;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgEnergy;
import com.bem.config.CfgServer;
import com.bem.config.CfgShop;
import com.bem.dao.mapping.*;
import com.bem.object.*;
import com.bem.object.event.AbstractEvent;
import com.bem.object.event.TopTrophy;
import com.bem.util.Actions;
import grep.database.HibernateUtil;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.handler.game.AAHandler;
import com.k2tek.Constans;
import com.k2tek.common.slib_Logger;
import net.sf.json.JSONArray;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.slf4j.Logger;

import java.util.*;

/**
 * Created by vieth_000 on 5/19/2017.
 */
public class Bonus {
    public final static int GOLD = 1;
    public final static int GEM = 2;
    public final static int ITEM = 3;
    public final static int AVATAR = 4;
    public final static int TROPHY = 5;
    public final static int EXP = 6;
    public final static int BOMB = 7;
    public final static int MATERIAL = 8;
    public final static int PET_FOOD = 9;
    public final static int PET_FRAGMENT = 10;
    public final static int AVATAR_FRAGMENT = 11;
    public final static int PET = 12;
    public final static int HERO_EXP = 13;
    public final static int PET_EXP = 14;
    public final static int USER_ENERGY = 15;
    public final static int PET_ENERGY = 16;
    public final static int HERO = 17;
    public final static int ATOMIC = 18;
    public final static int ACCESSORIES = 20;
    public final static int MEDAL_BOSS = 21;
    public final static int MEDAL = 22;
    public final static int CLAN_MEDAL = 23;
    public final static int GAME_NUMBER_ATK = 24;
    public final static int RANK_ID = 25;
    public final static int UFO = 26;
    public final static int UFO_STONE = 27;


    //
    public final static int WIN_RATE = 100;
    public final static int UPDATE_DATA_INT = 101;
    public final static int UPDATE_MAP_LEVEL = 102;
    public final static int UPDATE_SHOP_SPECIAL = 103;
    public final static int USER_EVENT = 104;


    public static Map<Integer, Integer> mParamSize = new HashMap<Integer, Integer>() {{
        put(GOLD, 1);
        put(ATOMIC, 1);
        put(GEM, 1);
        put(ITEM, 2);
        put(AVATAR, 2);
        put(TROPHY, 1);
        put(EXP, 1);
        put(BOMB, 2);
        put(MATERIAL, 2);
        put(PET_FOOD, 2);
        put(PET_FRAGMENT, 2);
        put(AVATAR_FRAGMENT, 2);
        put(PET, 3);
        put(HERO_EXP, 2);
        put(PET_EXP, 2);
        put(USER_ENERGY, 1);
        put(PET_ENERGY, 2);
        put(HERO, 1);
        put(ACCESSORIES, 2);
        put(MEDAL_BOSS, 1);
        put(MEDAL, 1);
        put(UFO, 1);
        put(UFO_STONE, 2);


        put(WIN_RATE, 2);
        put(USER_EVENT, 2);

        put(UPDATE_DATA_INT, 3);
        put(UPDATE_MAP_LEVEL, 3);
        put(UPDATE_SHOP_SPECIAL, 0);
        put(GAME_NUMBER_ATK, 1);
    }};

    public static JSONArray convertListToJson(List<Integer> aBonus) {
        JSONArray arr = new JSONArray();
        for (Integer bonus : aBonus) {
            arr.add(bonus);
        }
        return arr;
    }

    public static List<Long> receiveListItem(UserInfo user, JSONArray aBonus, String detailAction) {
        debug("aBonus = " + aBonus.toString());

        BonusInfo bonus = new Bonus().getBonusInfo();
        parseBonusObject(user, bonus, aBonus);
        if (dbUpdateBonus(user, bonus)) {
            return getBonusResult(user, bonus, aBonus, detailAction);
        }
        return new ArrayList<Long>();
    }

    static List<Long> getBonusResult(UserInfo user, BonusInfo bonus, JSONArray aBonus, String detailAction) {
//        System.out.println("aBonus--->"+aBonus);
        List<Long> aLong = new ArrayList<Long>();
        int index = 0;
        while (index < aBonus.size()) {
            int type = aBonus.getInt(index++);
//            System.out.println("type-------------->"+type);
            if (type >= WIN_RATE) {
                if (type == UPDATE_SHOP_SPECIAL) {
                    index += mParamSize.get(type);
                    continue;
                }
            } else {
                aLong.add((long) type);
            }
            switch (type) {
                case GAME_NUMBER_ATK: {
                    aLong.remove(aLong.size() - 1);
                    int gameId = aBonus.getInt(index++);
                    int numberInfo = aBonus.getInt(index++);
                    long[] aInfor = new long[numberInfo];
                    for (int i = 0; i < numberInfo; i++) {
                        aInfor[i] = aBonus.getLong(index++);
                    }
                    AAHandler event = Constans.mEvent.get(gameId);
                    if (event == null) event = Constans.mEvent.get(user.getDbUser().getServer() * 100 + gameId);
                    if (event != null) event.addNumberAtk(user, aInfor);
                    break;
                }
                case USER_EVENT:
                    index++;
                    index++;
                    break;
                case WIN_RATE:

                    int addWin = (int) aBonus.getLong(index++);
                    user.getDbUser().addWinLoose(addWin, (int) aBonus.getLong(index++));
                    if (addWin > 0) user.getUData().getUInt().addValue(UserInt.NUMBER_WIN, UserInt.TYPE_ADD, 1);
                    else user.getUData().getUInt().addValue(UserInt.NUMBER_WIN, UserInt.TYPE_SET, 0);
                    break;
                case HERO: {
                    int heroId = aBonus.getInt(index++);
                    if (!user.getRes().getMHero().containsKey(heroId)) {
                        UserHeroEntity hero = new UserHeroEntity(user.getId(), heroId);
                        user.getRes().getHeroes().add(hero);
                        user.getRes().getMHero().put(hero.getHeroId(), hero);
                        aLong.add((long) heroId);
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "hero",
                                "id", String.valueOf(heroId))));
                    } else {
                        aLong.remove(aLong.size() - 1);
                    }
                    break;
                }
                case UFO: {
                    int ufoId = aBonus.getInt(index++);
                    ResUfo resUfo = Resources.mUfo.get(ufoId);
                    if (!user.getRes().getMUfo().containsKey(ufoId)) {
                        UserUfoEntity ufo = new UserUfoEntity(user.getId(), ufoId, resUfo.getAtk(), resUfo.getHp());
                        user.getRes().getUfos().add(ufo);
                        user.getRes().getMUfo().put(ufo.getUfoId(), ufo);
                        aLong.add((long) ufoId);
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "ufo",
                                "id", String.valueOf(ufoId))));
                    } else {
                        aLong.remove(aLong.size() - 1);
                    }
                    break;
                }
                case UFO_STONE: {
                    int stoneId = aBonus.getInt(index++);
                    int number = aBonus.getInt(index++);
                    StoneUpgradeUfo resStoneUpgradeUfo = Resources.ufoUpgrade.getMStone().get(stoneId);
                    UserStoneUfoEntity stone = user.getRes().getMStoneUfo().get(stoneId);
                    if (stone != null) {
                        stone.addNumber(number);
                    } else {
                        stone = new UserStoneUfoEntity(user.getId(), stoneId, number);
                        user.getRes().getMStoneUfo().put(stoneId, stone);
                        user.getRes().getStoneUfos().add(stone);
                    }
                    aLong.addAll(Arrays.asList((long) stone.getStoneId(), (long) stone.getNumber(), (long) number));
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "stone_ufo",
                            "id", String.valueOf(stoneId), "curNumber", String.valueOf(number))));
                    break;
                }
                case GOLD: {
                    long gold = aBonus.getLong(index++);
                    user.getDbUser().addGold(gold);
                    aLong.add(user.getDbUser().getGold());
                    aLong.add(gold);
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "gold",
                            "value", String.valueOf(user.getDbUser().getGold()), "addValue", String.valueOf(gold))));
                    break;
                }
                case GEM: {
                    long gem = aBonus.getLong(index++);
                    user.getDbUser().addGem(gem);
                    aLong.add(user.getDbUser().getGem());
                    aLong.add(gem);
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "gem",
                            "value", String.valueOf(user.getDbUser().getGem()), "addValue", String.valueOf(gem))));
                    break;
                }
                case MEDAL: {
                    long medal = aBonus.getLong(index++);
                    UserMoneys moneys = user.getUData().getUserMoneys();
                    moneys.addValue(UserMoneys.MEDAL, (int) medal);
                    aLong.add((long) moneys.getValue(UserMoneys.MEDAL));
                    aLong.add(medal);
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "medal",
                            "value", String.valueOf(moneys.getValue(UserMoneys.MEDAL)), "addValue", String.valueOf(medal))));
                    break;
                }
                case MEDAL_BOSS: {
                    long boss = aBonus.getLong(index++);
                    UserMoneys moneys = user.getUData().getUserMoneys();
                    moneys.addValue(UserMoneys.MEDAL_BOSS, (int) boss);
                    aLong.add((long) moneys.getValue(UserMoneys.MEDAL_BOSS));
                    aLong.add(boss);
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "boss",
                            "value", String.valueOf(moneys.getValue(UserMoneys.MEDAL_BOSS)), "addValue", String.valueOf(boss))));
                    break;
                }
                case ATOMIC: {
                    int atomic = aBonus.getInt(index++);
                    user.getDbUser().setAtomic(user.getDbUser().getAtomic() + atomic);
                    aLong.add((long) user.getDbUser().getAtomic());
                    aLong.add((long) atomic);
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "atomic",
                            "value", String.valueOf(user.getDbUser().getAtomic()), "addValue", String.valueOf(atomic))));
                    break;
                }
                case TROPHY: {
                    long trophy = aBonus.getLong(index++);
                    TopTrophy event = (TopTrophy) user.getUEvent().getEvent(Constans.EVENT_TOP_TROPHY);
                    int[] eventIndex = event.getEventIndex();
                    if (eventIndex[0] == event.getEvent().getEventIndex()) {
                        event.getEvent().addValue(trophy);
                        user.getDbUser().setTrophy(trophy, user);
                    } else {
                        UserEventEntity uEvent = new UserEventEntity(user.getId(), Constans.EVENT_TOP_TROPHY, eventIndex[0]);
                        uEvent.setValue(trophy);
                        uEvent.setServerId(user.getDbUser().getServer());
                        event.setEvent(uEvent);
                        user.getDbUser().setTrophy((int) trophy);
                    }
                    aLong.add((long) user.getDbUser().getTrophy());
                    aLong.add(trophy);
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "trophy",
                            "value", String.valueOf(user.getDbUser().getTrophy()), "addValue", String.valueOf(trophy))));
                    break;
                }
                case ITEM: {
                    int itemId = aBonus.getInt(index++);
                    int number = aBonus.getInt(index++);
//                    System.out.println("itemId----->"+itemId);
                    if (itemId == ResShopItem.PHAO_HOA || itemId == ResShopItem.KEO || itemId == ResShopItem.HOA_HONG || itemId == 100) {
                        aLong.remove(aLong.size() - 1);
//                        aLong.addAll(Arrays.asList((long) itemId,(long)number, (long) number));
//                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "item",
//                                "id", String.valueOf(itemId), "value", String.valueOf(number), "addValue", String.valueOf(number))));
                    } else {
                        UserItemEntity item = user.getRes().getMItem().get(itemId);
                        if (item != null) {
                            item.addNumber(number);
                        } else {
                            item = new UserItemEntity(user.getId(), (byte) itemId, number);
                            user.getRes().getMItem().put(itemId, item);
                        }
                        aLong.addAll(Arrays.asList((long) itemId, (long) item.getNumber(), (long) number));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "item",
                                "id", String.valueOf(itemId), "value", String.valueOf(item.getNumber()), "addValue", String.valueOf(number))));
                    }
                    break;
                }
                case AVATAR: {
                    int avatarId = aBonus.getInt(index++);
                    int level = aBonus.getInt(index++);
                    UserAvatarEntity avatar = user.getRes().getMAvatar().get(avatarId);
                    if (avatar == null) {
                        avatar = new UserAvatarEntity(user.getId(), avatarId, level);
                        user.getRes().getMAvatar().put(avatar.getAvatarId(), avatar);
                        aLong.addAll(Arrays.asList((long) avatar.getAvatarId(), (long) avatar.getLevel()));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "avatar",
                                "id", String.valueOf(avatarId), "level", String.valueOf(level))));
                    } else if (avatar.getLevel() == 0) {
                        avatar.setLevel(level);
                        aLong.addAll(Arrays.asList((long) avatar.getAvatarId(), (long) avatar.getLevel()));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "avatar",
                                "id", String.valueOf(avatarId), "level", String.valueOf(level))));
                    } else if (avatar.getLevel() >= 1) {
                        avatar.addFragment(50);
                        aLong.set(aLong.size() - 1, (long) AVATAR_FRAGMENT);
                        aLong.addAll(Arrays.asList((long) avatar.getAvatarId(), (long) avatar.getFragment(), 50L));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "avatar_fragment",
                                "id", String.valueOf(avatarId), "value", String.valueOf(avatar.getFragment()), "addValue", "50")));
                    }
                    break;
                }
                case AVATAR_FRAGMENT: {
                    int avatarId = aBonus.getInt(index++);
                    int number = aBonus.getInt(index++);
                    user.getRes().getMAvatar().toString();
                    UserAvatarEntity avatar = user.getRes().getMAvatar().get(avatarId);
                    if (avatar == null) {
                        avatar = new UserAvatarEntity(user.getId(), avatarId, 0, number);
                        user.getRes().getMAvatar().put(avatar.getAvatarId(), avatar);
                    } else {
                        avatar.addFragment(number);
                    }
                    aLong.addAll(Arrays.asList((long) avatar.getAvatarId(), (long) avatar.getFragment(), (long) number));
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "avatar_fragment",
                            "id", String.valueOf(avatarId), "value", String.valueOf(avatar.getFragment()), "addValue", String.valueOf(number))));
                    break;
                }
                case BOMB: {
                    index += 2;
                    if (!bonus.retBomb.isEmpty()) {
                        UserBombEntity bomb = bonus.getBombEntity();
                        user.getRes().getMBomb().put(bomb.getId(), bomb);
                        aLong.addAll(Arrays.asList((long) bomb.getId(), (long) bomb.getBombId(), (long) bomb.getLevel()));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "bomb",
                                "id", String.valueOf(bomb.getId()), "bombId", String.valueOf(bomb.getBombId()), "level", String.valueOf(bomb.getLevel()))));
                    }
                    break;
                }
                case ACCESSORIES: {
                    index += 2;
                    if (!bonus.retAccess.isEmpty()) {
                        UserAccessoriesEntity access = bonus.getAccessoriesEntity();
                        user.getRes().getMAccessories().put(access.getId(), access);
                        aLong.addAll(Arrays.asList((long) access.getId(), (long) access.getAccessoriesId(), (long) access.getLevel()));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "accessory",
                                "id", String.valueOf(access.getId()), "accessId", String.valueOf(access.getAccessoriesId()), "level", String.valueOf(access.getLevel()))));
                    }
                    break;
                }
                case PET_FOOD: {
                    int foodId = aBonus.getInt(index++);
                    int number = aBonus.getInt(index++);
                    UserPetFoodEntity food = user.getRes().getMPetFood().get(foodId);
                    if (food != null) {
                        food.addNumber(number);
                    } else {
                        food = new UserPetFoodEntity(user.getId(), foodId, number);
                        user.getRes().getMPetFood().put(foodId, food);
                    }
                    aLong.addAll(Arrays.asList((long) food.getFoodId(), (long) food.getNumber(), (long) number));
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "pet_food",
                            "id", String.valueOf(foodId), "value", String.valueOf(food.getNumber()), "addValue", String.valueOf(number))));
                    break;
                }
                case MATERIAL: {
                    int materialId = aBonus.getInt(index++);
                    int number = aBonus.getInt(index++);
                    UserMaterialEntity material = user.getRes().getMMaterial().get(materialId);
                    if (material != null) {
                        material.addNumber(number);
                    } else {
                        material = new UserMaterialEntity(user.getId(), materialId, number);
                        user.getRes().getMMaterial().put(materialId, material);
                    }
                    aLong.addAll(Arrays.asList((long) materialId, (long) material.getNumber(), (long) number));
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "material",
                            "id", String.valueOf(materialId), "value", String.valueOf(material.getNumber()), "addValue", String.valueOf(number))));
                    break;
                }
                case EXP: {
                    int addExp = aBonus.getInt(index++);
                    user.getDbUser().addExp(addExp, user);
                    aLong.addAll(Arrays.asList((long) user.getDbUser().getLevel(), user.getDbUser().getExp(), (long) addExp));
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "exp",
                            "value", String.valueOf(user.getDbUser().getExp()), "addValue", String.valueOf(addExp), "level", String.valueOf(user.getDbUser().getLevel()))));
                    break;
                }
                case HERO_EXP: {
                    int heroId = aBonus.getInt(index++);
                    int addExp = aBonus.getInt(index++);
                    UserHeroEntity uHero = user.getRes().getMHero().get(heroId);
                    if (uHero != null) {
                        uHero.addExp(addExp, user.getDbUser().getLevel());
                        aLong.addAll(Arrays.asList((long) heroId, (long) uHero.getLevel(), uHero.getExp(), (long) addExp));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "hero_exp",
                                "id", String.valueOf(heroId), "value", String.valueOf(uHero.getExp()), "addValue", String.valueOf(addExp), "level", String.valueOf(uHero.getLevel()))));
                    } else {
                        aLong.remove(aLong.size() - 1);
                    }
                    break;
                }
                case PET_EXP: {
                    int petId = aBonus.getInt(index++);
                    int addExp = aBonus.getInt(index++);
                    UserPetEntity uPet = user.getRes().getMPet().get(petId);
                    if (uPet != null) {
                        uPet.addExp(addExp);
                        aLong.addAll(Arrays.asList((long) petId, (long) uPet.getLevel(), uPet.getExp(), (long) addExp));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "hero_exp",
                                "id", String.valueOf(petId), "value", String.valueOf(uPet.getExp()), "addValue", String.valueOf(addExp), "level", String.valueOf(uPet.getLevel()))));
                    } else {
                        aLong.remove(aLong.size() - 1);
                    }
                    break;
                }
                case PET: {
                    int petId = aBonus.getInt(index++);
                    int level = aBonus.getInt(index++);
                    int star = aBonus.getInt(index++);

                    UserPetEntity pet = user.getRes().getMPet().get(petId);
                    if (pet == null) {
                        pet = new UserPetEntity(user.getId(), petId, level, star, 0);
                        user.getRes().getMPet().put(pet.getPetId(), pet);
                        aLong.addAll(Arrays.asList((long) pet.getPetId(), (long) pet.getLevel(), (long) star));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "pet",
                                "id", String.valueOf(petId), "level", String.valueOf(level), "star", String.valueOf(star))));
                    } else if (pet.getLevel() == 0) {
                        pet.setLevel(level);
                        pet.setStar(star);
                        aLong.addAll(Arrays.asList((long) pet.getPetId(), (long) pet.getLevel(), (long) star));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "pet",
                                "id", String.valueOf(petId), "level", String.valueOf(level), "star", String.valueOf(star))));
                    } else if (pet.getLevel() >= 1) {
                        pet.addFragment(50);
                        aLong.set(aLong.size() - 1, (long) PET_FRAGMENT);
                        aLong.addAll(Arrays.asList((long) pet.getPetId(), (long) pet.getFragment(), 50L));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "pet_fragment",
                                "id", String.valueOf(petId), "value", String.valueOf(pet.getFragment()), "addValue", "50")));
                    }
                    break;
                }
                case PET_FRAGMENT: {
                    int petId = aBonus.getInt(index++);
                    int number = aBonus.getInt(index++);
                    UserPetEntity pet = user.getRes().getMPet().get(petId);
                    if (pet == null) {
                        pet = new UserPetEntity(user.getId(), petId, 0, 0, number);
                        user.getRes().getMPet().put(petId, pet);
                    } else {
                        pet.addFragment(number);
                    }
                    aLong.addAll(Arrays.asList((long) pet.getPetId(), (long) pet.getFragment(), (long) number));
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "pet_fragment",
                            "id", String.valueOf(petId), "value", String.valueOf(pet.getFragment()), "addValue", String.valueOf(number))));
                    break;
                }
                case USER_ENERGY: {
                    int energy = aBonus.getInt(index++);
                    CfgEnergy.MyEnergy mEnergy = CfgEnergy.getMyEnergy(user);
                    aLong.addAll(Arrays.asList((long) mEnergy.energy, (long) energy, mEnergy.getCountdown()));
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "user_energy",
                            "value", String.valueOf(mEnergy.energy), "addValue", String.valueOf(energy))));
                    break;
                }
                case PET_ENERGY: {
                    int petId = aBonus.getInt(index++);
                    int energy = aBonus.getInt(index++);
                    UserPetEntity pet = user.getRes().getMPet().get(petId);
                    if (pet != null) {
                        pet.addEnergy(energy);
                        aLong.addAll(Arrays.asList((long) petId, (long) pet.getEnergy(), (long) energy));
                        Actions.save(user.getDbUser(), Actions.GRECEIVE, detailAction, Actions.convertToLogString(Arrays.asList("type", "pet_energy",
                                "value", String.valueOf(pet.getEnergy()), "addValue", String.valueOf(energy))));
                    }
                    break;
                }
                case UPDATE_DATA_INT: {
                    int updateIndex = aBonus.getInt(index++);
                    int updateType = aBonus.getInt(index++);
                    int updateValue = aBonus.getInt(index++);
                    user.getUData().getUInt().addValue(updateIndex, updateType, updateValue);
                    break;
                }
                case UPDATE_MAP_LEVEL: {
                    int mode = aBonus.getInt(index++);
                    int mapId = aBonus.getInt(index++);
                    int star = aBonus.getInt(index++);
                    user.getUData().getMap().setReal(mode, mapId, star);
                }
            }
            debug("along recieve---------------------->" + aLong);
        }
        return aLong;
    }

    static boolean dbUpdateBonus(UserInfo user, BonusInfo bonus) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            // gold gem
            long addGold = bonus.getTotalGold(), addGem = bonus.getTotalGem(), addExp = bonus.getTotalExp(), addAtomic = bonus.getTotalAtomic();
            if (addGold != 0 || addGem != 0 || addExp != 0 || addAtomic != 0 || (bonus.winLoose[0] != 0 || bonus.winLoose[1] != 0)) {
                int oldLevel = user.getDbUser().getLevel();
                long[] tmpLevel = user.getDbUser().addFakeExp(addExp);
                bonus.userEnergy += (tmpLevel[0] - oldLevel) * CfgEnergy.config.upLevel;
                SQLQuery query = session.createSQLQuery("update user set level=:level,gold=gold+:gold,gem=gem+:gem,exp=:exp,atomic=atomic+:atomic,win=win+:win,loose=loose+:loose where id=" + user.getId());
                query.setLong("gold", addGold);
                query.setLong("gem", addGem);
                query.setLong("level", tmpLevel[0]);
                query.setLong("exp", tmpLevel[1]);
                query.setLong("atomic", addAtomic);
                query.setLong("win", bonus.winLoose[0]);
                query.setLong("loose", bonus.winLoose[1]);
                query.executeUpdate();
            }
            // newUser energy
            if (bonus.userEnergy != 0) {
                CfgEnergy.MyEnergy myEnergy = CfgEnergy.getMyEnergy(user);
                user.getUData().setEnergy(myEnergy.toString());
                myEnergy.addEnergy(bonus.userEnergy);
                SQLQuery query = session.createSQLQuery("update user_data set energy=:energy where user_id=" + user.getId());
                query.setString("energy", myEnergy.toString());
                query.executeUpdate();
            }
            // pet energy
            for (int i = 0; i < bonus.aPetEnergy.size(); i++) {
                session.createSQLQuery("update user_pet set energy=energy+(" + bonus.aPetEnergy.get(i + 1) + ") where user_id=" + user.getId() + " and pet_id=" + bonus.aPetEnergy.get(i)).executeUpdate();
            }
            // hero exp
            for (int i = 0; i < bonus.aHeroExp.size(); i += 2) {
                UserHeroEntity uHero = user.getRes().getMHero().get(bonus.aHeroExp.get(i).intValue());
                if (uHero != null) {
                    long[] tmpLevel = uHero.addFakeExp(bonus.aHeroExp.get(i + 1), user.getDbUser().getLevel());
                    SQLQuery query = session.createSQLQuery("update user_hero set level=:level, exp=:exp where user_id=" + user.getId() + " and hero_id=:heroId");
                    query.setLong("heroId", bonus.aHeroExp.get(i));
                    query.setLong("level", tmpLevel[0]);
                    query.setLong("exp", tmpLevel[1]);
                    query.executeUpdate();
                }
            }
            // hero
            for (int i = 0; i < bonus.aHero.size(); i++) {
                if (!user.getRes().getMHero().containsKey(bonus.aHero.get(i))) {
                    session.save(new UserHeroEntity(user.getId(), bonus.aHero.get(i)));
                }
            }
            //ufo
            for (int i = 0; i < bonus.aUfo.size(); i++) {
                if (!user.getRes().getMUfo().containsKey(bonus.aUfo.get(i)) && Resources.mUfo.get(bonus.aUfo.get(i)) != null) {
                    int atk = Resources.mUfo.get(bonus.aUfo.get(i)).getAtk();
                    int hp = Resources.mUfo.get(bonus.aUfo.get(i)).getHp();
                    session.save(new UserUfoEntity(user.getId(), bonus.aUfo.get(i), atk, hp));
                }
            }
            // ufo_stone
            {
                Iterator<Integer> iter = bonus.mStoneUfo.keySet().iterator();
                while (iter.hasNext()) {
                    int stoneId = iter.next();
                    if (user.getRes().getMStoneUfo().containsKey(stoneId)) {
                        session.createSQLQuery("update user_stone_ufo set number=number+(" + bonus.mStoneUfo.get(stoneId) + ") where user_id=" + user.getId() + " and stone_id=" + stoneId).executeUpdate();
                    } else {
                        session.save(new UserStoneUfoEntity(user.getId(), stoneId, bonus.mStoneUfo.get(stoneId)));
                    }
                }
            }
            // item
            {
                Iterator<Integer> iter = bonus.mItem.keySet().iterator();
                while (iter.hasNext()) {
                    int itemId = iter.next();
                    if (user.getRes().getMItem().containsKey(itemId)) {
                        session.createSQLQuery("update user_item set number=number+(" + bonus.mItem.get(itemId) + ") where user_id=" + user.getId() + " and item_id=" + itemId).executeUpdate();
                    } else {
                        session.save(new UserItemEntity(user.getId(), (byte) itemId, bonus.mItem.get(itemId)));
                    }
                }
            }
            // bomb
            for (int i = 0; i < bonus.aBomb.size(); i += 2) {
                UserBombEntity bomb = new UserBombEntity(user.getId(), bonus.aBomb.get(i), bonus.aBomb.get(i + 1));
                session.save(bomb);
                bonus.retBomb.add(bomb);
            }
            //userevent
            for (int i = 0; i < bonus.aUserEvent.size(); i += 2) {
                AbstractEvent event = user.getUEvent().getEvent(bonus.aUserEvent.get(i));
                if (event == null) {
                    continue;
                }
                int[] eventIndex = null;
                try {
                    eventIndex = event.getEventIndex();
                } catch (Exception ex) {
//                    user.loadEvent();
                    eventIndex = new int[]{event.getEvent().getEventIndex()};
                }
                if (eventIndex == null) {
                    continue;
                }
                try {
                    if (eventIndex[0] != event.getEvent().getEventIndex()) {
                        UserEventEntity uEvent = new UserEventEntity(user.getId(), bonus.aUserEvent.get(i), eventIndex[0]);
                        uEvent.setValue(bonus.aUserEvent.get(i + 1));
                        uEvent.setServerId(user.getDbUser().getServer());
                        session.save(uEvent);
                        user.getUEvent().getEvent(bonus.aUserEvent.get(i)).getEvent().setValue(bonus.aUserEvent.get(i + 1));
//                    System.out.println("user.getUEvent().getEvent(bonus.aUserEvent.get(i)).getEvent().getValue()-1->"+user.getUEvent().getEvent(bonus.aUserEvent.get(i)).getEvent().getValue());
                    } else {
                        session.createSQLQuery("update user_event set value=( value + " + bonus.aUserEvent.get(i + 1) + ") where user_id=" + user.getId()
                                + " and event_id=" + bonus.aUserEvent.get(i) + " and event_index=" + eventIndex[0]).executeUpdate();
                        user.getUEvent().getEvent(bonus.aUserEvent.get(i)).getEvent().setValue(user.getUEvent().getEvent(bonus.aUserEvent.get(i)).getEvent().getValue() + bonus.aUserEvent.get(i + 1));
//                    System.out.println("user.getUEvent().getEvent(bonus.aUserEvent.get(i)).getEvent().getValue()-2->"+user.getUEvent().getEvent(bonus.aUserEvent.get(i)).getEvent().getValue());
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            //accessories
            for (int i = 0; i < bonus.aAccessories.size(); i += 2) {
                UserAccessoriesEntity accessories = new UserAccessoriesEntity(user.getId(), bonus.aAccessories.get(i), bonus.aAccessories.get(i + 1));
                session.save(accessories);
                bonus.retAccess.add(accessories);
            }
            // petFood
            {
                Iterator<Integer> iter = bonus.mPetFood.keySet().iterator();
                while (iter.hasNext()) {
                    int foodId = iter.next();
                    if (user.getRes().getMPetFood().containsKey(foodId)) {
                        session.createSQLQuery("update user_pet_food set number=number+(" + bonus.mPetFood.get(foodId) + ") where user_id=" + user.getId() + " and food_id=" + foodId).executeUpdate();
                    } else {
                        session.save(new UserPetFoodEntity(user.getId(), foodId, bonus.mPetFood.get(foodId)));
                    }
                }
            }

            // avatar + avatar fragment
            {
                List<String> aSql = new ArrayList<>();
                List<UserAvatarEntity> aAvatar = new ArrayList<>();
                for (int i = 0; i < bonus.aAvatar.size(); i += 2) {
                    int avatarId = bonus.aAvatar.get(i);
                    UserAvatarEntity avatar = user.getRes().getMAvatar().get(avatarId);
                    if (avatar == null) {
                        avatar = bonus.getAvatarEntity(aAvatar, avatarId);
                        if (avatar == null) {
                            aAvatar.add(new UserAvatarEntity(user.getId(), avatarId, bonus.aAvatar.get(i + 1)));
                        } else {
                            avatar.addFragment(50);
                        }
                    } else if (avatar.getLevel() == 0) { // has fragment
                        aSql.add("update user_avatar set level=" + bonus.aAvatar.get(i + 1) + " where user_id=" + user.getId() + " and avatar_id=" + avatarId);
                    } else if (avatar.getLevel() > 0) {
                        aSql.add("update user_avatar set fragment=fragment+50 where user_id=" + user.getId() + "  and avatar_id=" + avatarId);
                    }
                }

                for (int i = 0; i < bonus.aAvatarFragment.size(); i += 2) {
                    int avatarId = bonus.aAvatarFragment.get(i);
                    UserAvatarEntity avatar = user.getRes().getMAvatar().get(avatarId);
                    if (avatar == null) {
                        avatar = bonus.getAvatarEntity(aAvatar, avatarId);
                        if (avatar == null) {
                            aAvatar.add(new UserAvatarEntity(user.getId(), avatarId, 0, bonus.aAvatarFragment.get(i + 1)));
                        } else {
                            avatar.addFragment(50);
                        }
                    } else { // has fragment
                        aSql.add("update user_avatar set fragment=fragment+(" + bonus.aAvatarFragment.get(i + 1) + ") where user_id=" + user.getId() + "  and avatar_id=" + avatarId);
                    }
                }
                for (UserAvatarEntity avatarEntity : aAvatar) {
                    session.save(avatarEntity);
                }
                for (String sql : aSql) {
                    session.createSQLQuery(sql).executeUpdate();
                }
            }

            // Pet + Pet fragment
            {
                List<String> aSql = new ArrayList<>();
                List<UserPetEntity> aPet = new ArrayList<>();
                for (int i = 0; i < bonus.aPet.size(); i += 3) {
                    int petId = bonus.aPet.get(i);
                    int level = bonus.aPet.get(i + 1);
                    int star = bonus.aPet.get(i + 2);
                    UserPetEntity pet = user.getRes().getMPet().get(petId);
                    if (pet == null) {
                        pet = bonus.getPetEntity(aPet, petId);
                        if (pet == null) {
                            aPet.add(new UserPetEntity(user.getId(), petId, level, star, 0));
                        } else {
                            pet.addFragment(50);
                        }
                    } else if (pet.getLevel() == 0) { // has fragment
                        aSql.add("update user_pet set level=" + level + ", star=" + star + " where user_id=" + user.getId() + " and pet_id=" + petId);
                    } else if (pet.getLevel() > 0) {
                        aSql.add("update user_pet set fragment=fragment+50 where user_id=" + user.getId() + "  and pet_id=" + petId);
                    }
                }
                // Pet fragment
                for (int i = 0; i < bonus.aPetFragment.size(); i += 2) {
                    int petId = bonus.aPetFragment.get(i);
                    UserPetEntity pet = user.getRes().getMPet().get(petId);
                    if (pet == null) {
                        pet = bonus.getPetEntity(aPet, petId);
                        if (pet == null) {
                            aPet.add(new UserPetEntity(user.getId(), petId, 0, 0, bonus.aPetFragment.get(i + 1)));
                        } else {
                            pet.addFragment(bonus.aPetFragment.get(i + 1));
                        }
                    } else {
                        aSql.add("update user_pet set fragment=fragment+(" + bonus.aPetFragment.get(i + 1) + ") where user_id=" + user.getId() + "  and pet_id=" + petId);
                    }
                }
                for (UserPetEntity petEntity : aPet) {
                    session.save(petEntity);
                }
                for (String sql : aSql) {
                    session.createSQLQuery(sql).executeUpdate();
                }
            }

            //material
            {
                Iterator<Integer> iter = bonus.mMaterial.keySet().iterator();
                while (iter.hasNext()) {
                    int materialId = iter.next();
                    if (user.getRes().getMMaterial().containsKey(materialId)) {
                        session.createSQLQuery("update user_material set number =number+(" + bonus.mMaterial.get(materialId) + ") where user_id=" + user.getId() + "  and material_id=" + materialId).executeUpdate();
                    } else {
                        session.save(new UserMaterialEntity(user.getId(), materialId, bonus.mMaterial.get(materialId)));
                    }
                }
            }
            // user_int trong table user_data
            if (!bonus.aUserInt.isEmpty()) {
                List<Integer> aInt = new ArrayList<Integer>(user.getUData().getUInt().aInt);
                int maxWin = user.getDbUser().getMaxwin();
                for (int i = 0; i < bonus.aUserInt.size(); i += 3) {
                    if (bonus.aUserInt.get(i + 1) == 0) { // set
                        aInt.set(bonus.aUserInt.get(i), bonus.aUserInt.get(i + 2));
                    } else { // add
                        aInt.set(bonus.aUserInt.get(i), aInt.get(bonus.aUserInt.get(i)) + bonus.aUserInt.get(i + 2));
                    }
                    if (bonus.aUserInt.get(i) == UserInt.NUMBER_WIN) {
                        if (aInt.get(bonus.aUserInt.get(i)) > maxWin) {
                            maxWin = aInt.get(bonus.aUserInt.get(i));
                        }
                    }
                }
                SQLQuery query = session.createSQLQuery("update user_data set data_int=:dataInt where user_id=" + user.getId());
                query.setString("dataInt", new Gson().toJson(aInt));
                query.executeUpdate();
//                System.out.println("maxwin 2-0---->"+maxWin + "|" +user.getDbUser().getName());
                if (maxWin > user.getDbUser().getMaxwin()) {
                    user.getDbUser().setMaxwin(maxWin);
//                        session.createSQLQuery("INSERT INTO user_event (user_id, event_id, value) VALUES(" + user.getId() + ", "
//                                + Constans.EVENT_TOP_WIN + ", " + maxWin + ") ON DUPLICATE KEY UPDATE value=" + maxWin).executeUpdate();
                    AbstractEvent event = user.getUEvent().getEvent(Constans.EVENT_TOP_WIN);
                    int[] eventIndex = event.getEventIndex();
                    if (eventIndex[0] != event.getEvent().getEventIndex()) {
                        if (maxWin > 0) {
                            UserEventEntity uEvent = new UserEventEntity(user.getId(), Constans.EVENT_TOP_WIN, eventIndex[0]);
                            uEvent.setServerId(user.getDbUser().getServer());
                            uEvent.setValue(maxWin);
                            session.save(uEvent);
                        }
                    } else {
                        if (maxWin + event.getEvent().getValue() < 0) maxWin = -user.getDbUser().getTrophy();
                        session.createSQLQuery("update user_event set value=(" + maxWin + ") where user_id=" + user.getId()
                                + " and event_id=" + Constans.EVENT_TOP_WIN + " and event_index=" + eventIndex[0]).executeUpdate();
                    }

                }
            }
            // map_level trong table user_data
            if (!bonus.aMapLevel.isEmpty()) {
                MapLevel map = user.getUData().getMap();
                int addStar = 0;
                for (int i = 0; i < bonus.aMapLevel.size(); i += 3) {
                    map.setFake(bonus.aMapLevel.get(i), bonus.aMapLevel.get(i + 1), bonus.aMapLevel.get(i + 2));
                    addStar += bonus.aMapLevel.get(i + 2);
                }
                SQLQuery query = session.createSQLQuery("update user_data set map_level=:mapLevel where user_id=" + user.getId());
                query.setString("mapLevel", map.toString(false));
                query.executeUpdate();
                int starMap = user.getUData().getMap().getStar();
                starMap += addStar;
//                List<Integer> mapNomal = user.getUData().getMap().getNormal();
//                for (int i = 0; i < mapNomal.size(); i++) {
//                    if (mapNomal.get(i) > 0) {
//                        starMap = +mapNomal.get(i);
//                    }
//                }
//                System.out.println("starMap---->"+starMap);
//                System.out.println("user.getDbUser().getStarMap()-->"+user.getDbUser().getStarMap());
                if (user.getDbUser().getStarMap() != starMap) {
                    SQLQuery query1 = session.createSQLQuery("update user set star_map=:starmap where id=" + user.getId());
                    query1.setString("starmap", starMap + "");
                    query1.executeUpdate();
                    user.getDbUser().setStarMap(starMap);
                    if (CfgAchievement.inEvent(CfgAchievement.TOP_STAR_MAP, user.getDbUser())) {
                        session.createSQLQuery("INSERT INTO user_event (user_id, event_id, value) VALUES(" + user.getId() + ", "
                                + Constans.EVENT_TOP_STAR_MAP + ", " + starMap + ") ON DUPLICATE KEY UPDATE value=" + starMap).executeUpdate();
                    }
                }
            }
            if (!bonus.aMedal.isEmpty() || !bonus.aBoss.isEmpty()) {
                int totalMedal = 0, totalBoss = 0;
                for (Long value : bonus.aMedal) {
                    totalMedal += value;
                }
                for (Long value : bonus.aBoss) {
                    totalBoss += value;
                }
                UserMoneys moneys = user.getUData().getUserMoneys();
                List<Long> aFake = new ArrayList<>(moneys.aLong);
                aFake.set(UserMoneys.MEDAL_BOSS, aFake.get(UserMoneys.MEDAL_BOSS) + totalBoss);
                aFake.set(UserMoneys.MEDAL, aFake.get(UserMoneys.MEDAL) + totalMedal);
                SQLQuery query = session.createSQLQuery("update user_data set moneys=:money where user_id=" + user.getId());
                query.setString("money", new Gson().toJson(aFake));
                query.executeUpdate();
            }
            if (bonus.updateShopSpecial) {
                CfgShop.MyShop myShop = CfgShop.getMyShop(user);
                SQLQuery query = session.createSQLQuery("update user_data set shop_special=:shopSpecial where user_id=" + user.getId());
                query.setString("shopSpecial", myShop.toString());
                query.executeUpdate();
            }

            long trophy = bonus.getTotalTrophy();
            if (trophy != 0) {
                System.out.println("update tropy != 0");
                TopTrophy event = (TopTrophy) user.getUEvent().getEvent(Constans.EVENT_TOP_TROPHY);
                int[] eventIndex = event.getEventIndex();
                if (eventIndex[0] != event.getEvent().getEventIndex()) {
                    if (trophy > 0) {
                        UserEventEntity uEvent = new UserEventEntity(user.getId(), Constans.EVENT_TOP_TROPHY, eventIndex[0]);
                        uEvent.setValue(trophy);
                        uEvent.setServerId(user.getDbUser().getServer());
                        session.save(uEvent);
                    }
                } else {
                    System.out.println("update troy phy---------------------->" + trophy);
                    if (trophy + event.getEvent().getValue() < 0) trophy = -user.getDbUser().getTrophy();
                    session.createSQLQuery("update user_event set value=value+(" + trophy + ") where user_id=" + user.getId()
                            + " and event_id=" + Constans.EVENT_TOP_TROPHY + " and event_index=" + eventIndex[0]).executeUpdate();
                }
                if (trophy > 0 && CfgAchievement.inEvent(CfgAchievement.TOP_TROPHY, user.getDbUser())) {
                    session.createSQLQuery("INSERT INTO user_event (user_id, event_id, value, server_id) VALUES(" + user.getId() + ", "
                            + Constans.EVENT_TOP_TROPHY_TIME + ", " + trophy + "," + user.getDbUser().getServer() + ") ON DUPLICATE KEY UPDATE value=value+" + trophy).executeUpdate();
                }
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
            if (bonus.userEnergy > 0) {
                CfgEnergy.resetMyEnergy(user);
            }
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
        return false;
    }

    static void parseBonusObject(UserInfo user, BonusInfo bonus, JSONArray aBonus) {
//        {
//            JSONArray test = new JSONArray();
//            int index = 0;
//            debug("=============");
//            while (index < aBonus.size()) {
//                JSONArray child = new JSONArray();
//                int type = aBonus.getInt(index++);
//                child.add(type);
//                int size = mParamSize.get(type);
//                for (int i = 0; i < size; i++) {
//                    child.add(aBonus.getInt(index++));
//                }
//                test.add(child);
//                debug(child.toString());
//            }
//            debug("=============");
//        }
        int index = 0;
        while (index < aBonus.size()) {
            int type = aBonus.getInt(index++);
            switch (type) {
                case GAME_NUMBER_ATK:
                    int gameId = aBonus.getInt(index++);
                    int numberInfo = aBonus.getInt(index++);
                    for (int i = 0; i < numberInfo; i++) {
                        index++;
                    }
                    break;
                case HERO: {
                    int heroId = aBonus.getInt(index++);
                    if (!bonus.aHero.contains(heroId)) {
                        bonus.aHero.add(heroId);
                    }
                    break;
                }
                case UFO: {
                    int ufoId = aBonus.getInt(index++);
                    if (!bonus.aUfo.contains(ufoId)) {
                        bonus.aUfo.add(ufoId);
                    }
                    break;
                }
                case UFO_STONE: {
                    int stoneId = aBonus.getInt(index++);
                    if (!bonus.mStoneUfo.containsKey(stoneId)) {
                        bonus.mStoneUfo.put(stoneId, aBonus.getInt(index++));
                    } else {
                        bonus.mStoneUfo.put(stoneId, bonus.mStoneUfo.get(stoneId) + aBonus.getInt(index++));
                    }
                    break;
                }
                case GOLD: {
                    bonus.aGold.add(aBonus.getLong(index++));
                    break;
                }
                case MEDAL_BOSS: {
                    bonus.aBoss.add(aBonus.getLong(index++));
                    break;
                }
                case MEDAL: {
                    bonus.aMedal.add(aBonus.getLong(index++));
                    break;
                }
                case ATOMIC: {
                    bonus.aAtomic.add(aBonus.getLong(index++));
                    break;
                }
                case EXP: {
                    bonus.aExp.add(aBonus.getLong(index++));
                    break;
                }
                case TROPHY: {
                    bonus.aTrophy.add(aBonus.getLong(index++));
                    break;
                }
                case HERO_EXP: {
                    bonus.aHeroExp.add(aBonus.getLong(index++));
                    bonus.aHeroExp.add(aBonus.getLong(index++));
                    break;
                }
                case PET_EXP: {
                    bonus.aPetExp.add(aBonus.getLong(index++));
                    bonus.aPetExp.add(aBonus.getLong(index++));
                    break;
                }
                case GEM: {
                    bonus.aGem.add(aBonus.getLong(index++));
                    break;
                }
                case ITEM: {
                    int id = aBonus.getInt(index++);
                    int number = aBonus.getInt(index++);
                    if (id == ResShopItem.PHAO_HOA || id == ResShopItem.KEO || id == ResShopItem.HOA_HONG || id == 100) {
                        bonus.aUserEvent.add(Constans.EVENT_SAOVANG);
                        bonus.aUserEvent.add(number);
                    } else {
                        if (!bonus.mItem.containsKey(id)) {
                            bonus.mItem.put(id, number);
                        } else {
                            bonus.mItem.put(id, bonus.mItem.get(id) + number);
                        }
                    }
                    break;
                }
                case AVATAR: {
                    bonus.aAvatar.add(aBonus.getInt(index++));
                    bonus.aAvatar.add(aBonus.getInt(index++));
                    break;
                }
                case AVATAR_FRAGMENT: {
                    bonus.aAvatarFragment.add(aBonus.getInt(index++));
                    bonus.aAvatarFragment.add(aBonus.getInt(index++));
                    break;
                }
                case BOMB: {
                    bonus.aBomb.add(aBonus.getInt(index++));
                    bonus.aBomb.add(aBonus.getInt(index++));
                    break;
                }
                case ACCESSORIES: {
                    bonus.aAccessories.add(aBonus.getInt(index++));
                    bonus.aAccessories.add(aBonus.getInt(index++));
                    break;
                }
                case PET_FOOD: {
                    int id = aBonus.getInt(index++);
                    if (!bonus.mPetFood.containsKey(id)) {
                        bonus.mPetFood.put(id, aBonus.getInt(index++));
                    } else {
                        bonus.mPetFood.put(id, bonus.mPetFood.get(id) + aBonus.getInt(index++));
                    }
                    break;
                }
                case MATERIAL: {
                    int id = aBonus.getInt(index++);
                    if (!bonus.mMaterial.containsKey(id)) {
                        bonus.mMaterial.put(id, aBonus.getInt(index++));
                    } else {
                        bonus.mMaterial.put(id, bonus.mMaterial.get(id) + aBonus.getInt(index++));
                    }
                    break;
                }
                case WIN_RATE: {
                    bonus.winLoose[0] += aBonus.getInt(index++);
                    bonus.winLoose[1] += aBonus.getInt(index++);
                    if (bonus.winLoose[0] > 0) { // win
                        bonus.aUserInt.add(UserInt.NUMBER_WIN);
                        bonus.aUserInt.add(UserInt.TYPE_ADD);
                        bonus.aUserInt.add(1);
                    } else { // loose
                        bonus.aUserInt.add(UserInt.NUMBER_WIN);
                        bonus.aUserInt.add(UserInt.TYPE_SET);
                        bonus.aUserInt.add(0);
                    }
                    break;
                }
                case USER_EVENT: {
                    bonus.aUserEvent.add(aBonus.getInt(index++));
                    bonus.aUserEvent.add(aBonus.getInt(index++));
                    break;
                }
                case PET: {
                    bonus.aPet.add(aBonus.getInt(index++));
                    bonus.aPet.add(aBonus.getInt(index++));
                    bonus.aPet.add(aBonus.getInt(index++));
                    break;
                }
                case PET_FRAGMENT: {
                    bonus.aPetFragment.add(aBonus.getInt(index++));
                    bonus.aPetFragment.add(aBonus.getInt(index++));
                    break;
                }
                case USER_ENERGY: {
                    bonus.userEnergy += aBonus.getInt(index++);
                    break;
                }
                case UPDATE_DATA_INT: {
                    bonus.aUserInt.add(aBonus.getInt(index++));
                    bonus.aUserInt.add(aBonus.getInt(index++));
                    bonus.aUserInt.add(aBonus.getInt(index++));
                    break;
                }
                case UPDATE_MAP_LEVEL: {
                    bonus.aMapLevel.add(aBonus.getInt(index++));
                    bonus.aMapLevel.add(aBonus.getInt(index++));
                    bonus.aMapLevel.add(aBonus.getInt(index++));
                    break;
                }
                case UPDATE_SHOP_SPECIAL: {
                    bonus.updateShopSpecial = true;
                }
            }
        }
    }

    public static List<Long> defaultLongBonusView(int type, int info) {
        return defaultLongBonusView(type, info, 0);
    }

    public static List<Long> defaultLongBonusView(int type, int info, int value) {
        JSONArray arr = defaultBonusView(type, info, value);
        List<Long> aLong = new ArrayList<>();
        for (int i = 0; i < arr.size(); i++) {
            aLong.add(arr.getLong(i));
        }
        return aLong;
    }

    public static JSONArray defaultBonusView(int type, long info) {
        return defaultBonusView(type, info, 0);
    }

    public static JSONArray defaultBonusView(int type, long info, int value) {
        switch (type) {
            case GAME_NUMBER_ATK:
                return JSONArray.fromObject(String.format("[%s,%s,%s,%s]", type, info, 1, value));
            case TROPHY:
            case GOLD:
            case GEM:
            case EXP:
            case USER_ENERGY:
            case ATOMIC:
            case MEDAL:
            case MEDAL_BOSS:
                return JSONArray.fromObject(String.format("[%s,%s]", type, info));
            case ITEM:
            case AVATAR_FRAGMENT:
            case MATERIAL:
            case BOMB:
            case AVATAR:
            case PET_FRAGMENT:
            case PET_FOOD:
            case UFO_STONE:
            case ACCESSORIES:
                return JSONArray.fromObject(String.format("[%s,%s,%s]", type, info, value));
            case PET:
                return JSONArray.fromObject(String.format("[%s,%s,%s,%s]", type, info, 1, 1));
            case HERO_EXP:
            case PET_EXP:
            case PET_ENERGY:
            case HERO:
            case UFO:
                break;
        }
        return new JSONArray();
    }

    public static Logger getLogger() {
        return slib_Logger.root();
    }

    class BonusInfo {
        List<Long> aGold = new ArrayList<Long>();
        List<Long> aBoss = new ArrayList<Long>();
        List<Long> aMedal = new ArrayList<Long>();
        List<Long> aAtomic = new ArrayList<Long>();
        List<Long> aExp = new ArrayList<Long>();
        List<Long> aGem = new ArrayList<Long>();
        List<Long> aHeroExp = new ArrayList<Long>();
        List<Long> aTrophy = new ArrayList<Long>();
        List<Long> aPetExp = new ArrayList<Long>();
        List<Integer> aAvatar = new ArrayList<Integer>();
        List<Integer> aAvatarFragment = new ArrayList<Integer>();
        List<Integer> aBomb = new ArrayList<Integer>();
        List<Integer> aAccessories = new ArrayList<Integer>();
        List<Integer> aPet = new ArrayList<Integer>();
        List<Integer> aPetFragment = new ArrayList<Integer>();
        int userEnergy = 0;
        List<Integer> aPetEnergy = new ArrayList<Integer>();
        List<Integer> aUserInt = new ArrayList<Integer>();
        List<Integer> aUserEvent = new ArrayList<Integer>();
        List<Integer> aMapLevel = new ArrayList<Integer>();
        List<Integer> aHero = new ArrayList<Integer>();
        List<Integer> aUfo = new ArrayList<Integer>();
        List<Integer> aStoneUfo = new ArrayList<Integer>();
        Map<Integer, Integer> mStoneUfo = new HashMap<Integer, Integer>();
        Map<Integer, Integer> mPetFood = new HashMap<Integer, Integer>();
        Map<Integer, Integer> mMaterial = new HashMap<Integer, Integer>();
        Map<Integer, Integer> mItem = new HashMap<Integer, Integer>();
        int[] winLoose = {0, 0};
        boolean updateShopSpecial = false;

        List<UserBombEntity> retBomb = new ArrayList<UserBombEntity>();
        List<UserAccessoriesEntity> retAccess = new ArrayList<UserAccessoriesEntity>();

        List<UserAvatarEntity> retAvatarEntity = new ArrayList<UserAvatarEntity>();
        List<UserPetEntity> retPetEntity = new ArrayList<UserPetEntity>();

        UserAvatarEntity getAvatarEntity() {
            UserAvatarEntity uAvatar = retAvatarEntity.get(0);
            retAvatarEntity.remove(0);
            return uAvatar;
        }

        UserBombEntity getBombEntity() {
            UserBombEntity uBomb = retBomb.get(0);
            retBomb.remove(0);
            return uBomb;
        }

        UserAccessoriesEntity getAccessoriesEntity() {
            UserAccessoriesEntity uAccess = retAccess.get(0);
            retAccess.remove(0);
            return uAccess;
        }

        UserAvatarEntity getAvatarEntity(int avatarId) {
            for (UserAvatarEntity avatar : retAvatarEntity) {
                if (avatar.getAvatarId() == avatarId) {
                    return avatar;
                }
            }
            return null;
        }

        UserAvatarEntity getAvatarEntity(List<UserAvatarEntity> aAvatar, int avatarId) {
            for (UserAvatarEntity avatar : aAvatar) {
                if (avatar.getAvatarId() == avatarId) {
                    return avatar;
                }
            }
            return null;
        }

        UserPetEntity getPetEntity(List<UserPetEntity> aPet, int petId) {
            for (UserPetEntity pet : aPet) {
                if (pet.getPetId() == petId) {
                    return pet;
                }
            }
            return null;
        }

        long getTotalGold() {
            long value = 0;
            for (Long tmp : aGold) {
                value += tmp;
            }
            return value;
        }

        long getTotalAtomic() {
            long value = 0;
            for (Long tmp : aAtomic) {
                value += tmp;
            }
            return value;
        }

        long getTotalTrophy() {
            long value = 0;
            for (Long tmp : aTrophy) {
                value += tmp;
            }
            return value;
        }

        long getTotalExp() {
            long value = 0;
            for (Long tmp : aExp) {
                value += tmp;
            }
            return value;
        }

        long getTotalGem() {
            long value = 0;
            for (Long tmp : aGem) {
                value += tmp;
            }
            return value;
        }
    }

    BonusInfo getBonusInfo() {
        return new BonusInfo();
    }

    static void debug(String msg) {
        if (CfgServer.debug)
            slib_Logger.root().debug(msg);
    }
}
