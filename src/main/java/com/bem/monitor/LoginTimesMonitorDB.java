/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.monitor;

import com.bem.object.TaskSchedule;
import com.bem.util.Util;
import grep.database.HibernateUtil;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 */
public class LoginTimesMonitorDB extends TaskSchedule {

	public LoginTimesMonitorDB(long jobInterval) {
		super(jobInterval);
		// TODO Auto-generated constructor stub
	}

	private List<Integer> userIds = new ArrayList<Integer>();
	private boolean isRun = true, forceCreateSession = false;
	private Session session;

	@Override
	protected void doJob() {
		// while (isRun) {
		try {
			while (userIds.size() > 0) {
				Integer userId = userIds.get(0);
				updateUserLoginTime(userId);
				userIds.remove(0);

				Thread.yield();
				sleepTime(100);
			}
		} catch (Exception ex) {
			if (userIds == null) {
				userIds = new ArrayList<Integer>();
			} else if (!userIds.isEmpty()) {
				userIds.remove(0);
			}
		}
		// else {
		// getLogger().info("LoginTimesMonitorDB empty sleep " + SLEEP_TIME);
		// sleepTime(SLEEP_TIME);
		// }
		// Thread.yield();
		// }
	}

	public int updateUserLoginTime(int userId) {
		Session session = null;
		try {
			// session = HibernateUtil.getSessionFactory().openSession();
			session = getSession();
			session.beginTransaction();
			SQLQuery query = session.createSQLQuery("update user set last_login=now(), login_times=login_times+1 where id=" + userId);
			int rowCount = query.executeUpdate();
			if (rowCount > 0) {
				return 1;
			} else {
				return 0;
			}
		} catch (HibernateException he) {
			forceCreateSession = true;
			getLogger().error(Util.exToString(he));
		} finally {
			try {
				session.getTransaction().commit();
			} catch (Exception ex) {
				forceCreateSession = true;
				getLogger().error(Util.exToString(ex));
			}
		}
		return -1;
	}

	private Session getSession() {
		if (forceCreateSession || session == null || !session.isConnected() || !session.isOpen()) {
			try {
				forceCreateSession = false;
				session = HibernateUtil.getSessionFactory().openSession();
			} catch (Exception ex) {
				getLogger().error(Util.exToString(ex));
			}
		}
		return session;
	}

	private void sleepTime(long time) {
		try {
			Thread.sleep(time);
		} catch (Exception ex) {
		}
	}

	public void addUserLogin(int userId) {
		userIds.add(new Integer(userId));
	}

}
