package com.bem.monitor;

import com.bem.AbstractTable;
import com.bem.matcher.TeamMatcher;
import com.bem.matcher.TeamObject;
import com.bem.object.UserInfo;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.cache.JService;
import com.google.protobuf.AbstractMessage;
import com.handler.game.Arena;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.slib_Logger;
import grep.database.Database2;
import com.bem.util.ChUtil;
import io.netty.channel.Channel;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.util.concurrent.GlobalEventExecutor;

import java.util.*;

public class Online {

    public static ChannelGroup channels = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);
    //
    public static ChannelGroup authChannels = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);
    public static Map<Long, Channel> mChannel = new Hashtable<Long, Channel>();
    //
    public static List<String> aSession = new ArrayList<String>();
    public static Map<String, String> mSession = new Hashtable<String, String>();
    //
    private static Object lockOnline = new Object();

    public static void addChannel(long userId, Channel channel) {
        synchronized (lockOnline) {
            mChannel.put(userId, channel);
            authChannels.add(channel);
        }
    }

    public static void removeChannel(Long userId) {
        synchronized (lockOnline) {
            authChannels.remove(mChannel.get(userId));
            mChannel.remove(userId);
        }
    }

    public static boolean isSpamLogin(String username) {
        long loginTime = JCache.getInstance().getLongValue(JService.KEY_LOGIN_TIME + username);
        if (loginTime != -1) {
            long difTime = System.currentTimeMillis() - loginTime;
            if (difTime < 1000 * 10) {
                return true;
            }
        }
        JCache.getInstance().setValue(JService.KEY_LOGIN_TIME + username, String.valueOf(System.currentTimeMillis()));
        return false;
    }

    private static Object lockSession = new Object();

    public static String generateSession(String username) {
        String sessionId = UUID.randomUUID().toString().replaceAll("-", "") + String.valueOf(System.currentTimeMillis());
        Online.addSession(username, sessionId);
        JCache.getInstance().setValue(JService.KEY_SESSION + username, sessionId);
        // JCache.getInstance().setValue(JedisService.KEY_LOGIN_TIME + username, String.valueOf(System.currentTimeMillis()));
        return sessionId;
    }

    public static boolean isValidRedisSession(String username, String session) {
        String cacheSession = mSession.get(username);
        if (cacheSession == null || !cacheSession.equals(session)) {
            return false;
        }
        String jedisSession = JCache.getInstance().getValue(JService.KEY_SESSION + username);
        if (jedisSession == null || !jedisSession.equals(session)) {
            return false;
        }
        return true;
    }

    public static boolean isValidSession(String username, String session) {
        String cacheSession = mSession.get(username);
        if (cacheSession == null || !cacheSession.equals(session)) {
            return false;
        }
        return true;
    }

    public static void addSession(String username, String session) {
        synchronized (lockSession) {
            aSession.remove(username);
            aSession.add(username);
            mSession.put(username, session);
            if (aSession.size() > 4000) {
                String key = aSession.get(0);
                aSession.remove(0);
                mSession.remove(key);
            }
        }
    }

    public static Channel getChannel(long userId) {
        return mChannel.get(userId);
    }

    public static void closeAllChannel() {
        Object[] obj = mChannel.values().toArray();
        for (Object object : obj) {
            try {
                if (object != null) {
                    Channel ch = (Channel) object;
                    ch.close();
                }
            } catch (Exception ex) {
            }
        }
    }

    public static boolean isOnline(Long userId) {
        return mChannel.containsKey(userId);
    }

    public static void sendMessage(AbstractMessage output, int service) {
        long time = System.currentTimeMillis();
        Object[] obj = mChannel.values().toArray();
        for (Object object : obj) {
            Channel ch = (Channel) object;
            Util.sendProtoData(ch, output, service, time);
        }
    }

    public static void logoutChannel(Channel channel) {
        try {
            UserInfo user = (UserInfo) ChUtil.get(channel, Constans.KEY_USER);
            if (user != null) {
                user.getUData().myAttendance.checkStatusOnline(user, true);
                user.getUData().myAttendance.update(user.getId());
//                Actions.saveUserAsset("logout", user);

                TeamObject team = user.getTeam();
                if (team != null) {
                    if (team.status == TeamObject.STATUS_NONE) {
                        team.removeUser(user.getId());
                        team.sendMessage(IAction.LEAVE_TEAM, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getId(), (long) team.hostId), null));
                    } else if (team.status == TeamObject.STATUS_SEARCH) {
                        int rs = TeamMatcher.removeQueue(team);
                        if (rs == 0) {
                            team.sendMessage(IAction.CANCEL_GAME, null);
                        }
                        if (team.type == TeamObject.SOLO) {

                        } else {
                            team.status = TeamObject.STATUS_NONE;
                            team.removeUser(user.getId());
                            team.sendMessage(IAction.LEAVE_TEAM, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getId(), (long) team.hostId), null));
                        }
                    } else if (team.status == TeamObject.STATUS_PLAY) {
                    }
                }
                Arena.getInstance(user.getDbUser().getServer()).outArena(user);
            }

            AbstractTable tableCache = (AbstractTable) ChUtil.get(channel, Constans.KEY_TABLE);
            if (tableCache != null) {
                //tableCache.doAction(channel, IAction.CLIENT_DISCONNECTED, null, System.currentTimeMillis());
                tableCache.doAction(channel, IAction.LEAVE_TABLE, null, System.currentTimeMillis());
            }

            // Trước khi rời bàn thì ko được xóa User
            ChUtil.remove(channel, Constans.KEY_USER);
            ChUtil.remove(channel, Constans.KEY_TABLE);

            if (user != null) {
                Online.removeChannel(user.getId());
                Database2.update("user", Arrays.asList("logout", String.valueOf(System.currentTimeMillis())), Arrays.asList("id", String.valueOf(user.getId())));
                BotMonitor.removeBot(user.getAuthUser().getUdid());
            }
        } catch (Exception ex) {
            slib_Logger.root().error(Util.exToString(ex));
        } finally {
        }
    }

    public static void clearChannel(Channel channel) {
        System.out.println("aaaaaaaaaaaa bbbbbbbbbbb");
        ChUtil.remove(channel, Constans.KEY_USER);
        ChUtil.remove(channel, Constans.KEY_TABLE);
        channel.close();
    }
}
