package com.bem.monitor;

import grep.helper.StringHelper;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BonusBuilder {

    List<Long> bonus = new ArrayList<>();

    public String toString() {
        return StringHelper.toDBString(bonus);
    }

    public BonusBuilder add(List<Long> values) {
        bonus.addAll(values);
        return this;
    }

    public BonusBuilder addGem(int value) {
        bonus.addAll(Bonus.defaultLongBonusView(Bonus.GEM, value));
        return this;
    }

    public BonusBuilder addGold(int value) {
        bonus.addAll(Bonus.defaultLongBonusView(Bonus.GOLD, value));
        return this;
    }

    public BonusBuilder addPetFragment(int petId, int number) {
        bonus.addAll(Bonus.defaultLongBonusView(Bonus.PET_FRAGMENT, petId, number));
        return this;
    }

    public static BonusBuilder newInstance() {
        return new BonusBuilder();
    }

}
