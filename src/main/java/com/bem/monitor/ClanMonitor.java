package com.bem.monitor;

import com.bem.dao.ClanDAO;
import com.bem.dao.mapping.ClanEntity;
import com.k2tek.Constans;

import java.util.*;

/**
 * Created by vieth_000 on 3/24/2017.
 */
public class ClanMonitor {
    public static Map<Integer, ClanEntity> mClan = new HashMap<Integer, ClanEntity>();
    public static List<ClanEntity> aClan = new ArrayList<ClanEntity>();

    public static synchronized ClanEntity getClan(int clanId) {
        ClanEntity clan = mClan.get(clanId);
        if (clan == null) {
            clan = new ClanDAO().getClan(clanId);
            if (clan != null) {
                mClan.put(clanId, clan);
                aClan.add(clan);
                clan.getAUser();
            }
        } else {
            aClan.remove(clan);
            aClan.add(clan);
        }
        return clan;
    }

    public static synchronized void removeClan(int clanId) {
        if (mClan.containsKey(clanId)) {
            aClan.remove(mClan.get(clanId));
            mClan.remove(clanId);
        }
    }

    public static synchronized void addClan(ClanEntity clan) {
        aClan.add(clan);
        mClan.put(clan.getId(), clan);
    }

    public static List<ClanEntity> suggestClan(int trophy) {
        Random ran = new Random();
        List<ClanEntity> aClan = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            int startIndex = ran.nextInt(aClan.size());
            for (int j = 0; j < aClan.size(); j++) {
                ClanEntity clan = aClan.get(startIndex);
                if (clan.getJoinRule() == Constans.CLAN_JOIN_OPEN && clan.getJoinTrophy() <= trophy) {
                    if (!aClan.contains(clan)) {
                        aClan.add(clan);
                        break;
                    }
                }
                if (++startIndex >= aClan.size()) startIndex = 0;
            }
        }
        return aClan;
    }

}
