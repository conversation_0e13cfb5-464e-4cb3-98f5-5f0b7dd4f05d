package com.bem.monitor;

import com.bem.object.UserInfo;
import com.bem.util.Util;
import com.cache.MCache;
import com.k2tek.common.slib_Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by vieth_000 on 3/24/2017.
 */
public class UserMonitor {
    public static Map<String, UserInfo> mUser = new HashMap<String, UserInfo>(); // session -> UserInfo
    public static Map<Long, String> mUserId = new HashMap<Long, String>(); // username -> Session

    public static void clearCache() {
        try {
//            Object[] obj = aSession.keySet().toArray();
//            for (int i = 0; i < obj.length; i++) {
//                String username = (String) obj[i];
//                String session = aSession.get(username);
//                if (session != null) {
//                    if (getSession(username) == null) {
//                        UserInfo user = mUser.get(session);
//                        if (user != null) {
//                            NotifyMonitor.clearNotify(user.getId());
//                        }
//                        aSession.remove(username);
//                        mUser.remove(session);
//                    }
//                }
//                Thread.sleep(10);
//            }
        } catch (Exception ex) {
            slib_Logger.root().warn("clear session ->" + Util.exToString(ex));
        }
    }

    //    public static void saveSession(String username, String session) {
//        MyCache.getInstance().set(session, username);
//    }
//
//    public static String getUsernameFromSession(String session) {
//        return (String) MyCache.getInstance().getNormal(session);
//    }
//
    public static String getSession(String username) {
        return (String) MCache.getInstance().getNormal("s:" + username);
    }

    //
    public static void saveUser(UserInfo user) {
        if (mUserId.containsKey(user.getId()) && user.getId() != 8) {
            String oldSession = mUserId.get(user.getId());
            mUser.remove(oldSession);
        }
        mUser.put(user.getSession(), user);
        mUserId.put(user.getId(), user.getSession());
    }

    public static UserInfo getUser(long userId) {
        if (mUserId.containsKey(userId)) {
            return mUser.get(mUserId.get(userId));
        }
        return null;
    }

    //
//    public static User getDbUser(String username) {
//        String session = aSession.get(username);
//        if (session != null) {
//            MyUser mUser = aMyUser.get(session);
//            if (mUser != null) {
//                return mUser.getUser();
//            }
//        }
//        return new UserDAO().getUser(username);
//    }
//
//    public static MyUser getUserFromUsername(String username) {
//        String session = aSession.get(username);
//        if (session != null) {
//            return aMyUser.get(session);
//        }
//        return null;
//    }
//
    public static UserInfo getUser(String session) {
        return mUser.get(session);
    }
//
//    /**
//     * Shield
//     */
//    public static void setShield(String username, Long time) {
//        MyCache.getInstance().set(MyCache.KEY_SHIELD + username, time);
//    }
//
//    public static Long getShield(String username) {
//        return (Long) MyCache.getInstance().get(MyCache.KEY_SHIELD + username);
//    }
//
//    /**
//     * Cache trophy
//     */
//    public static Integer getTrophyByRank(int rankTrophy) {
//        return (Integer) MyCache.getInstance().get(MyCache.KEY_RANK_TROPHY + rankTrophy);
//    }
}
