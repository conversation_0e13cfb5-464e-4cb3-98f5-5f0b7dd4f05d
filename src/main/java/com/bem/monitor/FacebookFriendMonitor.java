package com.bem.monitor;

import com.bem.object.TaskSchedule;
import com.bem.ovi.OviStore;
import com.bem.util.Util;
import com.k2tek.common.slib_Logger;
import grep.database.HibernateUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.slf4j.Logger;

import java.net.URLEncoder;
import java.util.*;

public class FacebookFriendMonitor extends TaskSchedule {

	Map<String, String> mUsername = new HashMap<String, String>();
	Calendar caProcess = Calendar.getInstance();

	public FacebookFriendMonitor(long jobInterval) {
		super(jobInterval);
	}

	List<JobInfor> jobs = new ArrayList<JobInfor>();

	@Override
	protected void doJob() {
		// while (true) {
		try {
			while (!jobs.isEmpty()) {
				JobInfor job = jobs.get(0);
				JSONArray fb = getFBFriend(job.token);
				if (fb != null) {
					saveFacebookFriend(job.userId, fb.toString());
				}
				doAction(job, 2);
				Thread.yield();
				sleepTime(100);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			getLogger().info(Util.exToString(ex));
			if (jobs == null) {
				jobs = new ArrayList<JobInfor>();
			}
			if (jobs.size() > 0) {
				jobs.remove(0);
			}
		}
		// }
	}

	private void sleepTime(long time) {
		try {
			Thread.sleep(time);
		} catch (Exception ex) {
		}
	}

	public static void main(String[] args) {
		FacebookFriendMonitor m = new FacebookFriendMonitor(1);
		m.dodo();
	}
	
	public void dodo() {
		String sql = "SELECT page_id FROM page_fan WHERE page_id=603965309648772 and uid=me()";
		JSONObject obj = new JSONObject();
		obj.put("data", sql);
		String sqlString = obj.toString();
		String url = "fql?q=" + URLEncoder.encode(sqlString);
		JSONObject data = graph(url, "CAAChEXUuB3wBAODgoV5vJKvAZAqZBq3NuuSQQleMhuPMigwpm0dRdcATTqHBMjIWA7aeK5rmMqK1CCMLOZBUcL5dEi3fWYtLRK0Ibf2budqKri7kGkHnD5Hz5Vn2viso66742P1PxCJg83RxI2RxpzBX44WGGAMWTo0sDaiMZA2NLkOVasc5RiBagTuqTST3hnRFT32ljxQxO8ZCBuUjswS8JwCZC6nRpzI26PhYGkFQZDZD");
		getLogger().info(data.toString());
	}
	
	// {"data":[{"name":"data","fql_result_set":[{"uid":100000234548763,"name":"Chinh Hoang Dinh"},{"uid":100001271065894,"name":"Mạnh Lương"}]}]}
	private JSONArray getFBFriend(String token) {
		try {
			String sql1 = "SELECT uid,name FROM user WHERE has_added_app=1 AND uid IN (SELECT uid2 FROM friend WHERE uid1 = me())";
			// String sql2 = "SELECT uid FROM newUser WHERE uid=me()";

			JSONObject obj = new JSONObject();
			// mapObject.put("me", sql2);
			obj.put("data", sql1);

			String sqlString = obj.toString();
			String url = "fql?q=" + URLEncoder.encode(sqlString);
			JSONObject data = graph(url, token);
			return data.getJSONArray("data").getJSONObject(0).getJSONArray("fql_result_set");
		} catch (Exception ex) {
		}
		return null;
	}

	private JSONObject graph(String params, String token) {
		String url = "https://graph.facebook.com/" + params + "&format=json-strings&access_token=" + token;
		String data = OviStore.getContentFromUrl(url);
		return JSONObject.fromObject(data);
	}

	private int saveFacebookFriend(int userId, String data) {
		Session session = null;
		try {
			session = HibernateUtil.getSessionFactory().openSession();
			int count = Integer.parseInt(session.createSQLQuery("select count(*) from facebook_friend where user_id=" + userId).uniqueResult().toString());
			session.beginTransaction();
			if (count == 0) {
				SQLQuery query = session.createSQLQuery("insert facebook_friend(user_id, friend) values(?,?)");
				query.setInteger(0, new Integer(userId));
				query.setString(1, data);
				query.executeUpdate();
			} else {
				SQLQuery query = session.createSQLQuery("update facebook_friend set friend=? where user_id=?");
				query.setString(0, data);
				query.setInteger(1, new Integer(userId));
				query.executeUpdate();
			}
			session.getTransaction().commit();
			return 0;
		} catch (Exception ex) {
			ex.printStackTrace();
			getLogger().info(Util.exToString(ex));
		} finally {
			closeSession(session);
		}
		return -1;
	}

	public synchronized void doAction(JobInfor job, int action) {
		switch (action) {
		case 1:
			jobs.add(job);
			break;
		case 2:
			jobs.remove(0);
			break;
		}
	}

	public void addJob(int userId, String username, String token) {
		Calendar ca = Calendar.getInstance();
		if (ca.get(Calendar.DATE) != caProcess.get(Calendar.DATE)) {
			mUsername.clear();
		}
		if (!mUsername.containsKey(username)) {
			mUsername.put(username, username);
			doAction(new JobInfor(userId, username, token), 1);
		}
	}

	private void closeSession(Session session) {
		if (session != null) {
			session.close();
		}
	}

	public Logger getLogger() {
		return slib_Logger.quay();
	}

	class JobInfor {
		int userId;
		String username, token;

		public JobInfor(int userId, String username, String token) {
			super();
			this.userId = userId;
			this.username = username;
			this.token = token;
		}

	}

}
