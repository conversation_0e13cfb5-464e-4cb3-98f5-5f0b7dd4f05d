package com.bem.ovi;

import com.bem.util.Util;
import com.k2tek.common.slib_Logger;
import org.slf4j.Logger;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;

public class OviStore {
    public static final String PARTNER_CODE = "K2TEK"; // partner code MVCorp cung cap
    public static final int IS_TEST = 0; // CHU Y: khi test ket noi thi IS_TEST = 1, khi chay that thi IS_TEST = 0
    public static final String URL_PRE = "http://local.api/bem/?data=";
    public static final String SERVICE_DK = "DK";
    public static final String SERVICE_NAP = "NAP";
    public static final String MAIN_CP = "CP1K";

    public static void main(String[] args) {
        // dangKy();
        // napKoin();
    }

    public static boolean isChargeRegister(String mobile) {
        if (mobile == null) {
            return false;
        }
        if (mobile.equals("000000000") || mobile.equals("123456788") || mobile.equals("123456787") || mobile.equals("123456786")) {
            return false;
        }
        if (mobile.length() == 10 || mobile.length() == 11) {
            return true;
        }
        return false;
    }

    // Nếu đăng ký facebook thì để sender = 123456788
    public static String dangKy(String user, String pass, String cp, boolean byFacebook, String mobile, String email, String registered) {
        // THAY N~ BIEN SAU = GIA TRI TUONG UNG
        String sender = mobile;// byFacebook ? "123456788" : "000000000";
        // if (mobile.length() == 10 || mobile.length() == 11) {
        // sender = mobile;
        // }
        String recipient = "0000"; // dau so
        String service = SERVICE_DK; // ten service
        String usergioithieu = "";
        long reqId = System.currentTimeMillis(); // id request cua partner, tu ta(ng
        cp = cp.toUpperCase();
        String parentCP = "";//CfgServer.getParentCP(cp);
        if (parentCP == null) {
            parentCP = "CP1K-" + cp;
            cp = "";
        } else {
            parentCP = "CP1K-" + parentCP.toUpperCase();
        }
        // String subCP = cp.toUpperCase();
        // if (cp.equalsIgnoreCase("K2TEK")) {
        // cp = "CP1K";
        // } else if (!cp.equalsIgnoreCase("OVISTORE")) {
        // cp = "CP1K-" + cp;
        // }

        // String subCP = "";
        // if (parentCP == null) {
        // parentCP = cp;
        // } else {
        // subCP = cp;
        // cp = parentCP;
        // }
        String message = user + " " + pass + " " + usergioithieu;
        String data = "email=" + email + "&sender=" + sender + "&recipient=" + recipient + "&service=" + service + "&message=" + message + "&reqid=" + reqId + "&partner=" + parentCP + "&test=" + IS_TEST;
        String dataB64 = Base64Coder.encodeString(data);
        String dataEn = URLEncoder.encode(dataB64);
        String url = URL_PRE + dataEn;
        url = url + "&subpartner=" + cp;
     //   url = url + "&registered=" + (CfgServer.androidFarmRegister ? "0" : registered);
        long l = System.currentTimeMillis();
        String ret = getContentFromUrl(url);
        getLogger().info(url + " -> " + ret + " -> " + (System.currentTimeMillis() - l));
        return ret;
    }

    public static void dangKy() {
        // THAY N~ BIEN SAU = GIA TRI TUONG UNG
        String sender = "84123456789"; // sdt cua ng dk
        String recipient = "6042"; // dau so
        String service = SERVICE_DK; // ten service
        String user = "u_zero";
        String pass = "pass0";
        String usergioithieu = "";
        int reqId = 10001; // id request cua partner, tu ta(ng
        // /

        String message = user + " " + pass + " " + usergioithieu;
        String data = "sender=" + sender + "&recipient=" + recipient + "&service=" + service + "&message=" + message + "&reqid=" + reqId + "&partner=" + PARTNER_CODE + "&test=" + IS_TEST;
        String dataB64 = Base64Coder.encodeString(data);
        String dataEn = URLEncoder.encode(dataB64);
        String url = URL_PRE + dataEn;
        String ret = getContentFromUrl(url);
        System.out.println(ret);
    }

    public static void napKoin() {
        // THAY N~ BIEN SAU = GIA TRI TUONG UNG
        String sender = "84123456789";
        String recipient = "6742";
        String service = SERVICE_NAP;
        String user = "u_zero";
        String message = user;
        int reqId = 10002;
        // /

        String data = "sender=" + sender + "&recipient=" + recipient + "&service=" + service + "&message=" + message + "&reqid=" + reqId + "&partner=" + PARTNER_CODE + "&test=" + IS_TEST;
        String dataB64 = Base64Coder.encodeString(data);
        String dataEn = URLEncoder.encode(dataB64);
        String url = URL_PRE + dataEn;
        String ret = getContentFromUrl(url);
        System.out.println(ret);
    }

    public static String getContentFromUrl(String url) {
        StringWriter sw = null;
        InputStream is = null;
        InputStreamReader reader = null;
        long curTime = System.currentTimeMillis();
        try {
            URL u = new URL(url);
            URLConnection uConn = u.openConnection();
            uConn.setConnectTimeout(20000);
            uConn.setReadTimeout(20000);
            uConn.connect();
            is = uConn.getInputStream();
            reader = new InputStreamReader(is, "utf-8");
            sw = new StringWriter();
            char[] buffer = new char[1024 * 8];
            int count;
            while ((count = reader.read(buffer)) != -1) {
                sw.write(buffer, 0, count);
            }
        } catch (Exception ex) {
            getLogger().error(url + "->" + Util.exToString(ex) + "->" + (System.currentTimeMillis() - curTime));
        } finally {
            try {
                if (is != null)
                    is.close();
                if (reader != null)
                    reader.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return sw == null ? null : sw.toString();
    }

    public static Logger getLogger() {
        return slib_Logger.userAPI();
    }
}
