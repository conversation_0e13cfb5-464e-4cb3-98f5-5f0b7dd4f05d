/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem;

import com.bem.boom.*;
import com.bem.boom.effect.EffectPits;
import com.bem.boom.effect.IEffect;
import com.bem.boom.map.MapObject;
import com.bem.boom.map.MapResource;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.CachePlayer;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.IMove;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.config.CfgCommon;
import com.bem.config.CfgServer;
import com.bem.matcher.TableMonitor;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.ITimer;
import com.bem.object.IUser;
import com.bem.object.ResShopItem;
import com.bem.object.UserInfo;
import com.bem.util.ChUtil;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.cache.MCache;
import com.google.gson.Gson;
import com.google.protobuf.AbstractMessage;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.Xerver;
import com.k2tek.common.Logs;
import com.k2tek.common.slib_Logger;
import com.proto.GGProto;
import com.proto.custom.XAbstract;
import com.proto.custom.XInput;
import grep.database.HibernateUtil;
import io.netty.channel.Channel;
import lombok.Data;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.quartz.JobKey;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 */
@Data
public abstract class AbstractTable implements ITimer {
    public static int counterId = 0;

    public static synchronized String getCounterId() {
        return String.valueOf(++counterId);
    }

    protected String id;
    protected int turnId;
    protected boolean isPlay = false;
    protected long curTime;

    long deltaTimer = 0;

    // table boom
    public static final int PROTO_UPDATE_BOOM = 1;
    public static final int PROTO_UPDATE_PLAYER = 2;
    public static final int PROTO_UPDATE_MONSTER = 3;
    public static final int PROTO_UPDATE_JUMP = 4;

    //
    public static final int SOLO = 0;
    public static final int TEAM = 1;
    public static final int BOSS = 2;
    public static final int TRAIN = 3;

    static final int ID_CREATE_MAP = -1;
    static final int ID_START_GAME = -2;
    static final int ID_CHECK_ALL_READY = -3;

    //
    public static final int STATE_JOIN_PLAYER = 0;
    public static final int STATE_START_GAME = 1;
    public static final int STATE_END_GAME = 2;
    public static final int STATE_WAIT_RESULT = 3;
    public static final int STATE_NONE = 4;
    //
    static final String KEY_TABLE = Constans.KEY_TABLE;
    static final String KEY_PLAYER = Constans.KEY_PLAYER;
    final int max_size = 4;
    //
    public int gameState, numberPlayerDie, numberMonsterDie, numberBossDie, numberBreakSquare, teamWin = -1, delayStartGame = 3;
    float timeEndgame = 0;
    Pos lastBossPos = null;
    MapData map;
    List<String> botUsername;
    List<TeamObject> aTeam;
    List<Player> aPlayer = new ArrayList<Player>();
    List<IMonster> aMonster = new ArrayList<IMonster>();
    List<IMove> aMoveUnit = new ArrayList<IMove>();
    List<Item> aItem = new ArrayList<Item>();
    List<IEffect> aEffect = new ArrayList<IEffect>();
    boolean isTableBoss = false;
    boolean showHiddenItem = true, showBombLength = true;

    // List return Proto
    List<GGProto.ProtoUnitAdd.Builder> aProtoAdd = new ArrayList<GGProto.ProtoUnitAdd.Builder>();
    List<GGProto.ProtoBoom.Builder> aProtoBoom = new ArrayList<GGProto.ProtoBoom.Builder>();
    List<GGProto.ProtoPlayerState.Builder> aProtoPlayerState = new ArrayList<GGProto.ProtoPlayerState.Builder>();
    List<GGProto.ProtoMonsterState.Builder> aProtoMonsterState = new ArrayList<GGProto.ProtoMonsterState.Builder>();
    List<GGProto.ProtoUnitJumpPos.Builder> aProtoJumpPos = new ArrayList<GGProto.ProtoUnitJumpPos.Builder>();
    List<GGProto.ProtoUnitPos.Builder> aProtoPos = new ArrayList<GGProto.ProtoUnitPos.Builder>();
    GGProto.ProtoEndgame.Builder protoEndgame;
    GGProto.ProtoInitTable.Builder protoInit;
    List<Integer> bossUnit = new ArrayList<Integer>();
    int mode, mapId, numberPlayer;
    int countTime = 0;
    CacheBattle cacheBattle;

    public AbstractTable() {

    }

    public AbstractTable(CacheBattle cacheBattle) {
        this.id = getCounterId();
        this.turnId = 0;
        this.server_time = 0f;
        this.gameState = STATE_JOIN_PLAYER;
        this.cacheBattle = cacheBattle;
        this.numberPlayer = cacheBattle.getAPlayer().size();
        TaskMonitor.getInstance().addTable(this);
    }

    public AbstractTable(List<TeamObject> aTeam, int mode, int mapId) {
        this.id = getCounterId();
        this.turnId = 0;
        this.mode = mode;
        this.server_time = 0f;
        this.gameState = STATE_JOIN_PLAYER;
        this.aTeam = aTeam;
        if (mode == TeamObject.RANK) {
            this.mapId = MapResource.getSoloMap();
        }
        if (mode == TeamObject.ZOMBIE) {
            this.mapId = MapResource.getZombieMap();
        } else {
            this.mapId = mapId > 0 ? mapId : 1;
        }
        TaskMonitor.getInstance().addTable(this);
        for (TeamObject team : aTeam) {
            numberPlayer += team.aUser.size();
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public long getCurTime() {
        return curTime;
    }

    public void setCurTime(long curTime) {
        this.curTime = curTime;
    }

    public synchronized boolean joinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return true;
    }

    public int doAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        if (Constans.UNSYNC_SERVICE.contains(action)) {
            switch (action) {
                default:
                    doSubUnSyncAction(channel, action, srcRequest, curTime);
                    break;
            }
        } else {
            return doSyncAction(channel, action, srcRequest, curTime);
        }
        return 0;
    }

    private synchronized int doSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        int ret = 0;
        try {
            switch (action) {
                case IAction.CLIENT_INPUT:
                    if (gameState == STATE_START_GAME) {
                        Player player = (Player) ChUtil.get(channel, "player");
                        handle_server_input(player, new Input(XInput.parse(srcRequest)));
                    }
                    break;
                case IAction.CLIENT_DISCONNECTED:
                    playerDisconnected(channel);
                    break;
                case IAction.CLIENT_RESUME:
                    if (isPlay) {
                        return playerResume(channel);
                    } else {
                        return 0;
                    }
                case IAction.LEAVE_TABLE:
                    leavePlayer(channel, srcRequest);
                    break;
                case IAction.MAP_DATA:
                    initMap();
                    break;
                case IAction.PLAYER_READY:
                    playerReady(channel);
                    break;
                case IAction.START_GAME:
                    startGame();
                    break;
                case IAction.START_PHYSIC_UPDATE:
                    startPhysic();
                    break;
                case IAction.CHAT_IN_GAME:
                    chat(channel, action, srcRequest);
                    break;
                case IAction.JOIN_BATTLE:
                    return joinBattle(channel, srcRequest);
                default:
                    ret = doSubSyncAction(channel, action, srcRequest, curTime);
                    break;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        }
        return ret;
    }

    int joinBattle(Channel channel, byte[] srcReq) {
        String username = CommonProto.parseCommonVector(srcReq).getAString(0);
        try {
            System.out.println("user name ------>" + username);
        } catch (Exception ex) {

        }
        for (Player player : aPlayer) {
            if (player.user.username.equals(username)) {
                player.channel = channel;
                ChUtil.set(channel, Constans.KEY_TABLE, this);
                ChUtil.set(channel, Constans.KEY_PLAYER, player);
                sendPlayer(channel, IAction.MAP_DATA, protoInit.build());
                return 1;
            }
        }
        return 0;
    }

    void chat(Channel channel, int action, byte[] srcReq) {
        Player player = getPlayer(channel);
        sendOtherPlayer(player, action, CommonProto.parseCommonVector(srcReq));
    }

    void playerReady(Channel channel) {
        Player player = getPlayer(channel);
        debug("PlayerReady = " + player.user.username);
        if (player != null) {
            player.ready = true;
            sendAllPlayer(IAction.PLAYER_READY, CommonProto.getCommonVectorProto(Arrays.asList(player.unitId), null));
            updateHasAction(player.user.id);
            if (mode == TeamObject.TUTORIAL || canStartGame()) {
                doAction(null, IAction.START_GAME, null, curTime);
            }
        }
    }

    boolean canStartGame() {
        for (Player player : aPlayer) {
            if (!player.ready)
                return false;
        }
        return true;
    }

    public void checkStartGame() {
        if (!isPlay && gameState == STATE_JOIN_PLAYER) {
            doAction(null, IAction.START_GAME, null, System.currentTimeMillis());
            for (int i = 0; i < aPlayer.size(); i++) {
                if (!aPlayer.get(i).ready) {
//                    if (CfgBot.isBot(aPlayer.get(i).userInfo.getAuthUser().getUdid())) {
//                        Logs.warn(aPlayer.get(i).dbUser.getName() + " " + aPlayer.get(i).userInfo.getAuthUser().getUdid() + " not ready");
//                    }
                    doAction(aPlayer.get(i).channel, IAction.LEAVE_TABLE, CommonProto.getCommonVectorProto(Arrays.asList(i), null).toByteArray(), System.currentTimeMillis());
                }
            }
        }
    }

    void startGame() {
        if (!isPlay) {
            isPlay = true;
            timer(ID_START_GAME, delayStartGame);
        }
    }

    void startPhysic() {
        for (Player player : aPlayer) {
            if (player.user.point.isZombie()) {
                sendAllPlayer(IAction.MSG_TOAST, CommonProto.getCommonLongVectorProto(null, Arrays.asList(String.format(CfgCommon.config.zombieMsg, player.user.name))));
                break;
            }
        }
        gameState = STATE_START_GAME;
        //
        local_time = 0.016f; // The local timer in seconds
        _dte = System.currentTimeMillis(); // The local timer last frame time
        server_time = 0;
        numberMonsterDie = 0;
        numberPlayerDie = 0;
        numberBossDie = 0;
        checkEndGame();
        schedule_server_update();
        create_physics_simulation();
    }

    protected abstract boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin);

    protected abstract void subLeavePlayer(Player player, Channel channel, byte[] srcRequest);

    public void leavePlayer(Channel channel, byte[] srcRequest) {
        Player player = null;
        if (channel == null) {
            int index = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
            if (index < aPlayer.size()) player = aPlayer.get(index);
        } else {
            player = (Player) ChUtil.get(channel, Constans.KEY_PLAYER);
        }
        if (player != null && aPlayer.contains(player)) {
            // sendOtherPlayer(player, IAction.LEAVE_TABLE, CommonProto.getCommonLongVectorProto(Arrays.asList(player.user.id), Arrays.asList(player.user.name)));
            ChUtil.remove(channel, Constans.KEY_TABLE);
            ChUtil.remove(channel, Constans.KEY_PLAYER);
            player.channel = null;
            // aPlayer.remove(player);
            if (player.isAlive()) {
                aProtoAdd.add(player.protoDie());
                numberPlayerDie++;
            }
            List<Long> exBonus = new ArrayList<>();
            if (!isMatchType(TeamObject.GAME_CLAN_BATTLE) && player.user.reviveItem == 2) {
                exBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));
            }
            if (cacheBattle.getMode() == CacheBattle.MODE_GLOBAL) {
                for (IUser.UsedItem item : player.user.aUsedItem) {
                    if (item.getNumber() > 0)
                        exBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, item.getItemId(), -item.getNumber()));
                }
            }
            if (!exBonus.isEmpty())
                updateBattleBonus(player.user.id, new Gson().toJson(exBonus));
            subLeavePlayer(player, channel, srcRequest);
        }
    }

    void sendEndgame(AbstractMessage data) {
        for (Player player : aPlayer) {
            if (player != null && player.channel != null) {
                if (player.leaveStatus == Constans.PLAYER_STATUS_LEAVE) {
                    Util.sendProtoData(player.channel, data, IAction.LEAVE_TABLE_1, System.currentTimeMillis());
                } else {
                    Util.sendProtoData(player.channel, data, IAction.END_GAME, System.currentTimeMillis());
                }
            }
        }
    }

    void sendAllPlayer(int service, AbstractMessage data) {
        for (Player player : aPlayer) {
            if (player != null && player.channel != null) {
                Util.sendProtoData(player.channel, data, service, System.currentTimeMillis());
            }
        }
    }

    void sendTableState(int service, byte[] data) {
        for (Player player : aPlayer) {
            if (player != null && player.channel != null) {
                Util.sendGameData(player.channel, data, System.currentTimeMillis());
            }
        }
    }

    protected abstract void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime);

    protected abstract int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime);

    protected Logger getLoggerMoney() {
        return slib_Logger.money();
    }

    protected void debug(int value) {
        debug(String.valueOf(value));
    }

    protected void debug(String msg) {
        if (CfgServer.debug) {
            System.out.println(msg);
        }
    }

    protected void timer(int turnId) {
        timer(turnId, 3);
    }

    protected void timer(int turnId, int seconds) {
        Xerver.timer(this, turnId, seconds);
    }

    public synchronized void doExpireTurn(int turnId) {
        switch (turnId) {
            case ID_CREATE_MAP:
                doAction(null, IAction.MAP_DATA, null, System.currentTimeMillis());
                break;
            case ID_START_GAME:
                doAction(null, IAction.START_PHYSIC_UPDATE, null, System.currentTimeMillis());
                break;
            case ID_CHECK_ALL_READY:
                checkStartGame();
                break;
            default:
                break;
        }
    }

    public int playerResume(Channel channel) {
        UserInfo user = (UserInfo) ChUtil.get(channel, Constans.KEY_USER);
        for (Player player : aPlayer) {
//            if (player.dbUser.getUsername().equals(user.getUsername())) {
//                player.channel = channel;
//                //
//                GGProto.ProtoInitTable.Builder builder = GGProto.ProtoInitTable.newBuilder();
//                builder.setMapId(map.getMapId());
//                for (Player tmpPlayer : aPlayer) {
//                    if (tmpPlayer.isAlive()) {
//                        builder.addAUnitAdd(player.protoAdd());
//                        aProtoPlayerState.add(player.protoNumberBomb());
//                    }
//                }
//                for (IMonster monster : aMonster) {
//                    if (monster.isAlive())
//                        builder.addAUnitAdd(monster.protoAdd());
//                }
//                for (int i = 0; i < map.aUnit.length; i++) {
//                    for (int j = 0; j < map.aUnit[i].length; j++) {
//                        SquareUnit unit = map.aUnit[i][j];
//                        if ((unit.canMove && unit.type == SquareUnit.SUBTYPE_BOWER) || !unit.canMove) {
//                            builder.addAUnitAdd(unit.protoAdd());
//                        } else {
//                            for (Bomb bomb : unit.aBomb) {
//                                builder.addAUnitAdd(bomb.protoAdd());
//                            }
//                            for (Item item : unit.aItem) {
//                                builder.addAUnitAdd(item.protoAdd(unit.pos));
//                            }
//                        }
//                    }
//                }
//                sendPlayer(player.channel, IAction.CLIENT_RESUME, builder.build());
//                return 10;
//            }
        }
        return 0;
    }

    public void playerDisconnected(Channel channel) {
        Player player = (Player) ChUtil.get(channel, "player");
        if (player != null && aPlayer.contains(player)) {
            JCache.getInstance().setValue(Constans.KEY_RESUME, id, 180);
        }
    }

    void handle_server_input(Player client, Input input) {
        // Store the input on the player instance for processing in the physics
        if (client.isAlive()) {
//            Filer.append("receiver input = " + input.data.getSeq() + " -> " + input.data.getInputKeysCount());
            if (client.lastProcessInput == 0) {
                client.lastProcessInput = input.data.clientTime;
                client.inputs.add(input);
            } else {
//                if (input.data.clientTime - client.lastProcessInput <= 30) {
//                    System.out.println("client = " + input.data.clientTime + " " + client.lastProcessInput);
//                    System.out.println("1 = ");
//                    return;
//                }
                float timeTravel = (System.currentTimeMillis() - _dte) / 1000;
                if ((float) input.data.clientTime / 1000 > server_time + 1 + timeTravel) {
//                    client.decreaseHp(Integer.MAX_VALUE, true);
//                    sendAllPlayer(IAction.MSG_TOAST, CommonProto.getCommonVectorProto(null, Arrays.asList(client.user.name + " sử dụng hack speed")));
                    slib_Logger.root().warn("input = " + client.user.username + " 1");
                    return;
                }
//                if ((float) input.data.clientTime / 1000 < server_time - 10) {
//                    slib_Logger.root().warn("input = " + client.user.username + " 2");
//                    return;
//                }
                client.lastProcessInput = input.data.clientTime;
                client.inputs.add(input);
            }
        } else {
//            Util.sendProtoData(client.channel, CommonProto.getCommonLongVectorProto("YOU ARE DEAD"), IAction.MSG_POPUP, System.currentTimeMillis());
        }
    }

    void initMap() {
        server_time = 0;
        numberPlayerDie = 0;
        numberBossDie = 0;
        numberMonsterDie = 0;
        lastBossPos = null;
        // List return Proto
        aProtoAdd = new ArrayList<GGProto.ProtoUnitAdd.Builder>();
        aProtoBoom = new ArrayList<GGProto.ProtoBoom.Builder>();
        aProtoPlayerState = new ArrayList<GGProto.ProtoPlayerState.Builder>();
        aProtoMonsterState = new ArrayList<GGProto.ProtoMonsterState.Builder>();
        aProtoJumpPos = new ArrayList<GGProto.ProtoUnitJumpPos.Builder>();
        //
        aPlayer = new ArrayList<Player>();
        aMonster = new ArrayList<IMonster>();
        aMoveUnit = new ArrayList<IMove>();
        aItem = new ArrayList<Item>();

        aEffect.clear();
        map = new MapData(mode, aMonster, mapId, bossUnit);

        initPlayer();
        protoInit = GGProto.ProtoInitTable.newBuilder();
        protoInit.setMapId(map.getMapId());
        protoInit.setMode(cacheBattle.getMode());
        for (Player player : aPlayer) {
            protoInit.addAUnitAdd(player.protoAdd());
            aProtoPlayerState.add(player.protoNumberBomb());
            if (player.user.getPet() == 2) showHiddenItem = true;
            else if (player.user.getPet() == 1) showBombLength = true;
        }
        if (!isMatchType(TeamObject.GAME_MATERIAL) && !isMatchType(TeamObject.GAME_CLAN_BATTLE_MONSTER)) {
            for (IMonster monster : aMonster) {
                protoInit.addAUnitAdd(monster.protoAdd());
            }
        }
        if (showHiddenItem) {
            for (int i = 0; i < map.aUnit.length; i++) {
                for (int j = 0; j < map.aUnit[i].length; j++) {
                    if (map.aUnit[i][j].hiddenItem != null && map.aUnit[i][j].hiddenItem.getItemId() > 0) {
                        protoInit.addAItemAdd(map.aUnit[i][j].hiddenItem.protoAdd(map.aUnit[i][j].pos));
                    }
                }
            }
        }
        calculateTrophy();
        timer(ID_CHECK_ALL_READY, CfgCommon.config.battle.waitReady);
//        sendAllPlayer(IAction.MAP_DATA, protoInit.build());
//        timer(ID_CHECK_ALL_READY, CfgCommon.config.battle.waitReady);


//        if (!isTableBoss || mapId % 3 == 1 || aPlayer == null || aPlayer.size() < 1) {
//            initPlayer();
//        } else {
//            int posIndex = 0;
//            for (int i = 0; i < aPlayer.size(); i++) {
//                aPlayer.get(i).mPos = map.getStartPos().get(posIndex).clone();
//                aPlayer.get(i).pos = IMath.getUnit(aPlayer.get(i).mPos);
//                aPlayer.get(i).addStatus(PlayerStatus.ALIVE, 0, 10000, 0);
//                posIndex++;
//            }
//        }
//        protoInit = GGProto.ProtoInitTable.newBuilder();
//        protoInit.setMapId(map.getMapId());
//        for (Player player : aPlayer) {
//            protoInit.addAUnitAdd(player.protoAdd());
//            aProtoPlayerState.add(player.protoNumberBomb());
//        }
//        for (IMonster monster : aMonster) {
//            protoInit.addAUnitAdd(monster.protoAdd());
//        }
//        for (int i = 0; i < map.aUnit.length; i++) {
//            for (int j = 0; j < map.aUnit[i].length; j++) {
//                if (map.aUnit[i][j].hiddenItem != null && map.aUnit[i][j].hiddenItem.getItemId() > 0) {
//                    protoInit.addAItemAdd(map.aUnit[i][j].hiddenItem.protoAdd(map.aUnit[i][j].pos));
//                }
//            }
//        }
//        //
//        calculateTrophy();
//        sendAllPlayer(IAction.MAP_DATA, protoInit.build());
//        timer(ID_CHECK_ALL_READY, CfgCommon.config.battle.waitReady);
    }

    void calculateTrophy() {
        long totalTrophy = 0;
        for (Player player : aPlayer) {
            totalTrophy += player.user.userTrophy;
        }
        totalTrophy = totalTrophy == 0 ? 40 : totalTrophy;
        for (Player player : aPlayer) {
            player.user.trophy = (int) (40 * player.user.userTrophy / totalTrophy);
        }
    }

    protected void initPlayer() {
//        System.out.println("reset aplayer");
        aPlayer = new ArrayList<Player>();
        int posIndex = 0, totalCup = 0;

        for (CachePlayer cachePlayer : cacheBattle.getAPlayer()) {
            Player player = new Player(cachePlayer, map.getNextUnitId(), cacheBattle.getMode());
            player.table = this;
            player.map = map;
            player.mPos = map.getStartPos().get(posIndex).clone();
            player.pos = IMath.getUnit(player.mPos);
            map.getSquare(player.mPos).aPlayer.add(player);

            aPlayer.add(player);
            totalCup += cachePlayer.getTrophy();
//            ChUtil.set(user.getChannel(), KEY_TABLE, this);
//            ChUtil.set(user.getChannel(), KEY_PLAYER, player);
            if (++posIndex >= 4) {
                posIndex = new Random().nextInt(4);
            }
        }
//        System.out.println(aPlayer.size());
//        aPlayer = new ArrayList<Player>();
//        int posIndex = 0, totalCup = 0;
//        for (int i = 0; i < aTeam.size(); i++) {
//            List<UserInfo> aUser = aTeam.get(i).aUser;
//            for (UserInfo user : aUser) {
//                Player player = new Player(user, type == TeamObject.BOSS ? 0 : posIndex, map.getNextUnitId());
//                debug("player.userInfo.getId---->" + player.userInfo.getId());
//                player.table = this;
//                player.map = map;
//                player.mPos = map.getStartPos().get(posIndex).clone();
//                player.pos = IMath.getUnit(player.mPos);
//                map.getSquare(player.mPos).aPlayer.add(player);
//
//                aPlayer.add(player);
//                totalCup += user.getDbUser().getTrophy();
//                ChUtil.set(user.getChannel(), KEY_TABLE, this);
//                ChUtil.set(user.getChannel(), KEY_PLAYER, player);
//                posIndex++;
//            }
//        }
//        for (Player player : aPlayer) {
//            player.user.trophy = totalCup == 0 ? 0 : player.dbUser.getTrophy() / totalCup;
//        }
    }

    byte[] getTableState() {
        try {
            GGProto.ProtoTableState.Builder builder = GGProto.ProtoTableState.newBuilder();
            for (Player player : aPlayer)
                if (player != null && player.isAlive()) builder.addAUnitPos(player.protoPos());

            for (Bomb bomb : map.getLstBom()) if (bomb.isMoving()) builder.addAUnitPos(bomb.protoPos());

            for (IMove iMove : aMoveUnit) if (iMove.isMoving()) builder.addAUnitPos(iMove.protoPos());

            for (IMonster monster : aMonster) {
                if (monster != null && monster.isAlive() && monster.speed > 0) {
                    builder.addAUnitPos(monster.protoPos());
                }
            }
            int size = aProtoPos.size();
            for (int i = 0; i < size; i++) {
                builder.addAUnitPos(aProtoPos.get(i));
                aProtoPos.remove(0);
            }
            size = aProtoAdd.size();
            for (int i = 0; i < size; i++) {
                builder.addAUnitAdd(aProtoAdd.get(0));
                aProtoAdd.remove(0);
            }
            if (!aProtoBoom.isEmpty()) {
                builder.addAUnitUpdate(CommonProto.protoUnitUpdate(PROTO_UPDATE_BOOM, Bomb.protoListBoom(aProtoBoom)));
            }
            if (!aProtoPlayerState.isEmpty()) {
                builder.addAUnitUpdate(CommonProto.protoUnitUpdate(PROTO_UPDATE_PLAYER, Player.protoListPlayerState(aProtoPlayerState)));
            }
            if (!aProtoMonsterState.isEmpty()) {
                builder.addAUnitUpdate(CommonProto.protoUnitUpdate(PROTO_UPDATE_MONSTER, MapObject.protoListMonsterState(aProtoMonsterState)));
            }
            if (!aProtoJumpPos.isEmpty()) {
                builder.addAUnitUpdate(CommonProto.protoUnitUpdate(PROTO_UPDATE_JUMP, CommonProto.protoListJump(aProtoJumpPos)));
            }
            builder.setServerTime(server_time);
//            if (!saveFile) {
//                saveFile = true;
//                FileUtil.saveFile("flatbuffer", FlatUtil.convertProtoBuffToFlatBuffer(builder.build(), Arrays.asList(byteBoom, bytePlayerState, byteMonsterState, byteJumpPos)));
//                FileUtil.saveFile("protobuffer", builder.build().toByteArray());
//            }
            com.proto.GGProto.ProtoPlayerState playerState = null;
            if (builder.getAUnitUpdateCount() > 0) {
                playerState = com.proto.GGProto.ProtoPlayerState.parseFrom(builder.getAUnitUpdate(0).getData(0).toByteArray());
            }
            //FlatUtil.convertProtoBuffToFlatBuffer(builder.build(), playerState);
            return XAbstract.convertProtoBuffToCustom(builder.build());
//            return FlatUtil.convertProtoBuffToFlatBuffer(builder.build(), Arrays.asList(byteBoom, bytePlayerState, byteMonsterState, byteJumpPos));
        } catch (Exception ex) {
            debug("ex----->" + ex.toString());
        }
        return null;
    }

    public List<Integer> aAdd = new ArrayList<Integer>();
    public List<Integer> aRemove = new ArrayList<Integer>();

    // A local timer for precision on server and client
    float local_time = 0.016f; // The local timer in seconds
    long _dte = System.currentTimeMillis(); // The local timer last frame time
    float kill_boss_time = 0.000f;
    float server_time;

    // frametime = Client runs at 60fps /16 ms, Server runs at 22fps /45ms
    JobKey key1, key2;

    void schedule_server_update() {
//        new Thread(() -> {
//            try {
//                Thread.sleep(10000);
//            } catch (Exception Ex) {}
//            key1 = TaskMonitor.submit(this, 50);
//        }).start();
        key1 = TaskMonitor.getInstance().submit(this, 50);
//        TaskMonitor.submit("update" + id, new Runnable() {
//            public void run() {
//                server_update();
//            }
//        }, 50);
    }

    // physic on server runs at 15ms
    void create_physics_simulation() {
//        new Thread(() -> {
//            try {
//                Thread.sleep(10000);
//            } catch (Exception Ex) {}
//            key2 = TaskMonitor.submit(this, 15);
//        }).start();
        key2 = TaskMonitor.getInstance().submit(this, 15);
//        TaskMonitor.submit("physics" + id, new Runnable() {
//            public void run() {
//                server_update_physics();
//            }
//        }, 15);
    }

    // vector input se duoc *player_speed*thoigianfixedupdate (15)
    public synchronized void server_update_physics() {
        try {
            if (isPlay()) {
                for (int i = aEffect.size() - 1; i >= 0; i--) {
                    if (!aEffect.get(i).doEffect()) {
                        aEffect.remove(i);
                    }
                }
                for (int i = 0; i < aPlayer.size(); i++) {
                    this.process_input_new(aPlayer.get(i));
                }
                List<MapPos> mpPlayer = new ArrayList<MapPos>();
                for (int i = 0; i < aPlayer.size(); i++) {
                    if (aPlayer.get(i).isAlive() && aPlayer.get(i).mPos != null) {
                        mpPlayer.add(aPlayer.get(i).mPos);
                    }
                }
                for (int i = 0; i < aMonster.size(); i++) {
                    this.process_monster(aMonster.get(i));
                }
                this.process_item();
                this.process_move_unit();
                this.check_collision();
                for (Bomb bomb : map.getLstBom()) {
                    bomb.doAction(server_time, map, aProtoBoom, this);
                }
                for (Player player : aPlayer) {
                    player.killMyself(false);
                }
                for (IMonster monster : aMonster) {
                    monster.killMyself(this);
                }
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
    }

    private synchronized void check_collision() {
        try {
            // check an item
            for (Player player : aPlayer) {
                player.checkStatus(server_time);
                if (player.isOnlyAlive()) {// chua chet
                    // Item
                    SquareUnit square = map.getMap()[player.mPos.x][player.mPos.y];
                    for (int i = square.aItem.size() - 1; i >= 0; i--) {
                        Item item = square.aItem.get(i);
                        if (player.addItem(server_time, item, null)) {
                            aProtoAdd.add(item.protoRemove());
                            square.aItem.remove(i);
                        }
                    }
                    // colide with other player
                    if (!player.isBurning()) {
                        for (Player otherPlayer : aPlayer) {
                            if (player.unitId != otherPlayer.unitId && otherPlayer.isBurning() && player.pos.distance(otherPlayer.pos) < 0.3f) {
                                if (player.user.team == otherPlayer.user.team) {
                                    otherPlayer.removeStatus(PlayerStatus.BURNING);
                                    otherPlayer.healHp(Arrays.asList(otherPlayer), 5, true);
                                    otherPlayer.timeImmortal = server_time + 2;
                                } else {
                                    otherPlayer.iAmDie();
                                }
                            }
                        }
                    }
                }
                // Monster
                for (IMonster monster : aMonster) {
                    if (monster.isAlive() && player.pos.distance(monster.pos) < monster.closeToTarget) {
                        if (monster.isCollider(player.mPos)) {
                            if (player.isBurning()) {
                                player.iAmDie();
                            } else if (player.isOnlyAlive()) {
                                player.addDamage(monster.atk);
                            }
                        }
                    }
                }
            }

            // check kill bom
            for (IMonster monster : aMonster) {
                if (monster.isAlive()) {
                    SquareUnit square = map.getSquare(monster.mPos);
                    if (monster.monsterId == IMonster.KILL_BOOM) {
                        if (!square.aBomb.isEmpty()) {
                            for (Bomb bomb : square.aBomb) {
                                if (!bomb.isJump() && !bomb.isMoving()) {
                                    aProtoBoom.add(bomb.protoRemove());
                                    bomb.getPlayer().numberBomb--;
                                    map.getLstBom().remove(bomb);
                                }
                            }
                            square.setType(SquareUnit.TYPE_EMPTY);
                            square.aBomb.clear();
                        }
                    }
                    for (int i = square.aItem.size() - 1; i >= 0; i--) {
                        Item item = square.aItem.get(i);
                        if (item.getItemId() == Item.SPEED_DECREASE) {
                            aProtoAdd.add(item.protoRemove());
                            square.aItem.remove(i);
                            break;
                        } else if (item.getItemId() == Item.TRAP_DAMAGE && item.teamId != -1) {
                            aProtoAdd.add(item.protoRemove());
                            square.aItem.remove(i);
                            monster.addDamage(item.value);
                            break;
                        } else if (item.getItemId() == Item.DEAD_POISON && item.teamId >= 0) {
                            monster.addDamage(item.value);
                            break;
                        }
                    }
                }
            }

            checkBombExplosion();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
    }

    /**
     * Kiem tra va xu ly bomb no
     */
    boolean checkBombTimerExplosion(Player player) {
        int index = 0;
        boolean hasTimerBomb = false;
        List<Bomb> aBomb = map.getLstBom();
        List<MapPos> lstRemoveSquare = new ArrayList<MapPos>();
        while (index < aBomb.size()) {
            Bomb bomb = aBomb.get(index);
            index++;
            if (bomb.isTimer() && bomb.getPlayer().unitId == player.unitId) {
                hasTimerBomb = true;
                if (bombExplosion(bomb, lstRemoveSquare)) {
                    index = 0;
                }
            }
        }

        for (int i = 0; i < lstRemoveSquare.size(); i++) {
            map.getSquare(lstRemoveSquare.get(i)).destroy(map, this);
        }

        for (int i = aBomb.size() - 1; i >= 0; i--) {
            if (aBomb.get(i).getStatus() == Bomb.TYPE_EXPLOSIVE) {
                aProtoBoom.add(aBomb.get(i).protoRemove());
                aBomb.remove(i);
            }
        }
        return hasTimerBomb;
    }

    public void checkBombExplosion(List<Bomb> aExplodeBomb, List<MapPos> lstRemoveSquare) {
        for (Bomb bomb : aExplodeBomb) {
            bombExplosion(bomb, lstRemoveSquare);
        }
        for (int i = 0; i < lstRemoveSquare.size(); i++) {
            map.getSquare(lstRemoveSquare.get(i)).destroy(map, this);
        }
        List<Bomb> aBomb = map.getLstBom();
        for (int i = aBomb.size() - 1; i >= 0; i--) {
            if (aBomb.get(i).getStatus() == Bomb.TYPE_EXPLOSIVE) {
                aProtoBoom.add(aBomb.get(i).protoRemove());
                aBomb.remove(i);
            }
        }
    }

    void checkBombExplosion() {
        int index = 0;
        List<Bomb> aBomb = map.getLstBom();
        List<MapPos> lstRemoveSquare = new ArrayList<MapPos>();
        while (index < aBomb.size()) {
            Bomb bomb = aBomb.get(index);
            index++;
            if (server_time - bomb.getTimeStart() < (bomb.getDeltaTimeWait())) {// chua no
                continue;
            } else if (bomb.getStatus() == Bomb.TYPE_WAIT) {
                if (bombExplosion(bomb, lstRemoveSquare)) {
                    index = 0;
                }
            }
        }

        for (int i = 0; i < lstRemoveSquare.size(); i++) {
            map.getSquare(lstRemoveSquare.get(i)).destroy(map, this);
        }

        for (int i = aBomb.size() - 1; i >= 0; i--) {
            if (aBomb.get(i).getStatus() == Bomb.TYPE_EXPLOSIVE) {
                aProtoBoom.add(aBomb.get(i).protoRemove());
                aBomb.remove(i);
            }
        }
    }

    public void explodeBomb(Bomb bomb) {
        List<MapPos> lstRemoveSquare = new ArrayList<MapPos>();
        bombExplosion(bomb, lstRemoveSquare);
        for (int i = 0; i < lstRemoveSquare.size(); i++) {
            map.getSquare(lstRemoveSquare.get(i)).destroy(map, this);
        }
        List<Bomb> aBomb = map.getLstBom();
        for (int i = aBomb.size() - 1; i >= 0; i--) {
            if (aBomb.get(i).getStatus() == Bomb.TYPE_EXPLOSIVE) {
                aProtoBoom.add(aBomb.get(i).protoRemove());
                aBomb.remove(i);
            }
        }
    }

    boolean bombExplosion(Bomb bomb, List<MapPos> lstRemoveSquare) {
        boolean resetIndex = false;
        map.getPosMap(bomb.getMPos().x, bomb.getMPos().y).aBomb.clear();
        bomb.setStatus(Bomb.TYPE_EXPLOSIVE);
        if (!bomb.isTimer()) {
            bomb.getPlayer().numberBomb--;
        }
        if (map.mPit.containsKey(bomb.getMPos().toString())) {
            map.mPit.forEach((key, value) -> {
                if (!key.equals(bomb.getMPos().toString())) {
                    EffectPits effect = new EffectPits(this, map);
                    effect.init(value.mPos.clone(), bomb.getDamage(), 1, 100);
                    aEffect.add(effect);
                }
            });
        }
        // kill player tai vi tri bomb no
        int size = bomb.getLength();
        List<MapPos> direction = Arrays.asList(Input.VectorUp, Input.VectorRight, Input.VectorDown, Input.VectorLeft, Input.VectorCur); // up right down left
        for (int i = 0; i < direction.size(); i++) {
            if (bomb.getBorder()[i] >= 0) {
                continue;
            }
            MapPos mpos = bomb.getMPos().clone();
            bomb.getBorder()[i] = size;
            for (int j = 1; j <= size; j++) {
                mpos.move(direction.get(i));
                SquareUnit square = map.getPosMap(mpos.x, mpos.y);
                if (square.canDestroy && !lstRemoveSquare.contains(square.mPos)) {
                    square.addDamage(bomb.getDamage());
                    lstRemoveSquare.add(square.mPos);
                }
                if (square.canStopBoom) {
                    bomb.getBorder()[i] = j - 1;
                } else {
                    square.removeItem(this);
                    for (Bomb exBomb : square.aBomb) {
                        if (exBomb.getStatus() == Bomb.TYPE_WAIT) {
                            exBomb.setTimeStart(0);
//                            exBomb.getBorder()[oppositeDirection[i]] = 0;
//                            bomb.getBorder()[i] = j - 1;
                            resetIndex = true;
                        }
                    }
                    for (Player player : aPlayer) {
                        if (player.isAlive() && player.mPos.equals(mpos)) {
                            player.addDamage(Math.min(bomb.getDamage(),
                                    bomb.getTargetPercent() == 0 ? bomb.getDamage() : player.user.getIntPoint(Point.HP) * bomb.getTargetPercent() / 100));
                        }
                    }
                    if (bomb.getAvatar() != BoomConfig.BOMB_MONSTER_PUT_BOMB)
                        for (IMonster monster : aMonster) {
                            if (monster.isAlive() && monster.isCollider(mpos)) {
                                if (monster.getDirection() == null) Logs.error("map pos null ");
                                if (monster.monsterId == IMonster.PART_IMMORTAL_BOOM && direction.get(i).equals(monster.getDirection())) {
                                    bomb.getBorder()[i] = j - 1;
                                    aProtoMonsterState.add(monster.protoStatus(IMonster.STATUS_BLOCK_DAMAGE, 0f));
                                } else {
                                    monster.addDamage((int) (bomb.getDamage() * Resources.getRateDamage(bomb.getAvatar(), monster.getType())));
                                }
                            }
                        }
                }
                if (bomb.getBorder()[i] < size) {
                    break;
                }
            }
        }
        return resetIndex;
    }

    long timeProcess = 0;

    public synchronized void server_update() {
        // Update the state of our local clock to match the timer
        try {
            long _dt = System.currentTimeMillis() - _dte;
//            if (_dt > 150) {
//                Logs.warn("delayTime = " + _dt + " " + timeProcess + " " + TaskMonitor.monitorValue());
//            }
            _dte = System.currentTimeMillis();
            local_time += _dt / 1000.0;
            kill_boss_time += _dt;
            server_time = local_time;
            // Make a snapshot of the current state, for updating the clients
            if (isPlay) {
                checkEndGame();
                if (gameState == STATE_START_GAME) {
                    sendTableState(IAction.TABLE_STATE, getTableState());
                } else if (gameState == STATE_END_GAME) {
                    gameState = STATE_WAIT_RESULT;
                    sendTableState(IAction.TABLE_STATE, getTableState());
                    endGame();
                }
            } else if (server_time < timeEndgame) {
                sendTableState(IAction.TABLE_STATE, getTableState());
            } else if (gameState == STATE_WAIT_RESULT) {
                gameState = STATE_NONE;
                sendEndgame(protoEndgame.build());
                cancelTask();
            }
            timeProcess = System.currentTimeMillis() - _dte;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        }
    }

    void endGame() {
        updateHistory(protoEndgame);
        timeEndgame = server_time + 3;
        TaskMonitor.getInstance().removeTable(id);
        TableMonitor.removeTable(cacheBattle.getKey());
        MCache.getInstance().deleteNormal(cacheBattle.getKey());
    }

    /**
     * bombType
     * 0 normal
     * 1 timer
     * 2 autoput
     */
    public void throwBomb(Player player, int posX, int posY) {
//        if (posX != 0) {
//            posX = posX / Math.abs(posX);
//        }
//        if (posY != 0) {
//            posY = posY / Math.abs(posY);
//        }
        SquareUnit square = map.getSquare(player.mPos);
//        if (posX != -1 && (Math.abs(player.mPos.x - posX) <= 1 || Math.abs(player.mPos.y - posY) <= 1)) {
//            square = map.getSquare(posX, posY);
//        }
        if (player.isGloves() && !square.aBomb.isEmpty()) {
            aProtoBoom.add(square.aBomb.get(0).throwTheBomb(server_time, new MapPos(posX, posY), map));
        }
    }

    public void setBoom(Player player, int bombType) {
        setBoom(player, bombType, -1, -1);
    }

    public void setBoom(Player player, int bombType, int posX, int posY) {
        if (gameState == STATE_START_GAME) {
            if (bombType == Bomb.TYPE_TIMER || !checkBombTimerExplosion(player)) {
                SquareUnit square = map.getSquare(player.mPos);
                if (posX != -1 && (Math.abs(player.mPos.x - posX) <= 1 || Math.abs(player.mPos.y - posY) <= 1)) {
                    square = map.getSquare(posX, posY);
                }
                if (player.isGloves() && !square.aBomb.isEmpty()) {
                    if (bombType != Bomb.TYPE_AUTO_PUT) {
                        aProtoBoom.add(square.aBomb.get(0).throwTheBomb(server_time, player.lastDirection, map));
                    }
                } else if (square.canMove && square.aBomb.size() < 1) {
                    int maxBomb = player.isMaxBombPeriod() ? (int) player.user.getPoint(Point.BOOM_MAX) : (int) player.user.getPoint(Point.NUMBER_BOOM);
                    if (player.numberBomb < maxBomb) {
                        Bomb bomb = new Bomb(map.getNextUnitId(), player, bombType == Bomb.TYPE_TIMER);
                        bomb.setTimeStart(server_time);
                        bomb.setMPos(square.mPos.clone());
                        bomb.setPos(square.pos.clone());
                        square.aBomb.add(bomb);
                        if (bombType != Bomb.TYPE_TIMER)
                            player.numberBomb++;
                        map.getLstBom().add(bomb);
                        aProtoAdd.add(bomb.protoAdd(this));
                        if (square.breakBomb()) {
                            bomb.setTimeStart(server_time - bomb.getDeltaTimeWait() + 0.1f);
                        }
                    }
                }
            }
        }
    }

    void process_monster(IMonster monster) {
        if (isPlay()) {
            monster.doAction(this);
        }
    }

    void process_item() {
        if (isPlay()) {
            for (int i = aItem.size() - 1; i >= 0; i--) {
                Item item = aItem.get(i);
                if (item.removeTime == -1) {
                    aItem.remove(i);
                } else {
                    if (item.removeTime < server_time) {
                        aProtoAdd.add(item.protoRemove());
                        item.square.aItem.remove(item);
                        aItem.remove(i);
                    }
                }
            }
        }
    }

    void process_move_unit() {
        if (isPlay()) {
            int size = aMoveUnit.size();
            for (int i = 0; i < size; i++) {
                aMoveUnit.get(i).doAction(server_time);
            }
            for (int i = aMoveUnit.size() - 1; i >= 0; i--) {
                if (!aMoveUnit.get(i).isMoving()) {
                    aMoveUnit.remove(i);
                }
            }
        }
    }

    Pos process_input_new(Player player) {
        String debug = "";
        try {
            int ic = player.inputs.size();
            SquareUnit square = map.getSquare(player.mPos);
            if (player.isAlive() && ic > 0) {
                square.aPlayer.remove(player);
                Pos playerPos = player.pos.clone();
                //
                long last_input_seq = 0;
                double last_input_time = 0;
                for (int index = 0; index < ic; ++index) {
                    Input input = player.getAndRemoveInput(0);
                    if (player.onAir()) {
                        continue;
                    }
                    last_input_seq = input.data.seq;

                    // don't process ones we already have simulated locally
                    if (input.data.seq <= player.last_input_seq) {
                        continue;
                    }
                    int kc = input.data.keys.size();
                    debug += input.data.keys;
                    for (int i = 0; i < kc; i++) {
                        int typeInput = input.data.keys.get(i);
                        if (typeInput == Input.NONE) {
                            continue;
                        }
                        switch (typeInput) {
                            case Input.PutBomb:
                                if (!player.isBurning() && !player.user.point.isZombie()) {
                                    setBoom(player, Bomb.TYPE_NORMAL, input.data.keys.get(i + 1), input.data.keys.get(i + 2));
                                }
                                i += 2;
                                break;
                            case Input.ThrowBomb:
                                if (!player.isBurning()) {
                                    throwBomb(player, input.data.keys.get(i + 1), input.data.keys.get(i + 2));
                                }
                                i += 2;
                                break;
                            case Input.Teleport:
                                SquareUnit newSquare = map.getSquare(input.data.keys.get(i + 1), input.data.keys.get(i + 2));
                                playerPos = newSquare.pos.clone();
                                aProtoJumpPos.add(CommonProto.protoJumpPos(BoomConfig.JUMP_TELEPORT, player.unitId, square.pos));
                                break;
                            case Input.UseItem1:
                                player.useBattleItem(server_time, playerPos);
                                break;
                            case Input.InputPlayerSkill:
                                if (cacheBattle.getMode() != CacheBattle.MODE_GLOBAL) player.usePlayerSkill(playerPos);
                                break;
                            case Input.InputPetSkill:
                                if (cacheBattle.getMode() != CacheBattle.MODE_GLOBAL) player.usePetSkill(playerPos);
                                break;
                            case Input.InputReviveItem:
                                player.useReviveItem(server_time);
                                break;
                            case Input.InputPlayerItem:
                                player.usePlayerItem(playerPos, server_time, input.data.keys.get(++i));
                                break;
                            default:
                                float speed = (float) input.data.keys.get(i + 1) / 10;
                                i += 1;
                                float curSpeed = player.isFreeze() ? 0 : (player.isMaxSpeedPeriod() ? player.user.getPoint(Point.SPEED_MAX) : player.user.getPoint(Point.SPEED));
                                if (speed <= curSpeed + 1f && !player.isBurning() && !player.isStatus(PlayerStatus.USER_ANIMATION)) {
                                    List<MapPos> direction = new ArrayList<>();
                                    for (int j = 0; j < Input.mapInput.get(typeInput).length; j++) {
                                        direction.add(Input.mapInput.get(typeInput)[j]);
                                    }
                                    if (direction != null) {
                                        float distance = speed * BoomConfig.baseSpeedByFrame;
                                        for (MapPos mapPos : direction) {
                                            if (distance > 0) {
                                                distance = player.move(server_time, distance, playerPos, mapPos);
                                            }
                                        }
                                    }
                                }
                                break;
                        }
//                        Logs.info(String.format("typeInput=%s, afterPos=%s", typeInput, playerPos.toString()));
                    }
                } // for each input command
                player.last_input_time = last_input_time;
                player.last_input_seq = last_input_seq;
                player.pos = playerPos;
                player.mPos = IMath.getBlock(player.pos);
                map.getSquare(player.mPos).aPlayer.add(player);
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex) + " -> " + player.user.username + " " + debug);
        }
        return null;
    }

    public abstract void checkEndGame();

    public boolean isPlay() {
        return ((gameState == STATE_START_GAME) && isPlay);
    }

    public MapData getMap() {
        return map;
    }

    public void setMap(MapData map) {
        this.map = map;
    }

    Player getPlayer(Channel channel) {
        return (Player) ChUtil.get(channel, KEY_PLAYER);
    }


    void sendPlayer(Channel channel, int service, AbstractMessage data) {
        Util.sendProtoData(channel, data, service, System.currentTimeMillis());
    }

    void sendOtherPlayer(Player pl, int service, AbstractMessage data) {
        for (Player player : aPlayer) {
            if (player != null && pl.user.id != player.user.id) {
                Util.sendProtoData(player.channel, data, service, System.currentTimeMillis());
            }
        }
    }

    protected void cancelTask() {
        debug("cancel task id-->" + id + " + loai choi --->" + mode);
        TaskMonitor.getInstance().cancel(key1);
        TaskMonitor.getInstance().cancel(key2);
    }

    protected boolean isMatchType(int value) {
        return mode == value;
    }

    protected boolean updateBattleBonus(long userId, String bonusBattleEx) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            int battleId = cacheBattle.getBattleId();
//            String db = "boom_s" + cacheBattle.getServerId() + ".";
            SQLQuery query = session.createSQLQuery("update battle_user set bonus_battle_ex= :data where battle_id=" + battleId + " and user_id=" + userId);
            query.setString("data", bonusBattleEx);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
        return false;
    }

    protected boolean updateHistory(GGProto.ProtoEndgame.Builder endgame) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            int battleId = cacheBattle.getBattleId();
//            String db = "boom_s" + cacheBattle.getServerId() + ".";

            SQLQuery query = session.createSQLQuery("update battle_data set data=:data where id=" + battleId);
            query.setString("data", new Gson().toJson(endgame.build()));
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
        return false;
    }

    protected boolean updateHasAction(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            int battleId = cacheBattle.getBattleId();
//            String db = "boom_s" + cacheBattle.getServerId() + ".";
            session.createSQLQuery("update battle_user set has_action=1 where battle_id=" + battleId + " and user_id=" + userId).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
        return false;
    }

    abstract void doAction();

    void resetPlayer(Player player) {
        player.aStatus.clear();
        player.mStatus.clear();
        ChUtil.remove(player.channel, Constans.KEY_TABLE);
        ChUtil.remove(player.channel, Constans.KEY_PLAYER);
    }

    public abstract AbstractTable getNewInstance(CacheBattle cacheBattle);
}
