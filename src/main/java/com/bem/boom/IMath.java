package com.bem.boom;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * Created by vieth_000 on 7/14/2016.
 */
public class IMath {
    public static void main(String[] args) {
        List<Integer> aInt = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            aInt.add(new Random().nextInt(10));
        }
        System.out.println(getLatency(aInt));
    }

    public static int getLatency(List<Integer> aInput) {
        Collections.sort(aInput);
        int value1 = 0, value2 = 0, n = aInput.size();
        for (Integer value : aInput) {
            value1 += value * value;
            value2 += value;
        }
        value2 = value2 * value2 / n;
        int s = (int) Math.sqrt((value1 - value2) / (n - 1));
        int midPoint = n % 2 == 0 ? (aInput.get(n / 2) + aInput.get(n / 2 - 1)) / 2 : aInput.get(n / 2);
        int[] range = new int[]{midPoint - s, midPoint + s};
        int count = 0, sum = 0;
        for (Integer value : aInput) {
            if (range[0] <= value && value <= range[1]) {
                count++;
                sum += value;
            }
        }
        return sum / count;
    }

    public static Pos getUnit(int x, int y) {
        try {
            return new Pos(x * BoomConfig.squareEdge, -y * BoomConfig.squareEdge);
        } catch (Exception ex) {
            return null;
        }
    }

    public static Pos getUnit(MapPos mPos) {
        try {
            Pos rs = new Pos(mPos.x * BoomConfig.squareEdge, -mPos.y * BoomConfig.squareEdge);
            rs.round();
            return rs;
        } catch (Exception ex) {
            return null;
        }
    }

    public static MapPos getBlock(Pos pos) {
        try {
            MapPos mp = new MapPos(((int) ((pos.x + (BoomConfig.squareEdge / 2)) / BoomConfig.squareEdge)),
                    ((int) (Math.abs((pos.y - (BoomConfig.squareEdge / 2))) / BoomConfig.squareEdge)));
            return mp;
        } catch (Exception ex) {
            return null;
        }
    }

    public static float round(float value) {
        return (float) Math.round(value * 1000f) / 1000f;
    }

}
