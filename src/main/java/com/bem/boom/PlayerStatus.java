package com.bem.boom;

import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 9/21/2016.
 */
public class PlayerStatus {
    public static final int ALIVE = 1;
    public static final int IMMORTAL = 2;
    public static final int INVISIBLE = 3;
    public static final int GLOVES = 4;
    public static final int PUSH_BOMB = 5;
    public static final int CHAOTIC = 6;// hon loan
    public static final int LOSE_ITEM = 7;// mat item
    public static final int AUTO_PUT_BOM = 8;// mat item
    public static final int SUPERMAN = 9;// mat item
    //
    public static final int GET_ITEM = 10;
    public static final int GET_BATTLE_ITEM = 11;
    public static final int GET_GOLD = 12;
    public static final int GET_GEM = 13;
    public static final int USE_MY_ITEM = 14;
    public static final int USE_BATTLE_ITEM = 15;
    //
    public static final int UPDATE_SPEED = 16;
    public static final int RADAR = 17;
    public static final int ANTI_RADAR = 18;
    public static final int BOMB_INVISIBLE = 19;
    public static final int ANTI_INVISIBLE = 20;
    public static final int MAGNIFY = 21;
    public static final int BURNING = 22;
    public static final int NUMBER_BOMB = 23;
    public static final int UPDATE_HP = 24;
    public static final int USER_SKILL = 25;
    public static final int PET_SKILL = 26;
    public static final int POWER_ATK = 27;
    public static final int DECREASE_DAMAGE = 28;
    public static final int USER_ANIMATION = 29;
    public static final int USER_EFFECT = 30;
    public static final int BOMB_LENGTH = 31;
    public static final int PET_ANIMATION = 32;
    public static final int BLOCK_DAMAGE = 33;
    public static final int ZOMBIE = 34;
    public static final int TELEPORT = 35;


    public static final int FAKE_ON_AIR = 100;
    public static final int FREEZE = 101;
    public static final int MAX_SPEED_PERIOD = 102;
    public static final int MAX_BOMB_PERIOD = 103;
    public static final int MAX_LENGTH_PERIOD = 104;
    //
    public static final int EFFECT_BUFF_HP = 1;
    public static final int EFFECT_BUFF_ATK = 2;
    public static final int EFFECT_FREEZE = 11;
    public static final int EFFECT_ZOMBIE_BUFF_HP = 16;



    public static final List<Integer> aFakeStatus = Arrays.asList(FAKE_ON_AIR, BURNING, USER_ANIMATION, USER_SKILL, PET_SKILL, MAX_BOMB_PERIOD, MAX_LENGTH_PERIOD, MAX_SPEED_PERIOD);
    public static final List<Integer> addPoint = Arrays.asList(POWER_ATK, DECREASE_DAMAGE);
    //

    public PlayerStatus(int id) {
        this.id = id;
        this.timeout = 10000;
    }

    public PlayerStatus(int id, float timeout) {
        this.id = id;
        this.timeout = timeout;
    }

    public PlayerStatus(int id, float timeout, int value) {
        this.id = id;
        this.value = value;
        this.timeout = timeout;
    }

    public int id;
    public float timeout;
    public int value;
}
