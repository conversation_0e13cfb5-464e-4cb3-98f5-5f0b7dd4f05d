package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.BoomConfig;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Item;
import com.bem.util.CommonProto;

import java.util.*;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class BossPoison extends IMonster {
    long timeSkill = 15000;
    long startTime = System.currentTimeMillis();
    boolean doSkill = false;
    Map<String, List<MapPos>> mExplode = new HashMap<String, List<MapPos>>() {{
        put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 1), new MapPos(2, 0), new MapPos(2, -1)));
        put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 1), new MapPos(-2, 0), new MapPos(-2, -1)));
        put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
        put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
    }};

    public BossPoison(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        this.isBoss = true;
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0),
                new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1));
        closeToTarget = 0.5f;
        timeWaitStun = 1400;
    }

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            if (System.currentTimeMillis() > timeStun) {
                move(table.getServer_time());
            }
            doSkill(table);
        }
    }

    List<SquareUnit> aSquare = new ArrayList<SquareUnit>();

    public void doSkill(AbstractTable table) {
        if (doSkill && System.currentTimeMillis() - startTime >= timeSkill + 1300) {
            doSkill = false;
            for (SquareUnit square : aSquare) {
                Item item = new Item(map.getNextUnitId(), Item.DEAD_POISON);
                item.value = atk;
                item.setRemoveTime(table.getServer_time() + 15);
                item.setSquare(square);
                square.aItem.add(item);
                table.getAProtoAdd().add(item.protoAdd(pos));
                table.getAProtoJumpPos().add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, item.getId(), square.pos));
            }
            aSquare.clear();
            startTime = System.currentTimeMillis();
        } else if (System.currentTimeMillis() - startTime >= timeSkill - 1700 && aSquare.isEmpty()) {
            aSquare = map.getRandomSquare(map.getSquareEmpty(), 10);
            for (SquareUnit square : aSquare) {
                table.getAProtoAdd().add(CommonProto.protoAddAlertDanger(square.pos));
            }
        } else if (System.currentTimeMillis() - startTime >= timeSkill) {
            timeStun = System.currentTimeMillis() + timeWaitStun;
            table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
            doSkill = true;
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossPoison(id, monsterId, mPos, point);
    }
}
