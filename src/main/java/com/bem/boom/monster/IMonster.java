package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.TableBossGlobal;
import com.bem.boom.*;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Player;
import com.bem.util.CommonProto;
import com.proto.GGProto;
import lombok.Data;

import java.util.*;

/**
 * Created by vieth_000 on 9/12/2016.
 */
@Data
public class IMonster {
    public static final int NO_MONSTER = 0;
    public static final int CREEP1 = 1;
    public static final int CREEP2 = 2;
    public static final int BOSS_NAM = 3;
    public static final int SNOW_BALL = 4;
    public static final int INVISIBLE = 5;
    public static final int BOSS_EXPLODE = 6;
    public static final int PART_IMMORTAL_BOOM = 7;
    public static final int KICK_BOMB = 8;
    public static final int BOSS_BREAK_BOMB = 9;
    public static final int MONSTER_POISON = 10;
    public static final int MONSTER_SHOOTER = 11;
    public static final int BOSS_POISON = 12;
    public static final int MONSTER_IMMORTAL = 13;
    public static final int MONSTER_SHOOTER2 = 14;
    public static final int BOSS_SPACE = 15;
    public static final int BOSS_MATERIAL = 16;
    public static final int BOSS_CRAB = 21;
    public static final int MONSTER_KILLER = 22;
    public static final int PUT_BOMB = 23;
    public static final int KILL_BOOM = 24;
    public static final int WAR_SHIP = 25;
    public static final int MONSTER_TRAP_DAMAGE = 26;
    public static final int MONSTER_TRAP_FREEZE = 27;
    public static final int BOSS_WATER_SEAL = 28;
    //

    //
    public static final int STATUS_INJURED = 0;
    public static final int STATUS_ANIMATION = 1;
    public static final int STATUS_INVISIBLE = 2;
    public static final int STATUS_BLOCK_DAMAGE = 3;
    public static final int STATUS_IMMORTAL = 4;
    public static final int STATUS_TELEPORT = 5;
    public static final int STATUS_SKILL = 6;
    public static final int STATUS_EFFECT = 7;
    public static final int STATUS_BOSS_DAMAGE = 8;
    //
    public float closeToTarget = 0.25f;
    //
    Map<String, List<MapPos>> mTarget = new HashMap<String, List<MapPos>>() {{
        put(Input.VectorRight.toString(), Arrays.asList(Input.VectorRight.clone()));
        put(Input.VectorLeft.toString(), Arrays.asList(Input.VectorLeft.clone()));
        put(Input.VectorUp.toString(), Arrays.asList(Input.VectorUp.clone()));
        put(Input.VectorDown.toString(), Arrays.asList(Input.VectorDown.clone()));
    }};
    List<MapPos> aCollider = Arrays.asList(new MapPos(0, 0));
    //
    float timeImmortal = 0;
    public int unitId, monsterId;
    public float speed = 0.8f, speedBack, lastAction;
    public MapData map;
    MapPos direction = Input.VectorRight.clone();
    public Pos pos;
    public MapPos mPos;
    boolean alive;
    public int hp, maxHp = 5, atk;
    public Map<Integer, Integer> mStatus = new HashMap<Integer, Integer>();
    protected long timeWaitStun = 2000;
    long timeStun = System.currentTimeMillis();
    long timeBirth = System.currentTimeMillis();
    public boolean isBabyMonster = false, isBoss = false, isTower = false;
    int beDamage = 0, type;
    Map<Integer, int[]> mPoint;

    public IMonster(int unitId, int monsterId, MapPos mPos, float[] point) {
        this.unitId = unitId;
        this.mPos = mPos;
        this.pos = IMath.getUnit(mPos);
        this.direction = Input.VectorRight;
        this.monsterId = monsterId;
        this.alive = true;
        this.hp = (int) point[0];
        this.atk = (int) point[1];
        this.maxHp = hp;
        this.type = Resources.mMonsterType.get(monsterId);
        timeBirth = System.currentTimeMillis();
//        if (monsterId == 2) {
//            speed *= 2;
//        }
    }

    public void initPoint(int hp, int atk) {
        if (hp > 0) {
            this.hp = hp;
            this.maxHp = hp;
        }
        if (atk > 0) {
            this.atk = atk;
        }
    }

    public void addDamage(int damage) {
        beDamage = beDamage < damage ? damage : beDamage;
    }

    public boolean isBoss() {
        return isBoss;
    }

    public boolean isAlive() {
        return alive;
    }

    public void doAction(AbstractTable table) {
        move(table.getServer_time());
    }

    public GGProto.ProtoMonsterState.Builder protoInjured(int damage) {
        return protoStatus(STATUS_INJURED, (float) hp);
    }

    public GGProto.ProtoMonsterState.Builder protoStatus(Integer status, Float info) {
        GGProto.ProtoMonsterState.Builder builder = GGProto.ProtoMonsterState.newBuilder();
        builder.setId(unitId);
        builder.addStatus(status);
        builder.addInfo(info);
        return builder;
    }

    public GGProto.ProtoMonsterState.Builder protoStatus(List<Integer> aStatus, List<Float> aInfo) {
        GGProto.ProtoMonsterState.Builder builder = GGProto.ProtoMonsterState.newBuilder();
        builder.setId(unitId);
        builder.addAllStatus(aStatus);
        builder.addAllInfo(aInfo);
        return builder;
    }

    public boolean isCollider(MapPos tmp) {
        for (MapPos mapPos : aCollider) {
            if (tmp.x == mapPos.x + mPos.x && tmp.y == mapPos.y + mPos.y) {
                return true;
            }
        }
        return false;
    }

    public boolean killMyself(AbstractTable table) {
        if (beDamage > 0) {
            int damage = beDamage;
            beDamage = 0;
            if (table.getServer_time() >= timeImmortal && !hasStatus(IMonster.STATUS_IMMORTAL)) {
                hp -= damage;
                if (hp < 0) hp = 0;
                table.getAProtoMonsterState().add(protoInjured(damage));
                timeImmortal = table.getServer_time() + 1;
                injuredAction(table, damage);
            }
        }
        if (isAlive() && hp == 0) {
            table.getAProtoAdd().add(protoDie());
            if (isBoss()) {
                table.setLastBossPos(pos.clone());
                table.numberBossDie++;
            }
            map.getSquare(mPos).aMonster.remove(this);
            table.numberMonsterDie++;
            return true;
        }
        return false;
    }

    protected void injuredAction(AbstractTable table, int damage) {
        if (isBoss() && table instanceof TableBossGlobal) {
            this.hp += damage;
            for (Player player : table.getAPlayer()) {
                if (player.isAlive()) player.totalDamage += damage;
            }
        }
    }

    //region Move
    void move(float serverTime) {
        if (isAlive()) {
            float distance = speed * BoomConfig.baseSpeedByFrame / 2;
            MapPos lastDirection = direction == null ? null : direction.clone();
            if (direction != null) {
                SquareUnit curSquare = map.getSquare(mPos);
                curSquare.aMonster.remove(this);
                if (isWalkable(direction)) {
                    SquareUnit targetSquare = map.getSquare(mPos.x + direction.x, mPos.y + direction.y);
                    if (pos.distance(targetSquare.pos) > curSquare.pos.distance(targetSquare.pos)) {// neu chua di den giua o
                        MapPos dr = findNewDirectionPlayer();
                        if (dr != null) {
                            distance = moveTo(distance, curSquare.pos);
                        } else {
                            if (this.monsterId == BOSS_NAM) {
                                int rs = new Random().nextInt(5);
                                if (rs < 1) {
                                    distance = moveTo(distance, curSquare.pos);
                                } else {
                                    distance = moveTo(distance, targetSquare.pos);
                                }
                            } else {
                                distance = moveTo(distance, targetSquare.pos);
                            }
                        }
                    } else {
                        distance = moveTo(distance, targetSquare.pos);
                    }
                } else {
                    distance = moveTo(distance, curSquare.pos);
                    if (monsterId == KICK_BOMB) {
                        SquareUnit targetSquare = map.getSquare(mPos.x + direction.x, mPos.y + direction.y);
                        if (!targetSquare.aBomb.isEmpty()) {
                            for (Bomb bomb : targetSquare.aBomb) {
                                bomb.pushTheBomb(serverTime, direction);
                            }
                        }
                    }
                }
            }
            if (distance > 0) { // find new direction
                MapPos dr = findNewDirectionPlayer();
                if (dr != null) {
                    direction = dr;
                } else {
                    direction = findNewDirection();
                }
                if (direction != null) {
                    moveTo(distance, map.getSquare(mPos.x + direction.x, mPos.y + direction.y).pos);
                }
            }
            if (direction == null && lastDirection != null) direction = lastDirection;
            pos.round();
            mPos = IMath.getBlock(pos);
            map.getSquare(mPos).aMonster.add(this);
        }
    }

    boolean isWalkable(MapPos direction) {
        if (direction != null) {
            List<MapPos> aSquare = mTarget.get(direction.toString());
            for (MapPos mapPos : aSquare) {
                SquareUnit square = map.getSquare(mPos.x + mapPos.x, mPos.y + mapPos.y);
                if (square == null || !square.walkAbles(this)) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    boolean isWalkableWithPlayer(MapPos direction) {
        if (direction != null) {
            List<MapPos> aSquare = mTarget.get(direction.toString());
            boolean hasPlayer = false;
            for (MapPos mapPos : aSquare) {
                SquareUnit square = map.getSquare(mPos.x + mapPos.x, mPos.y + mapPos.y);
                if (square == null || !square.walkAbles(this)) {
                    return false;
                }
                for (Player player : square.aPlayer) {
                    if (player.isAlive()) {
                        hasPlayer = true;
                    }
                }
            }
            return hasPlayer;
        }
        return false;
    }

    MapPos findNewDirectionPlayer() {
        List<MapPos> aVector = Arrays.asList(Input.VectorLeft, Input.VectorDown, Input.VectorUp, Input.VectorRight);
        Collections.shuffle(aVector);
        for (MapPos tmp : aVector) {
            if (isWalkableWithPlayer(tmp)) {
                return tmp;
            }
        }
        return null;
    }

    MapPos findNewDirection() {
        List<MapPos> aVector = Arrays.asList(Input.VectorLeft, Input.VectorDown, Input.VectorUp, Input.VectorRight);
        Collections.shuffle(aVector);
        for (MapPos tmp : aVector) {
            if (tmp != direction && isWalkable(tmp)) {
                return tmp;
            }
        }
        return null;
    }

    float moveTo(float distance, Pos targetPos) {
        if (pos.x == targetPos.x && pos.y != targetPos.y) { // follow y
            if (Math.abs(targetPos.y - pos.y) < distance) {
                distance = Math.abs(targetPos.y - pos.y);
                pos.y = targetPos.y;
            } else {
                pos.y += distance * (targetPos.y > pos.y ? 1 : -1);
                distance = 0;
            }
        } else if (pos.x != targetPos.x && pos.y == targetPos.y) { // follow x
            if (Math.abs(targetPos.x - pos.x) < distance) {
                distance = Math.abs(targetPos.x - pos.x);
                pos.x = targetPos.x;
            } else {
                pos.x += distance * (targetPos.x > pos.x ? 1 : -1);
                distance = 0;
            }
        }
        return distance;
    }
    //endregion

    //region Proto
    public GGProto.ProtoUnitPos.Builder protoPos() {
        GGProto.ProtoUnitPos.Builder builder = GGProto.ProtoUnitPos.newBuilder();
        builder.setId(unitId);
        builder.setPosX(pos.x);
        builder.setPosY(pos.y);
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoDie() {
        alive = false;
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(unitId);
        builder.setAdd(false);
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoAdd() {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(unitId);
        builder.setType(BoomConfig.TYPE_MONSTER);
        builder.addAvatar(monsterId);
        builder.addAvatar(hp);
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        return builder;
    }
    //endregion

    public boolean hasStatus(int statusId) {
        return mStatus.containsKey(statusId);
    }

    public void log(String msg) {
//        if (unitId == 170) {
//            debug(msg);
//        }
    }

    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new IMonster(id, monsterId, mPos, point);
    }
}
