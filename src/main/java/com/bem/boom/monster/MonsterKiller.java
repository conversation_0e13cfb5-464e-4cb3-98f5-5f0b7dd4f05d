package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Player;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterKiller extends IMonster {
    long startTime;
    boolean doSkill = false;
    public static final int STATUS_BOSS_EXPLODE = 100;
    public static final int STATE_ANIMATION = 0;
    public static final int STATE_EXPLOSION = 1;
    int state = 0;

    public MonsterKiller(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        startTime = System.currentTimeMillis();
    }

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - startTime >= 5000 && !doSkill) {
            if (state == STATE_ANIMATION) {
                this.speed = 0;
                startTime += 2000;
                state = STATE_EXPLOSION;
                table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
            } else if (state == STATE_EXPLOSION) {
                this.hp = 0;
                this.doSkill = true;

                List<MapPos> lstRemoveSquare = new ArrayList<>();
                List<Bomb> aBombExplode = new ArrayList<>();

                List<MapPos> aDirection = Arrays.asList(Input.VectorUp, Input.VectorRight, Input.VectorDown, Input.VectorLeft, Input.VectorCur); // up right down left
                List<Integer> aLength = new ArrayList<>();
                for (MapPos tmpDirection : aDirection) {
                    MapPos startPos = tmpDirection.clone(mPos);
                    int length = 0;
                    SquareUnit square = map.getSquare(startPos);
                    while (!square.canStopBoom) {
                        for (Player player : square.aPlayer) {
                            if (player.isAlive()) {
                                player.addDamage(atk);
                            }
                        }
                        for (Bomb bomb : square.aBomb) {
                            if (!aBombExplode.contains(bomb)) {
                                aBombExplode.add(bomb);
                            }
                        }
                        length++;
                        square = map.getSquare(startPos.x + length * tmpDirection.x, startPos.y + length * tmpDirection.y);
                        if (length == 11) {
                            break;
                        }
                    }
                    if (square.canDestroy) {
                        square.addDamage(1000);
                        lstRemoveSquare.add(square.mPos);
                    }
                    aLength.add(length - 1);
                }
                table.getAProtoBoom().add(protoExplode(mPos, aLength));
                table.checkBombExplosion(aBombExplode, lstRemoveSquare);
            }
        }
    }

    public GGProto.ProtoBoom.Builder protoExplode(MapPos startPos, List<Integer> aLength) {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        builder.addAllBombLength(aLength); // up, right, down, left
        builder.setImageId(2);
        builder.setStatus(STATUS_BOSS_EXPLODE);
        builder.setMPos(CommonProto.protoMPos(startPos.x, startPos.y));
        return builder;
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterKiller(id, monsterId, mPos, point);
    }
}
