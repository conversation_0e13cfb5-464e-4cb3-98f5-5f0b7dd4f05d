package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Item;

import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterImmortal extends IMonster {
    long startTime = System.currentTimeMillis();

    public MonsterImmortal(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - startTime >= 15000 && !mStatus.containsKey(STATUS_IMMORTAL)) {
            mStatus.put(STATUS_IMMORTAL, 1);
            table.getAProtoMonsterState().add(protoStatus(STATUS_IMMORTAL, 0f));
        } else if (System.currentTimeMillis() - startTime >= 20000) {
            mStatus.remove(STATUS_IMMORTAL);
            table.getAProtoMonsterState().add(protoStatus(-STATUS_IMMORTAL, 0f));
            startTime = System.currentTimeMillis();
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterImmortal(id, monsterId, mPos, point);
    }
}
