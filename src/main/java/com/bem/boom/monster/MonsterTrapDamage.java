package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.BoomConfig;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Item;
import com.bem.util.CommonProto;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterTrapDamage extends IMonster {
    long timeEffect = System.currentTimeMillis();
    int step = 0;

    public MonsterTrapDamage(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
    }

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - timeEffect >= 20000) {
            SquareUnit square = map.getSquare(mPos.x, mPos.y);
            if (!square.canTrap && square.aItem.isEmpty()) {
                Item item = new Item(map.getNextUnitId(), Item.TRAP_DAMAGE);
                item.teamId = -1;
                item.targetPercent = 100;
                item.value = atk;
                square.aItem.add(item);
                table.getAProtoAdd().add(item.protoAdd(square.pos, -10000));
                table.getAProtoAdd().add(CommonProto.protoAddAlertDanger(square.pos));
                timeEffect = System.currentTimeMillis();
            }
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterTrapDamage(id, monsterId, mPos, point);
    }
}
