package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterShooter2 extends IMonster {
    long startTime = System.currentTimeMillis();

    public MonsterShooter2(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - startTime >= 5000) {
            if (direction != null) {
                com.bem.boom.unit.FireBall ball = new com.bem.boom.unit.FireBall(map.getNextUnitId(), 3, pos, table);
                ball.damage = atk;
                ball.startToMove(table.getServer_time(), direction);
                table.getAProtoAdd().add(ball.protoAdd());
                table.getAMoveUnit().add(ball);
            }
            //
            startTime = System.currentTimeMillis();
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterShooter2(id, monsterId, mPos, point);
    }
}
