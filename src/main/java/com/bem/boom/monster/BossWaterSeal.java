package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.boom.effect.EffectBombRain;
import com.bem.boom.effect.EffectMermaid;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class BossWaterSeal extends IMonster {
    final int STEP_ANIMATION = 0;
    final int STEP_ADD_BOMB = 1;

    long timeEffect = System.currentTimeMillis();
    int step = 0;

    public BossWaterSeal(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        this.isBoss = true;
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0),
                new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1));
        closeToTarget = 0.5f;
        timeWaitStun = 1000;
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - timeEffect >= 40000) {
            if (step == STEP_ANIMATION) {
                table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
                step++;
                timeEffect += 1200;
                timeStun = System.currentTimeMillis() + timeWaitStun;
            } else {
                List<Integer> xPos = Arrays.asList(2, 6, 10, 2, 10);
                List<Integer> yPos = Arrays.asList(2, 6, 10, 10, 2);
                for (int i = 0; i < xPos.size(); i++) {
                    EffectMermaid effect = new EffectMermaid(table, map);
                    effect.init(new MapPos(xPos.get(i), yPos.get(i)), atk, 5);
                    table.getAEffect().add(effect);

                    IMonster mU = Resources.mMonster.get(MONSTER_TRAP_FREEZE).clone(map.getNextUnitId(), MONSTER_TRAP_FREEZE, new MapPos(xPos.get(i), yPos.get(i)), new float[]{0f, 0f});
                    if (mPoint != null) {
                        int[] point = mPoint.get(MONSTER_TRAP_FREEZE);
                        if (point != null) {
                            mU.initPoint(point[0], point[1]);
                        }
                    }
                    mU.map = map;
                    mU.isBabyMonster = true;
                    table.getAMonster().add(mU);
                    table.getAProtoAdd().add(mU.protoAdd());
                }
                step = 0;
                timeEffect += 40000;
            }
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossWaterSeal(id, monsterId, mPos, point);
    }
}
