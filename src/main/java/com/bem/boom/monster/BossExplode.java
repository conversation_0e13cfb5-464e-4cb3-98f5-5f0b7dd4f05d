package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Player;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.*;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class BossExplode extends IMonster {
    public static final int STATUS_BOSS_EXPLODE = 100;
    long startTime = System.currentTimeMillis();
    boolean doSkill = false;
    Map<String, List<MapPos>> mExplode = new HashMap<String, List<MapPos>>() {{
        put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 1), new MapPos(2, 0), new MapPos(2, -1)));
        put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 1), new MapPos(-2, 0), new MapPos(-2, -1)));
        put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
        put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
    }};

    public BossExplode(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        this.isBoss = true;
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0),
                new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1));
        closeToTarget = 0.5f;
        timeWaitStun = 1000;
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (doSkill) {
            doSkill = false;
            List<MapPos> lstRemoveSquare = new ArrayList<MapPos>();
            List<Bomb> aBombExplode = new ArrayList<Bomb>();
            List<MapPos> explodePos = mExplode.get(direction.toString());
            for (MapPos tmp : explodePos) {
                MapPos startPos = tmp.clone(mPos);
                int length = 0;
                SquareUnit square = map.getSquare(startPos);
                while (!square.canStopBoom) {
                    for (Player player : square.aPlayer) {
                        if (player.isAlive()) {
                            player.addDamage(atk);
                        }
                    }
                    for (Bomb bomb : square.aBomb) {
                        if (!aBombExplode.contains(bomb)) {
                            aBombExplode.add(bomb);
                        }
                    }
                    length++;
                    square = map.getSquare(startPos.x + length * direction.x, startPos.y + length * direction.y);
                }
                if (square.canDestroy) {
                    square.addDamage(1000);
                    lstRemoveSquare.add(square.mPos);
                }
                table.getAProtoBoom().add(protoExplode(startPos, direction, length - 1));
                table.checkBombExplosion(aBombExplode, lstRemoveSquare);
                startTime = System.currentTimeMillis();
            }
        } else if (System.currentTimeMillis() - startTime >= 10000) {
            if (direction != null) {
                timeStun = System.currentTimeMillis() + timeWaitStun;
                table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 2f));
                doSkill = true;
            }
        }
    }

    public GGProto.ProtoBoom.Builder protoExplode(MapPos startPos, MapPos direction, int length) {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        if (direction.equals(Input.VectorUp)) {
            builder.addAllBombLength(Arrays.asList(length, 0, 0, 0)); // up, right, down, left
        } else if (direction.equals(Input.VectorRight)) {
            builder.addAllBombLength(Arrays.asList(0, length, 0, 0)); // up, right, down, left
        } else if (direction.equals(Input.VectorDown)) {
            builder.addAllBombLength(Arrays.asList(0, 0, length, 0)); // up, right, down, left
        } else if (direction.equals(Input.VectorLeft)) {
            builder.addAllBombLength(Arrays.asList(0, 0, 0, length)); // up, right, down, left
        }
        builder.setImageId(0);
        builder.setStatus(STATUS_BOSS_EXPLODE);
        builder.setMPos(CommonProto.protoMPos(startPos.x, startPos.y));
        return builder;
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossExplode(id, monsterId, mPos, point);
    }
}
