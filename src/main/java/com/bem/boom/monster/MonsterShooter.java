package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.BoomConfig;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Bomb;
import com.bem.util.CommonProto;

import java.util.*;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterShooter extends IMonster {
    long startTime = System.currentTimeMillis();
    int count = 0;

    public MonsterShooter(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        speed = 0;
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            //move(table.getServer_time(), lstMpPlayer);
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - startTime >= 5000) {
            count++;
            List<MapPos> aDirection = Arrays.asList(Input.VectorUp, Input.VectorRight, Input.VectorDown, Input.VectorLeft);
            for (MapPos mapPos : aDirection) {
                if (!map.getSquare(mapPos.x + mPos.x, mapPos.y + mPos.y).canStopBoom) {
                    com.bem.boom.unit.SnowBall ball = new com.bem.boom.unit.SnowBall(map.getNextUnitId(), 2, pos, table);
                    ball.damage = atk;
                    ball.startToMove(table.getServer_time(), mapPos);
                    table.getAProtoAdd().add(ball.protoAdd());
                    table.getAMoveUnit().add(ball);
                }
            }
            //
            startTime = System.currentTimeMillis();
        }
        if (count == 3) {
            count = 0;
            map.getSquare(mPos).aMonster.remove(this);
            List<SquareUnit> lstSquare = map.getSquareEmpty();
            SquareUnit square = lstSquare.get(new Random().nextInt(lstSquare.size()));
            square.aMonster.add(this);
            pos = square.pos.clone();
            mPos = square.mPos.clone();
            table.getAProtoJumpPos().add(CommonProto.protoJumpPos(BoomConfig.JUMP_TELEPORT, unitId, pos));
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterShooter(id, monsterId, mPos, point);
    }
}
