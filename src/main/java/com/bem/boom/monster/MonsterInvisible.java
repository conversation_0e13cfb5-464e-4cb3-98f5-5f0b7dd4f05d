package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.MapPos;
import com.bem.boom.unit.SnowBall;

import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterInvisible extends IMonster {
    long startTime = System.currentTimeMillis();

    public MonsterInvisible(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
    }

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (mStatus.containsKey(INVISIBLE)) {
            if (System.currentTimeMillis() - startTime >= 10000) {
                mStatus.remove(INVISIBLE);
                startTime = System.currentTimeMillis();
                table.getAProtoMonsterState().add(protoStatus(-STATUS_INVISIBLE, 0f));
            }
        } else {
            if (System.currentTimeMillis() - startTime >= 5000) {
                mStatus.put(INVISIBLE, 1);
                startTime = System.currentTimeMillis();
                table.getAProtoMonsterState().add(protoStatus(STATUS_INVISIBLE, 0f));
            }
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterInvisible(id, monsterId, mPos, point);
    }
}
