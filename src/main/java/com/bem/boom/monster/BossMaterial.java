package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

public class BossMaterial extends IMonster {
    long startTime;

    public BossMaterial(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        startTime = System.currentTimeMillis();
        timeStun = System.currentTimeMillis();
        speed = 0;
        isTower = true;
        isBoss = true;
        timeWaitStun = 1000;
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0),
                new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1));
    }

    public void doAction(AbstractTable table, List<MapPos> lstMpPlayer) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            //doSkill(table);
        }
    }

    boolean doSkill = false;

    public void doSkill(AbstractTable table) {
        if (doSkill) {
            for (int i = 0; i < table.getAPlayer().size(); i++) {
                if (table.getAPlayer().get(i).isAlive()) {
                    table.getAPlayer().get(i).addDamage(atk);
                }
            }
        } else if (System.currentTimeMillis() - startTime >= 10000) {
            doSkill = true;
            timeStun = System.currentTimeMillis() + timeWaitStun;
            table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 2f));
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossMaterial(id, monsterId, mPos, point);
    }
}
