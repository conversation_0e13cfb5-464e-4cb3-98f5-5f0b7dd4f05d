package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.BoomConfig;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.effect.EffectBombRain;
import com.bem.boom.effect.EffectEggRain;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Player;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class BossWarShip extends IMonster {
    final int STEP_ANIMATION = 0;
    final int STEP_ADD_BOMB = 1;

    long timeEffect = System.currentTimeMillis();
    int step = 0;

    public BossWarShip(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        this.isBoss = true;
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0),
                new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1));
        closeToTarget = 0.5f;
        timeWaitStun = 1000;
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - timeEffect >= 10000) {
            if (step == STEP_ANIMATION) {
                table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
                step++;
                timeEffect += 1200;
                timeStun = System.currentTimeMillis() + timeWaitStun;
            } else {
                List<SquareUnit> aSquare = map.getRandomSquare(map.getSquareEmpty(), 10);//map.getRandomSquare(map.getSquareEmpty(), 10);
                for (int i = 0; i < aSquare.size(); i++) {
                    EffectBombRain effect = new EffectBombRain(table, map);
                    effect.init(aSquare.get(i).mPos.clone(), atk, 1, 10, i * 200, mPoint);
                    effect.setTargetPercent(100);
                    table.getAEffect().add(effect);
                }
                step = 0;
                timeEffect += 10000;
            }
        }
    }

    GGProto.ProtoUnitAdd.Builder protoAddEffect() {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(0);
        builder.setType(BoomConfig.TYPE_EFFECT);
        builder.addAvatar(7); // Egg
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(mPos.x, mPos.y));
        return builder;
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossWarShip(id, monsterId, mPos, point);
    }
}
