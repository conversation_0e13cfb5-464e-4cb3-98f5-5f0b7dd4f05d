package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;

import java.util.Arrays;
import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterSnowBall extends IMonster {
    long startTime = System.currentTimeMillis();

    public MonsterSnowBall(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
    }

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - startTime >= 5000) {
            List<MapPos> aDirection = Arrays.asList(Input.VectorUp, Input.VectorDown, Input.VectorLeft, Input.VectorRight);
            for (MapPos direction : aDirection) {
                com.bem.boom.unit.SnowBall ball = new com.bem.boom.unit.SnowBall(map.getNextUnitId(), 1, pos, table);
                ball.damage = atk;
                ball.startToMove(table.getServer_time(), direction);
                table.getAProtoAdd().add(ball.protoAdd());
                table.getAMoveUnit().add(ball);
            }
            //
            startTime = System.currentTimeMillis();
            timeStun = System.currentTimeMillis() + timeWaitStun;
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterSnowBall(id, monsterId, mPos, point);
    }
}
