package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.BoomConfig;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Player;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterPutBomb extends IMonster {
    final int STEP_ANIMATION = 0;
    final int STEP_ADD_BOMB = 1;

    long timeEffect = System.currentTimeMillis() - 5000;
    int step = 0;

    public MonsterPutBomb(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() >= timeStun) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (System.currentTimeMillis() - timeEffect >= 10000) {
            if (step == STEP_ANIMATION) {
                table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
                step++;
                timeEffect += 1200;
                timeStun = System.currentTimeMillis() + timeWaitStun;
            } else if (step == STEP_ADD_BOMB) {
                SquareUnit square = map.getSquare(mPos);
                Bomb bomb = new Bomb(map.getNextUnitId(), BoomConfig.BOMB_MONSTER_PUT_BOMB, atk, 9);
                bomb.setTimeStart(table.getServer_time());
                bomb.setMPos(square.mPos.clone());
                bomb.setPos(square.pos.clone());
                square.aBomb.add(bomb);
                map.getLstBom().add(bomb);
                table.getAProtoAdd().add(bomb.protoAdd(table));
                if (square.breakBomb()) {
                    bomb.setTimeStart(table.getServer_time() - bomb.getDeltaTimeWait() + 0.1f);
                }
                step = 0;
                timeEffect = System.currentTimeMillis();
            }
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterPutBomb(id, monsterId, mPos, point);
    }
}
