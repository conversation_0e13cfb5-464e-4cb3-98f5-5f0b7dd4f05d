package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.Resources;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class BossCallMonster extends IMonster {
    long startTime;
    int babyMonsterId = 2;

    public BossCallMonster(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        this.isBoss = true;
        startTime = System.currentTimeMillis();
        timeStun = System.currentTimeMillis();
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0),
                new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1));
        closeToTarget = 0.5f;
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            move(table.getServer_time());
            if (System.currentTimeMillis() - startTime >= 5000) {
                List<IMonster> aMonster = table.getAMonster();
                startTime = System.currentTimeMillis();
                List<MapPos> mP = new ArrayList<MapPos>();
                mP.add(this.mPos);
                int count = 0;
                for (IMonster monster : aMonster) {
                    if (monster.monsterId == babyMonsterId && monster.isBabyMonster && monster.isAlive()) {
                        count++;
                    }
                }
                if (count < 5) {
                    IMonster mU = Resources.mMonster.get(babyMonsterId).clone(map.getNextUnitId(), babyMonsterId, new MapPos(mP.get(0).x, mP.get(0).y), new float[]{0f, 0f});
                    if (mPoint != null) {
                        int[] point = mPoint.get(babyMonsterId);
                        if (point != null) {
                            mU.initPoint(point[0], point[1]);
                        }
                    }
                    mU.map = map;
                    mU.isBabyMonster = true;
                    aMonster.add(mU);
                    timeStun = System.currentTimeMillis() + timeWaitStun;
                    table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
                    table.getAProtoAdd().add(mU.protoAdd());
                }
            }
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossCallMonster(id, monsterId, mPos, point);
    }
}
