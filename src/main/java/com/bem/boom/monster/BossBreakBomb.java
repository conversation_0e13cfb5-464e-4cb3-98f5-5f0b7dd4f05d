package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Player;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.*;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class BossBreakBomb extends IMonster {
    long startTime = System.currentTimeMillis() - 5;
    boolean doSkill = false;

    public BossBreakBomb(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        this.isBoss = true;
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0),
                new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1));
        closeToTarget = 0.5f;
        timeWaitStun = 1800;
    }

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            if (System.currentTimeMillis() > timeStun) {
                move(table.getServer_time());
            }
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        if (doSkill && System.currentTimeMillis() - startTime >= 11200) {
            doSkill = false;
            List<MapPos> lstRemoveSquare = new ArrayList<MapPos>();
            List<Bomb> aBombExplode = new ArrayList<Bomb>();
            aBombExplode.addAll(map.getLstBom());
            table.checkBombExplosion(aBombExplode, lstRemoveSquare);
            mStatus.put(STATUS_IMMORTAL, 1);
            table.getAProtoMonsterState().add(protoStatus(STATUS_IMMORTAL, 0f));
            startTime = System.currentTimeMillis();
        } else if (!doSkill && System.currentTimeMillis() - startTime >= 10000) {
            if (direction != null) {
                timeStun = System.currentTimeMillis() + timeWaitStun;
                table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
                doSkill = true;
            }
        } else if (mStatus.containsKey(STATUS_IMMORTAL) && System.currentTimeMillis() - startTime >= 5000) {
            mStatus.remove(STATUS_IMMORTAL);
            table.getAProtoMonsterState().add(protoStatus(-STATUS_IMMORTAL, 0f));
        }
    }

    @Override
    public BossBreakBomb clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossBreakBomb(id, monsterId, mPos, point);
    }
}
