package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Item;

import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterPoison extends IMonster {
    long startTime = System.currentTimeMillis();

    public MonsterPoison(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        speed = 0.4f;
    }

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    public void doSkill(AbstractTable table) {
        SquareUnit square = map.getSquare(mPos);
        boolean addPoison = true;

        for (Item item : square.aItem) {
            if (item.getAvatar() == Item.DEAD_POISON) {
                addPoison = false;
            }
        }
        if (addPoison) {
            Item item = new Item(map.getNextUnitId(), Item.DEAD_POISON);
            item.value = atk;
            item.setRemoveTime(table.getServer_time() + 3);
            item.setSquare(square);
            square.aItem.add(item);
            table.getAItem().add(item);
            table.getAProtoAdd().add(item.protoAdd(square.pos));
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterPoison(id, monsterId, mPos, point);
    }
}
