package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.MapData;
import com.bem.boom.MapPos;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class MonsterKillBoom extends IMonster {
    long startTime;
    long curtime;
    List<IMonster> aMyMonster;

    public MonsterKillBoom(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        startTime = System.currentTimeMillis();
        aMyMonster = new ArrayList<IMonster>();
        timeStun = System.currentTimeMillis();
    }

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            move(table.getServer_time());
//            if (System.currentTimeMillis() - startTime >= 5000) {
//                startTime = System.currentTimeMillis();
//                List<MapPos> mP = new ArrayList<MapPos>();
//                mP.add(this.mPos);
//                if (aMyMonster.size() < 5) {
//                    for (int i = 0; i < mP.size(); i++) {
//                        IMonster mU = new IMonster(map.getNextUnitId(), 1, new MapPos(mP.get(i).x, mP.get(i).y));
//                        aMonster.add(mU);
//                        aMyMonster.add(mU);
//                    }
//                    timeStun = System.currentTimeMillis() + timeWaitStun;
//                    aProtoMonsterState.add(this.updateMonster(this, IMonster.STATUS_ANIMATION));
//                }
//            }
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new MonsterKillBoom(id, monsterId, mPos, point);
    }
}
