package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.Resources;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Player;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class BossCrab extends IMonster {
    static final int STATE_ALERT = 0;
    static final int STATE_ADD_MONSTER = 1;
    long startTime;
    int babyMonsterId = IMonster.MONSTER_KILLER;
    int state = 0;

    public BossCrab(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        this.speed = 0;
        this.isBoss = true;
        startTime = System.currentTimeMillis();
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-2, 0), new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0), new MapPos(2, 0),
                new MapPos(-2, -1), new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1), new MapPos(2, -1),
                new MapPos(-2, -2), new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2), new MapPos(2, -2),
                new MapPos(-2, -3), new MapPos(-1, -3), new MapPos(0, -3), new MapPos(1, -3), new MapPos(2, -3),
                new MapPos(-2, -4), new MapPos(-1, -4), new MapPos(0, -4), new MapPos(1, -4), new MapPos(2, -4));
        closeToTarget = 0.5f;
    }

    List<SquareUnit> aSquare = new ArrayList<SquareUnit>();

    public void doAction(AbstractTable table) {
        if (isAlive()) {
            if (System.currentTimeMillis() - startTime >= 5000) {
                if (state == STATE_ALERT) {
                    aSquare = map.getRandomSquare(map.getSquareEmpty(), 3);
                    for (SquareUnit square : aSquare) {
                        table.getAProtoAdd().add(CommonProto.protoAddAlertDanger(square.pos));
                    }
                    table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
                    startTime += 2000;
                    state = STATE_ADD_MONSTER;
                } else if (state == STATE_ADD_MONSTER) {
                    for (SquareUnit square : aSquare) {
                        IMonster mU = Resources.mMonster.get(babyMonsterId).clone(map.getNextUnitId(), babyMonsterId, square.mPos.clone(), new float[]{0f, 0f});
                        mU.setHp(1000);
                        mU.setAtk(1000);
                        mU.map = map;
                        table.getAMonster().add(mU);
                        table.getAProtoAdd().add(mU.protoAdd());
                    }
                    startTime = System.currentTimeMillis();
                    state = STATE_ALERT;
                }
            }
        }
    }

    @Override
    public GGProto.ProtoMonsterState.Builder protoInjured(int damage) {
        return protoStatus(STATUS_BOSS_DAMAGE, (float) damage);
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossCrab(id, monsterId, mPos, point);
    }
}
