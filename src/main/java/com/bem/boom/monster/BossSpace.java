package com.bem.boom.monster;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Bomb;
import com.bem.util.CommonProto;

import java.util.*;

/**
 * Created by LEANHVU on 9/22/2016.
 */
public class BossSpace extends IMonster {
    long startTime = System.currentTimeMillis();
    int maxBabyMonster = 6, maxCall = 4;
    int babyMonsterId = 14;

    public BossSpace(int unitId, int monsterId, MapPos mPos, float[] point) {
        super(unitId, monsterId, mPos, point);
        this.isBoss = true;
        mTarget = new HashMap<String, List<MapPos>>() {{
            put(Input.VectorRight.toString(), Arrays.asList(new MapPos(2, 0), new MapPos(2, -1)));
            put(Input.VectorLeft.toString(), Arrays.asList(new MapPos(-2, 0), new MapPos(-2, -1)));
            put(Input.VectorUp.toString(), Arrays.asList(new MapPos(-1, -2), new MapPos(0, -2), new MapPos(1, -2)));
            put(Input.VectorDown.toString(), Arrays.asList(new MapPos(-1, 1), new MapPos(0, 1), new MapPos(1, 1)));
        }};
        aCollider = Arrays.asList(new MapPos(-1, 0), new MapPos(0, 0), new MapPos(1, 0),
                new MapPos(-1, -1), new MapPos(0, -1), new MapPos(1, -1));
        closeToTarget = 0.5f;
        timeWaitStun = 1000;
    }

    public void doAction(AbstractTable table) {
        if (isAlive() && System.currentTimeMillis() > timeStun) {
            move(table.getServer_time());
            doSkill(table);
        }
    }

    List<SquareUnit> aSquare = new ArrayList<SquareUnit>();

    public void doSkill(AbstractTable table) {
        if (mStatus.containsKey(INVISIBLE)) {
            if (System.currentTimeMillis() - startTime >= 7000 && aSquare.isEmpty()) {
                List<IMonster> aMonster = table.getAMonster();
                int count = 0;
                for (IMonster monster : aMonster) {
                    if (monster.monsterId == 14 && monster.isBabyMonster && monster.isAlive()) {
                        count++;
                    }
                }
                if (count < maxBabyMonster) {
                    int numberCall = Math.min(maxCall, maxBabyMonster - count);
                    aSquare = map.getRandomSquare(map.getSquareEmpty(), numberCall);
                    for (SquareUnit square : aSquare) {
                        table.getAProtoAdd().add(CommonProto.protoAddAlertDanger(square.pos));
                    }
                }
            } else if (System.currentTimeMillis() - startTime >= 10000) {
                mStatus.remove(INVISIBLE);
                startTime = System.currentTimeMillis();
                table.getAProtoMonsterState().add(protoStatus(-STATUS_INVISIBLE, 0f));
                //
                List<IMonster> aMonster = table.getAMonster();
                for (SquareUnit square : aSquare) {
                    MonsterShooter2 mU = new MonsterShooter2(map.getNextUnitId(), babyMonsterId, square.mPos.clone(), new float[]{0f, 0f});
                    if (mPoint != null) {
                        int[] point = mPoint.get(babyMonsterId);
                        if (point != null) {
                            mU.initPoint(point[0], point[1]);
                        }
                    }

                    mU.map = map;
                    mU.isBabyMonster = true;
                    table.getAProtoAdd().add(mU.protoAdd());
                    aMonster.add(mU);
                }
                aSquare.clear();
                timeStun = System.currentTimeMillis() + timeWaitStun;
                table.getAProtoMonsterState().add(protoStatus(STATUS_ANIMATION, 1f));
            }
        } else {
            if (System.currentTimeMillis() - startTime >= 5000) {
                mStatus.put(INVISIBLE, 1);
                startTime = System.currentTimeMillis();
                table.getAProtoMonsterState().add(protoStatus(STATUS_INVISIBLE, 0f));
            }
        }
    }

    @Override
    public IMonster clone(int id, int monsterId, MapPos mPos, float[] point) {
        return new BossSpace(id, monsterId, mPos, point);
    }
}
