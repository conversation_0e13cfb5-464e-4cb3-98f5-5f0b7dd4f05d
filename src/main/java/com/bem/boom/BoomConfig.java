package com.bem.boom;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by vieth_000 on 7/14/2016.
 */
public class BoomConfig {

    public static float squareEdge = 0.55f;
    public static final float startFireLength = 4.05f;//1.375f;0.825;
    public static final float startSpeed = 2f;
    public static final float PlayerCollider = 0.15f;
    public static int rankboom = 1;
    public static int rankpet = 1;
    public static int rankclother = 1;

    public static final float baseSpeedByFrame = 0.03f;
    //
    public static final float increaseStepSpeed = 0.1f;
    public static final float increaseFireLength = 0.55f;

    //
    public static final int maxBomb = 5;
    public static final float maxSpeed = 4;
    public static final float maxFireLength = 4.05f;

    //Boom
    public static final int startBomb = 5;
    public static final float boomTimeWait = 3.0f;
    public static final float boomTimeExsit = 0;
    public static final float boomStepFireLength = 2.75f;

    // User Setting Index
    public static final int SETTING_BLOCK_ADD_FRIEND = 0;
    public static final int SETTING_TIME_GOLD_CHEST = 1;
    public static final int SETTING_TIME_GEM_CHEST = 2;
    public static final int SETTING_FIRST_GEM_CHEST = 3;

    //
    public static boolean isServer = true;
    public static boolean isClient = false;

    // SquareDistance to move
    public static final float DistanceToMove = 0.2f;
    public static final float timeBurning = 6f;
    //
    public static final int TYPE_PLAYER = 1;
    public static final int TYPE_BOMB = 2;
    public static final int TYPE_ITEM = 3;
    public static final int TYPE_SQUARE = 4;
    public static final int TYPE_SQUARE_SOFT = 5;
    public static final int TYPE_MONSTER = 6;
    public static final int TYPE_WATER = 7;
    public static final int TYPE_BUSH = 8;
    public static final int TYPE_TRAP = 9;
    public static final int TYPE_BULLET = 10;
    public static final int TYPE_ALERT_DANGER = 11;
    public static final int TYPE_DROP_SQUARE = 12;
    public static final int TYPE_EFFECT = 13;
    public static final int TYPE_SQUARE_HIDDEN = 14;
    public static final int TYPE_PITS = 15;
    public static final int TYPE_MOVE_LEFT = 16;
    public static final int TYPE_MOVE_RIGHT = 17;
    public static final int TYPE_MOVE_UP = 18;
    public static final int TYPE_MOVE_DOWN = 19;

    //
    public static Map<Integer, MapPos> mSquareDirection = new HashMap<Integer, MapPos>() {
        {
            put(TYPE_MOVE_UP, Input.VectorUp.clone());
            put(TYPE_MOVE_RIGHT, Input.VectorRight.clone());
            put(TYPE_MOVE_DOWN, Input.VectorDown.clone());
            put(TYPE_MOVE_LEFT, Input.VectorLeft.clone());
        }
    };
    // Jump Type
    public static final int JUMP_NORMAL = 0;
    public static final int JUMP_TELEPORT = 1;
    public static final int JUMP_FLASH = 2;

    public static final int BOMB_SKILL_PIG = 1000;
    public static final int BOMB_MONSTER_PUT_BOMB = 1001;
}
