package com.bem.boom;

import com.bem.AbstractTable;
import com.bem.config.CfgServer;
import com.bem.matcher.TeamObject;
import com.bem.object.ITimer;
import com.bem.object.SimulateJob;
import com.bem.util.DateUtil;
import com.bem.util.Util;
import com.k2tek.IAction;
import com.k2tek.Xerver;
import com.k2tek.common.Logs;
import com.k2tek.common.slib_Logger;
import grep.database.Database2;
import io.netty.channel.Channel;
import net.sf.json.JSONObject;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.slf4j.Logger;

import java.util.Calendar;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static org.quartz.JobBuilder.newJob;
import static org.quartz.SimpleScheduleBuilder.simpleSchedule;
import static org.quartz.TriggerBuilder.newTrigger;

/**
 * Created by vieth_000 on 10/12/2016.
 */
public class TaskMonitor implements ITimer {

    static TaskMonitor instance;

    public static TaskMonitor getInstance() {
        if (instance == null) instance = new TaskMonitor();
        return instance;
    }

    public Map<String, AbstractTable> mTable = new HashMap<String, AbstractTable>();
    ScheduledThreadPoolExecutor executor = (ScheduledThreadPoolExecutor) Executors.newScheduledThreadPool(200);
    public Map<String, ScheduledFuture<?>> mTask = new HashMap<String, ScheduledFuture<?>>();
    int threadCount = 0, timeCounter = 0;

    public int joinTable(String tableId, Channel channel) {
        if (tableId == null) {
            return 0;
        }
        AbstractTable table = mTable.get(tableId);
        return table.doAction(channel, IAction.CLIENT_RESUME, null, System.currentTimeMillis());
    }

    public void addTable(AbstractTable table) {
        mTable.put(table.getId(), table);
    }

    public void removeTable(String id) {
        mTable.remove(id);
    }

    static Scheduler sched;

    static {
        try {
            sched = new StdSchedulerFactory().getScheduler();
            sched.start();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void submit(String key, Runnable runnable, long rate) {
//        if (!mTask.containsKey(key)) {
//            mTask.put(key, executor.scheduleAtFixedRate(runnable, 0, rate, TimeUnit.MILLISECONDS));
//        }
    }

    public void cancel(JobKey key) {
//        if (mTask.containsKey(key)) {
//            mTask.get(key).cancel(false);
//            mTask.remove(key);
//        }
        try {
            sched.deleteJob(key);
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
        threadCount--;
    }

//    public static void submit(String key, Runnable runnable, long rate) {
//        if (!mTask.containsKey(key)) {
//            mTask.put(key, executor.scheduleAtFixedRate(runnable, 0, rate, TimeUnit.MILLISECONDS));
//        }
//    }
//
//    public static void cancel(String key) {
//        if (mTask.containsKey(key)) {
//            mTask.get(key).cancel(false);
//            mTask.remove(key);
//        }
//    }

    public JobKey submit(AbstractTable table, int intervals) {
        JobDetail job = newJob(SimulateJob.class).withIdentity("job_" + table.getId() + "_" + intervals, "bomb").build();
        SimpleTrigger trigger = newTrigger().withIdentity("trigger_" + table.getId() + "_" + intervals, "bomb").startAt(new Date())
                .withSchedule(simpleSchedule().withIntervalInMilliseconds(intervals).withRepeatCount(270000 / intervals)).build();
        job.getJobDataMap().put("table", table);
        job.getJobDataMap().put("intervals", intervals);
        try {
            sched.scheduleJob(job, trigger);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        threadCount++;
        return job.getKey();
    }

    public static void cancel() {

    }

    public synchronized void joinTable(String tableId, Channel channel, List<TeamObject> aTeam) {
        if (mTable.containsKey(tableId)) {
            AbstractTable table = mTable.get(tableId);

        } else {

        }
    }

    public JSONObject monitorValue() {
        JSONObject obj = new JSONObject();
        obj.put("active", executor.getActiveCount());
        obj.put("size", executor.getPoolSize());
        obj.put("largestSize", executor.getLargestPoolSize());
        obj.put("coreSize", executor.getCorePoolSize());
        obj.put("keepAlive", executor.getKeepAliveTime(TimeUnit.MILLISECONDS));
        obj.put("maxPoolSize", executor.getMaximumPoolSize());

        return obj;
    }

    void saveMonitorInfo() {
        if (timeCounter == 0) {
            try {
                int number = threadCount;
                int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
                int minute = 5 * (Calendar.getInstance().get(Calendar.MINUTE) / 5);

                String sql = String.format("INSERT INTO boom.cache_battle_job(server_id, date_created, hours, minutes, online, times)"
                                + " values(%s,%s,%s,%s,%s,%s) ON DUPLICATE KEY UPDATE online=(online * times + %s) / (times +1), times=times+1",
                        CfgServer.SERVER_ID, DateUtil.getDateYMD(new Date()), hour, minute, number, 1, number);
                Database2.rawSQL(sql);
            } catch (Exception ex) {
                Logs.error(Util.exToString(ex));
            }
        }
        if (++timeCounter % 10 == 0) timeCounter = 0;
    }

    @Override
    public void doExpireTurn(int turnId) {
        saveMonitorInfo();
        timer();
    }

    void timer() {
        try {
            Xerver.timer(this, 0, 3);
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
    }

    protected Logger getLogger() {
        return slib_Logger.root();
    }
}
