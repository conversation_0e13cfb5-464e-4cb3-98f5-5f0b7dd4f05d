package com.bem.boom;

import com.bem.boom.map.MapObject;
import com.bem.boom.map.MapResource;
import com.bem.boom.monster.IMonster;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Item;
import com.bem.dao.mapping.MapEntity;
import com.bem.util.Util;
import com.k2tek.common.slib_Logger;

import java.util.*;

/**
 * Created by vieth_000 on 7/14/2016.
 */
public class MapData {

    public static final int MODE_TEAM = 1;
    public static final int MODE_BOSS = 2;
    int[][] matrix = {
            {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
            {0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0},
            {0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 0},
            {0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0},
            {0, 0, 2, 0, 2, 0, 2, 0, 2, 1, 2, 0, 0},
            {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0},
            {0, 0, 2, 0, 2, 0, 2, 2, 2, 0, 2, 0, 0},
            {0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0},
            {0, 0, 2, 0, 2, 0, 2, 0, 2, 2, 2, 0, 0},
            {0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0},
            {0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 0, 0, 0},
            {0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0},
            {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
    };
    int mapId, unitId;
    int column, row;
    public int poisonDamage, mode;
    public SquareUnit[][] aUnit = new SquareUnit[matrix[0].length][matrix.length];
    public Map<String, SquareUnit> mPit = new HashMap<>();
    List<MapPos> mapEmpty = new ArrayList<MapPos>();
    List<MapPos> startPos = new ArrayList<MapPos>();
    public String dropItem;
    public MapEntity map;

    public MapData(int mode, List<IMonster> aMonster, int mapIds, List<Integer> unitBoss) {
        this.mode = mode;
        initMap(aMonster, mapIds, unitBoss);
    }

    void initMap(List<IMonster> aMonster, int mapIds, List<Integer> unitBoss) {
        map = MapResource.getMap(mapIds);
        try {
            dropItem = map.getDropItem();
        } catch (Exception ex) {
            slib_Logger.root().error(Util.exToString(ex) + " -> " + mapIds);
        }
        aUnit = map.getMapObject().convert();
        startPos = map.getMapObject().player_pos;
        mapId = map.getId();
        unitId = map.getMapObject().lastId;
        Collections.shuffle(startPos);
        //
        for (MapObject.MonsterUnit monster : map.getMapObject().monsters) {
            IMonster mt = monster.convert(unitBoss);
            mt.map = this;
            int[] point = map.getMPoint().get(mt.monsterId);
            mt.initPoint(point[0], point[1]);
            mt.setMPoint(map.getMPoint());
            aMonster.add(mt);
        }
        //
        if (map.getMapObject().items != null) {
            for (MapObject.ItemUnit item : map.getMapObject().items) {
                aUnit[item.posX][item.posY].aItem.add(new Item(item.id, item.itemId));
            }
        }
        //
        column = map.getMapObject().col;//aUnit[0].length;
        row = map.getMapObject().row;//aUnit.length;
        //
        for (int i = 0; i < aUnit.length; i++) {
            for (int j = 0; j < aUnit[i].length; j++) {
                aUnit[i][j].setHiddenItem(this);
                aUnit[i][j].map = this;
                if (aUnit[i][j].isPits) mPit.put(aUnit[i][j].mPos.toString(), aUnit[i][j]);
                if (BoomConfig.mSquareDirection.containsKey(aUnit[i][j].type))
                    aUnit[i][j].moveDirection = BoomConfig.mSquareDirection.get(aUnit[i][j].type).clone();
                else aUnit[i][j].moveDirection = new MapPos(0, 0);
            }
        }
    }

    public int getGold() {
        return map.getGold();
    }

    public int getGem() {
        return map.getGem();
    }

    public int getExp() {
        return map.getExp();
    }

    public synchronized int getNextUnitId() {
        return ++unitId;
    }

    public int getMapId() {
        return mapId;
    }

    public List<MapPos> getStartPos() {
        return startPos;
    }

    public SquareUnit[][] getMap() {
        return aUnit;
    }

    public void setMap(SquareUnit[][] map) {
        this.aUnit = map;
    }

    List<Bomb> lstBom = new ArrayList<Bomb>();

    public synchronized List<Bomb> getLstBom() {
        return lstBom;
    }

    public SquareUnit getSquare(MapPos mPos) {
        return getSquare(mPos.x, mPos.y);
    }

    public SquareUnit getSquare(int x, int y) {
        return x < 0 || x >= aUnit.length || y < 0 || y >= aUnit[0].length ? null : aUnit[x][y];
    }

    public SquareUnit getPosMap(int x, int y) {
        if (x < 0) {
            x = 0;
        }
        if (x >= aUnit.length) {
            x = aUnit.length - 1;
        }
        if (y < 0) {
            y = 0;
        }
        if (Math.abs(y) >= aUnit[0].length) {
            y = aUnit[0].length - 1;
        }
        return aUnit[x][y];
    }

    public int getRow() {
        return row;
    }

    public int getColumn() {
        return column;
    }

    public List<SquareUnit> getRandomSquare(List<SquareUnit> squareId, int number) {
        List<SquareUnit> rs = new ArrayList<SquareUnit>();
        if (!squareId.isEmpty()) {
            if (number > squareId.size()) {
                number = squareId.size();
            }
            while (number > 0) {
                int ran = new Random().nextInt(squareId.size());
                rs.add(squareId.get(ran));
                squareId.remove(ran);
                number--;
            }
        }
        return rs;
    }

    public List<SquareUnit> getSquareEmpty() {
        List<SquareUnit> output = new ArrayList<SquareUnit>();
        for (int i = 1; i < aUnit.length - 1; i++) {
            for (int j = 1; j < aUnit[i].length - 1; j++) {
                if (aUnit[i][j].walkAbles() && aUnit[i][j].aItem.size() < 1 && aUnit[i][j].aPlayer.size() < 1) {
                    output.add(aUnit[i][j]);
                }
            }
        }
        return output;
    }

    public List<SquareUnit> getAllSquare() {
        List<SquareUnit> output = new ArrayList<SquareUnit>();
        for (int i = 1; i < aUnit.length - 1; i++) {
            for (int j = 1; j < aUnit[i].length - 1; j++) {
                output.add(aUnit[i][j]);
            }
        }
        return output;
    }
}
