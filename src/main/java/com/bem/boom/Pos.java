package com.bem.boom;

/**
 * Created by vieth_000 on 7/14/2016.
 */
public class Pos {
    public float x, y;

    public Pos(float x, float y) {
        this.x = x;
        this.y = y;
    }

    public Pos clone() {
        return new Pos(x, y);
    }

    public void addAndRound(Pos p) {
        x += p.x;
        y += p.y;
        x = (float) Math.round(x * 1000f) / 1000f;
        y = (float) Math.round(y * 1000f) / 1000f;
    }

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public Pos v_add(Pos a, Pos b) {
        float tmpX = a.x + b.x;
        tmpX = (float) Math.round(tmpX * 1000f) / 1000f;
        float tmpY = a.y + b.y;
        tmpY = (float) Math.round(tmpY * 1000f) / 1000f;
        return new Pos(tmpX, tmpY);
    }

    public boolean equals(Pos pos) {
        return x == pos.x && y == pos.y;
    }

    public void round() {
        x = (float) Math.round(x * 1000f) / 1000f;
        y = (float) Math.round(y * 1000f) / 1000f;
    }

    public double distance(Pos pos) {
        return Math.sqrt((x - pos.x) * (x - pos.x) + (y - pos.y) * (y - pos.y));
    }

    public String toString() {
        return String.format("(%s, %s)", x, y);
    }
}
