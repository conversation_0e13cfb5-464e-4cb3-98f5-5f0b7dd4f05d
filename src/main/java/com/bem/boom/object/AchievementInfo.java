package com.bem.boom.object;

import lombok.Data;
import net.sf.json.JSONArray;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Data
public class AchievementInfo {
    int id;
    int status;
    String name, desc, dateBegin, dateEnd;
    int[] aRequired;
    List<List<Integer>> aBonus;
    int time1, time2, time3;
    int image;

    long miliStart, miliEnd;

    public void init() {
        try {
            SimpleDateFormat formater = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            miliStart = formater.parse(this.dateBegin).getTime();
            miliEnd = formater.parse(this.dateEnd).getTime();
        } catch (Exception ex) {
            miliStart = 0;
            miliEnd = 0;
            ex.printStackTrace();
        }
    }

    public boolean inTime() {
        return miliStart < System.currentTimeMillis() && System.currentTimeMillis() < miliEnd;
    }
}
