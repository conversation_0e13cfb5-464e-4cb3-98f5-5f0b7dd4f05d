package com.bem.boom.object;

import com.bem.config.CfgAchievement;
import com.bem.config.CfgMission;
import com.bem.config.CfgNewMission;
import com.bem.dao.mapping.UserEntity;
import com.bem.object.UserInfo;
import lombok.Data;
import net.sf.json.JSONArray;

import java.io.Serializable;
import java.util.Arrays;

/**
 * Created by <PERSON><PERSON> on 10/1/2014.
 */
@Data
public class MissionObject implements Serializable {
    public static final int SOLOPLAYMISSION = 1;
    public static final int MISSIONCHARACTER = 8;
    public static final int FEEDPETMISSION = 2;
    public static final int UPGRADEBOOMMISSION = 3;
    public static final int BOSSPLAYMISSION = 4;
    public static final int BUYSHOPMISSION = 100;
    public static final int TOTALMISSION = 1000;



    String desc;
    int missionType, requiredValue;
    int currentValue, notifyStatus = 0;
    JSONArray award;

    public MissionObject( int missionType, String desc, int requiredValue,String awardailyMission) {
        this.missionType = missionType;
        this.requiredValue = requiredValue;
        this.currentValue = 0;
        if(this.missionType!=TOTALMISSION) {
            this.desc = String.format(desc, requiredValue);

        }else {
            this.desc = desc;
        }
        award = new JSONArray();
        award= JSONArray.fromObject(awardailyMission);
//        if(this.missionType!=TOTALMISSION) {
//            int addSilver = 5000;
//            award.addAll(JSONArray.fromObject(Arrays.asList(CfgAchievement.GOLD, addSilver)));
//        }
//        else{
//            award.addAll(JSONArray.fromObject(Arrays.asList(CfgAchievement.GOLD, 1000)));
//
//        }
    }

    public void increaseValue(int value) {
        if(this.getMissionType()==TOTALMISSION){
            currentValue =0;
        }
            if (currentValue < requiredValue) {
                currentValue += value;
            }
        if (notifyStatus == 0 && currentValue >= requiredValue) {
            notifyStatus = 1;
        }
    }

    public boolean isComplete() {
        return currentValue >= requiredValue;
    }

    public boolean isNotifyComplete() {
        if (notifyStatus == 1) {
            notifyStatus = 2;
            return true;
        }
        return false;
    }

    public boolean isReceive(int type) {
//        System.out.println("nhan qua nay-->"+type);
//        System.out.println("notifyStatus--->"+notifyStatus);
        if (notifyStatus == 2||(notifyStatus==1&&type== MissionObject.TOTALMISSION)) {
            notifyStatus = 3;
            return true;
        }
        return false;
    }
}
