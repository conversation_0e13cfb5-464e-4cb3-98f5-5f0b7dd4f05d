package com.bem.boom.object;

import com.bem.boom.Resources;
import com.bem.object.UserResources;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.Database2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 10/3/2016.
 */
public class MyAvatar {
    final static int NUMBER_AVATAR = 21;
    //
    public final static int HERO = 0;
    public final static int BOMB_ID = 1;
    public final static int CLOTHES = 2;
    public final static int PET = 3;
    public final static int BOMB_IMAGE = 4;
    public final static int USER_AVATAR = 5;
    public final static int ACCESS_ID1 = 6;
    public final static int ACCESS_IMAGE1 = 7;
    public final static int ACCESS_ID2 = 8;
    public final static int ACCESS_IMAGE2 = 9;
    public final static int ACCESS_ID3 = 10;
    public final static int ACCESS_IMAGE3 = 11;
    public final static int ACCESS_ID4 = 12;
    public final static int ACCESS_IMAGE4 = 13;
    public final static int ACCESS_ID5 = 14;
    public final static int ACCESS_IMAGE5 = 15;
    public final static int ACCESS_ID6 = 16;
    public final static int ACCESS_IMAGE6 = 17;
    public final static int SYMBOL = 18;
    public final static int UFO = 19;
    public final static int PET_TIENHOA = 20;


    //
    List<Integer> aInt = new ArrayList<Integer>();

    public MyAvatar() {
        checkValid();
    }

    public MyAvatar(String avatar) {
        aInt = new Gson().fromJson(avatar, new TypeToken<ArrayList<Integer>>() {
        }.getType());
        checkValid();
    }

    void checkValid() {
        while (aInt.size() < NUMBER_AVATAR) {
            aInt.add(0);
        }
    }

//    public void setAvatar(List<Long> aLong) {
//        aInt.clear();
//        for (int i = 0; i < aLong.size(); i++) {
//            aInt.add(aLong.get(i).intValue());
//        }
//    }

//    public void changeCharacter(int characterId) {
//        aInt.clear();
//        aInt.add(characterId);
//        for (int i = 1; i < NUMBER_AVATAR; i++) {
//            aInt.add(0);
//        }
//    }

    public List<Integer> toList() {
        return aInt;
    }

    public void setAvatar(int index, int avatar) {
        aInt.set(index, avatar);
    }

    public int getHero() {
        return aInt.get(HERO);
    }

    public int getBombImage() {
        return aInt.get(BOMB_IMAGE);
    }

    public List<Integer> getListAccessories() {
        List<Integer> rs = new ArrayList<>();
        for (int i = ACCESS_ID1; i <= ACCESS_IMAGE6; i++) {
            rs.add(aInt.get(i));
        }
        return rs;
    }

    public boolean setListAccessories(List<Integer> lstAccess) {
        if (lstAccess == null || lstAccess.size() == 0) {
            return true;
        }
        try {
            if (lstAccess.size() > (ACCESS_IMAGE6 - ACCESS_ID1 + 1)) {
                return false;
            }
//          System.out.println("listAccess-->"+lstAccess.toString());
            for (int i = 0; i < lstAccess.size(); i++) {
                aInt.set(ACCESS_ID1 + i, lstAccess.get(i));
//                System.out.println(ACCESS_ID1 + i + "|||" + lstAccess.get(i));
            }
            return true;
        } catch (Exception ex) {

        }
        return false;
    }

    public int getUserAvatar() {
        return aInt.get(USER_AVATAR);
    }

    public void checkUfo(long userId) {
        if (getUfo() == 0) {
            setAvatar(MyAvatar.UFO, 1);
            Database2.update("user", Arrays.asList("avatar", String.valueOf(toString())), Arrays.asList("id", String.valueOf(userId)));
        }
    }

    public void checkDefaultAvatar(long userId, UserResources uRes) {
        if (aInt.get(0) == 1 && aInt.stream().mapToInt(i -> i.intValue()).sum() == 1) {
            if (uRes.getHeroes().isEmpty()) {
                if (aInt.get(0) == 1) {
                    aInt.set(0, 0);
                }
            } else {
                setAvatar(HERO, uRes.getHeroes().get(0).getHeroId());
                setAvatar(BOMB_ID, uRes.getBombs().get(0).getId());
                setAvatar(BOMB_IMAGE, uRes.getBombs().get(0).getBombId());
                if (!uRes.getPets().isEmpty()) setAvatar(PET, uRes.getPets().get(0).getPetId());
                setAvatar(CLOTHES, Resources.getHero(getHero()).getAvatarId());
                update(userId);
            }
        }
    }

    public int getIdBomb() {
        return aInt.get(BOMB_ID);
    }

    public int getClothes() {
        return aInt.get(CLOTHES);
    }

    public int getSymbol() {
        return aInt.get(SYMBOL);
    }

    public int getUfo() {
        return aInt.get(UFO);
    }

    public int getPet() {
        return aInt.get(PET);
    }

    @Override
    public String toString() {
        return new Gson().toJson(aInt);
    }

    public boolean update(long userId) {
        return Database2.update("user", Arrays.asList("avatar", String.valueOf(toString())), Arrays.asList("id", String.valueOf(userId)));
    }
}
