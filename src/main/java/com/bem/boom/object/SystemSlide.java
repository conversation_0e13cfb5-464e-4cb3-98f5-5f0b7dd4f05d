package com.bem.boom.object;

import javax.xml.crypto.Data;
import java.util.*;

/**
 * Created by LEANHVU on 5/18/2017.
 */
public class SystemSlide {
    private static List<MessageSlide> lstMessage = new ArrayList<>();

    public static void addMessage(String message) {
        MessageSlide msg = new MessageSlide(message);
        entryList(msg);
    }

    public static synchronized List<MessageSlide> entryList(MessageSlide addData) {
        if (addData != null && addData.message != null && addData.message.trim().length() > 0) {
            if (lstMessage.size() >= 3) {
                lstMessage.remove(0);
            }

            lstMessage.add(addData);
        }
        return lstMessage;
    }
}
