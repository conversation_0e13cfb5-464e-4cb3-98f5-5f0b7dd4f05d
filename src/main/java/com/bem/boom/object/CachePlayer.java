package com.bem.boom.object;

import com.bem.boom.Resources;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgGlobal;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.UserItemEntity;
import com.bem.dao.mapping.UserPetEntity;
import com.bem.object.ResShopItem;
import com.bem.object.Skill;
import com.bem.object.UserInfo;
import com.bem.object.UserInt;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 4/20/2017.
 */
@Data
public class CachePlayer implements Serializable {
    Point point;
    Skill userSkill, petSkill;
    int team, trophy, rankId, reviveItem;
    long userId;
    String name, username;
    List<Integer> avatars, battleItems;

    public CachePlayer(UserInfo user, int team) {
        this.team = team;
        this.trophy = user.getDbUser().getTrophy();
        this.userId = user.getId();
        this.name = user.getDbUser().getName();
        this.username = user.getDbUser().getUsername();
        this.avatars = user.getMAvatar().toList();
        this.rankId = user.getDbUser().getRank();
        UserPetEntity uPet = user.getRes().getMPet().get(user.getMAvatar().getPet());
        if (uPet != null) {
            List<Skill> aSkill = uPet.getSkill();
            if (!aSkill.isEmpty()) {
                petSkill = aSkill.get(0);
            }
        }
        user.getPoint().reloadPoint(user.getRes(), user.getMAvatar(), user.getDbUser().getLevel());
        this.point = user.getPoint().clone();
        if (user.getUsername().contains("mashi")) {
            this.point.point[Point.ATK] = 10000;
            this.point.point[Point.CUR_ATK] = 10000;
            this.point.point[Point.HP] = 500000;
            this.point.point[Point.CUR_HP] = 500000;
        }
        this.userSkill = Resources.getHero(user.getMAvatar().getHero()).getSkill();
        UserItemEntity uItem = user.getRes().getMItem().get(ResShopItem.REVIVE);
        if (uItem != null && uItem.getNumber() > 0) reviveItem = 1;
        this.battleItems = new ArrayList<>();
        uItem = user.getRes().getMItem().get(user.getUData().getUInt().getValue(UserInt.BATTLE_ITEM_1));
        if (uItem != null && uItem.getNumber() > 0) {
            battleItems.add((int) uItem.getItemId());
            battleItems.add(CfgGlobal.getNumberItem(uItem.getItemId(), uItem.getNumber()));
        }
        uItem = user.getRes().getMItem().get(user.getUData().getUInt().getValue(UserInt.BATTLE_ITEM_2));
        if (uItem != null && uItem.getNumber() > 0) {
            battleItems.add((int) uItem.getItemId());
            battleItems.add(CfgGlobal.getNumberItem(uItem.getItemId(), uItem.getNumber()));
        }
    }
}
