package com.bem.boom.object;

import com.bem.boom.BoomConfig;
import com.bem.boom.Resources;
import com.bem.boom.unit.Item;
import com.bem.config.CfgCommon;
import com.bem.config.CfgTienhoapet;
import com.bem.dao.mapping.*;
import com.bem.object.*;
import com.google.gson.Gson;
import com.k2tek.common.Logs;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 1/3/2017.
 */
public class Point implements Serializable {

    public static int BOOM_MIN = 0;
    public static int BOOM_MAX = 1;
    public static int LENGTH_MIN = 2;
    public static int LENGTH_MAX = 3;
    public static int SPEED_MIN = 4;
    public static int SPEED_MAX = 5;
    public static int HP = 6;
    public static int ATK = 7;
    public static int NUMBER_BOOM = 8;
    public static int SPEED = 9;
    public static int BOOM_LENGTH = 10;
    public static int CUR_HP = 11;
    public static int CUR_ATK = 12;
    public static int DECREASE_DAMAGE = 13;
    public static int ISZOMBIE = 14;

    public final static int zombie = 1;
    public final static int zombie2 = 2;
    public final static int zombie3 = 3;
    public final static int[] zombieSkill = {0, 5, 1, 2};
    public final static int notzombie = 0;

    static final List<Integer> aAddValue = Arrays.asList(BOOM_MIN, BOOM_MAX, LENGTH_MIN, LENGTH_MAX);

    int numberPoint = 15;
    //
    public float[] point = new float[numberPoint];
    float addSpeedMin = 0, addSpeedMax = 0;

    public Point() {
    }

    public Point(UserInfo user) {
        reloadPoint(user.getRes(), user.getMAvatar(), user.getDbUser().getLevel());
    }

    public void addZombie(int isZombie) {
        point[ISZOMBIE] = isZombie;
    }

    public boolean isZombie() {
        return point[ISZOMBIE] != notzombie;
    }

    public float typeZombie() {
        return point[ISZOMBIE];
    }

    public void reloadPoint(UserResources res, MyAvatar mAvatar, int userLevel) {
        point = new float[numberPoint];
        addSpeedMin = 0;
        addSpeedMax = 0;
        UserHeroEntity uHero = res.mHero.get(mAvatar.getHero());
        if (uHero != null) {
            ResHero hero = Resources.getHero(uHero.getHeroId());
            loadHero(hero, uHero.getLevel());
        }
        UserPetEntity uPet = res.mPet.get(mAvatar.getPet());
        if (uPet != null) {
            loadPet(uPet);
        }
        UserAvatarEntity uAvatar = res.mAvatar.get(mAvatar.getClothes());
        if (uAvatar != null) {
            ResAvatar avatar = Resources.getAvatar(uAvatar.getAvatarId());
            if (avatar == null) {
                Logs.debug("what is avatar = " + uAvatar.getAvatarId());
            }
            loadAvatar(avatar, uAvatar.getLevel());
        }
        UserBombEntity uBomb = res.mBomb.get(mAvatar.getIdBomb());
        if (uBomb != null) {
            ResBomb bomb = Resources.getBomb(uBomb.getBombId());
            loadBomb(bomb, uBomb.getLevel());
        }
        List<Integer> aAccess = mAvatar.getListAccessories();
        for (int i = 0; i < aAccess.size(); i += 2) {
            UserAccessoriesEntity uAccess = res.mAccessories.get(aAccess.get(i));
            if (uAccess != null) {
                ResAccessories access = Resources.getAccessories(uAccess.getAccessoriesId());
                loadAccessory(access, uAccess.getLevel());
            }
        }
        UserSymbolEntity uSymbol = res.mSymbol.get(mAvatar.getSymbol());
        if (uSymbol != null) {
            ResSymbol symbol = Resources.getSymbol(uSymbol.getSymbolId());
            if (symbol != null) {
                loadSymbol(symbol);
            }
        }
        UserUfoEntity uUfo = res.mUfo.get(mAvatar.getUfo());
        if (uUfo != null) {
            loadUfo(uUfo);
        }
        point[SPEED_MAX] = point[SPEED_MIN] * CfgCommon.config.battle.baseSpeed[1] + (addSpeedMax + point[SPEED_MAX] - point[SPEED_MIN]) * CfgCommon.config.battle.baseSpeed[0];
        point[SPEED_MIN] = point[SPEED_MIN] * CfgCommon.config.battle.baseSpeed[1] + addSpeedMin * CfgCommon.config.battle.baseSpeed[0];
        point[LENGTH_MAX] = point[LENGTH_MAX];// * BoomConfig.squareEdge + BoomConfig.squareEdge / 2;
        point[LENGTH_MIN] = point[LENGTH_MIN];// * BoomConfig.squareEdge + BoomConfig.squareEdge / 2;
        //
        point[NUMBER_BOOM] = point[BOOM_MIN];
        point[BOOM_LENGTH] = point[LENGTH_MIN];
        point[CUR_HP] = point[HP];
        point[CUR_ATK] = point[ATK];
        point[SPEED] = point[SPEED_MIN];
    }

    public void loadHero(ResHero hero, int level) {
        point[BOOM_MIN] = hero.getBoom_min();
        point[BOOM_MAX] = hero.getBoom_max();
        point[LENGTH_MIN] = hero.getLength_min();
        point[LENGTH_MAX] = hero.getLength_max();
        point[SPEED_MIN] = hero.getSpeed_min();
        point[SPEED_MAX] = hero.getSpeed_max();
        point[HP] = hero.getHp() + hero.getShp() * (level - 1);
        point[ATK] = hero.getAtk() + hero.getSatk() * (level - 1);
    }

    public void loadSymbol(ResSymbol symbol) {
        point[ATK] += symbol.getAtk();
        point[HP] += symbol.getHp();
    }

    public void loadUfo(UserUfoEntity ufo) {
        point[ATK] += ufo.getAtk();
        point[HP] += ufo.getHp();
    }

    public void loadBomb(ResBomb bomb, int level) {
        point[ATK] += bomb.getAtk() + bomb.getSAtk() * (level - 1);
    }

    public void loadAvatar(ResAvatar avatar, int level) {
        point[HP] += avatar.getHp() + avatar.getSHp() * (level - 1);
        point[ATK] += avatar.getAtk() + avatar.getSAtk() * (level - 1);
    }

    public void loadAccessory(ResAccessories access, int level) {
        point[HP] += access.getHp() + access.getShp() * (level - 1);
        point[ATK] += access.getAtk() + access.getSAtk() * (level - 1);
    }

    public void loadPet(UserPetEntity uPet) {
        ResPet rPet = Resources.getPet(uPet.getPetId());
        int hp = rPet.getHp() + (uPet.getLevel() - 1) * rPet.getSHp() * (uPet.getStar() + 1);
        int atk = rPet.getAtk() + (uPet.getLevel() - 1) * rPet.getSAtk() * (uPet.getStar() + 1);
        List<Skill> aSkill = uPet.getSkill();
        for (Skill skill : aSkill) {
            for (Skill.SkillDetail detail : skill.data) {
                switch (detail.id) {
                    case Item.SKILL_ADD_POINT: {
                        for (int i = 0; i < detail.value.length; i += 2) {
                            int index = detail.value[i];
                            if (index == SPEED_MIN) {
                                addSpeedMin = detail.value[i + 1];
                            } else if (index == SPEED_MAX) {
                                addSpeedMax = detail.value[i + 1];
                            } else if (aAddValue.contains(index)) {
                                point[index] += detail.value[i + 1];
                            } else {
                                if (index == HP) {
                                    hp += hp * detail.value[i + 1] / 100;
                                } else if (index == ATK) {
                                    atk += atk * detail.value[i + 1] / 100;
                                }
                            }
                        }
                        break;
                    }
                }
            }
        }
        if (uPet.getTienhoalv() > 0) {
            try {
                int increase = rPet.evolutionStat.get(uPet.getTienhoalv() - 1);
                hp += hp * (100 + increase) / 100;
                atk += atk * (100 + increase) / 100;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        point[HP] += hp;
        point[ATK] += atk;
    }

    public int addHp(int hp) {
        point[CUR_HP] += hp;
        point[CUR_HP] = point[CUR_HP] < 0 ? 0 : point[CUR_HP];
        point[CUR_HP] = point[CUR_HP] > point[HP] ? point[HP] : point[CUR_HP];
        return (int) point[CUR_HP];
    }

    public int addAtk(int atk) {
        point[CUR_ATK] += atk;
        return (int) point[CUR_ATK];
    }

    public int addDecreaseDamage(int value) {
        point[DECREASE_DAMAGE] += value;
        return (int) point[DECREASE_DAMAGE];
    }

    public void addSpeed(int rate) {
        if (rate > 0) {
            point[SPEED] = Math.min(point[SPEED_MAX], point[SPEED] + rate * CfgCommon.config.battle.baseSpeed[0]);
        } else if (rate < 0) {
            point[SPEED] = Math.max(point[SPEED_MIN], point[SPEED] + rate * CfgCommon.config.battle.baseSpeed[0]);
        }
    }

    public void addBombNumber(int rate) {
        if (rate > 0) {
            point[NUMBER_BOOM] = Math.min(point[BOOM_MAX], point[NUMBER_BOOM] + rate);
        } else if (rate < 0) {
            point[NUMBER_BOOM] = Math.max(point[BOOM_MIN], point[NUMBER_BOOM] + rate);
        }
    }

    public void addBombLength(int rate) {
        if (rate > 0) {
            point[BOOM_LENGTH] = Math.min(point[LENGTH_MAX], point[BOOM_LENGTH] + rate);
        } else if (rate < 0) {
            point[BOOM_LENGTH] = Math.max(point[LENGTH_MIN], point[BOOM_LENGTH] + rate);
        }
    }

    public void addSpeedMin(int rate) {
        point[SPEED_MIN] += rate;
    }

    public void addBombNumberMin(int rate) {
        point[BOOM_MIN] += rate;
    }

    public void addBombLengthMin(int rate) {
        point[LENGTH_MIN] += rate;
    }

    public Point clone() {
        Point point = new Point();
        for (int i = 0; i < this.point.length; i++) {
            point.point[i] = this.point[i];
        }
        return point;
    }

    public String toString() {
        return new Gson().toJson(point);
    }
}
