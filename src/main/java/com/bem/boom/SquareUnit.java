package com.bem.boom;

import com.bem.AbstractTable;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.config.CfgDropItem;
import com.bem.config.CfgNewDropItem;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 7/14/2016.
 */
public class SquareUnit {
    public static final int TYPE_EMPTY = 0;
    public static final int TYPE_ROCK = 4;
    public static final int TYPE_STEEL = 5;
    public static final int TYPE_WATER = 7;
    public static final int SUBTYPE_BOWER = 8; // bui
    public static final int TYPE_TRAP = 9; // nail
    public static final int TYPE_SQUARE_HIDDEN = 14;
    public static final int TYPE_PITS = 15;
    public static final int TYPE_MOVE_LEFT = 16;
    public static final int TYPE_MOVE_RIGHT = 17;
    public static final int TYPE_MOVE_UP = 18;
    public static final int TYPE_MOVE_DOWN = 19;

    //
    public int id;
    public int type = 0;
    public int imgId = 0, hp = 1, beDamage;
    public int item = 0;
    public Pos pos;
    public MapPos mPos, moveDirection = new MapPos(0, 0);
    public List<Bomb> aBomb = new ArrayList<Bomb>();
    public List<Item> aItem = new ArrayList<Item>();
    public List<Player> aPlayer = new ArrayList<Player>();
    public List<IMonster> aMonster = new ArrayList<IMonster>();
    public boolean canMove = false;
    public boolean canBornItem = false;
    public boolean canStopBoom = false;
    public boolean canDestroy = false;
    public boolean canTrap = false;
    public boolean isPits = false;
    public Item hiddenItem;
    public MapData map;

    public void setType(int type) {
        this.type = type;
        switch (type) {
            case SquareUnit.TYPE_EMPTY:
                canMove = true;
                canBornItem = false;
                canStopBoom = false;
                canDestroy = false;
                canTrap = false;
                break;
            case SquareUnit.TYPE_ROCK:
                canMove = false;
                canBornItem = true;
                canStopBoom = true;
                canDestroy = true;
                canTrap = false;
                break;
            case SquareUnit.TYPE_STEEL:
                canMove = false;
                canBornItem = false;
                canStopBoom = true;
                canDestroy = false;
                canTrap = false;
                break;
            case SquareUnit.TYPE_WATER:
                canMove = false;
                canBornItem = false;
                canStopBoom = false;
                canDestroy = false;
                canTrap = false;
                break;
            case SquareUnit.SUBTYPE_BOWER:
                canMove = true;
                canBornItem = false;
                canStopBoom = false;
                canDestroy = true;
                canTrap = false;
                break;
            case SquareUnit.TYPE_TRAP:
                canMove = true;
                canBornItem = false;
                canStopBoom = false;
                canDestroy = imgId >= 1000;
                canTrap = true;
                break;
            case SquareUnit.TYPE_PITS:
                isPits = true;
            case SquareUnit.TYPE_MOVE_UP:
            case SquareUnit.TYPE_MOVE_DOWN:
            case SquareUnit.TYPE_MOVE_LEFT:
            case SquareUnit.TYPE_MOVE_RIGHT:
            case SquareUnit.TYPE_SQUARE_HIDDEN:
                canMove = true;
                canBornItem = false;
                canStopBoom = false;
                canDestroy = false;
                canTrap = false;
                break;
        }
    }

    public void removeItem(AbstractTable table) {
        for (int index = aItem.size() - 1; index >= 0; index--) {
            if (!Arrays.asList(Item.DEAD_POISON, Item.BEAR_TRAP, Item.TRAP_DAMAGE).contains(aItem.get(index).getItemId())) {
                table.getAProtoAdd().add(aItem.get(index).protoRemove());
                aItem.remove(index);
            }
        }
    }

    public void addDamage(int damage, int team, int maxTargetPercent) {
        for (Player player : aPlayer) {
            if (team != player.user.team) {
                int maxTargetDamage = (int) (maxTargetPercent * player.user.getPoint(Point.HP) / 100);
                player.addDamage(damage > maxTargetDamage ? maxTargetDamage : damage);
            }
        }
        for (IMonster monster : aMonster) {
            if (monster.isAlive() && team < 10) {
//                int maxTargetDamage = maxTargetPercent * monster.maxHp / 100;
//                monster.addDamage(damage > maxTargetDamage ? maxTargetDamage : damage);
                monster.addDamage(damage);
            }
        }
    }


    public void addDamage(int damage) {
        beDamage = beDamage < damage ? damage : beDamage;
    }

    public void clear() {
        setType(TYPE_EMPTY);
        aBomb = new ArrayList<Bomb>();
        aItem = new ArrayList<Item>();
        aMonster = new ArrayList<IMonster>();
    }

    public SquareUnit(int type, MapPos mPos, int id) {
        setType(type);
        this.id = id;
        this.mPos = mPos;
    }

    public boolean breakBomb() {
        return canTrap && imgId <= 1000;
    }

    public boolean walkAbles() {
        return walkAbles(null);
    }

    public boolean walkAbles(IMonster monster) {
        boolean isBoomEmpty = aBomb.isEmpty();
        if (monster != null) {
            if (monster.getMonsterId() == IMonster.BOSS_SPACE) {
                return !(mPos.x == 0 || mPos.y == 0 || mPos.x == map.column - 1 || mPos.y == map.row - 1);
            }
            if (monster.getMonsterId() == IMonster.KILL_BOOM) {
                isBoomEmpty = true;
            }
        }
        return canMove && isBoomEmpty;
    }

    public void moveTheBomb(float serverTime, MapPos direction) {
        for (Bomb bomb : aBomb) {
            bomb.pushTheBomb(serverTime, direction);
        }
    }

    public boolean destroy(MapData map, AbstractTable table) {
        if (canDestroy) {
            if (beDamage == 0) {
                return false;
            }
            int damage = beDamage;
            beDamage = 0;
            if (damage >= hp) {
                hp = 0;
                setType(SquareUnit.TYPE_EMPTY);
                table.getAProtoAdd().add(protoRemove());
                dropItem(table.getAProtoAdd());
                table.numberBreakSquare++;
                return true;
            } else if (damage > 0) {
                hp -= damage;
                table.getAProtoMonsterState().add(protoStatus(IMonster.STATUS_INJURED, (float) hp));
            }
        }
        return false;
    }

    public void setHiddenItem(MapData map) {
        if (canBornItem) {
            int itemId = CfgNewDropItem.getItemId(map.mode);
            if (itemId > 0) {
                hiddenItem = new Item(map.getNextUnitId(), itemId);
            }
        }
    }

    public void dropItem(List<GGProto.ProtoUnitAdd.Builder> aProtoAdd) {
//        if (canBornItem && new Random().nextInt(100) >= 50) {
//            Item item = new Item(map.getNextUnitId(), map.mapId, TeamObject.BOSS);
//            if (item.getItemId() > 0) {
//                aItem.add(item);
//                aProtoAdd.add(item.protoAdd(pos));
//            }
//        }
        if (hiddenItem != null) {
            aItem.add(hiddenItem);
            aProtoAdd.add(hiddenItem.protoAdd(pos));
        }
    }

    //region Proto
    public GGProto.ProtoMonsterState.Builder protoStatus(Integer status, Float info) {
        GGProto.ProtoMonsterState.Builder builder = GGProto.ProtoMonsterState.newBuilder();
        builder.setId(id);
        builder.addStatus(status);
        builder.addInfo(info);
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoAdd() {
        return protoAdd(false);
    }

    public GGProto.ProtoUnitAdd.Builder protoAdd(boolean dropBox) {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(id);
        if (dropBox) {
            builder.setType(BoomConfig.TYPE_DROP_SQUARE);
        } else {
            builder.setType(canDestroy ? BoomConfig.TYPE_SQUARE_SOFT : BoomConfig.TYPE_SQUARE);
        }
        builder.addAvatar(imgId);
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoRemove() {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(id);
        builder.setAdd(false);
        return builder;
    }
    //endregion

    public String toString() {
        return String.format("(%s, %s)", mPos.x, mPos.y);
    }
}

