package com.bem.boom;

import com.proto.custom.XInput;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 7/14/2016.
 */
public class Input {
    public static final String[] keys = {"Up", "Down", "Left", "Right", "UpRight", "UpLeft", "BottomRight", "BottomLeft"};

    public static final int NONE = -2;
    public static final int Right = 0;
    public static final int RightUp = 1;
    public static final int UpRight = 2;
    public static final int Up = 3;
    public static final int UpLeft = 4;
    public static final int LeftUp = 5;
    public static final int Left = 6;
    public static final int LeftDown = 7;
    public static final int DownLeft = 8;
    public static final int Down = 9;
    public static final int DownRight = 10;
    public static final int RightDown = 11;
    //
    public static final MapPos VectorRight = new MapPos(1, 0);
    public static final MapPos VectorUp = new MapPos(0, -1);
    public static final MapPos VectorLeft = new MapPos(-1, 0);
    public static final MapPos VectorDown = new MapPos(0, 1);
    public static final MapPos VectorCur = new MapPos(0, 0);

    //
    public static final Map<Integer, MapPos[]> mapInput = new HashMap<Integer, MapPos[]>() {{
        put(Right, new MapPos[]{VectorRight});
        put(RightUp, new MapPos[]{VectorRight, VectorUp});
        put(UpRight, new MapPos[]{VectorUp, VectorRight});
        put(Up, new MapPos[]{VectorUp});
        put(UpLeft, new MapPos[]{VectorUp, VectorLeft});
        put(LeftUp, new MapPos[]{VectorLeft, VectorUp});
        put(Left, new MapPos[]{VectorLeft});
        put(LeftDown, new MapPos[]{VectorLeft, VectorDown});
        put(DownLeft, new MapPos[]{VectorDown, VectorLeft});
        put(Down, new MapPos[]{VectorDown});
        put(DownRight, new MapPos[]{VectorDown, VectorRight});
        put(RightDown, new MapPos[]{VectorRight, VectorDown});
    }};

    public static final int PutBomb = 20;
    public static final int UseItem1 = 21;
    public static final int InputPlayerSkill = 22;
    public static final int InputPetSkill = 23;
    public static final int InputReviveItem = 24;
    public static final int ThrowBomb = 25;
    public static final int Teleport = 26;
    public static final int InputPlayerItem = 27;
    public static List<Integer> logInput = Arrays.asList(PutBomb, UseItem1, InputPlayerSkill, InputPetSkill, InputReviveItem, ThrowBomb);
    public XInput data;

    public Input(XInput data) {
        this.data = data;
    }

    static int mainDegree = 10, subDegree = 10;
    static final int[] DegreeMap = {mainDegree, 45 - subDegree, 90 - mainDegree, 90 + mainDegree, 135 - subDegree, 180 - mainDegree, 180 + mainDegree,
            225 - subDegree, 270 - mainDegree, 270 + mainDegree, 315 + subDegree, 360 - mainDegree};

    public int inputFromAngle(float degree) {
        for (int i = 0; i < DegreeMap.length; i++) {
            if (degree < DegreeMap[i]) {
                return i;
            }
        }
        return 0;
    }
}
