package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.config.CfgCommon;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 2/2/2017.
 */
public class EffectAddDamage extends IEffect {

    Player player;
    int damage, team, targetPercent;
    float timeEffect;

    public EffectAddDamage(AbstractTable table, MapData map) {
        super(table, map);
    }

    public void init(Player player, int damage, int team, float timeAdd) {
        this.player = player;
        this.damage = damage;
        this.timeEffect = timeAdd;
        this.team = team;
    }

    public boolean doEffect() {
        if (table.getServer_time() >= timeEffect) {
            if (player.isAlive()) {
                List<MapPos> aMPos = new ArrayList<MapPos>();
                int startX = player.mPos.x - CfgCommon.config.petLucifer, startY = player.mPos.y - CfgCommon.config.petLucifer;
                for (int i = 0; i <= CfgCommon.config.petLucifer * 2; i++) {
                    for (int j = 0; j <= CfgCommon.config.petLucifer * 2; j++) {
                        int x = startX + i, y = startY + j;
                        SquareUnit square = map.getSquare(x, y);
                        if (square != null) aMPos.add(new MapPos(x, y));
                    }
                }
                for (MapPos mPos : aMPos) {
                    SquareUnit square = map.getSquare(mPos);
                    if (square != null) {
                        square.addDamage(damage, team, targetPercent);
                        for (Player tmp : square.aPlayer) {
                            if (tmp.user.team != team && tmp.user.getPoint(Point.SPEED) > tmp.user.getPoint(Point.SPEED_MIN)) {
                                tmp.user.point.addSpeed(-1);
                                table.getAProtoPlayerState().add(tmp.protoSpeed());
                                List<SquareUnit> aSquare = map.getRandomSquare(map.getSquareEmpty(), 1);
                                if (aSquare.size() > 0) {
                                    Item tmpItem = new Item(table.getMap().getNextUnitId(), Item.ITEM_SHOES);
                                    table.getAProtoAdd().add(tmpItem.protoAdd(square.pos));
                                    table.getAProtoJumpPos().add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, tmpItem.getId(), aSquare.get(0).pos));
                                    aSquare.get(0).aItem.add(tmpItem);
                                }
                            }
                        }
                    }
                }
                timeEffect = 10000;
            }
            return false;
        }
        return true;
    }

    public void setTargetPercent(int targetPercent) {
        this.targetPercent = targetPercent;
    }
}
