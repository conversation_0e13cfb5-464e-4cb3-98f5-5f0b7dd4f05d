package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.boom.monster.IMonster;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 2/2/2017.
 */
public class EffectBombRain extends IEffect {

    final int STATUS_BOSS_EXPLODE = 100;
    final int STEP_ALERT = 0;
    final int STEP_ADD_EFFECT_1 = 1;
    final int STEP_ADD_EFFECT_2 = 2;
    final int STEP_DAMAGE = 3;

    MapPos mPos;
    int damage, team, targetPercent;
    long timeEffect;
    int step = 0, length;
    Map<Integer, int[]> mPoint;

    public EffectBombRain(AbstractTable table, MapData map) {
        super(table, map);
    }

    public void init(MapPos mPos, int damage, int length, int team, long timeAdd, Map<Integer, int[]> mPoint) {
        this.mPos = mPos;
        this.damage = damage;
        this.length = length;
        this.timeEffect = System.currentTimeMillis() + timeAdd;
        this.mPoint = mPoint;
        this.team = team;
    }

    public boolean doEffect() {
        if (System.currentTimeMillis() >= timeEffect) {
            if (step == STEP_ALERT) {
                int startX = mPos.x - 1, startY = mPos.y - 1;
                for (int i = 0; i < 3; i++) {
                    for (int j = 0; j < 3; j++) {
                        int x = startX + i, y = startY + j;
                        if (map.getSquare(x, y) != null)
                            table.getAProtoAdd().add(CommonProto.protoAddAlertDanger(map.getSquare(x, y).pos));
                    }
                }
                timeEffect += 1000;
                step++;
            } else if (step == STEP_ADD_EFFECT_1) {
                table.getAProtoAdd().add(protoAdd());
                timeEffect += 500;
                step++;
            } else if (step == STEP_ADD_EFFECT_2) {
                table.getAProtoBoom().add(protoExplode(mPos));
                timeEffect += 100;
                step++;
            } else if (step == STEP_DAMAGE) {
                map.getSquare(mPos.x, mPos.y).addDamage(damage, team, targetPercent);
                for (int i = 1; i <= length; i++) {
                    if (map.getSquare(mPos.x + i, mPos.y) != null) map.getSquare(mPos.x + i, mPos.y).addDamage(damage, team, targetPercent);
                    if (map.getSquare(mPos.x - i, mPos.y) != null) map.getSquare(mPos.x - i, mPos.y).addDamage(damage, team, targetPercent);
                    if (map.getSquare(mPos.x, mPos.y + i) != null) map.getSquare(mPos.x, mPos.y + i).addDamage(damage, team, targetPercent);
                    if (map.getSquare(mPos.x, mPos.y - i) != null) map.getSquare(mPos.x, mPos.y - i).addDamage(damage, team, targetPercent);
                }
                timeEffect += 60000000;
                // add new monster
                List<IMonster> aMonster = table.getAMonster();
                int count = 0;
                for (IMonster monster : aMonster) {
                    if (monster.monsterId == 24 && monster.isBabyMonster && monster.isAlive()) {
                        count++;
                    }
                }
                if (count < 5) {
                    IMonster mU = Resources.mMonster.get(24).clone(map.getNextUnitId(), 24, mPos.clone(), new float[]{0f, 0f});
                    int[] point = mPoint.get(24);
                    if (point != null) {
                        mU.initPoint(point[0], point[1]);
                    }
                    mU.map = map;
                    mU.isBabyMonster = true;
                    aMonster.add(mU);
                    table.getAProtoAdd().add(mU.protoAdd());
                }
                return false;
            }
        }
        return true;
    }

    GGProto.ProtoUnitAdd.Builder protoAdd() {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(0);
        builder.setType(BoomConfig.TYPE_EFFECT);
        builder.addAvatar(7); // Egg
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(mPos.x, mPos.y));
        return builder;
    }

    public GGProto.ProtoBoom.Builder protoExplode(MapPos startPos) {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        builder.addAllBombLength(Arrays.asList(length, length, length, length)); // up, right, down, left
        builder.setImageId(1);
        builder.setStatus(STATUS_BOSS_EXPLODE);
        builder.setMPos(CommonProto.protoMPos(startPos.x, startPos.y));
        return builder;
    }

    public void setTargetPercent(int targetPercent) {
        this.targetPercent = targetPercent;
    }
}
