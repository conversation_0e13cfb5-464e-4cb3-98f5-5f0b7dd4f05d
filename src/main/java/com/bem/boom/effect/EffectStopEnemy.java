package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.MapData;
import com.bem.boom.PlayerStatus;
import com.bem.boom.monster.IMonster;
import com.bem.boom.unit.Player;
import com.bem.config.CfgCommon;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 2/13/2017.
 */
public class EffectStopEnemy extends IEffect {

    Player player;
    int timeEffect;
    float timeStartEffect, timeEndEffect;
    List<IMonster> effMonster = new ArrayList<>();
    boolean isAddEffect = false;

    public EffectStopEnemy(AbstractTable table, MapData map) {
        super(table, map);
    }

    public void init(Player player, int timeEffect) {
        this.player = player;
        this.timeEffect = timeEffect;
        this.timeStartEffect = table.getServer_time() + 2;
    }

    public boolean doEffect() {
        if (table.getServer_time() > timeStartEffect && !isAddEffect) {
            if (player.isAlive()) {
                List<IMonster> aMonster = table.getAMonster();
                for (IMonster monster : aMonster) {
                    if (monster.isAlive()) {
                        if (Math.abs(player.mPos.x - monster.mPos.x) <= CfgCommon.config.petHorse && Math.abs(player.mPos.y - monster.mPos.y) <= CfgCommon.config.petHorse) {
                            monster.speedBack = monster.speed;
                            monster.speed = 0;
                            effMonster.add(monster);
                            table.getAProtoMonsterState().add(monster.protoStatus(IMonster.STATUS_EFFECT, (float) PlayerStatus.EFFECT_FREEZE));
                        }
                    }
                }
                for (Player tmpPlayer : table.getAPlayer()) {
                    if (tmpPlayer.isOnlyAlive() && tmpPlayer.user.team != player.user.team) {
                        if (Math.abs(player.mPos.x - tmpPlayer.mPos.x) <= CfgCommon.config.petHorse && Math.abs(player.mPos.y - tmpPlayer.mPos.y) <= CfgCommon.config.petHorse) {
                            tmpPlayer.addStatus(PlayerStatus.FREEZE, table.getServer_time(), timeEffect, 0);
                            table.getAProtoPlayerState().add(tmpPlayer.protoStatus(Arrays.asList(PlayerStatus.UPDATE_SPEED, PlayerStatus.USER_EFFECT), Arrays.asList(0f, (float) PlayerStatus.EFFECT_FREEZE)));
                        }
                    }
                }
                timeEndEffect = table.getServer_time() + timeEffect;
                isAddEffect = true;
            } else {
                return false;
            }
        } else if (table.getServer_time() > timeEndEffect && isAddEffect) {
            effMonster.forEach(monster -> {
                if (monster.isAlive() && monster.speedBack > 0) {
                    monster.speed = monster.speedBack;
                    monster.speedBack = 0;
                    // table.getAProtoMonsterState().add(monster.protoStatus(IMonster.STATUS_EFFECT, (float) -PlayerStatus.EFFECT_FREEZE));
                }
            });
            return false;
        }
        return true;
    }
}
