package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.BoomConfig;
import com.bem.boom.MapData;
import com.bem.boom.Pos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Item;
import com.bem.config.CfgNewDropItem;
import com.bem.util.CommonProto;

import java.util.List;

/**
 * Created by vieth_000 on 2/2/2017.
 */
public class EffectSuddenItem extends IEffect {
    public EffectSuddenItem(AbstractTable table, MapData map) {
        super(table, map);
    }

    float timeEffect = 120;

    public boolean doEffect() {
        if (table.getServer_time() >= timeEffect) {
            List<Integer> aItem = CfgNewDropItem.getSuddenDeathItem(map.mode);
            List<SquareUnit> aSquare = map.getRandomSquare(map.getSquareEmpty(), aItem.size());
            for (int i = 0; i < aSquare.size(); i++) {
                Item tmpItem = new Item(map.getNextUnitId(), aItem.get(i));
                table.getAProtoAdd().add(tmpItem.protoAdd(new Pos(0, 0)));
                aSquare.get(i).aItem.add(tmpItem);
                table.getAProtoJumpPos().add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, tmpItem.getId(), aSquare.get(i).pos));
            }
            return false;
        }
        return true;
    }

}
