package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.MapData;
import com.bem.boom.MapPos;
import com.bem.boom.PlayerStatus;
import com.bem.boom.SquareUnit;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by vieth_000 on 2/13/2017.
 */
public class EffectMermaid extends IEffect {

    static final int STATE_INIT = 0;
    static final int STATE_EFFECT = 1;

    Player player;
    float timeStartEffect, timeEndEffect;
    int damage, state = 0, targetPercent, team;
    Item item;
    List<SquareUnit> aSquare = new ArrayList<>();
    MapPos mPos;

    public EffectMermaid(AbstractTable table, MapData map) {
        super(table, map);
    }

    public void init(Player player, int percent, float timeEffect) {
        this.player = player;
        this.timeStartEffect = table.getServer_time() + 1;
        this.timeEndEffect = timeStartEffect + timeEffect;
        this.damage = (int) (player.user.getPoint(Point.CUR_ATK) * percent / 100);
        this.targetPercent = 70;
        this.team = player.user.team;
    }

    public void init(MapPos mPos, int damage, float timeEffect) {
        this.timeStartEffect = table.getServer_time() + 1;
        this.timeEndEffect = timeStartEffect + timeEffect;
        this.damage = damage;
        this.mPos = mPos;
        this.team = -1;
    }


    public boolean doEffect() {
        if (table.getServer_time() > timeEndEffect) {
            table.getAProtoAdd().add(item.protoRemove());
            return false;
        } else if (table.getServer_time() > timeStartEffect) {
            if (state == STATE_INIT) {
                aSquare.clear();
                if (player != null) mPos = player.mPos;
                int startX = mPos.x - 1, startY = mPos.y - 1;
                for (int i = 0; i < 3; i++) {
                    for (int j = 0; j < 3; j++) {
                        int x = startX + i, y = startY + j;
                        SquareUnit square = map.getSquare(x, y);
                        if (square != null) aSquare.add(square);
                    }
                }
                item = new Item(map.getNextUnitId(), Item.ITEM_MERMAID);
                table.getAProtoAdd().add(item.protoAdd(map.getSquare(mPos).pos));
                state = STATE_EFFECT;
            } else if (state == STATE_EFFECT) {
                aSquare.forEach(square -> {
                    if (team != -1)
                        for (IMonster monster : square.aMonster) {
                            monster.addDamage(damage);
                        }

                    for (Player player : square.aPlayer) {
                        if (team != player.user.team && player.isAlive()) {
                            int maxTargetDamage = (int) (targetPercent * player.user.getPoint(Point.HP) / 100);
                            player.addDamage(damage > maxTargetDamage ? maxTargetDamage : damage);
                            if (!(player.hasStatus(PlayerStatus.AUTO_PUT_BOM) || player.hasStatus(PlayerStatus.CHAOTIC))) {
                                int ran = new Random().nextInt(2);
                                if (ran == 0) {
                                    if (!player.user.point.isZombie()) {
                                        player.addStatus(PlayerStatus.AUTO_PUT_BOM, table.getServer_time(), 15, 0);
                                        table.getAProtoPlayerState().add(player.protoStatus(PlayerStatus.AUTO_PUT_BOM, 0f));
                                    }
                                } else {
                                    player.addStatus(PlayerStatus.CHAOTIC, table.getServer_time(), 15, 0);
                                    table.getAProtoPlayerState().add(player.protoStatus(PlayerStatus.CHAOTIC, 0f));
                                }
                            }
                        }
                    }
                });
            }
        }
        return true;
    }

}
