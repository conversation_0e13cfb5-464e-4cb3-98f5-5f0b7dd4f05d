package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.MapData;
import com.bem.boom.PlayerStatus;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Player;

import java.util.List;

/**
 * Created by vieth_000 on 2/13/2017.
 */
public class EffectPoison extends IEffect {
    public EffectPoison(AbstractTable table, MapData map) {
        super(table, map);
        damage = map.poisonDamage;
    }

    int damage;
    float lastEffect = 4;

    public boolean doEffect() {
        if (table.getServer_time() > lastEffect) {
            lastEffect += 5;
            List<Player> aPlayer = table.getAPlayer();
            for (Player player : aPlayer) {
                if (player.isOnlyAlive() && !player.isBurning()) {
                    player.decreaseHp(damage, false);
                } else if (player.isBurning()) {
                    return false;
                }
            }
        }
        return true;
    }
}
