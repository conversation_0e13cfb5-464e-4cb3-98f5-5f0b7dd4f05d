package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.MapData;
import com.bem.boom.SquareUnit;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;

/**
 * Created by vieth_000 on 2/2/2017.
 */
public class EffectDropBox extends IEffect {
    public EffectDropBox(AbstractTable table, MapData map) {
        super(table, map);
        leftX = 1;
        rightX = map.getColumn() - 2;
        topY = 2;
        botY = map.getRow() - 2;
    }

    int x = 1, y = 1, addX = 1, addY = 0;
    float lastEffect = 180;
    float nextTime = 0.5f;
    int leftX, rightX;
    int topY, botY;

    public boolean doEffect() {
        if (table.getServer_time() >= lastEffect) {
            lastEffect = table.getServer_time() + nextTime;
            SquareUnit square = map.getSquare(x, y);
            if (!square.aBomb.isEmpty()) {
                table.explodeBomb(square.aBomb.get(0));
            }
            for (Item item : square.aItem) {
                table.getAProtoAdd().add(item.protoRemove());
            }
            square.aItem.clear();
            //
            square.imgId = 1;
            square.setType(SquareUnit.TYPE_STEEL);
            table.getAProtoAdd().add(square.protoAdd(true));
            for (IMonster monster : square.aMonster) {
                if (monster.isAlive()) {
                    //monster.addDamage(monster.hp);
                    monster.setHp(0);
                }
            }
            for (Player player : square.aPlayer) {
                //player.addDamage((int) player.user.getPoint(Point.CUR_HP));
                player.decreaseHp(Integer.MAX_VALUE, true);
            }
            setNextDrop();
        }
        return true;
    }

    void setNextDrop() {
        if (addX > 0) {
            if (x + addX > rightX) {
                addX = 0;
                addY = 1;
                rightX--;
            }
        } else if (addX < 0) {
            if (x + addX < leftX) {
                addX = 0;
                addY = -1;
                leftX++;
            }
        } else if (addY > 0) {
            if (y + addY > botY) {
                addY = 0;
                addX = -1;
                botY--;
            }
        } else if (addY < 0) {
            if (y + addY < topY) {
                addY = 0;
                addX = 1;
                topY++;
            }
        }
        x += addX;
        y += addY;
    }
}
