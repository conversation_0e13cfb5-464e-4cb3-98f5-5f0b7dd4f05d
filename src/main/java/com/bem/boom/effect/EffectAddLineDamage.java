package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.Input;
import com.bem.boom.MapData;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Bomb;
import com.bem.boom.unit.Player;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 2/2/2017.
 */
public class EffectAddLineDamage extends IEffect {
    public static final int STATUS_BOSS_EXPLODE = 100;
    MapPos mPos, direction;
    int damage, team, targetPercent;
    float timeEffect;
    boolean monkeySkill = false;

    public EffectAddLineDamage(AbstractTable table, MapData map, boolean monkeySkill) {
        super(table, map);
        this.monkeySkill = monkeySkill;
    }

    public void init(MapPos mPos, MapPos direction, int damage, int team, float timeAdd) {
        this.mPos = mPos;
        this.direction = direction;
        this.damage = damage;
        this.timeEffect = timeAdd;
        this.team = team;
    }

    public boolean doEffect() {
        if (table.getServer_time() >= timeEffect) {
            MapPos startPos = mPos.clone(direction);
            List<Bomb> aBombExplode = new ArrayList<>();
            int length = monkeySkill ? monekySkill(startPos, aBombExplode) : normalLineDamage(startPos, aBombExplode);
            table.getAProtoBoom().add(protoExplode(startPos, direction, length - 1));
            table.checkBombExplosion(aBombExplode, new ArrayList<MapPos>());
            timeEffect = 10000;
            return false;
        }
        return true;
    }

    int monekySkill(MapPos startPos, List<Bomb> aBombExplode) {
        int length = 0;
        SquareUnit square = map.getSquare(startPos);
        while (square != null) {
            length++;
            if (!square.canStopBoom) addDamage(square, aBombExplode);
            square = map.getSquare(square.mPos.clone(direction));
            if (length == 6) break;
        }
        return length;
    }

    int normalLineDamage(MapPos startPos, List<Bomb> aBombExplode) {
        int length = 0;
        SquareUnit square = map.getSquare(startPos);
        while (square != null && !square.canStopBoom) {
            length++;
            addDamage(square, aBombExplode);
            square = map.getSquare(square.mPos.clone(direction));
        }
        return length;
    }

    void addDamage(SquareUnit square, List<Bomb> aBombExplode) {
        for (Player player : square.aPlayer) {
            if (player.user.team != team) {
                int maxTargetDamage = (int) (targetPercent * player.user.getPoint(Point.HP) / 100);
                player.addDamage(damage > maxTargetDamage ? maxTargetDamage : damage);
            }
        }
        for (IMonster monster : square.aMonster) {
//                    int maxTargetDamage = targetPercent * monster.maxHp / 100;
//                    monster.addDamage(damage > maxTargetDamage ? maxTargetDamage : damage);
            monster.addDamage(damage);
        }
        for (Bomb bomb : square.aBomb) {
            if (!aBombExplode.contains(bomb)) {
                aBombExplode.add(bomb);
            }
        }
    }

    public GGProto.ProtoBoom.Builder protoExplode(MapPos startPos, MapPos direction, int length) {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        if (direction.equals(Input.VectorUp)) {
            builder.addAllBombLength(Arrays.asList(length, 0, 0, 0)); // up, right, down, left
        } else if (direction.equals(Input.VectorRight)) {
            builder.addAllBombLength(Arrays.asList(0, length, 0, 0)); // up, right, down, left
        } else if (direction.equals(Input.VectorDown)) {
            builder.addAllBombLength(Arrays.asList(0, 0, length, 0)); // up, right, down, left
        } else if (direction.equals(Input.VectorLeft)) {
            builder.addAllBombLength(Arrays.asList(0, 0, 0, length)); // up, right, down, left
        }
        builder.setImageId(3);
        builder.setStatus(STATUS_BOSS_EXPLODE);
        builder.setMPos(CommonProto.protoMPos(startPos.x, startPos.y));
        return builder;
    }

    public void setTargetPercent(int targetPercent) {
        this.targetPercent = targetPercent;
    }
}
