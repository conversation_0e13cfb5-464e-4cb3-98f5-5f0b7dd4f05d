package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 2/2/2017.
 */
public class EffectEggRain extends IEffect {

    final int STEP_ALERT = 0;
    final int STEP_ADD_EFFECT = 1;
    final int STEP_DAMAGE = 2;

    MapPos mPos;
    int damage, team, targetPercent;
    long timeEffect;
    int step = 0;

    public EffectEggRain(AbstractTable table, MapData map) {
        super(table, map);
    }

    public void init(MapPos mPos, int damage, int team, long timeAdd) {
        this.mPos = mPos;
        this.damage = damage;
        this.timeEffect = System.currentTimeMillis() + timeAdd;
        this.team = team;
    }

    public boolean doEffect() {
        if (System.currentTimeMillis() >= timeEffect) {
            if (step == STEP_ALERT) {
                int startX = mPos.x - 1, startY = mPos.y - 1;
                for (int i = 0; i < 3; i++) {
                    for (int j = 0; j < 3; j++) {
                        int x = startX + i, y = startY + j;
                        if (map.getSquare(x, y) != null)
                            table.getAProtoAdd().add(CommonProto.protoAddAlertDanger(map.getSquare(x, y).pos));
                    }
                }
                timeEffect += 1000;
                step++;
            } else if (step == STEP_ADD_EFFECT) {
                table.getAProtoAdd().add(protoAdd());
                timeEffect += 500;
                step++;
            } else if (step == STEP_DAMAGE) {
                int startX = mPos.x - 1, startY = mPos.y - 1;
                for (int i = 0; i < 3; i++) {
                    for (int j = 0; j < 3; j++) {
                        int x = startX + i, y = startY + j;
                        SquareUnit square = map.getSquare(x, y);
                        if (square != null) {
                            square.addDamage(damage, team, targetPercent);
                        }
                    }
                }
                timeEffect += 60000000;
                return false;
            }
        }
        return true;
    }

    GGProto.ProtoUnitAdd.Builder protoAdd() {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(0);
        builder.setType(BoomConfig.TYPE_EFFECT);
        builder.addAvatar(7); // Egg
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(mPos.x, mPos.y));
        return builder;
    }

    public void setTargetPercent(int targetPercent) {
        this.targetPercent = targetPercent;
    }
}
