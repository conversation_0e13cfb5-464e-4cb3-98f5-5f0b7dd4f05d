package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.MapData;
import com.bem.boom.SquareUnit;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.config.CfgCommon;

import java.util.List;

/**
 * Created by vieth_000 on 2/13/2017.
 */
public class EffectFireTail extends IEffect {

    Player player;
    float timeEndEffect;
    int percent, targetPercent;

    public EffectFireTail(AbstractTable table, MapData map) {
        super(table, map);
    }

    public void init(Player player, int percent, float timeEndEffect) {
        this.player = player;
        this.timeEndEffect = timeEndEffect;
        this.percent = percent;
    }

    public boolean doEffect() {
        if (table.getServer_time() > timeEndEffect) {
            return false;
        } else {
            SquareUnit square = map.getSquare(player.mPos);
            boolean addFire = true;

            for (Item item : square.aItem) {
                if (item.getAvatar() == Item.DEAD_FIRE) {
                    addFire = false;
                }
            }

            if (addFire) {
                Item item = new Item(map.getNextUnitId(), Item.DEAD_POISON);
                item.setAvatar(Item.DEAD_FIRE);
                item.teamId = player.user.team;
                item.value = (int) (player.user.getPoint(Point.CUR_ATK) * percent / 100);
                item.setRemoveTime(table.getServer_time() + CfgCommon.config.petUnicorn);
                item.setSquare(square);
                item.targetPercent = percent;
                square.aItem.add(item);
                table.getAItem().add(item);
                table.getAProtoAdd().add(item.protoAdd(square.pos));
            }
        }
        return true;
    }

    public void setTargetPercent(int targetPercent) {
        this.targetPercent = targetPercent;
    }
}
