package com.bem.boom.effect;

import com.bem.AbstractTable;
import com.bem.boom.MapData;
import com.bem.boom.MapPos;
import com.bem.boom.SquareUnit;
import com.bem.boom.unit.Bomb;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class EffectPits extends IEffect {
    final int STATUS_BOSS_EXPLODE = 100;
    final int STEP_ADD_EFFECT = 0;
    final int STEP_DAMAGE = 1;

    MapPos mPos;
    int damage, team=-1, targetPercent=100;
    long timeEffect;
    int step = 0, length;
    List<SquareUnit> aDmgSquare = new ArrayList<>();
    List<SquareUnit> aDestroySquare = new ArrayList<>();

    public EffectPits(AbstractTable table, MapData map) {
        super(table, map);
    }

    public void init(MapPos mPos, int damage, int length, long timeAdd) {
        this.mPos = mPos;
        this.damage = damage;
        this.length = length;
        this.timeEffect = System.currentTimeMillis() + timeAdd;
    }

    public boolean doEffect() {
        if (System.currentTimeMillis() >= timeEffect) {
            if (step == STEP_ADD_EFFECT) {
                int[] aLength = new int[]{0, 0, 0, 0};
                aDmgSquare.add(map.getSquare(mPos.x, mPos.y));
                for (int i = 1; i <= length; i++) {
                    aLength[0] += checkExplosive(mPos.x, mPos.y - 1);
                    aLength[1] += checkExplosive(mPos.x + 1, mPos.y);
                    aLength[2] += checkExplosive(mPos.x, mPos.y + 1);
                    aLength[3] += checkExplosive(mPos.x - 1, mPos.y);
                }
                table.getAProtoBoom().add(protoExplode(mPos, aLength));
                timeEffect += 100;
                step++;
            } else if (step == STEP_DAMAGE) {
                aDmgSquare.forEach(square -> {
                    square.removeItem(table);
                    square.addDamage(damage, team, targetPercent);
                });
                aDestroySquare.forEach(square -> {
                    square.addDamage(damage);
                    square.destroy(map, table);
                });

//                int[] aLength = new int[]{0, 0, 0, 0};
//                map.getSquare(mPos.x, mPos.y).addDamage(damage, team, targetPercent);
//                for (int i = 1; i <= length; i++) {
//                    if (map.getSquare(mPos.x + i, mPos.y) != null)
//                        map.getSquare(mPos.x + i, mPos.y).addDamage(damage, team, targetPercent);
//                    if (map.getSquare(mPos.x - i, mPos.y) != null)
//                        map.getSquare(mPos.x - i, mPos.y).addDamage(damage, team, targetPercent);
//                    if (map.getSquare(mPos.x, mPos.y + i) != null)
//                        map.getSquare(mPos.x, mPos.y + i).addDamage(damage, team, targetPercent);
//                    if (map.getSquare(mPos.x, mPos.y - i) != null)
//                        map.getSquare(mPos.x, mPos.y - i).addDamage(damage, team, targetPercent);
//                }
                return false;
            }
        }
        return true;
    }

    int checkExplosive(int x, int y) {
        SquareUnit square = map.getSquare(x, y);
        if (square == null) return 0;
        if (!square.aBomb.isEmpty()) {
            if (square.aBomb.get(0).getStatus() == Bomb.TYPE_WAIT) square.aBomb.get(0).setTimeStart(0);
            return 0;
        }
        if (square.canStopBoom) {
            if (square.canDestroy) aDestroySquare.add(square);
            return 0;
        }
        aDmgSquare.add(square);
        return 1;
    }

    public GGProto.ProtoBoom.Builder protoExplode(MapPos startPos, int[] aLength) {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        builder.addAllBombLength(Arrays.asList(aLength[0], aLength[1], aLength[2], aLength[3])); // up, right, down, left
        builder.setImageId(1);
        builder.setStatus(STATUS_BOSS_EXPLODE);
        builder.setMPos(CommonProto.protoMPos(startPos.x, startPos.y));
        return builder;
    }
}
