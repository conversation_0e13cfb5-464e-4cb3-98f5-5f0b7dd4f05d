package com.bem.boom.unit;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.util.CommonProto;
import com.proto.GGProto;
import lombok.Data;

/**
 * Created by vieth_000 on 11/3/2016.
 */
@Data
public class SnowBall extends IMove {
    public SnowBall(int unitId, Pos pos, AbstractTable table) {
        super(unitId, pos, table);
    }

    public SnowBall(int unitId, int imageId, Pos pos, AbstractTable table) {
        super(unitId, pos, table);
        this.imageId = imageId;
    }

    public void doAction(float serverTime) {
        move(serverTime);
        checkKillPlayer();
    }

    void checkKillPlayer() {
        SquareUnit curSquare = map.getSquare(mPos);
        if (!curSquare.aPlayer.isEmpty()) {
            for (int i = curSquare.aPlayer.size() - 1; i >= 0; i--) {
                Player player = curSquare.aPlayer.get(i);
                player.addDamage(damage);
            }
            table.getAProtoAdd().add(protoRemove());
        }
    }

    protected boolean moveFreely(SquareUnit targetSquare) {
        return !targetSquare.canStopBoom;
    }

    protected void checkTargetSquare(SquareUnit square, MapData map) {
        if (square.canStopBoom) {
            table.getAProtoAdd().add(protoRemove());
        }
    }

//    //region Proto
//    public GGProto.ProtoUnitAdd.Builder protoAdd() {
//        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
//        builder.setId(unitId);
//        builder.setItemId(BoomConfig.TYPE_BULLET);
//        builder.addAvatar(imageId);
//        builder.setAdd(true);
//        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
//        return builder;
//    }
//    //endregion
}
