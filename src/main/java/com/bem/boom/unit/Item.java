package com.bem.boom.unit;

import com.bem.boom.BoomConfig;
import com.bem.boom.PlayerStatus;
import com.bem.boom.Pos;
import com.bem.boom.SquareUnit;
import com.bem.object.ResShopItem;
import com.bem.util.CommonProto;
import com.proto.GGProto;

import java.util.*;

/**
 * Created by vieth_000 on 8/22/2016.
 */
public class Item {
    public static final int ITEM_BOMB = 1;
    public static final int ITEM_FIRE = 2;
    public static final int ITEM_SHOES = 3;
    public static final int ITEM_SHIELD = 4;
    public static final int ITEM_GLOVES = 5;
    public static final int ITEM_INVISIBLE = 6;
    public static final int MAX_BOMB_LENGTH = 7;
    public static final int MAX_SHOES = 8;
    public static final int PUSH_BOMB = 9;
    public static final int VIOLET_MONSTER = 10;
    public static final int BLUE_MONSTER = 11;
    public static final int RED_MONSTER = 12;
    public static final int SUPPER_MAN = 13;
    public static final int STEEL_KOIN = 14;
    public static final int SILVER_KOIN = 15;
    public static final int GOLD_KOIN = 16;
    public static final int WHITE_WALLETS = 17;
    public static final int BLUE_WALLETS = 18;
    public static final int RED_WALLETS = 19;
    public static final int CHEST = 20;
    //
    public static final int RADAR = 21;
    public static final int ANTI_RADAR = 22;
    public static final int BOMB_INVISIBLE = 23;
    public static final int ANTI_INVISIBLE = 24;
    public static final int MAGNIFY = 25;

    public static final int DRILLER = 26;
    public static final int DROP_NAIL = 27;
    public static final int STEAL_SHIELD = 28;
    public static final int BOMB_TIMER = 29;
    public static final int DARTS = 30;
    public static final int CURE = 31;
    public static final int JUMP_SHOES = 32;
    public static final int FIRST_AID_MYSELF = 33;
    public static final int RED_BULL = 34;
    public static final int BEAR_TRAP = 35;
    public static final int FIRST_AID_FRIEND = 36;
    public static final int POWER_10ATK = 37;
    public static final int POWER_50ATK = 38;
    public static final int HEAL_10HP = 39;
    public static final int HEAL_30HP = 40;
    public static final int ITEM_RECOVERY = 41;
    public static final int MAX_BOMB = 42;
    public static final int TRAP_DAMAGE = 43;
    public static final int UNKNOWN_MATERIAL = 44;
    public static final int UNKNOWN_GOLD = 45;

    // Special Item
    public static final int SPEED_DECREASE = 1001;
    public static final int DEAD_POISON = 1002;
    public static final int SPEED_DECREASE_MAX = 1003;
    public static final int SKILL_HEAL_HP = 1004;
    public static final int SKILL_POWER_ATK = 1005;
    public static final int SKILL_DECREASE_DAMAGE = 1006;
    public static final int SKILL_PUT_ITEM = 1007;
    public static final int DEAD_FIRE = 1008;
    public static final int SKILL_COLORS_HORSE = 1009;
    public static final int SKILL_LUCIFER = 1010;
    public static final int SKILL_PANDA = 1011;
    public static final int SKILL_PIG = 1012;
    public static final int SKILL_UNICORN = 1013;
    public static final int SKILL_MONKEY = 1014;
    public static final int SKILL_EGG_RAIN = 1015;
    public static final int SKILL_ADD_POINT = 1016;
    public static final int SKILL_MAX_SPEED = 1017;
    public static final int SKILL_BLOCK_DAMAGE = 1018;
    public static final int SKILL_MAX_BOMB_LENGTH = 1019;
    public static final int SKILL_MAX_BOMB_NUMBER = 1020;
    public static final int SKILL_MERMAID = 1021;
    public static final int ITEM_MERMAID = 1022;
    public static final int SKILL_FLASH = 1023;

    public static final List<Integer> activeItems = Arrays.asList(ITEM_SHIELD, RADAR, ANTI_RADAR, BOMB_INVISIBLE, ANTI_INVISIBLE, MAGNIFY,
            DRILLER, DROP_NAIL, STEAL_SHIELD, BOMB_TIMER, DARTS, CURE, JUMP_SHOES, FIRST_AID_MYSELF, RED_BULL, BEAR_TRAP, FIRST_AID_FRIEND);

    public static final List<Integer> skillItems = Arrays.asList(SKILL_MAX_BOMB_NUMBER, SKILL_MAX_BOMB_LENGTH, SKILL_BLOCK_DAMAGE, SKILL_HEAL_HP,
            SKILL_DECREASE_DAMAGE, SKILL_POWER_ATK, SKILL_PUT_ITEM, SKILL_EGG_RAIN, SKILL_MONKEY, SKILL_PANDA, SKILL_LUCIFER, SKILL_PIG, SKILL_UNICORN,
            SKILL_COLORS_HORSE, SKILL_MAX_SPEED, SKILL_MERMAID, SKILL_FLASH);

    public static final Map<Integer, int[]> mItemStatus = new HashMap<Integer, int[]>() {{
        put(PUSH_BOMB, new int[]{PlayerStatus.PUSH_BOMB, 10000});
        put(ITEM_GLOVES, new int[]{PlayerStatus.GLOVES, 10000});
        put(ITEM_INVISIBLE, new int[]{PlayerStatus.INVISIBLE, 15});
        //
        put(ITEM_SHIELD, new int[]{PlayerStatus.IMMORTAL, 5});
        put(RADAR, new int[]{PlayerStatus.RADAR, 60});
        put(ANTI_RADAR, new int[]{PlayerStatus.ANTI_RADAR, -1});
        put(BOMB_INVISIBLE, new int[]{PlayerStatus.BOMB_INVISIBLE, 15});
        put(ANTI_INVISIBLE, new int[]{PlayerStatus.ANTI_INVISIBLE, 60});
        put(MAGNIFY, new int[]{PlayerStatus.MAGNIFY, 20});
        //
        put(STEEL_KOIN, new int[]{PlayerStatus.GET_GOLD, 10});
        put(SILVER_KOIN, new int[]{PlayerStatus.GET_GOLD, 20});
        put(GOLD_KOIN, new int[]{PlayerStatus.GET_GOLD, 50});
        put(WHITE_WALLETS, new int[]{PlayerStatus.GET_GOLD, 100});
        put(BLUE_WALLETS, new int[]{PlayerStatus.GET_GOLD, 200});
        put(RED_WALLETS, new int[]{PlayerStatus.GET_GOLD, 500});
        put(CHEST, new int[]{PlayerStatus.GET_GOLD, 3000});
        put(UNKNOWN_GOLD, new int[]{PlayerStatus.GET_GOLD, 0});
    }};

    int id;
    int itemId, avatar;
    public float removeTime = 10000;
    public SquareUnit square;
    public int value, teamId = -1, targetPercent;
    static List<Integer> lstItemBoss = new ArrayList<Integer>();
    static List<Integer> lstItemSolo = new ArrayList<Integer>();

    public Item(int id, int mapId, int type) {
        this.id = id;
        if (mapId >= 2000) {//solo
            this.itemId = lstItemSolo.get(new Random().nextInt(lstItemSolo.size()));
        } else if (mapId > 0 && mapId < 2000) {// boss
            this.itemId = lstItemBoss.get(new Random().nextInt(lstItemBoss.size()));
        } else {
            this.itemId = lstItemSolo.get(new Random().nextInt(lstItemSolo.size()));
        }
        this.avatar = type;
    }

    public Item(int id, int itemId) {
        this.id = id;
        this.itemId = itemId;
        this.avatar = this.itemId;
    }

    public static void importListItem() {
        lstItemBoss = new ArrayList<Integer>();
        lstItemSolo = new ArrayList<Integer>();
        for (int i = 0; i < 20; i++) {
            lstItemBoss.add(ITEM_BOMB);
        }
        for (int i = 0; i < 20; i++) {
            lstItemBoss.add(ITEM_FIRE);
        }
        for (int i = 0; i < 20; i++) {
            lstItemBoss.add(ITEM_SHOES);
        }
        lstItemBoss.add(ITEM_GLOVES);
        lstItemBoss.add(ITEM_INVISIBLE);

        for (int i = 0; i < 5; i++) {
            lstItemBoss.add(MAX_BOMB_LENGTH);
        }
        lstItemBoss.add(MAX_SHOES);
        lstItemBoss.add(PUSH_BOMB);
        for (int i = 0; i < 2; i++) {
            lstItemBoss.add(VIOLET_MONSTER);
        }
        for (int i = 0; i < 2; i++) {
            lstItemBoss.add(BLUE_MONSTER);
        }
        for (int i = 0; i < 2; i++) {
            lstItemBoss.add(RED_MONSTER);
        }
        for (int i = 0; i < 2; i++) {
            lstItemBoss.add(SUPPER_MAN);
        }
        for (int i = 0; i < 2; i++) {
            lstItemBoss.add(STEEL_KOIN);
        }
        for (int i = 0; i < 2; i++) {
            lstItemBoss.add(SILVER_KOIN);
        }
        for (int i = 0; i < 2; i++) {
            lstItemBoss.add(GOLD_KOIN);
        }

        // solo
        for (int i = 0; i < 25; i++) {
            lstItemSolo.add(ITEM_BOMB);
        }
        for (int i = 0; i < 25; i++) {
            lstItemSolo.add(ITEM_FIRE);
        }
        for (int i = 0; i < 25; i++) {
            lstItemSolo.add(ITEM_SHOES);
        }
        for (int i = 0; i < 1; i++) {
            lstItemSolo.add(MAX_BOMB_LENGTH);
        }
        for (int i = 0; i < 1; i++) {
            lstItemSolo.add(MAX_SHOES);
        }
        lstItemSolo.add(RED_MONSTER);
        lstItemSolo.add(SUPPER_MAN);
    }

    public GGProto.ProtoUnitAdd.Builder protoAdd(Pos pos) {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(id);
        builder.setType(BoomConfig.TYPE_ITEM);
        builder.addAvatar(avatar);
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoAdd(Pos pos, int ownerId) {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(id);
        builder.setType(BoomConfig.TYPE_ITEM);
        builder.addAvatar(avatar);
        builder.addAvatar(ownerId);
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoRemove() {
        removeTime = -1;
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(id);
        builder.setAdd(false);
        return builder;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getItemId() {
        return itemId;
    }

    public int getAvatar() {
        return avatar;
    }

    public void setAvatar(int avatar) {
        this.avatar = avatar;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    public void setRemoveTime(float removeTime) {
        this.removeTime = removeTime;
    }

    public void setSquare(SquareUnit square) {
        this.square = square;
    }

}
