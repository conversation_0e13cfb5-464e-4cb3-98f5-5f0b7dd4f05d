package com.bem.boom.unit;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.util.CommonProto;
import com.proto.GGProto;

/**
 * Created by vieth_000 on 11/3/2016.
 */
public abstract class IMove {
    protected AbstractTable table;
    protected MapData map;
    protected float speed = 1;
    protected int unitId, imageId;
    protected MapPos mPos;
    protected Pos pos;
    protected MapPos direction;
    protected float timeToMove = 10001;
    public int damage = 0;

    public IMove(int unitId, Pos pos, AbstractTable table) {
        this.unitId = unitId;
        this.table = table;
        this.map = table.getMap();
        this.pos = pos.clone();
        this.mPos = IMath.getBlock(pos);
    }

    public void startToMove(float serverTime, MapPos direction) {
        this.timeToMove = serverTime;
        this.direction = direction.clone();
    }

    public boolean isMoving() {
        return timeToMove != 10001;
    }

    protected void move(float serverTime) {
        if (serverTime > timeToMove) {
            float distance = speed * 0.015f;
            SquareUnit curSquare = map.getSquare(mPos);
            SquareUnit targetSquare = map.getSquare(mPos.x + direction.x, mPos.y + direction.y);
            if (moveFreely(targetSquare)) {
                pos.x += direction.x * distance;
                pos.y -= direction.y * distance;
            } else {
                if (direction.x == 0) { // follow y
                    if (Math.abs(curSquare.pos.y - pos.y) < distance) {
                        pos.y = curSquare.pos.y;
                    } else {
                        pos.y += distance * (curSquare.pos.y > pos.y ? 1 : -1);
                    }
                } else { // follow x
                    if (Math.abs(curSquare.pos.x - pos.x) < distance) {
                        pos.x = curSquare.pos.x;
                    } else {
                        pos.x += distance * (curSquare.pos.x > pos.x ? 1 : -1);
                    }
                }
                if (curSquare.pos.x == pos.x || curSquare.pos.y == pos.y) { // stop move
                    timeToMove = 10000;
                    checkTargetSquare(targetSquare, map);
                }
            }
            pos.round();
            mPos = IMath.getBlock(pos);
        }
    }

    protected abstract boolean moveFreely(SquareUnit targetSquare);

    protected abstract void checkTargetSquare(SquareUnit targetSquare, MapData map);

    public abstract void doAction(float serverTime);

    public GGProto.ProtoUnitPos.Builder protoPos() {
        GGProto.ProtoUnitPos.Builder builder = GGProto.ProtoUnitPos.newBuilder();
        builder.setId(unitId);
        builder.setPosX(pos.x);
        builder.setPosY(pos.y);
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoRemove() {
        timeToMove = 10001;
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(unitId);
        builder.setAdd(false);
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoAdd() {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(unitId);
        builder.setType(BoomConfig.TYPE_BULLET);
        builder.addAvatar(imageId);
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        return builder;
    }
}
