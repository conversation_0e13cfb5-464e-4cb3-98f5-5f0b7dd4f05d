package com.bem.boom.unit;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.boom.object.Point;
import com.bem.util.CommonProto;
import com.google.protobuf.ByteString;
import com.proto.GGProto;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 7/14/2016.
 */
@Data
public class Bomb {
    public static final int TYPE_NORMAL = 0;
    public static final int TYPE_TIMER = 1;
    public static final int TYPE_AUTO_PUT = 2;
    //
    public static final int TYPE_WAIT = 0;
    public static final int TYPE_EXPLOSIVE = 1;
    public static final int TYPE_END = 2;
    //
    public static final int STATUS_REMOVE = 0;
    public static final int STATUS_JUMP = 1;
    public static final int STATUS_JUMP_CONTINUE = 2;
    public static final int STATUS_LANDING = 3;
    public static final int STATUS_BOSS_EXPLODE = 100;
    //
    public static final int IMG_BOMB_TIMER = 1000;
    public static final int IMG_BOMB_9SQUARE = 1001;
    int unitId;
    MapPos mPos;
    Pos pos;
    int status = 0;
    float timeStart = 0, timeExplosive = 0, deltaTimeWait = 0;
    int length = 0;
    MapPos posLeft, posRight, posUp, posDown;
    Player player;
    int[] border = {-1, -1, -1, -1, -1};
    float timeToMove = 10001, timeToJump = 10000;
    MapPos moveDirection, jumpDirection;
    int numberJump;
    int avatar, damage, removeAvatar = 1, targetPercent;
    boolean isTimer;
    public final float delayTrap = 0.0f;

    public Bomb(int unitId, Player player, boolean isTimer) {
        this.unitId = unitId;
        this.player = player;
        this.length = player.isMaxLengthPeriod() ? (int) player.user.getPoint(Point.LENGTH_MAX) : (int) player.user.getPoint(Point.BOOM_LENGTH);
        this.deltaTimeWait = isTimer ? 10000 : BoomConfig.boomTimeWait;
        this.avatar = player.user.getBombImage();
        this.damage = (int) player.user.getPoint(Point.CUR_ATK);
        if (isTimer) {
            avatar = IMG_BOMB_TIMER;
        }
        this.isTimer = isTimer;
    }

    public Bomb(int unitId, int avatar, int damage, int length) {
        this.unitId = unitId;
        this.length = length;
        this.avatar = avatar;
        this.deltaTimeWait = BoomConfig.boomTimeWait;
        this.damage = damage;
        this.player = new Player();
    }

    public GGProto.ProtoBoom.Builder throwTheBomb(float serverTime, MapPos jumpDirection, MapData map) {
        this.timeToJump = serverTime;
        this.jumpDirection = jumpDirection;
        this.numberJump = 4;
        this.timeStart = 10000;
        map.getSquare(mPos).aBomb.remove(this);
        //
        float timeTravel = 0;
        while (numberJump > 0) {
            numberJump--;
            mPos.move(jumpDirection);
            timeTravel = mPos.round(map.getColumn(), map.getRow());
            if (timeTravel > 0) {
                break;
            }
        }
        timeTravel = timeTravel > 0 ? timeTravel : 0.2f;
        this.timeToJump += timeTravel;
        return protoJump(player.unitId, jumpDirection);
    }

    public void pushTheBomb(float serverTime, MapPos direction) {
        this.timeToMove = serverTime;
        this.moveDirection = direction.clone();
    }

    public boolean isMoving() {
        boolean ret = timeToMove != 10001;
        if (timeToMove == 10000) {
            timeToMove = 10001;
        }
        return ret;
    }

    public boolean isJump() {
        boolean ret = timeToJump != 10000;
        return ret;
    }

    public void doAction(float serverTime, MapData map, List<GGProto.ProtoBoom.Builder> aProtoBoom, AbstractTable table) {
        move(serverTime, map, table);
        jumping(serverTime, map, aProtoBoom);
    }

    public void jumping(float serverTime, MapData map, List<GGProto.ProtoBoom.Builder> aProtoBoom) {
        if (serverTime > timeToJump) {
            SquareUnit square = map.getSquare(mPos);
            if (numberJump <= 0 && square.canMove && square.aBomb.isEmpty()) {
                timeStart = serverTime;
                timeToJump = 10000;
                square.aBomb.add(this);
                pos = square.pos.clone();
                aProtoBoom.add(protoLanding());
                if (map.getSquare(this.getMPos()).breakBomb()) {
                    if (this.getStatus() == Bomb.TYPE_WAIT) {
                        this.setTimeStart(serverTime - this.getDeltaTimeWait() + delayTrap);
                    }
                }
            } else { // jumping
                numberJump--;
                mPos.move(jumpDirection);
                float timeTravel = mPos.round(map.getColumn(), map.getRow());
                timeTravel = timeTravel == 0 ? 0.2f : timeTravel;
                timeToJump = serverTime + timeTravel;
                aProtoBoom.add(protoJumpContinue());
            }
        }
    }

    public void move(float serverTime, MapData map, AbstractTable table) {
        if (serverTime > timeToMove) {
            float distance = 12f * 0.015f;
            SquareUnit curSquare = map.getSquare(mPos);
            curSquare.aBomb.remove(this);
            SquareUnit targetSquare = map.getSquare(mPos.x + moveDirection.x, mPos.y + moveDirection.y);
            if (targetSquare == null) {
                timeToMove = 10000;
                return;
            }
            boolean ck = true;
            if (targetSquare.walkAbles() && targetSquare.aMonster.isEmpty() && targetSquare.aPlayer.isEmpty() && !curSquare.canTrap) {
                pos.x += moveDirection.x * distance;
                pos.y -= moveDirection.y * distance;
            } else {
                if (moveDirection.x == 0) { // follow y
                    if (Math.abs(curSquare.pos.y - pos.y) < distance) {
                        pos.y = curSquare.pos.y;
                    } else {
                        pos.y += distance * (curSquare.pos.y > pos.y ? 1 : -1);
                    }
                } else { // follow x
                    if (Math.abs(curSquare.pos.x - pos.x) < distance) {
                        pos.x = curSquare.pos.x;
                    } else {
                        pos.x += distance * (curSquare.pos.x > pos.x ? 1 : -1);
                    }
                }
                if (curSquare.pos.equals(pos)) { // stop move
                    timeToMove = 10000;
                }
                if (map.getSquare(this.getMPos()).breakBomb()) {
                    if (this.getStatus() == Bomb.TYPE_WAIT) {
                        this.setTimeStart(serverTime - this.getDeltaTimeWait() + delayTrap);
                    }
                }
            }
            pos.round();
            mPos = IMath.getBlock(pos);
            map.getSquare(mPos).aBomb.add(this);
        }
    }

    //region Proto
    public GGProto.ProtoUnitPos.Builder protoPos() {
        GGProto.ProtoUnitPos.Builder builder = GGProto.ProtoUnitPos.newBuilder();
        builder.setId(unitId);
        builder.setPosX(pos.x);
        builder.setPosY(pos.y);
        return builder;
    }

    public GGProto.ProtoBoom.Builder protoRemove() {
        return protoRemove(removeAvatar);
    }

    public GGProto.ProtoBoom.Builder protoRemove(int avatar) {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        builder.setId(unitId);
        builder.setImageId(avatar);
        builder.addAllBombLength(Arrays.asList(border[0], border[1], border[2], border[3])); // up, right, down, left
        builder.setStatus(STATUS_REMOVE);
        return builder;
    }

    public GGProto.ProtoBoom.Builder protoJump(int playerUnitId, MapPos direction) {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        builder.setId(unitId);
        builder.setPlayerUnitId(playerUnitId);
        builder.setMPos(CommonProto.protoMPos(direction.x * 5, direction.y * 5));
        builder.setStatus(STATUS_JUMP);
        return builder;
    }

    int count = 0;

    public GGProto.ProtoBoom.Builder protoJumpContinue() {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        builder.setId(unitId);
        builder.setStatus(STATUS_JUMP_CONTINUE);
        count++;
        return builder;
    }

    public GGProto.ProtoBoom.Builder protoLanding() {
        GGProto.ProtoBoom.Builder builder = GGProto.ProtoBoom.newBuilder();
        builder.setId(unitId);
        builder.setMPos(CommonProto.protoMPos(mPos.x, mPos.y));
        builder.setStatus(STATUS_LANDING);
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoAdd(AbstractTable table) {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(unitId);
        builder.setType(BoomConfig.TYPE_BOMB);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        builder.addAvatar(avatar);
        builder.addAvatar(table.isShowBombLength() ? length : 0);
        builder.addAvatar(player.unitId);
        builder.setAdd(true);
        return builder;
    }

    public static ByteString protoListBoom(List<GGProto.ProtoBoom.Builder> aBoom) {
        GGProto.ProtoListBoom.Builder builder = GGProto.ProtoListBoom.newBuilder();
        int size = aBoom.size();
        for (int i = 0; i < size; i++) {
            builder.addABoom(aBoom.get(0));
            aBoom.remove(0);
        }
        return builder.build().toByteString();
    }

//    public static byte[] flatListBoom(List<GGProto.ProtoBoom.Builder> aBoom) {
//        FlatBufferBuilder fbb = new FlatBufferBuilder(1);
//        int size = aBoom.size();
//        int[] arrBomb = new int[size];
//        for (int i = 0; i < size; i++) {
//            GGProto.ProtoBoom.Builder bomb = aBoom.get(i);
//            int aLength = ProtoBoom.createBombLengthVector(fbb, Util.listToArray(bomb.getBombLengthList()));
//            int mapPos = Util.flatMapPos(fbb, bomb.getMPos().getX(), bomb.getMPos().getY());
//
//            ProtoBoom.startProtoBoom(fbb);
//            ProtoBoom.addBombLength(fbb, aLength);
//            ProtoBoom.addMPos(fbb, mapPos);
//            ProtoBoom.addId(fbb, bomb.getId());
//            ProtoBoom.addImageId(fbb, bomb.getImageId());
//            ProtoBoom.addStatus(fbb, bomb.getStatus());
//            ProtoBoom.addPlayerUnitId(fbb, bomb.getPlayerUnitId());
//            arrBomb[i] = ProtoBoom.endProtoBoom(fbb);
//        }
//        int vector = ProtoListBoom.createABoomVector(fbb, arrBomb);
//        ProtoListBoom.startProtoListBoom(fbb);
//        ProtoListBoom.addABoom(fbb, vector);
//        int mon = ProtoListBoom.endProtoListBoom(fbb);
//        fbb.finish(mon);
//        return fbb.sizedByteArray();
//    }
    //endregion
}

