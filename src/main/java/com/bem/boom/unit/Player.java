package com.bem.boom.unit;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.boom.MapPos;
import com.bem.boom.Pos;
import com.bem.boom.effect.*;
import com.bem.boom.object.CachePlayer;
import com.bem.boom.object.Point;
import com.bem.config.CfgCommon;
import com.bem.config.CfgGlobal;
import com.bem.matcher.TeamObject;
import com.bem.object.IUser;
import com.bem.object.ResShopItem;
import com.bem.object.Skill;
import com.bem.object.UserInfo;
import com.bem.util.CommonProto;
import com.google.protobuf.ByteString;
import com.proto.GGProto.*;
import io.netty.channel.Channel;

import java.util.*;

/**
 * Created by vieth_000 on 7/14/2016.
 */
public class Player {

    static final int TARGET_MYSELF = 0;
    static final int TARGET_MY_TEAM = 1;
    static final int TARGET_OPP_TEAM = 2;

    public Channel channel;
    //    public UserInfo userInfo;
    public IUser user;
    //    public UserEntity dbUser;
    public com.bem.boom.Pos pos; // x,y
    public com.bem.boom.MapPos mPos, lastDirection = Input.VectorDown; // x,y theo block
    public List<Input> inputs = new ArrayList<Input>();
    public List<Long> inputCount = new ArrayList<Long>();
    public int leaveStatus;
    public long last_input_seq = -1;
    public double last_input_time;
    public int numberBomb = 0;
    public boolean ready = false;
    public int unitId;
    public int bonusGold, bonusExp, bonusGem;
    public Map<Integer, Integer> mBonus = new HashMap<Integer, Integer>();
    public List<Item> lstHaveItem = new ArrayList<Item>();
    public int beDamage = 0;
    public List<Integer> materials = new ArrayList<>();
    public long totalDamage;

    public float timeImmortal = 0;
    public List<PlayerStatus> aStatus = new ArrayList<PlayerStatus>();
    public Map<Integer, PlayerStatus> mStatus = new HashMap<Integer, PlayerStatus>();
    Item battleItem;
    public AbstractTable table;
    public MapData map;
    public long lastProcessInput = 0;
    float lastUseMyItem = 0;

    public Player() {
    }

    public Player(UserInfo userInfo, int team, int unitId) {
        this.user = new IUser(userInfo, team);
//        this.userInfo = userInfo;
//        this.dbUser = userInfo.getDbUser();
//        this.channel = userInfo.getChannel();
        this.unitId = unitId;
        addStatus(PlayerStatus.ALIVE, 0, 10000, 0);
        //
    }

    public Player(CachePlayer cachePlayer, int unitId, int mode) {
        this.user = new IUser(cachePlayer, mode);
//        this.userInfo = userInfo;
//        this.dbUser = userInfo.getDbUser();
//        this.channel = userInfo.getChannel();
        this.unitId = unitId;
        addStatus(PlayerStatus.ALIVE, 0, 10000, 0);
//        user.point.addBombNumber(100);
//        user.point.addBombLength(100);
//        user.point.addAtk(10000);
    }

    public void addDamage(int damage) {
        if (map.getMapId() <= 10 && damage > user.getPoint(Point.HP) / 10) {
            damage = (int) user.getPoint(Point.HP) / 10;
        }
        beDamage = beDamage < damage ? damage : beDamage;
    }

    public boolean onAir() {
        return mStatus.containsKey(PlayerStatus.FAKE_ON_AIR);
    }

    public boolean isStatus(int status) {
        return mStatus.containsKey(status);
    }

    public boolean isBurning() {
        return mStatus.containsKey(PlayerStatus.BURNING);
    }

    public boolean isOnlyAlive() {
        return mStatus.containsKey(PlayerStatus.ALIVE);
    }

    public boolean isAlive() {
        return mStatus.containsKey(PlayerStatus.ALIVE) || mStatus.containsKey(PlayerStatus.BURNING);
    }

    public boolean isImmortal() {
        return mStatus.containsKey(PlayerStatus.IMMORTAL) || mStatus.containsKey(PlayerStatus.FAKE_ON_AIR) || mStatus.containsKey(PlayerStatus.BURNING);
    }

    public boolean isGloves() {
        return mStatus.containsKey(PlayerStatus.GLOVES);
    }

    public boolean isPushBomb() {
        return mStatus.containsKey(PlayerStatus.PUSH_BOMB);
    }

    public Input getAndRemoveInput(int index) {
        Input input = inputs.get(index);
        inputs.remove(input);
        return input;
    }

    public boolean hasStatus(int status) {
        return mStatus.containsKey(status);
    }

    public void addStatus(int id, float serverTime, float timeout, int value) {
        if (timeout == -1) {
            return;
        }
        PlayerStatus status = mStatus.get(id);
        if (status == null || PlayerStatus.addPoint.contains(id)) {
            PlayerStatus newStatus = new PlayerStatus(id, serverTime + timeout, value);
            aStatus.add(newStatus);
            mStatus.put(id, newStatus);
        } else {
            status.timeout += timeout;
        }
    }

    public boolean removeStatus(int id) {
        PlayerStatus status = mStatus.get(id);
        if (status != null) {
            aStatus.remove(status);
            mStatus.remove(id);
            table.getAProtoPlayerState().add(protoStatus(-id, 0f));
            return true;
        }
        return false;
    }

    public boolean killMyself(boolean hasToDie) {
        if (beDamage == 0) {
            return false;
        }
        int damage = beDamage;
        beDamage = 0;
        if (isImmortal()) {
            return false;
        }
        if (table.getServer_time() < timeImmortal) {
            return false;
        }
        PlayerStatus status = mStatus.get(PlayerStatus.BLOCK_DAMAGE);
        if (status != null) {
            damage -= damage * status.value / 100;
        }
        return decreaseHp(damage, hasToDie);
    }

    public boolean decreaseHp(int damage, boolean hasToDie) {
        if (isOnlyAlive()) {
            if (hasToDie) {
                table.getAProtoAdd().add(protoDie());
                dropItem();
            } else {
                user.point.addHp(-damage);
                if (user.getPoint(Point.CUR_HP) == 0) { // burning
                    addStatus(PlayerStatus.BURNING, table.getServer_time(), BoomConfig.timeBurning, 0);
                    table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.UPDATE_HP, PlayerStatus.BURNING), Arrays.asList(user.getPoint(Point.CUR_HP), 0f)));
//                    table.getAProtoAdd().add(protoDie());
//                    dropItem();
                } else {
                    table.getAProtoPlayerState().add(protoStatus(PlayerStatus.UPDATE_HP, user.getPoint(Point.CUR_HP)));
                    timeImmortal = table.getServer_time() + 1;
                }
            }
            return true;
        }
        return false;
    }

    public void iAmDie() {
        table.getAProtoAdd().add(protoDie());
        dropItem();
    }

    void dropItem() {
        int number = lstHaveItem.size();
        List<SquareUnit> lstSquare = map.getRandomSquare(map.getSquareEmpty(), number);
        if (!lstSquare.isEmpty()) {
            if (lstSquare.size() < number) {
                number = lstSquare.size();
            }
            for (int i = 0; i < number; i++) {
                lstHaveItem.get(i).setId(map.getNextUnitId());
                table.getAProtoAdd().add(lstHaveItem.get(i).protoAdd(pos));
            }
            for (int i = 0; i < number; i++) {
                SquareUnit square = map.getPosMap(lstSquare.get(i).mPos.x, lstSquare.get(i).mPos.y);
                square.aItem.add(lstHaveItem.get(i));
                table.getAProtoJumpPos().add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, lstHaveItem.get(i).getId(), square.pos));
            }
        }
    }

    //region Proto
    public void updatePlayerInfo() {
        updatePlayerInfo(user.getPoint(Point.SPEED), user.getPoint(Point.NUMBER_BOOM), user.getPoint(Point.BOOM_LENGTH));
    }

    public void updatePlayerInfo(float speed, float numberBomb, float bombLength) {
        table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.UPDATE_SPEED, PlayerStatus.NUMBER_BOMB, PlayerStatus.BOMB_LENGTH),
                Arrays.asList(speed, numberBomb, bombLength)));
    }

    public ProtoUnitPos.Builder protoPos() {
        ProtoUnitPos.Builder builder = ProtoUnitPos.newBuilder();
        builder.setId(unitId);
        builder.setPosX(pos.x);
        builder.setPosY(pos.y);
        builder.setLastInputSeq(last_input_seq);
        return builder;
    }

    public ProtoUnitAdd.Builder protoAdd() {
        ProtoUnitAdd.Builder builder = ProtoUnitAdd.newBuilder();
        builder.setId(unitId);
        builder.setType(BoomConfig.TYPE_PLAYER);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        builder.setPlayerInfo(protoInfo());
        builder.setAdd(true);
        return builder;
    }

    public ProtoPlayerInfo.Builder protoInfo() {
        ProtoPlayerInfo.Builder builder = ProtoPlayerInfo.newBuilder();
        builder.setId(user.id);
        builder.setName(user.name);
        builder.setTeam(user.team);
        builder.setRank(user.rankId);
        builder.addAllAvatar(user.avatars);//mAvatar.toList()
        builder.addAPoint(user.getPoint(Point.SPEED));
        builder.addAPoint(user.getPoint(Point.CUR_HP));
        builder.addAPoint(user.getPoint(Point.CUR_ATK));
        builder.addAPoint(user.getPoint(Point.NUMBER_BOOM));
        builder.addAPoint(user.getPoint(Point.BOOM_LENGTH));
        builder.addAPoint(user.getPoint(Point.ISZOMBIE));

        if (user.reviveItem > 0) builder.addAllAItem(Arrays.asList(18, 1));
        if (table.getCacheBattle().getMode() == CacheBattle.MODE_GLOBAL) {
            for (IUser.UsedItem item : user.aUsedItem) {
                builder.addAItem(item.getItemId());
                builder.addAItem(item.getTotalNumber());
            }
        }
        return builder;
    }

    public ProtoUnitAdd.Builder protoDie() {
        aStatus.clear();
        mStatus.clear();
        ProtoUnitAdd.Builder builder = ProtoUnitAdd.newBuilder();
        builder.setId(unitId);
        builder.setAdd(false);
        return builder;
    }

    public ProtoPlayerState.Builder protoSpeed(float speed) {
        ProtoPlayerState.Builder builder = ProtoPlayerState.newBuilder();
        builder.setId(unitId);
        builder.addStatus(PlayerStatus.UPDATE_SPEED);
        builder.addInfo(speed);
        return builder;
    }

    public ProtoPlayerState.Builder protoSpeed() {
        return protoSpeed(user.getPoint(Point.SPEED));
    }

    public ProtoPlayerState.Builder protoNumberBomb() {
        return protoNumberBomb(user.getPoint(Point.NUMBER_BOOM));
    }

    public ProtoPlayerState.Builder protoNumberBomb(float numberBomb) {
        ProtoPlayerState.Builder builder = ProtoPlayerState.newBuilder();
        builder.setId(unitId);
        builder.addStatus(PlayerStatus.NUMBER_BOMB);
        builder.addInfo(numberBomb);
        return builder;
    }

    public ProtoPlayerState.Builder protoBombLength() {
        return protoBombLength(user.getPoint(Point.BOOM_LENGTH));
    }

    public ProtoPlayerState.Builder protoBombLength(float bombLength) {
        ProtoPlayerState.Builder builder = ProtoPlayerState.newBuilder();
        builder.setId(unitId);
        builder.addStatus(PlayerStatus.BOMB_LENGTH);
        builder.addInfo(bombLength);
        return builder;
    }

    public ProtoPlayerState.Builder protoStatus(Integer status, Float info) {
        ProtoPlayerState.Builder builder = ProtoPlayerState.newBuilder();
        builder.setId(unitId);
        builder.addStatus(status);
        builder.addInfo(info);
        return builder;
    }

    public ProtoPlayerState.Builder protoStatus(List<Integer> aStatus, List<Float> aInfo) {
        ProtoPlayerState.Builder builder = ProtoPlayerState.newBuilder();
        builder.setId(unitId);
        builder.addAllStatus(aStatus);
        builder.addAllInfo(aInfo);
        return builder;
    }

    public static ByteString protoListPlayerState(List<ProtoPlayerState.Builder> aPlayerState) {
        ProtoListPlayerState.Builder builder = ProtoListPlayerState.newBuilder();
        int size = aPlayerState.size();
        for (int i = 0; i < size; i++) {
            builder.addAPlayerState(aPlayerState.get(0));
            aPlayerState.remove(0);
        }
        return builder.build().toByteString();
    }
    //endregion

    //region Game Logic
    public boolean isMaxSpeedPeriod() {
        return mStatus.containsKey(PlayerStatus.MAX_SPEED_PERIOD);
    }

    public boolean isMaxBombPeriod() {
        return mStatus.containsKey(PlayerStatus.MAX_BOMB_PERIOD);
    }

    public boolean isMaxLengthPeriod() {
        return mStatus.containsKey(PlayerStatus.MAX_LENGTH_PERIOD);
    }

    public boolean isFreeze() {
        return mStatus.containsKey(PlayerStatus.FREEZE);
    }

    public void checkStatus(float serverTime) {
        List<Integer> removeStatus = new ArrayList<Integer>();
        List<Float> removeInfo = new ArrayList<Float>();
        boolean isDie = false;
        for (int i = aStatus.size() - 1; i >= 0; i--) {
            PlayerStatus status = aStatus.get(i);
            if (status.timeout < serverTime) {
                aStatus.remove(i);
                mStatus.remove(status.id);
                if (!PlayerStatus.aFakeStatus.contains(status.id)) {
                    removeStatus.add(-status.id);
                    if (status.id == PlayerStatus.POWER_ATK) {
                        user.addAtk(-status.value);
                        removeInfo.add(user.getPoint(Point.CUR_ATK));
                    } else if (status.id == PlayerStatus.DECREASE_DAMAGE) {
                        user.addDecreaseDamage(-status.value);
                        removeInfo.add(user.getPoint(Point.DECREASE_DAMAGE));
                    } else {
                        removeInfo.add(0f);
                        if (status.id == PlayerStatus.FREEZE) {
                            if (mStatus.containsKey(PlayerStatus.MAX_SPEED_PERIOD)) {
                                removeStatus.add(PlayerStatus.UPDATE_SPEED);
                                removeInfo.add(user.getPoint(Point.SPEED_MAX));
                            } else {
                                removeStatus.add(PlayerStatus.UPDATE_SPEED);
                                removeInfo.add(user.getPoint(Point.SPEED));
                            }
                        }
                    }
                } else {
                    if (status.id == PlayerStatus.MAX_BOMB_PERIOD) {
                        removeStatus.add(PlayerStatus.NUMBER_BOMB);
                        removeInfo.add(user.getPoint(Point.NUMBER_BOOM));
                    } else if (status.id == PlayerStatus.MAX_SPEED_PERIOD) {
                        if (!mStatus.containsKey(PlayerStatus.FREEZE)) {
                            removeStatus.add(PlayerStatus.UPDATE_SPEED);
                            removeInfo.add(user.getPoint(Point.SPEED));
                        }
                    } else if (status.id == PlayerStatus.MAX_LENGTH_PERIOD) {
                        removeStatus.add(PlayerStatus.BOMB_LENGTH);
                        removeInfo.add(user.getPoint(Point.BOOM_LENGTH));
                    } else if (status.id == PlayerStatus.BURNING) {
                        isDie = true;
                    }
                }
            }
        }
        if (!removeStatus.isEmpty()) {
            table.getAProtoPlayerState().add(protoStatus(removeStatus, removeInfo));
        }
        if (isDie) {
            iAmDie();
        }
    }

    public boolean addItem(float serverTime, Item item, Skill.SkillDetail detail) {
        // item su dung sau
        if (Item.activeItems.contains(item.getItemId())) {
            battleItem = item;
            table.getAProtoPlayerState().add(protoStatus(PlayerStatus.GET_BATTLE_ITEM, (float) item.getItemId()));
            lstHaveItem.add(item);
            return true;
        }
        //
        switch (item.getItemId()) {
            case Item.ITEM_BOMB: {
                int oldNumberBomb = (int) user.getPoint(Point.NUMBER_BOOM);
                user.point.addBombNumber(1);
                lstHaveItem.add(item);
                if (oldNumberBomb != (int) user.getPoint(Point.NUMBER_BOOM) && !isMaxBombPeriod()) {
                    table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.GET_ITEM, PlayerStatus.NUMBER_BOMB), Arrays.asList((float) item.getItemId(), user.getPoint(Point.NUMBER_BOOM))));
                } else {
                    table.getAProtoPlayerState().add(protoStatus(PlayerStatus.GET_ITEM, (float) item.getItemId()));
                }
                break;
            }
            case Item.MAX_BOMB: {
                int oldNumberBomb = (int) user.getPoint(Point.NUMBER_BOOM);
                user.point.addBombNumber(100);
                lstHaveItem.add(item);
                if (oldNumberBomb != (int) user.getPoint(Point.NUMBER_BOOM) && !isMaxBombPeriod()) {
                    table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.GET_ITEM, PlayerStatus.NUMBER_BOMB), Arrays.asList((float) item.getItemId(), user.getPoint(Point.NUMBER_BOOM))));
                } else {
                    table.getAProtoPlayerState().add(protoStatus(PlayerStatus.GET_ITEM, (float) item.getItemId()));
                }
                break;
            }
            case Item.ITEM_SHOES: {
                float oldSpeed = user.getPoint(Point.SPEED);
                user.point.addSpeed(1);
                lstHaveItem.add(item);
                if (oldSpeed != user.getPoint(Point.SPEED) && !isMaxSpeedPeriod()) {
                    table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.GET_ITEM, PlayerStatus.UPDATE_SPEED), Arrays.asList((float) item.getItemId(), user.getPoint(Point.SPEED))));
                } else {
                    table.getAProtoPlayerState().add(protoStatus(PlayerStatus.GET_ITEM, (float) item.getItemId()));
                }
                break;
            }
            case Item.DEAD_POISON: {
                if (item.teamId != user.team) {
                    if (item.targetPercent > 0) {
                        int maxDamage = (int) (item.targetPercent * user.getPoint(Point.HP) / 100);
                        addDamage(item.value > maxDamage ? maxDamage : item.value);
                    } else {
                        addDamage(item.value);
                    }
                    killMyself(false);
                }
            }
            case Item.SPEED_DECREASE: {
                if (user.team == item.teamId) return false;
                float oldSpeed = user.getPoint(Point.SPEED);
                user.point.addSpeed(-1);
                if (oldSpeed != user.getPoint(Point.SPEED) && !isMaxSpeedPeriod()) {
                    table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.UPDATE_SPEED), Arrays.asList(user.getPoint(Point.SPEED))));
                }
                break;
            }
            case Item.SPEED_DECREASE_MAX: {
                if (user.team == item.teamId) return false;
                float oldSpeed = user.getPoint(Point.SPEED);
                user.point.addSpeed(-100);
                if (oldSpeed != user.getPoint(Point.SPEED) && !isMaxSpeedPeriod()) {
                    table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.UPDATE_SPEED), Arrays.asList(user.getPoint(Point.SPEED))));
                }
                break;
            }
            case Item.ITEM_FIRE:
                user.point.addBombLength(1);
                lstHaveItem.add(item);
                if (!isMaxLengthPeriod()) {
                    table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.GET_ITEM, PlayerStatus.BOMB_LENGTH), Arrays.asList((float) item.getItemId(), (float) user.getPoint(Point.BOOM_LENGTH))));
                } else {
                    table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.GET_ITEM), Arrays.asList((float) item.getItemId())));
                }
                break;
            case Item.MAX_BOMB_LENGTH: {
                user.point.addBombLength(user.getIntPoint(Point.LENGTH_MAX));
                lstHaveItem.add(item);
                table.getAProtoPlayerState().add(protoStatus(PlayerStatus.GET_ITEM, (float) item.getItemId()));
                if (!isMaxLengthPeriod()) {
                    updatePlayerInfo();
                }
                break;
            }
            case Item.MAX_SHOES: {
                float oldSpeed = user.getPoint(Point.SPEED);
                user.point.addSpeed(100);
                if (oldSpeed != user.getPoint(Point.SPEED) && !isMaxSpeedPeriod()) {
                    table.getAProtoPlayerState().add(protoSpeed());
                }
                lstHaveItem.add(item);
                table.getAProtoPlayerState().add(protoStatus(PlayerStatus.GET_ITEM, (float) item.getItemId()));
                break;
            }
            case Item.VIOLET_MONSTER:
                violetMonster();
                lstHaveItem.add(item);
                break;
            case Item.BLUE_MONSTER: {
                Player player = null;
                int startIndex = Math.abs((int) System.currentTimeMillis() % table.getAPlayer().size());
                for (int i = 0; i < table.getAPlayer().size(); i++) {
                    Player tmp = table.getAPlayer().get(startIndex);
                    if (tmp.isAlive() && tmp.unitId != unitId && tmp.user.team != user.team) {
                        player = tmp;
                        break;
                    }
                    if (++startIndex == table.getAPlayer().size()) {
                        startIndex = 0;
                    }
                }
                if (player != null) {
                    List<Integer> aItem = Arrays.asList(Item.ITEM_FIRE, Item.ITEM_BOMB, Item.ITEM_SHOES);
                    int itemId = aItem.get(new Random().nextInt(aItem.size()));
                    int numberItem = 0;
                    switch (itemId) {
                        case Item.ITEM_BOMB:
                            numberItem = (int) (player.user.getPoint(Point.NUMBER_BOOM) - player.user.getPoint(Point.BOOM_MIN));
                            player.user.point.addBombNumber(-100);
                            if (!isMaxBombPeriod()) table.getAProtoPlayerState().add(player.protoNumberBomb());
                            break;
                        case Item.ITEM_SHOES:
                            numberItem = (int) ((player.user.getPoint(Point.SPEED) - player.user.getPoint(Point.SPEED_MIN)) / CfgCommon.config.battle.baseSpeed[0]);
                            player.user.point.addSpeed(-100);
                            if (!isMaxSpeedPeriod()) table.getAProtoPlayerState().add(player.protoSpeed());
                            break;
                        case Item.ITEM_FIRE:
                            numberItem = (int) ((player.user.getPoint(Point.BOOM_LENGTH) / BoomConfig.squareEdge) - player.user.getPoint(Point.LENGTH_MIN));
                            player.user.point.addBombLength(-100);
                            if (!isMaxLengthPeriod()) table.getAProtoPlayerState().add(player.protoBombLength());
                            break;
                    }
                    numberItem = numberItem == 0 ? 1 : numberItem;
//                    player.addStatus(PlayerStatus.LOSE_ITEM, serverTime, 0.5f);
                    table.getAProtoPlayerState().add(player.protoStatus(PlayerStatus.LOSE_ITEM, 0f));
                    List<SquareUnit> lstSquare = map.getRandomSquare(map.getSquareEmpty(), numberItem);
                    for (int i = 0; i < lstSquare.size(); i++) {
                        Item tmpItem = new Item(table.getMap().getNextUnitId(), itemId);
                        table.getAProtoAdd().add(tmpItem.protoAdd(player.pos));
                        table.getAProtoJumpPos().add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, tmpItem.getId(), lstSquare.get(i).pos));
                        lstSquare.get(i).aItem.add(tmpItem);
                    }
                }
                lstHaveItem.add(item);
                break;
            }
            case Item.RED_MONSTER: {
                float oldSpeed = user.getPoint(Point.SPEED);
                user.point.addSpeed(100);
                if (oldSpeed != user.getPoint(Point.SPEED) && !isMaxSpeedPeriod()) {
                    table.getAProtoPlayerState().add(protoSpeed());
                }
                addStatus(PlayerStatus.PUSH_BOMB, serverTime, 10000, 0);
                table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.PUSH_BOMB, PlayerStatus.GET_ITEM), Arrays.asList(0f, (float) item.getItemId())));
                lstHaveItem.add(item);
                break;
            }
            case Item.SUPPER_MAN: {
//                user.setPoint(Point.SPEED_BACK, user.getPoint(Point.SPEED));
//                user.setPoint(Point.NUMBER_BOOM_BACK, user.getPoint(Point.NUMBER_BOOM));
//                user.setPoint(Point.LENGTH_BACK, user.getPoint(Point.BOOM_LENGTH));

//                user.point.addSpeed(user.getIntPoint(Point.SPEED_MAX));
//                user.point.addBombNumber(user.getIntPoint(Point.BOOM_MAX));
//                user.point.addBombLength(user.getIntPoint(Point.LENGTH_MAX));
                addStatus(PlayerStatus.SUPERMAN, serverTime, 15, 0);
                addStatus(PlayerStatus.MAX_SPEED_PERIOD, serverTime, 15, 0);
                addStatus(PlayerStatus.MAX_LENGTH_PERIOD, serverTime, 15, 0);
                addStatus(PlayerStatus.MAX_BOMB_PERIOD, serverTime, 15, 0);
                table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.SUPERMAN, PlayerStatus.GET_ITEM), Arrays.asList(0f, (float) item.getItemId())));
                updatePlayerInfo(user.getPoint(Point.SPEED_MAX), user.getPoint(Point.BOOM_MAX), user.getPoint(Point.LENGTH_MAX));

                lstHaveItem.add(item);
                break;
            }
            // gold
            case Item.STEEL_KOIN:
            case Item.SILVER_KOIN:
            case Item.GOLD_KOIN:
            case Item.WHITE_WALLETS:
            case Item.BLUE_WALLETS:
            case Item.RED_WALLETS:
            case Item.UNKNOWN_GOLD:
            case Item.CHEST: {
                int[] info = Item.mItemStatus.get(item.getItemId());
                this.bonusGold += item.value > 0 ? item.value : info[1];
                table.getAProtoPlayerState().add(protoStatus(info[0], (float) (item.value > 0 ? item.value : info[1])));
                break;
            }
            // item dung luon
            case Item.PUSH_BOMB:
            case Item.ITEM_GLOVES:
            case Item.ITEM_SHIELD:
            case Item.ITEM_INVISIBLE: {
                List<Player> aPlayer = getTarget(detail == null ? 0 : detail.target);
                int[] info = Item.mItemStatus.get(item.getItemId());
                int duration = detail != null && detail.duration > 0 ? detail.duration : info[1];
                for (Player player : aPlayer) {
                    player.addStatus(info[0], serverTime, duration, 0);
                    if (detail == null) {
                        table.getAProtoPlayerState().add(player.protoStatus(Arrays.asList(info[0], PlayerStatus.GET_ITEM), Arrays.asList(0f, (float) item.getItemId())));
                        lstHaveItem.add(item);
                    } else {
                        table.getAProtoPlayerState().add(player.protoStatus(Arrays.asList(info[0]), Arrays.asList(0f)));
                    }
                }
                break;
            }
            case Item.HEAL_10HP:
                return healHp(Arrays.asList(this), 10, false);
            case Item.HEAL_30HP:
                return healHp(Arrays.asList(this), 30, false);
            case Item.POWER_10ATK:
                return powerAtk(Arrays.asList(this), 10, 10000, false);
            case Item.POWER_50ATK:
                return powerAtk(Arrays.asList(this), 50, 10000, false);
            case Item.ITEM_RECOVERY:
                return skillRecovery(item);
            case Item.TRAP_DAMAGE:
                if (item.teamId != user.team) {
                    int maxDamage = item.targetPercent > 0 ? (int) (item.targetPercent * user.getPoint(Point.HP) / 100) : (int) user.getPoint(Point.CUR_HP);
                    decreaseHp(Math.min(item.value, maxDamage), false);
                    return true;
                }
                return false;
            case Item.UNKNOWN_MATERIAL:
                materials.add(item.value);
                break;
            default:
                if (item.getItemId() > 500 && item.getItemId() < 600) {
                    if (!mBonus.containsKey(item.getItemId())) {
                        mBonus.put(item.getItemId(), 0);
                    }
                    mBonus.put(item.getItemId(), mBonus.get(item.getItemId()) + 1);
                }
                break;
        }
        return true;
    }
    //endregion

    //region Use Item
    public void usePlayerItem(Pos tmpPos, float serverTime, int itemId) {
        if (serverTime >= lastUseMyItem + 2 && user.hasItem(itemId) && CfgGlobal.config.mBattleItem.containsKey(itemId)) {
            Skill skill = CfgGlobal.config.mBattleItem.get(itemId);
            if (useSkill(skill, PlayerStatus.USE_MY_ITEM, tmpPos)) {
                lastUseMyItem = serverTime;
                user.useItem(itemId);
                table.getAProtoPlayerState().add(protoStatus(PlayerStatus.USE_MY_ITEM, (float) itemId));
            }
        }
    }

    public void useBattleItem(float serverTime, Pos tmpPos) {
        if (battleItem != null) {
            if (isUseItem(serverTime, battleItem.getItemId(), tmpPos, null)) {
                table.getAProtoPlayerState().add(protoStatus(PlayerStatus.USE_BATTLE_ITEM, (float) battleItem.getItemId()));
                battleItem = null;
            }
        }
    }

    public void usePetSkill(Pos tmpPos) {
        if (user.petSkill != null && !isStatus(PlayerStatus.PET_SKILL)) {
            useSkill(user.petSkill, PlayerStatus.PET_SKILL, tmpPos);
            if (user.petSkill.effect > 0) {
                table.getAProtoPlayerState().add(protoStatus(PlayerStatus.USER_EFFECT, (float) user.petSkill.effect));
            }
        }
    }

    public void usePlayerSkill(Pos tmpPos) {
        if (user.userSkill != null && !isStatus(PlayerStatus.USER_SKILL)) {
            useSkill(user.userSkill, PlayerStatus.USER_SKILL, tmpPos);
        }
    }

    public boolean useSkill(Skill skill, int statusId, Pos tmpPos) {
        boolean hasSkill = false;
        for (Skill.SkillDetail value : skill.data) {
            if (Item.activeItems.contains(value.id)) { // item su dung luon
                hasSkill = isUseItem(table.getServer_time(), value.id, tmpPos, value);
            } else if (Item.skillItems.contains(value.id)) { // item fake thanh skill
                hasSkill = useSkillItem(table.getServer_time(), value, tmpPos);
            } else { // item an tren map
                Item item = new Item(0, value.id);
                if (value.value.length > 0) {
                    item.value = value.value[0];
                }
                hasSkill = addItem(table.getServer_time(), item, value); // item su dung an tren ban do
            }
        }
        if (hasSkill) {
            if (statusId == PlayerStatus.USE_MY_ITEM) {

            } else {
                addStatus(statusId, table.getServer_time(), skill.time_recover, 0);
                table.getAProtoPlayerState().add(protoStatus(Arrays.asList(statusId), Arrays.asList((float) skill.time_recover)));
            }
            return true;
        }
        return false;
    }

    public void useReviveItem(float serverTime) {
        if (isBurning() && user.reviveItem == 1 && !user.point.isZombie()) {
            removeStatus(PlayerStatus.BURNING);
            healHp(Arrays.asList(this), 5, true);
            user.reviveItem = 2;
            table.getAProtoPlayerState().add(protoStatus(PlayerStatus.USE_MY_ITEM, (float) ResShopItem.REVIVE));
            timeImmortal = table.getServer_time() + 2;
        }
//        int itemId = user.aItem.get(index * 2);
//        int number = user.aItem.get(index * 2 + 1);
//        if (number > 0) {
//            if (isUseItem(serverTime, itemId, tmpPos, null)) {
//                user.aItem.set(index * 2 + 1, number - 1);
//                table.getAProtoPlayerState().add(protoStatus(PlayerStatus.USE_MY_ITEM, (float) itemId));
//            }
//        }
    }

    boolean useSkillItem(float serverTime, Skill.SkillDetail detail, Pos tmpPos) {
        switch (detail.id) {
            case Item.SKILL_HEAL_HP:
                return healHp(getTarget(detail.target), detail.value[0], true);
            case Item.SKILL_POWER_ATK:
                return powerAtk(getTarget(detail.target), detail.value[0], detail.duration, true);
            case Item.SKILL_BLOCK_DAMAGE:
                return blockDamage(getTarget(detail.target), detail.value[0], detail.duration, true);
            case Item.SKILL_DECREASE_DAMAGE:
                return decreaseDamage(detail);
            case Item.SKILL_PUT_ITEM:
                return skillPutItem(detail);
            case Item.SKILL_EGG_RAIN:
                return skillEggRain(detail);
            case Item.SKILL_MONKEY:
                return skillMonkey(detail);
            case Item.SKILL_PANDA:
                return skillPanda(detail);
            case Item.SKILL_LUCIFER:
                return skillLucifer(detail);
            case Item.SKILL_PIG:
                return skillPig(detail);
            case Item.SKILL_UNICORN:
                return skillUnicorn(detail);
            case Item.SKILL_COLORS_HORSE:
                return skillColorsHorse(detail);
            case Item.SKILL_MAX_SPEED:
                return skillMaxSpeed(detail);
            case Item.SKILL_MAX_BOMB_LENGTH:
                return skillMaxBombLength(detail);
            case Item.SKILL_MAX_BOMB_NUMBER:
                return skillMaxBombNumber(detail);
            case Item.SKILL_MERMAID:
                return skillMermaid(detail);
            case Item.SKILL_FLASH:
                return skillFlash(tmpPos);
        }
        return true;
    }

    boolean isUseItem(float serverTime, int itemId, Pos tmpPos, Skill.SkillDetail detail) {
        if (isBurning() && itemId != Item.FIRST_AID_MYSELF) {
            return false;
        }
        switch (itemId) {
            case Item.RADAR:
            case Item.ANTI_RADAR:
            case Item.BOMB_INVISIBLE:
            case Item.ANTI_INVISIBLE:
            case Item.MAGNIFY:
            case Item.ITEM_SHIELD:
                List<Player> aPlayer = getTarget(detail == null ? 0 : detail.target);
                int[] info = Item.mItemStatus.get(itemId);
                int duration = detail != null && detail.duration > 0 ? detail.duration : info[1];
                for (Player player : aPlayer) {
                    player.addStatus(info[0], serverTime, duration, 0);
                    table.getAProtoPlayerState().add(player.protoStatus(Arrays.asList(info[0]), Arrays.asList(0f)));
                }
                break;
            case Item.DRILLER:
                return useDriller();
            case Item.DROP_NAIL:
                return useDropNail();
            case Item.STEAL_SHIELD:
                return useStealShield();
            case Item.BOMB_TIMER:
                return useBombTimer();
            case Item.DARTS:
                return useDarts();
            case Item.CURE:
                return useCure();
            case Item.JUMP_SHOES:
                return useJump(tmpPos);
            case Item.RED_BULL:
                return redBull();
            case Item.BEAR_TRAP:
                return bearTrap();
            case Item.VIOLET_MONSTER:
                return violetMonster();
            case Item.FIRST_AID_FRIEND:
                return firstAidFriend();
            case Item.FIRST_AID_MYSELF:
                return firstAidMyself();
        }
        return true;
    }

    List<Player> getTarget(int target) {
        List<Player> aPlayer = new ArrayList<Player>();
        if (target == TARGET_MYSELF)
            return Arrays.asList(this);
        for (Player player : table.getAPlayer()) {
            if (target == TARGET_MY_TEAM && player.user.team == user.team) {
                aPlayer.add(player);
            } else if (target == TARGET_OPP_TEAM && player.user.team != user.team) {
                aPlayer.add(player);
            }
        }
        return aPlayer;
    }

    public boolean healHp(List<Player> aPlayer, int percent, boolean isSkill) {
        int addHp = (int) (percent * user.getPoint(Point.HP) / 100);
        for (Player player : aPlayer) {
            if (player.isOnlyAlive()) {
                player.user.addHp(addHp);
                table.getAProtoPlayerState().add(player.protoStatus(PlayerStatus.UPDATE_HP, player.user.getPoint(Point.CUR_HP)));
                if (isSkill) {
                    table.getAProtoPlayerState().add(player.protoStatus(PlayerStatus.USER_EFFECT,
                            (float) (player.user.point.isZombie() ? PlayerStatus.EFFECT_ZOMBIE_BUFF_HP : PlayerStatus.EFFECT_BUFF_HP)));
                }
            }
        }
        return true;
    }

    boolean blockDamage(List<Player> aPlayer, int percent, int duration, boolean isSkill) {
        for (Player player : aPlayer) {
            player.addStatus(PlayerStatus.BLOCK_DAMAGE, table.getServer_time(), duration, percent);
            table.getAProtoPlayerState().add(player.protoStatus(PlayerStatus.BLOCK_DAMAGE, (float) percent));
        }
        return true;
    }

    boolean powerAtk(List<Player> aPlayer, int percent, int duration, boolean isSkill) {
        int addAtk = (int) (user.getPoint(Point.ATK) * percent / 100);
        for (Player player : aPlayer) {
            player.user.addAtk(addAtk);
            player.addStatus(PlayerStatus.POWER_ATK, table.getServer_time(), duration, addAtk);
            table.getAProtoPlayerState().add(player.protoStatus(PlayerStatus.POWER_ATK, (float) addAtk));
            if (isSkill) {
                table.getAProtoPlayerState().add(player.protoStatus(PlayerStatus.USER_EFFECT, (float) PlayerStatus.EFFECT_BUFF_ATK));
            }
        }
        return true;
    }

    boolean skillRecovery(Item item) {
        List<Player> aPlayer = getTarget(item.value);
        for (Player player : aPlayer) {
            player.removeStatus(PlayerStatus.USER_SKILL);
            if (item.id == 0 && player.user.petSkill != null && player.user.petSkill.canRecovery()) {
                player.removeStatus(PlayerStatus.PET_SKILL);
            } else {
                player.removeStatus(PlayerStatus.PET_SKILL);
            }
        }
        table.getAProtoPlayerState().add(protoStatus(Arrays.asList(PlayerStatus.GET_ITEM), Arrays.asList((float) item.getItemId())));
        return true;
    }

    boolean decreaseDamage(Skill.SkillDetail skill) {
        return false;
    }

    boolean skillMaxBombNumber(Skill.SkillDetail skill) {
        List<Player> targets = getTarget(skill.target);
        targets.forEach(player -> {
            player.addStatus(PlayerStatus.MAX_BOMB_PERIOD, table.getServer_time(), skill.duration, 0);
            table.getAProtoPlayerState().add(player.protoNumberBomb(player.user.getPoint(Point.BOOM_MAX)));
        });
        return true;
    }

    boolean skillMaxBombLength(Skill.SkillDetail skill) {
        List<Player> targets = getTarget(skill.target);
        targets.forEach(player -> {
            player.addStatus(PlayerStatus.MAX_LENGTH_PERIOD, table.getServer_time(), skill.duration, 0);
            table.getAProtoPlayerState().add(player.protoBombLength(user.getPoint(Point.LENGTH_MAX)));
        });
        return true;
    }

    boolean skillMaxSpeed(Skill.SkillDetail skill) {
        List<Player> targets = getTarget(skill.target);
        targets.forEach(player -> {
            player.addStatus(PlayerStatus.MAX_SPEED_PERIOD, table.getServer_time(), skill.duration, 0);
            table.getAProtoPlayerState().add(player.protoSpeed(player.user.getPoint(Point.SPEED_MAX)));
        });
        return true;
    }

    boolean skillColorsHorse(Skill.SkillDetail skill) {
        EffectStopEnemy effect = new EffectStopEnemy(table, map);
        effect.init(this, skill.duration);
        table.getAEffect().add(effect);
        return true;
    }

    boolean skillUnicorn(Skill.SkillDetail skill) {
        EffectFireTail effect = new EffectFireTail(table, map);
        effect.init(this, skill.value[0], table.getServer_time() + skill.duration);
        table.getAEffect().add(effect);
        return true;
    }

    boolean skillMermaid(Skill.SkillDetail skill) {
        table.getAProtoPlayerState().add(protoStatus(PlayerStatus.USER_EFFECT, (float) user.userSkill.effect));
        EffectMermaid effect = new EffectMermaid(table, map);
        effect.init(this, skill.value[0], skill.duration);
        table.getAEffect().add(effect);
        return true;
    }

    boolean skillPig(Skill.SkillDetail skill) {
        int numberBomb = (int) user.getPoint(Point.NUMBER_BOOM) + skill.value[1];
        SquareUnit square = map.getSquare(mPos.clone(lastDirection));
        int index = 0;
        while (square.canMove && numberBomb > index && square.aBomb.isEmpty()) {
            Bomb bomb = new Bomb(map.getNextUnitId(), this, false);
            bomb.setPlayer(new Player());
            bomb.avatar = BoomConfig.BOMB_SKILL_PIG;
            bomb.setDamage((int) (user.getPoint(Point.CUR_ATK) * skill.value[0] / 100));
            bomb.setTargetPercent(skill.value[0]);
            bomb.setTimeStart(table.getServer_time());
            bomb.setMPos(square.mPos.clone());
            bomb.setPos(square.pos.clone());
            square.aBomb.add(bomb);
            map.getLstBom().add(bomb);
            table.getAProtoAdd().add(bomb.protoAdd(table));
            if (square.breakBomb()) {
                bomb.setTimeStart(table.getServer_time() - bomb.getDeltaTimeWait() + 0.1f);
            }
            square = map.getSquare(square.mPos.clone(lastDirection));
            index++;
        }
        return true;
    }

    boolean skillLucifer(Skill.SkillDetail skill) {
        EffectAddDamage effect = new EffectAddDamage(table, map);
        effect.init(this, (int) (user.getPoint(Point.CUR_ATK) * skill.value[0] / 100), user.team, table.getServer_time() + CfgCommon.config.petLuciferTimeAnimation);
        effect.setTargetPercent(skill.value[0]);
        table.getAEffect().add(effect);
        return true;
    }

    boolean skillPanda(Skill.SkillDetail skill) {
        int totalDamage = 0;
        int startX = mPos.x - 2, startY = mPos.y - 2;
        for (int i = 0; i < 5; i++) {
            for (int j = 0; j < 5; j++) {
                int x = startX + i, y = startY + j;
                SquareUnit square = map.getSquare(x, y);
                if (square != null) {
                    for (Bomb bomb : square.aBomb) {
                        totalDamage += bomb.damage;
                        table.getAProtoBoom().add(bomb.protoRemove(2));
                        map.getLstBom().remove(bomb);
                        bomb.getPlayer().numberBomb--;
                    }
                    square.aBomb.clear();
                }
            }
        }
        int addHp = totalDamage * skill.value[0] / 100;
        user.addHp(addHp);
        table.getAProtoPlayerState().add(protoStatus(PlayerStatus.UPDATE_HP, user.getPoint(Point.CUR_HP)));
        return true;
    }

    boolean skillMonkey(Skill.SkillDetail skill) {
        table.getAProtoPlayerState().add(protoStatus(PlayerStatus.PET_ANIMATION, (float) lastDirection.getIntDirection()));
        {
            EffectAddLineDamage effect = new EffectAddLineDamage(table, map, true);
            effect.init(mPos.clone(), lastDirection.clone(), (int) (user.getPoint(Point.CUR_ATK) * skill.value[0] / 100), user.team, table.getServer_time() + 1.2f);
            effect.setTargetPercent(skill.value[0]);
            table.getAEffect().add(effect);
        }
        {

            EffectAddLineDamage effect = new EffectAddLineDamage(table, map, true);
            effect.init(mPos.clone(new MapPos(lastDirection.x == 0 ? 1 : lastDirection.x, lastDirection.y == 0 ? 1 : lastDirection.y)), lastDirection.clone(), (int) (user.getPoint(Point.CUR_ATK) * skill.value[0] / 100), user.team, table.getServer_time() + 1.2f);
            effect.setTargetPercent(skill.value[0]);
            table.getAEffect().add(effect);
        }
        {
            EffectAddLineDamage effect = new EffectAddLineDamage(table, map, true);
            effect.init(mPos.clone(new MapPos(lastDirection.x == 0 ? -1 : lastDirection.x, lastDirection.y == 0 ? -1 : lastDirection.y)), lastDirection.clone(), (int) (user.getPoint(Point.CUR_ATK) * skill.value[0] / 100), user.team, table.getServer_time() + 1.2f);
            effect.setTargetPercent(skill.value[0]);
            table.getAEffect().add(effect);
        }
        return true;
    }

    boolean skillEggRain(Skill.SkillDetail skill) {
        List<SquareUnit> aSquare = map.getRandomSquare(map.getAllSquare(), 5);//map.getRandomSquare(map.getSquareEmpty(), 10);
        List<Player> aPlayer = getTarget(TARGET_OPP_TEAM);
        for (Player player : aPlayer) {
            SquareUnit square = map.getSquare(player.mPos);
            aSquare.remove(square);
            aSquare.add(square);
        }
        while (aSquare.size() > 10) {
            aSquare.remove(0);
        }
        for (int i = 0; i < aSquare.size(); i++) {
            EffectEggRain effect = new EffectEggRain(table, map);
            effect.init(aSquare.get(i).mPos.clone(), (int) (user.getPoint(Point.CUR_ATK) * skill.value[0] / 100), user.team, i * 200);
            effect.setTargetPercent(skill.value[0]);
            table.getAEffect().add(effect);
        }
        return true;
    }

    boolean skillPutItem(Skill.SkillDetail skill) {
        SquareUnit square = map.getSquare(mPos.x, mPos.y);
        if (!square.canTrap && square.aItem.isEmpty()) {
            Item item = new Item(map.getNextUnitId(), skill.value[0]);
            if (skill.value[0] == Item.TRAP_DAMAGE) {
                item.value = (int) (skill.value[1] * user.getPoint(Point.CUR_ATK) / 100);
                item.targetPercent = skill.value[1];
                table.getAProtoAdd().add(CommonProto.protoAddAlertDanger(square.pos));
            }
            item.teamId = user.team;
            square.aItem.add(item);
            table.getAProtoAdd().add(item.protoAdd(square.pos, unitId));
            return true;
        }
        return false;
    }

    boolean firstAidFriend() {
        List<Player> aPlayer = table.getAPlayer();
        for (Player player : aPlayer) {
            if (player.isBurning() && player.user.team == user.team && player.unitId != unitId) {
                player.removeStatus(PlayerStatus.BURNING);
                player.addStatus(PlayerStatus.ALIVE, table.getServer_time(), 10000, 0);
                return true;
            }
        }
        return false;
    }

    boolean firstAidMyself() {
        if (isBurning()) {
            removeStatus(PlayerStatus.BURNING);
            addStatus(PlayerStatus.ALIVE, table.getServer_time(), 10000, 0);
            return true;
        }
        return false;
    }

    boolean violetMonster() {
        List<Integer> lstId = new ArrayList<Integer>();
        for (int i = 0; i < table.getAPlayer().size(); i++) {
            if (((table.getAPlayer().get(i).user.team != user.team) || (table.getAPlayer().get(i).user.id != user.id && table.getMode() == TeamObject.TRAIN)) && table.getAPlayer().get(i).isAlive()) {
                lstId.add(i);
            }
        }
        if (lstId.size() > 0) {
            Player plDiff = table.getAPlayer().get(lstId.get(new Random().nextInt(lstId.size())));
            if (new Random().nextInt(2) == 1) {
                plDiff.addStatus(PlayerStatus.AUTO_PUT_BOM, table.getServer_time(), 15, 0);
                table.getAProtoPlayerState().add(plDiff.protoStatus(PlayerStatus.AUTO_PUT_BOM, 0f));
                table.setBoom(plDiff, Bomb.TYPE_AUTO_PUT);
            } else {
                plDiff.addStatus(PlayerStatus.CHAOTIC, table.getServer_time(), 15, 0);
                table.getAProtoPlayerState().add(plDiff.protoStatus(PlayerStatus.CHAOTIC, 0f));
            }
        }
        return true;
    }

    boolean bearTrap() {
        SquareUnit square = map.getSquare(mPos.x, mPos.y);
        if (!square.canTrap && square.aItem.isEmpty()) {
            Item item = new Item(map.getNextUnitId(), Item.SPEED_DECREASE);
            item.teamId = user.team;
            square.aItem.add(item);
            table.getAProtoAdd().add(item.protoAdd(square.pos));
            return true;
        }
        return false;
    }

    boolean redBull() {
        user.point.addSpeed(1);
        user.point.addBombNumber(1);
        user.point.addBombLength(1);
        user.point.addSpeedMin(1);
        user.point.addBombNumberMin(1);
        user.point.addBombLengthMin(1);
        updatePlayerInfo();
        return true;
    }

    boolean skillFlash(Pos tmpPos) {
        SquareUnit square = map.getSquare(lastDirection.x + mPos.x, lastDirection.y + mPos.y);
        if (square.canMove && square.aBomb.isEmpty()) {
            List<SquareUnit> aSquare = new ArrayList<>();
            for (int i = 1; i <= 10; i++) {
                SquareUnit tmp = map.getSquare(lastDirection.x * i + mPos.x, lastDirection.y * i + mPos.y);
                if (tmp == null) {
                    break;
                } else if (tmp.canMove && tmp.aBomb.isEmpty()) {
                    aSquare.add(tmp);
                } else break;
            }
            for (int i = 0; i < aSquare.size(); i++) {
                SquareUnit tmp = aSquare.get(i);
                for (int index = tmp.aItem.size() - 1; index >= 0; index--) {
                    Item item = tmp.aItem.get(index);
                    if (item.getItemId() == Item.TRAP_DAMAGE && item.teamId != user.team) {
                        table.getAProtoAdd().add(item.protoRemove());
                        tmp.aItem.remove(index);
                    } else if (addItem(table.getServer_time(), item, null)) {
                        table.getAProtoAdd().add(item.protoRemove());
                        tmp.aItem.remove(index);
                    }
                }
                for (Player otherPlayer : tmp.aPlayer) {
                    if (unitId != otherPlayer.unitId && otherPlayer.isBurning()) {
                        if (user.team == otherPlayer.user.team) {
                            otherPlayer.removeStatus(PlayerStatus.BURNING);
                            otherPlayer.healHp(Arrays.asList(otherPlayer), 5, true);
                            otherPlayer.timeImmortal = table.getServer_time() + 2;
                        } else {
                            otherPlayer.iAmDie();
                        }
                    }
                }
            }
            if (!aSquare.isEmpty()) {
                SquareUnit targetFlash = aSquare.get(aSquare.size() - 1);
                addStatus(PlayerStatus.FAKE_ON_AIR, table.getServer_time(), 0.5f, 0);
                tmpPos.setX(targetFlash.pos.x);
                tmpPos.setY(targetFlash.pos.y);
                map.getSquare(mPos).aPlayer.remove(this);
                table.getAProtoJumpPos().add(CommonProto.protoJumpPos(BoomConfig.JUMP_FLASH, unitId, targetFlash.pos));
                return true;
            }
        }
        return false;
    }

    boolean useJump(Pos tmpPos) {
        SquareUnit square = map.getSquare(lastDirection.x + mPos.x, lastDirection.y + mPos.y);
        if (!square.canMove || square.aBomb.size() > 0) {
            SquareUnit targetJump = null;
            for (int i = 2; i <= 4; i++) {
                SquareUnit tmp = map.getSquare(lastDirection.x * i + mPos.x, lastDirection.y * i + mPos.y);
                if (tmp == null) {
                    return false;
                } else if (tmp.canMove && tmp.aBomb.isEmpty()) {
                    targetJump = tmp;
                    break;
                }
            }
            if (targetJump != null) {
                addStatus(PlayerStatus.FAKE_ON_AIR, table.getServer_time(), 0.5f, 0);
                tmpPos.setX(targetJump.pos.x);
                tmpPos.setY(targetJump.pos.y);
                map.getSquare(mPos).aPlayer.remove(this);
                table.getAProtoJumpPos().add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, unitId, targetJump.pos));
                return true;
            }
        }
        return false;
    }

    boolean useCure() {
        List<Integer> aStatus = Arrays.asList(PlayerStatus.AUTO_PUT_BOM, PlayerStatus.CHAOTIC);
        for (Integer statusId : aStatus) {
            removeStatus(statusId);
        }
        return true;
    }

    boolean useDarts() {
        Dart dart = new Dart(map.getNextUnitId(), pos, table);
        dart.startToMove(table.getServer_time(), lastDirection);
        table.getAProtoAdd().add(dart.protoAdd());
        table.getAMoveUnit().add(dart);
        return true;
    }

    boolean useBombTimer() {
        table.setBoom(this, Bomb.TYPE_TIMER);
        return true;
    }

    boolean useStealShield() {
        boolean hasShield = false;
        List<Player> aPlayer = table.getAPlayer();
        for (Player player : aPlayer) {
            if (player.unitId != unitId) {
                if (player.removeStatus(PlayerStatus.IMMORTAL)) {
                    player.unShield();
                    hasShield = true;
                }
            }
        }
        if (hasShield) {
            int[] info = Item.mItemStatus.get(Item.ITEM_SHIELD);
            addStatus(info[0], table.getServer_time(), info[1], 0);
            table.getAProtoPlayerState().add(protoStatus(Arrays.asList(info[0]), Arrays.asList(0f)));
        }
        return true;
    }

    boolean useDropNail() {
        SquareUnit square = map.getSquare(mPos.x, mPos.y);
        if (!square.canTrap && square.aItem.isEmpty()) {
            square.imgId = 1000;
            square.setType(SquareUnit.TYPE_TRAP);
            table.getAProtoAdd().add(square.protoAdd());
            return true;
        }
        return false;
    }

    boolean useDriller() {
        SquareUnit square = map.getSquare(lastDirection.x + mPos.x, lastDirection.y + mPos.y);
        square.addDamage(1000);
        return square.destroy(map, table);
    }

    boolean unShield() {
        if (mStatus.containsKey(PlayerStatus.IMMORTAL)) {
            removeStatus(PlayerStatus.IMMORTAL);
            return true;
        }
        return false;
    }

    //region Player Move
    public float move(float serverTime, float distance, com.bem.boom.Pos playerPos, com.bem.boom.MapPos direction) {
        lastDirection = direction;
        com.bem.boom.MapPos playerMPos = IMath.getBlock(playerPos);
        SquareUnit curSquare = map.getSquare(playerMPos);
        SquareUnit oppSquare = getOppSquare(map, playerPos, playerMPos, direction);
        SquareUnit targetSquare = map.getSquare(playerMPos.x + direction.x, playerMPos.y + direction.y);
        //
        if (isPushBomb() && !targetSquare.aBomb.isEmpty()) {
//            if (playerPos.equals(curSquare.pos) ||
//                    (targetSquare.pos.x == curSquare.pos.x && playerPos.y == curSquare.pos.y) ||
//                    (targetSquare.pos.y == curSquare.pos.y && playerPos.x == curSquare.pos.x)) {
//                System.out.println("aaaaaaaaaaaaaa");
//                targetSquare.moveTheBomb(serverTime, direction);
////                return 0;
//            }
            if (playerPos.distance(targetSquare.pos) < BoomConfig.squareEdge + 0.1)
                targetSquare.moveTheBomb(serverTime, direction);
        }
        //
        if (targetSquare.walkAbles() && oppSquare.walkAbles()) { // move freely
            playerPos.x += direction.x * distance;
            playerPos.y -= direction.y * distance;
            playerPos.round();
            if (this.mStatus.containsKey(PlayerStatus.AUTO_PUT_BOM)) {
                table.setBoom(this, Bomb.TYPE_AUTO_PUT);
            }
            return 0;
        }
        //
        if (playerPos.x != curSquare.pos.x && playerPos.y != curSquare.pos.y) { // weird position -> move to base line
            if (direction.x == 0) { // follow y
                if (Math.abs(curSquare.pos.y - playerPos.y) < distance) {
                    playerPos.y = curSquare.pos.y;
                    distance = Math.abs(curSquare.pos.y - playerPos.y);
                } else {
                    playerPos.y += distance * (curSquare.pos.y > playerPos.y ? 1 : -1);
                    distance = 0;
                }
            } else { // follow x
                if (Math.abs(curSquare.pos.x - playerPos.x) < distance) {
                    playerPos.x = curSquare.pos.x;
                    distance = Math.abs(curSquare.pos.x - playerPos.x);
                } else {
                    playerPos.x += distance * (curSquare.pos.x > playerPos.x ? 1 : -1);
                    distance = 0;
                }
            }
            if (this.mStatus.containsKey(PlayerStatus.AUTO_PUT_BOM)) {
                table.setBoom(this, Bomb.TYPE_AUTO_PUT);
            }
            if (distance == 0) {
                playerPos.round();
                return 0;
            }
        }
        //
        SquareUnit nextSquare = null;
        if (!targetSquare.walkAbles()) { // o truoc mat ko di duoc thi di sang 2 ben, di ben nao gan hon
            SquareUnit[] nextPos = new SquareUnit[2];
            nextPos[0] = map.getSquare(new com.bem.boom.MapPos(playerMPos.x + (direction.x == 0 ? 1 : 0), playerMPos.y + (direction.y == 0 ? 1 : 0)));
            nextPos[1] = map.getSquare(new com.bem.boom.MapPos(playerMPos.x + (direction.x == 0 ? -1 : 0), playerMPos.y + (direction.y == 0 ? -1 : 0)));
            double[] squareDistance = new double[2];
            squareDistance[0] = playerPos.distance(nextPos[0].pos);
            squareDistance[1] = playerPos.distance(nextPos[1].pos);

            targetSquare = squareDistance[0] < squareDistance[1] ? nextPos[0] : nextPos[1];
            nextSquare = map.getSquare(targetSquare.mPos.x + direction.x, targetSquare.mPos.y + direction.y);
            if ((playerPos.x == targetSquare.pos.x || playerPos.y == targetSquare.pos.y)
                    && !nextSquare.walkAbles()) {
                if (this.mStatus.containsKey(PlayerStatus.AUTO_PUT_BOM)) {
                    table.setBoom(this, Bomb.TYPE_AUTO_PUT);
                }
                return distance;
            }

            float minDistance = (float) Math.min(squareDistance[0], squareDistance[1]);
            minDistance = IMath.round(minDistance);
            if (minDistance <= BoomConfig.squareEdge && minDistance >= CfgCommon.config.battle.glideDistance) { // khoang khong cho truot sang hai ben
                if (this.mStatus.containsKey(PlayerStatus.AUTO_PUT_BOM)) {
                    table.setBoom(this, Bomb.TYPE_AUTO_PUT);
                }
                return distance;
            }
        }
        return moveToSquare(distance, playerPos, curSquare, targetSquare, nextSquare, table);
    }

    float moveToSquare(float distance, com.bem.boom.Pos playerPos, SquareUnit curSquare, SquareUnit targetSquare, SquareUnit nextSquare, AbstractTable table) {
        com.bem.boom.Pos targetPos = targetSquare.pos.clone();
        boolean walkAbles = targetSquare.walkAbles();
        if (!walkAbles) {
            targetSquare = curSquare;
        } else if (playerPos.x != targetSquare.pos.x && playerPos.y != targetSquare.pos.y) {
            targetSquare = curSquare;
        }
        if (playerPos.x == targetSquare.pos.x && playerPos.y != targetSquare.pos.y) { // follow y
            if (Math.abs(targetSquare.pos.y - playerPos.y) < distance) {
                playerPos.y = targetSquare.pos.y;
                distance = Math.abs(targetSquare.pos.y - playerPos.y);
            } else {
                playerPos.y += distance * (targetSquare.pos.y > playerPos.y ? 1 : -1);
                distance = 0;
            }
        } else if (playerPos.x != targetSquare.pos.x && playerPos.y == targetSquare.pos.y) { // follow x
            if (Math.abs(targetSquare.pos.x - playerPos.x) < distance) {
                playerPos.x = targetSquare.pos.x;
                distance = Math.abs(targetSquare.pos.x - playerPos.x);
            } else {
                playerPos.x += distance * (targetSquare.pos.x > playerPos.x ? 1 : -1);
                distance = 0;
            }
        }

        if (distance > 0 && (nextSquare == null || nextSquare.walkAbles()) && walkAbles) {
            if (playerPos.x == targetPos.x) { // follow y
                playerPos.y += distance * (targetPos.y > playerPos.y ? 1 : -1);
            } else { // follow x
                playerPos.x += distance * (targetPos.x > playerPos.x ? 1 : -1);
            }
            distance = 0;
        }
        playerPos.round();
        if (this.mStatus.containsKey(PlayerStatus.AUTO_PUT_BOM)) {
            table.setBoom(this, Bomb.TYPE_AUTO_PUT);
        }
        return distance;
    }

    public SquareUnit getOppSquare(MapData map, com.bem.boom.Pos playerPos, com.bem.boom.MapPos playerMPos, com.bem.boom.MapPos direction) {
        SquareUnit[] tmp = new SquareUnit[2];
        tmp[0] = map.getSquare(new com.bem.boom.MapPos(playerMPos.x + (direction.x == 0 ? 1 : direction.x), playerMPos.y + (direction.y == 0 ? 1 : direction.y)));
        tmp[1] = map.getSquare(new com.bem.boom.MapPos(playerMPos.x + (direction.x == 0 ? -1 : direction.x), playerMPos.y + (direction.y == 0 ? -1 : direction.y)));
        return playerPos.distance(tmp[0].pos) < playerPos.distance(tmp[1].pos) ? tmp[0] : tmp[1];
    }
    //endregion

}
