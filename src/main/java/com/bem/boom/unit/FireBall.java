package com.bem.boom.unit;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.util.CommonProto;
import com.proto.GGProto;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 11/3/2016.
 */
@Data
public class FireBall extends IMove {
    public FireBall(int unitId, Pos pos, AbstractTable table) {
        super(unitId, pos, table);
    }

    public FireBall(int unitId, int imageId, Pos pos, AbstractTable table) {
        super(unitId, pos, table);
        this.imageId = imageId;
    }

    public void doAction(float serverTime) {
        move(serverTime);
        checkKillPlayer();
        checkExplodeBomb();
    }

    void checkExplodeBomb() {
        SquareUnit curSquare = map.getSquare(mPos);
        if (!curSquare.aBomb.isEmpty()) {
            List<Bomb> aBomb = new ArrayList<Bomb>();
            aBomb.addAll(curSquare.aBomb);
            //
            table.checkBombExplosion(aBomb, new ArrayList<MapPos>());
            breakMyself();
        }
    }

    void checkKillPlayer() {
        SquareUnit curSquare = map.getSquare(mPos);
        if (!curSquare.aPlayer.isEmpty()) {
            for (int i = curSquare.aPlayer.size() - 1; i >= 0; i--) {
                Player player = curSquare.aPlayer.get(i);
                player.addDamage(damage);
            }
        }
    }

    void breakMyself() {
        table.getAProtoAdd().add(protoRemove());
        List<MapPos> aDirection = Arrays.asList(Input.VectorUp, Input.VectorRight, Input.VectorDown, Input.VectorLeft);
        for (MapPos mapPos : aDirection) {
            if (!map.getSquare(mapPos.x + mPos.x, mapPos.y + mPos.y).canStopBoom) {
                com.bem.boom.unit.SnowBall ball = new com.bem.boom.unit.SnowBall(map.getNextUnitId(), 4, pos, table);
                ball.damage = damage;
                ball.startToMove(table.getServer_time(), mapPos);
                table.getAProtoAdd().add(ball.protoAdd());
                table.getAMoveUnit().add(ball);
            }
        }
    }

    protected boolean moveFreely(SquareUnit targetSquare) {
        return !targetSquare.canStopBoom;
    }

    protected void checkTargetSquare(SquareUnit square, MapData map) {
        if (square.canStopBoom) {
            breakMyself();
        }
    }

//    //region Proto
//    public GGProto.ProtoUnitAdd.Builder protoAdd() {
//        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
//        builder.setId(unitId);
//        builder.setItemId(BoomConfig.TYPE_BULLET);
//        builder.addAvatar(imageId);
//        builder.setAdd(true);
//        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
//        return builder;
//    }
//    //endregion
}
