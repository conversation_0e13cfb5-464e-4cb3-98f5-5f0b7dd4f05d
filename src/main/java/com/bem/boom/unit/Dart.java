package com.bem.boom.unit;

import com.bem.AbstractTable;
import com.bem.boom.*;
import com.bem.util.CommonProto;
import com.proto.GGProto;
import lombok.Data;

/**
 * Created by vieth_000 on 11/3/2016.
 */
@Data
public class Dart extends IMove {
    public Dart(int unitId, Pos pos, AbstractTable table) {
        super(unitId, pos, table);
        speed = 10f;
    }

    public void doAction(float serverTime) {
        move(serverTime);
    }

    protected boolean moveFreely(SquareUnit targetSquare) {
        return !(targetSquare.canStopBoom || !targetSquare.aBomb.isEmpty());
    }

    protected void checkTargetSquare(SquareUnit square, MapData map) {
        if (square.aBomb.size() > 0) {
            table.explodeBomb(square.aBomb.get(0));
            table.getAProtoPos().add(protoPos());
            table.getAProtoAdd().add(protoRemove());
        } else if (square.canStopBoom) {
            table.getAProtoPos().add(protoPos());
            table.getAProtoAdd().add(protoRemove());
        }
    }

//    //region Proto
//    public GGProto.ProtoUnitAdd.Builder protoAdd() {
//        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
//        builder.setId(unitId);
//        builder.setItemId(BoomConfig.TYPE_BULLET);
//        builder.addAvatar(0);
//        builder.setAdd(true);
//        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
//        return builder;
//    }
//    //endregion
}
