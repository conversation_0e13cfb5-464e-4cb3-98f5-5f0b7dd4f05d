package com.bem.boom.unit;

import com.bem.boom.*;
import com.bem.boom.MapPos;
import com.bem.boom.Pos;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.MyAvatar;
import com.bem.object.UserInfo;
import com.bem.util.CommonProto;
import com.proto.GGProto;
import com.proto.GGProto.*;

import java.util.*;

/**
 * Created by vieth_000 on 7/14/2016.
 */
public class Bullet {
    public com.bem.boom.Pos pos; // x,y
    public com.bem.boom.MapPos mPos, lastDirection; // x,y theo block
    public List<Input> inputs = new ArrayList<Input>();
    public int unitId;
    MyAvatar mAvatar;
    public int unitOner;
    float speed = 0.5f;
    MapPos direction = Input.VectorRight;

    public Bullet(UserInfo userInfo, int team, int unitId) {
        this.unitId = unitId;
        mAvatar = userInfo.getDbUser().getMAvatar();
    }



    public ProtoUnitPos.Builder protoPosBullet() {
        ProtoUnitPos.Builder builder = ProtoUnitPos.newBuilder();
        builder.setId(unitId);
        builder.setPosX(pos.x);
        builder.setPosY(pos.y);
        return builder;
    }


    public ProtoUnitAdd.Builder protoAddBullet(Pos pos) {
        ProtoUnitAdd.Builder builder = ProtoUnitAdd.newBuilder();
        builder.setId(unitId);
        builder.setType(BoomConfig.TYPE_BULLET);
        builder.addAvatar(1);
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        return builder;
    }

    public GGProto.ProtoUnitAdd.Builder protoDie() {
            GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
            builder.setId(unitId);
            builder.setAdd(false);
            return builder;
    }

    public void doAction(MapData map, float serverTime, List<IMonster> aMonster, List<MapPos> lstMpPlayer, List<GGProto.ProtoMonsterState.Builder> aProtoMonsterState,List<Bullet> aBullet) {
//        debug("move1-->");
        move(map, aMonster,lstMpPlayer);
    }

    private void move(MapData map ,List<IMonster> aMonster,List<MapPos> lstMpPlayer){

    }





}
