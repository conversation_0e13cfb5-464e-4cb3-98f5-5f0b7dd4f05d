package com.bem.boom;

import com.bem.boom.monster.*;
import com.bem.boom.object.CommonGiftcode;
import com.bem.object.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.k2tek.common.slib_Logger;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;

import java.util.*;

//import com.bem.dao.mapping.HeroEntity;

/**
 * Created by vieth_000 on 11/24/2016.
 */
public class Resources {
    public final static int RANK_D = 1, RANK_C = 2, RANK_B = 3, RANK_A = 4, RANK_S = 5;
    public static List<ResBomb> aBomb = new ArrayList<ResBomb>();
    public static List<ResMaterial> aMaterial = new ArrayList<ResMaterial>();
    public static List<ResAccessories> aAccessories = new ArrayList<ResAccessories>();
    public static List<ResBombUpgrade> aBombUpgrade = new ArrayList<ResBombUpgrade>();
    public static List<ResBombGhep> aBombGhep = new ArrayList<ResBombGhep>();

    public static List<ResSaleItem> aSaleItem = new ArrayList<ResSaleItem>();
    public static List<ResAvatarUpgrade> aAvatarUpgrade = new ArrayList<ResAvatarUpgrade>();
    public static List<ResPetStarUpgrade> aPetStarUpgrade = new ArrayList<ResPetStarUpgrade>();
    public static int maxUserExp = 30, maxHeroExp = 30, maxPetExp = 30;
    public static double[][] aDamage = new double[6][6];
    public static ResAccessoriesUpgrade accessoriesUpgrade = new ResAccessoriesUpgrade();
    public static ResUfoUpgrade ufoUpgrade = new ResUfoUpgrade();

    public static List<ResPetFood> aPetFood = new ArrayList<ResPetFood>();
    public static List<ResAvatar> aAvatar = new ArrayList<ResAvatar>();
    public static List<ResIcon> aIcon = new ArrayList<ResIcon>();
    public static List<ResSymbol> aSymbol = new ArrayList<ResSymbol>();
    public static List<ResUfo> aUfo = new ArrayList<ResUfo>();
    public static List<ResRank> aRankTrophy = new ArrayList<>();
    public static List<CommonGiftcode> aCommonGiftCode = new ArrayList<>();


    public static List<ResShopItem> aShop = new ArrayList<ResShopItem>();
    public static List<ResPet> aPet = new ArrayList<ResPet>();
    public static List<ResHero> aHero = new ArrayList<ResHero>();
    public static long[] userExp = new long[1000], heroExp = new long[1000], petExp = new long[1000];
    //
    public static Map<Integer, ResRank> mRankTrophy = new HashMap<Integer, ResRank>();
    public static Map<Integer, ResBomb> mBomb = new HashMap<Integer, ResBomb>();
    public static Map<Integer, ResPetFood> mPetFood = new HashMap<Integer, ResPetFood>();
    public static Map<Integer, ResHero> mHero = new HashMap<Integer, ResHero>();
    public static Map<Integer, ResShopItem> mShop = new HashMap<Integer, ResShopItem>();
    public static Map<Integer, ResBombUpgrade> mBombUpgrade = new HashMap<Integer, ResBombUpgrade>();
    public static Map<Integer, ResBombGhep> mBombGhep = new HashMap<Integer, ResBombGhep>();
    public static Map<Integer, ResPetStarUpgrade> mPetStarUpgrade = new HashMap<Integer, ResPetStarUpgrade>();

    public static Map<Integer, ResMaterial> mMaterial = new HashMap<Integer, ResMaterial>();
    public static Map<Integer, ResAccessories> mAccessories = new HashMap<Integer, ResAccessories>();

    public static Map<Integer, ResAvatarUpgrade> mAvatarUpgrade = new HashMap<Integer, ResAvatarUpgrade>();
    public static Map<Integer, ResPet> mPet = new HashMap<Integer, ResPet>();
    public static Map<Integer, ResAvatar> mAvatar = new HashMap<Integer, ResAvatar>();
    public static Map<Integer, ResIcon> mIcon = new HashMap<Integer, ResIcon>();
    public static Map<Integer, ResSymbol> mSymbol = new HashMap<Integer, ResSymbol>();
    public static Map<Integer, ResUfo> mUfo = new HashMap<Integer, ResUfo>();

    public static List<ResBaseListLong> aPetLevel = new ArrayList<ResBaseListLong>();
    //
    public static Map<Integer, List<ResMaterial>> mRankMaterial = new HashMap<Integer, List<ResMaterial>>();
    public static Map<Integer, List<ResAvatar>> mRankAvatar = new HashMap<Integer, List<ResAvatar>>();
    public static Map<Integer, List<ResPet>> mRankPet = new HashMap<Integer, List<ResPet>>();
    public static Map<Integer, List<ResAccessories>> mRankAccessories = new HashMap<>();
    //
    public static Map<Integer, IMonster> mMonster = new HashMap<Integer, IMonster>();
    public static Map<Integer, Integer> mMonsterType = new HashMap<Integer, Integer>();

    //
    public static double getRateDamage(int bombId, int monsterType) {
        return mBomb.containsKey(bombId) ? aDamage[mBomb.get(bombId).getType()][monsterType] : 1;
    }

    public static ResIcon getRandomIcon() {
        return aIcon.get(new Random().nextInt(aIcon.size()));
    }

    public static ResHero getRandomHero() {
        return aHero.get(new Random().nextInt(aHero.size()));
    }

    public static ResBomb getRandomBomb() {
        int index = new Random().nextInt(aBomb.size());
        for (int i = 0; i < aBomb.size(); i++) {
            if (aBomb.get(index).getLock() == 0) {
                return aBomb.get(index);
            }
            if (++index == aBomb.size()) {
                index = 0;
            }
        }
        return null;
    }


    public static ResBomb getRandomBom(int rankBegin, int rankEnd) {
//        Collections.shuffle(aBomb);
        List<ResBomb> ab = new ArrayList<>();
        ab.addAll(aBomb);
        Collections.shuffle(ab);
        for (int i = 0; i < ab.size(); i++) {
            if (ab.get(i).getLock() == 0 && ab.get(i) != null && ab.get(i).getRank() <= rankEnd && ab.get(i).getRank() >= rankBegin) {
                return ab.get(i);
            }
        }
        return null;
    }

    public static ResAccessories getRandomAccessories(int rank) {
        List<ResAccessories> aAccessories = mRankAccessories.get(rank);
        int index = new Random().nextInt(aAccessories.size());
        for (int i = 0; i < aAccessories.size(); i++) {
            if (aAccessories.get(index).getLock() == 0) {
                return aAccessories.get(index);
            }
            if (++index == aAccessories.size()) {
                index = 0;
            }
        }
        return null;
    }

    public static ResAccessories getRandomAccessories() {
        int index = new Random().nextInt(aAccessories.size());
        for (int i = 0; i < aAccessories.size(); i++) {
            if (aAccessories.get(index).getLock() == 0) {
                return aAccessories.get(index);
            }
            if (++index == aAccessories.size()) {
                index = 0;
            }
        }
        return null;
    }

    public static ResAccessories getRandomAccessories(int rankBegin, int rankEnd) {
        List<ResAccessories> ab = new ArrayList<>();
        ab.addAll(aAccessories);
        Collections.shuffle(ab);
        for (int i = 0; i < ab.size(); i++) {
            if (ab.get(i).getLock() == 0 && ab.get(i) != null && ab.get(i).getRank() <= rankEnd && ab.get(i).getRank() >= rankBegin) {
                return ab.get(i);
            }
        }
        return null;
    }

    public static StoneUpgradeUfo getRandomUfoStone(int rankBegin, int rankEnd) {
        List<StoneUpgradeUfo> lst = new ArrayList<>();
        for (int i = 0; i < Resources.ufoUpgrade.getStones().size(); i++) {
            if (Resources.ufoUpgrade.getStones().get(i).rank >= rankBegin && Resources.ufoUpgrade.getStones().get(i).rank <= rankEnd) {
                lst.add(Resources.ufoUpgrade.getStones().get(i));
            }
        }
        if (lst.size() > 1) {
            return lst.get(new Random().nextInt(lst.size()));
        }
        return null;
    }

    public static StoneUpgradeUfo getStone(int id) {
        return Resources.ufoUpgrade.getMStone().get(id);
    }

    public static ResMaterial getRandomMaterial() {
        int index = new Random().nextInt(aMaterial.size());
        for (int i = 0; i < aMaterial.size(); i++) {
            if (aMaterial.get(index).getLock() == 0) {
                return aMaterial.get(index);
            }
            if (++index == aMaterial.size()) {
                index = 0;
            }
        }
        return null;
    }

    public static ResMaterial getRandomMaterial(int rank) {
        List<ResMaterial> aMaterial = mRankMaterial.get(rank);
        int index = new Random().nextInt(aMaterial.size());
        for (int i = 0; i < aMaterial.size(); i++) {
            if (aMaterial.get(index).getLock() == 0) {
                return aMaterial.get(index);
            }
            if (++index == aMaterial.size()) {
                index = 0;
            }
        }
        return null;
    }

    public static List<ResMaterial> getRandomMaterial(int rank, int type, int number) {
        List<ResMaterial> aMaterial = mRankMaterial.get(rank);
        List<ResMaterial> aTypeMaterial = new ArrayList<>();
        for (ResMaterial material : aMaterial) {
            if (material.getType() == type) aTypeMaterial.add(material);
        }
        Random ran = new Random();
        List<ResMaterial> result = new ArrayList<>();
        for (int i = 0; i < number; i++) {
            result.add(aTypeMaterial.get(ran.nextInt(aTypeMaterial.size())));
        }
        return result;
    }

    public static ResMaterial getRandomMaterialLevel(int level) {
        List<ResMaterial> ab = new ArrayList<>();
        ab.addAll(aMaterial);
        Collections.shuffle(ab);
        for (int i = 0; i < ab.size(); i++) {
            if (ab.get(i).getRank() == level) {
                return ab.get(i);
            }
        }
        return null;
    }

    public static ResMaterial getRandomMaterialLevel(int levelBegin, int levelEnd) {
        List<ResMaterial> ab = new ArrayList<>();
        ab.addAll(aMaterial);
        Collections.shuffle(ab);
        for (int i = 0; i < ab.size(); i++) {
            if (ab.get(i).getRank() >= levelBegin && ab.get(i).getRank() <= levelEnd) {
                return ab.get(i);
            }
        }
        return null;
    }

    public static ResPetFood getRandomFruit(int rank) {
        List<ResPetFood> ab = new ArrayList<>();
        ab.addAll(aPetFood);
        Collections.shuffle(ab);
        for (int i = 0; i < ab.size(); i++) {
            if (ab.get(i) != null && ab.get(i).getRank() == rank) {
                return ab.get(i);
            }
        }
        return null;
    }

    public static ResPetFood getRandomFruit(int rankBegin, int rankEnd) {
        List<ResPetFood> ab = new ArrayList<>();
        ab.addAll(aPetFood);
        Collections.shuffle(ab);
        for (int i = 0; i < ab.size(); i++) {
            if (ab.get(i) != null && ab.get(i).getRank() >= rankBegin && ab.get(i).getRank() <= rankEnd) {
                return ab.get(i);
            }
        }
        return null;
    }

    public static ResPet getRandomPet(int rank) {
        List<ResPet> aPet = mRankPet.get(rank);
        int index = new Random().nextInt(aPet.size());
        for (int i = 0; i < aPet.size(); i++) {
            if (aPet.get(index).getLock() == 0) {
                return aPet.get(index);
            }
            if (++index == aPet.size()) {
                index = 0;
            }
        }
        return null;
    }

    public static ResAvatar getRandomAvatar() {
        int index = new Random().nextInt(aAvatar.size());
        for (int i = 0; i < aAvatar.size(); i++) {
            if (aAvatar.get(index).getLock() == 0) {
                return aAvatar.get(index);
            }
            if (++index == aAvatar.size()) {
                index = 0;
            }
        }
        return null;
    }

    public static ResAvatar getRandomAvatar(int rank) {
        List<ResAvatar> aAvatar = mRankAvatar.get(rank);
        int index = new Random().nextInt(aAvatar.size());
        for (int i = 0; i < aAvatar.size(); i++) {
            if (aAvatar.get(index).getLock() == 0) {
                return aAvatar.get(index);
            }
            if (++index == aAvatar.size()) {
                index = 0;
            }
        }
        return null;
    }

    public static ResAvatar getRandomAvatar(int rankBegin, int rankEnd) {
        List<ResAvatar> ava = new ArrayList<ResAvatar>();
        ava.addAll(aAvatar);
        Collections.shuffle(ava);

        for (int i = 0; i < ava.size(); i++) {
            if (ava.get(i).getLock() == 0 && ava.get(i) != null && ava.get(i).getRank() <= rankEnd && ava.get(i).getRank() >= rankBegin) {
                return ava.get(i);
            }
        }
        return null;
    }

    public static ResAvatar getRandomAvatarByHero(int rankBegin, int rankEnd, List<Integer> lstHero) {
        List<ResAvatar> ava = new ArrayList<ResAvatar>();
        ava.addAll(aAvatar);
        Collections.shuffle(ava);

        for (int i = 0; i < ava.size(); i++) {
            if (ava.get(i).getLock() == 0 && ava.get(i) != null && ava.get(i).getRank() <= rankEnd && ava.get(i).getRank() >= rankBegin && lstHero.contains(ava.get(i).getCharacterId())) {
                return ava.get(i);
            }
        }
        return null;
    }

    public static List<ResAvatar> getRankAvatarMaxId(int rank, int number) {
        List<ResAvatar> rs = new ArrayList<ResAvatar>();
        int t = 0;
        for (int i = aAvatar.size() - 1; i > 0; i--) {
            if (t >= number) {
                break;
            } else {
                if (aAvatar.get(i) != null && aAvatar.get(i).getRank() == rank) {
                    rs.add(aAvatar.get(i));
                    t++;
                }
            }
        }
        return rs;
    }

    public static List<ResPetFood> getRankPetFoodMaxId(int rank, int number) {
        List<ResPetFood> rs = new ArrayList<ResPetFood>();
        int t = 0;
        for (int i = aPetFood.size() - 1; i > 0; i--) {
            if (t >= number) {
                break;
            } else {
                if (aPetFood.get(i) != null && aPetFood.get(i).getRank() == rank) {
                    rs.add(aPetFood.get(i));
                    t++;
                }
            }
        }
        return rs;
    }

    public static List<ResBomb> getRankBombMaxId(int rank, int number) {
        List<ResBomb> rs = new ArrayList<ResBomb>();
        int t = 0;
        for (int i = aBomb.size() - 1; i > 0; i--) {
            if (t >= number) {
                break;
            } else {
                if (aBomb.get(i) != null && aBomb.get(i).getRank() == rank) {
                    rs.add(aBomb.get(i));
                    t++;
                }
            }
        }
        return rs;
    }

    public static List<ResPet> getRankPetMaxId(int number) {
        List<ResPet> rs = new ArrayList<ResPet>();
        int t = 0;
        for (int i = aPet.size() - 1; i > 0; i--) {
            if (t >= number) {
                break;
            } else {
                if (aPet.get(i) != null) {
                    rs.add(aPet.get(i));
                    t++;
                }
            }
        }
        return rs;
    }

    public static ResPet getRandomPet() {
        int index = new Random().nextInt(aPet.size());
        for (int i = 0; i < aPet.size(); i++) {
            if (aPet.get(index).getLock() == 0) {
                return aPet.get(index);
            }
            if (++index == aPet.size()) {
                index = 0;
            }
        }
        return null;
    }

    public static ResPet getRandomPet(int rankBegin, int rankEnd) {
        List<ResPet> pet = new ArrayList<ResPet>();
        pet.addAll(aPet);
        Collections.shuffle(pet);

        for (int i = 0; i < pet.size(); i++) {
            if (pet.get(i).getLock() == 0 && pet.get(i) != null && pet.get(i).getRank() <= rankEnd && pet.get(i).getRank() >= rankBegin) {
                return pet.get(i);
            }
        }
        return null;
    }

    public static int getRankTrophy(int rank, boolean isWin) {
        if (mRankTrophy.containsKey(rank)) {
            return isWin ? mRankTrophy.get(rank).win : -mRankTrophy.get(rank).lose;
        }
        return isWin ? 10 : -10;
    }

    public static int getRankTrophyZombie(boolean isWin) {
        return isWin ? 30 : 0;
    }

    public static ResPetFood getRandomPetFood() {
        return aPetFood.get(new Random().nextInt(aPetFood.size()));
    }

    public static ResHero getHero(int heroId) {
        return mHero.get(heroId);
    }

    public static ResPet getPet(int petId) {
        return mPet.get(petId);
    }

    public static ResAvatar getAvatar(int avatarId) {
        return mAvatar.get(avatarId);
    }

    public static ResBomb getBomb(int bombId) {
        return mBomb.get(bombId);
    }

    public static ResSymbol getSymbol(int symbolId) {
        return mSymbol.get(symbolId);
    }

    public static ResUfo getUfo(int ufoId) {
        return mUfo.get(ufoId);
    }

    public static ResMaterial getMaterial(int materialId) {
        return mMaterial.get(materialId);
    }

    public static ResAccessories getAccessories(int AccessId) {
        return mAccessories.get(AccessId);
    }


    public static ResPetFood getPetFood(int petFoodId) {
        return mPetFood.get(petFoodId);
    }

    public static void load(String key, String value) {
        if (key.equals("res_bomb")) {
            aBomb = new Gson().fromJson(value, new TypeToken<ArrayList<ResBomb>>() {
            }.getType());
            getLogger().info("res_bomb = " + aBomb.size());
            int i = 0;
            while (i < aBomb.size()) {
                if (aBomb.get(i).getLock() < 0) {
                    aBomb.remove(aBomb.get(i));
                } else {
                    i++;
                }
            }
            for (ResBomb bomb : aBomb) {
                mBomb.put(bomb.getId(), bomb);
            }
        } else if (key.equals("res_pet")) {
            aPet = new Gson().fromJson(value, new TypeToken<ArrayList<ResPet>>() {
            }.getType());
            int i = 0;
            while (i < aPet.size()) {
                if (aPet.get(i).getLock() < 0) {
                    aPet.remove(aPet.get(i));
                } else {
                    i++;
                }
            }
            for (ResPet pet : aPet) {
                pet.init();
                mPet.put(pet.getId(), pet);
            }
            getLogger().info("res_pet = " + aPet.size());
        } else if (key.equals("res_item")) {
            aShop = new Gson().fromJson(value, new TypeToken<ArrayList<ResShopItem>>() {
            }.getType());
            for (ResShopItem shop : aShop) {
                mShop.put(shop.getId(), shop);
            }
            getLogger().info("res_item = " + aShop.size());
        } else if (key.equals("res_clothing")) {
            aAvatar = new Gson().fromJson(value, new TypeToken<ArrayList<ResAvatar>>() {
            }.getType());
            int i = 0;
            while (i < aAvatar.size()) {
                if (aAvatar.get(i).getLock() < 0) {
                    aAvatar.remove(aAvatar.get(i));
                } else {
                    i++;
                }
            }
            for (ResAvatar avatar : aAvatar) {
                mAvatar.put(avatar.getId(), avatar);
            }
            getLogger().info("res_clothing = " + aAvatar.size());
        } else if (key.equals("res_icon")) {
            aIcon = new Gson().fromJson(value, new TypeToken<ArrayList<ResIcon>>() {
            }.getType());
            for (ResIcon icon : aIcon) {
                mIcon.put(icon.getId(), icon);
            }
            getLogger().info("res_icon = " + aIcon.size());
        } else if (key.equals("res_symbol")) {
            aSymbol = new Gson().fromJson(value, new TypeToken<ArrayList<ResSymbol>>() {
            }.getType());
            for (ResSymbol symbol : aSymbol) {
                mSymbol.put(symbol.getId(), symbol);
            }
            getLogger().info("res_symbol = " + aSymbol.size());
        } else if (key.equals("res_ufo")) {
            aUfo = new Gson().fromJson(value, new TypeToken<ArrayList<ResUfo>>() {
            }.getType());
            for (ResUfo ufo : aUfo) {
                mUfo.put(ufo.getId(), ufo);
            }
            getLogger().info("res_Ufo = " + aUfo.size());
        } else if (key.equals("res_material")) {
            aMaterial = new Gson().fromJson(value, new TypeToken<ArrayList<ResMaterial>>() {
            }.getType());
            for (int i = 0; i < aMaterial.size(); i++) {
                mMaterial.put(aMaterial.get(i).getId(), aMaterial.get(i));
            }
            getLogger().info("res_material = " + aMaterial.size());
        } else if (key.equals("res_accessories")) {
            aAccessories = new Gson().fromJson(value, new TypeToken<ArrayList<ResAccessories>>() {
            }.getType());
            for (int i = 0; i < aAccessories.size(); i++) {
                mAccessories.put(aAccessories.get(i).getId(), aAccessories.get(i));
            }
            getLogger().info("res_accessories = " + aMaterial.size());
        } else if (key.equals("res_pet_fruit")) {
            aPetFood = new Gson().fromJson(value, new TypeToken<ArrayList<ResPetFood>>() {
            }.getType());
            for (ResPetFood food : aPetFood) {
                mPetFood.put(food.getId(), food);
            }
            getLogger().info("pet res_pet_fruit = " + aPetFood.size());
        } else if (key.equals("res_exp")) {
            initExp(JSONObject.fromObject(value).getJSONArray("user"), userExp);
            initExp(JSONObject.fromObject(value).getJSONArray("hero"), heroExp);
            initExp(JSONObject.fromObject(value).getJSONArray("pet"), petExp);
            maxUserExp = JSONObject.fromObject(value).getInt("maxUser");
            maxHeroExp = JSONObject.fromObject(value).getInt("maxHero");
            maxPetExp = JSONObject.fromObject(value).getInt("maxPet");

        } else if (key.equals("res_exp_pet")) {
            aPetLevel = new Gson().fromJson(value, new TypeToken<ArrayList<ResBaseListLong>>() {
            }.getType());
            getLogger().info("res_exp_pet = " + aPetLevel.size());
        } else if (key.equals("res_upgrade_boom")) {
            aBombUpgrade = new Gson().fromJson(value, new TypeToken<ArrayList<ResBombUpgrade>>() {
            }.getType());
            for (int i = 0; i < aBombUpgrade.size(); i++) {
                mBombUpgrade.put(aBombUpgrade.get(i).getLevel(), aBombUpgrade.get(i));
            }
            getLogger().info("res_upgrade_boom = " + aBombUpgrade.size());
        } else if (key.equals("res_ghep_boom")) {
            aBombGhep = new Gson().fromJson(value, new TypeToken<ArrayList<ResBombGhep>>() {
            }.getType());
            for (int i = 0; i < aBombGhep.size(); i++) {
                mBombGhep.put(aBombGhep.get(i).getRank(), aBombGhep.get(i));
            }
            getLogger().info("res_ghep_boom = " + aBombGhep.size());
        } else if (key.equals("res_upgrade_accessories")) {
            JSONObject obj = JSONObject.fromObject(value);
            JSONArray slot = obj.getJSONArray("slot");
            accessoriesUpgrade.setGoldBase(obj.getInt("goldBase"));
            accessoriesUpgrade.setLevelMax(obj.getInt("maxLevel"));
            for (int i = 0; i < slot.size(); i++) {
                accessoriesUpgrade.getSlotLevelRequire().put(slot.getJSONObject(i).getInt("slot"), slot.getJSONObject(i).getInt("unlock"));
            }
        } else if (key.equals("res_upgrade_ufo")) {
            JSONObject obj = JSONObject.fromObject(value);
            JSONArray stone = obj.getJSONArray("stone");
            ufoUpgrade.setMaxValue(obj.getInt("maxValue"));
            ufoUpgrade.stones.addAll(new Gson().fromJson(new Gson().toJson(stone), new TypeToken<ArrayList<StoneUpgradeUfo>>() {
            }.getType()));
            for (int i = 0; i < ufoUpgrade.stones.size(); i++) {
                if (ufoUpgrade.stones.get(i).lock >= 0) {
                    ufoUpgrade.getMStone().put(ufoUpgrade.stones.get(i).id, ufoUpgrade.stones.get(i));
                }
            }

        } else if (key.equals("res_sale_item")) {
            aSaleItem = new Gson().fromJson(value, new TypeToken<ArrayList<ResSaleItem>>() {
            }.getType());
//            for (int i = 0; i < aBombUpgrade.size(); i++) {
//                mBombUpgrade.put(aBombUpgrade.get(i).getLevel(), aBombUpgrade.get(i));
//            }
            getLogger().info("res_sale_item = " + aSaleItem.size());
        } else if (key.equals("res_upgrade_clothing")) {
            aAvatarUpgrade = new Gson().fromJson(value, new TypeToken<ArrayList<ResAvatarUpgrade>>() {
            }.getType());
            getLogger().info("aAvatarUpgrade.size()---->" + aAvatarUpgrade.size());
            for (int i = 0; i < aAvatarUpgrade.size(); i++) {
                mAvatarUpgrade.put(aAvatarUpgrade.get(i).getLevel(), aAvatarUpgrade.get(i));
            }
            getLogger().info("res_upgrade_clothing = " + mAvatarUpgrade.size());
        } else if (key.equals("res_upgrade_petStar")) {
            aPetStarUpgrade = new Gson().fromJson(value, new TypeToken<ArrayList<ResPetStarUpgrade>>() {
            }.getType());
            getLogger().info("res_upgrade_petStar.size()---->" + aPetStarUpgrade.size());
            for (int i = 0; i < aPetStarUpgrade.size(); i++) {
                mPetStarUpgrade.put(aPetStarUpgrade.get(i).getLv(), aPetStarUpgrade.get(i));
            }
            getLogger().info("res_petStarUpgrade = " + aPetStarUpgrade.size());
        } else if (key.equals("res_hero")) {
            aHero = new Gson().fromJson(value, new TypeToken<ArrayList<ResHero>>() {
            }.getType());
            for (ResHero hero : aHero) {
                hero.init();
                mHero.put(hero.getId(), hero);
            }
            getLogger().info("res_hero = " + aHero.size());
        } else if (key.equals("res_pet")) {
            aPet = new Gson().fromJson(value, new TypeToken<ArrayList<ResPet>>() {
            }.getType());
            getLogger().info("pet lvl = " + aPet.size());
        } else if (key.equals("res_rank")) {
            aRankTrophy = new Gson().fromJson(value, new TypeToken<ArrayList<ResRank>>() {
            }.getType());
            aRankTrophy.forEach(rank -> mRankTrophy.put(rank.id, rank));
        } else if (key.equals("res_opposition")) {
            JSONArray arr = JSONArray.fromObject(value);
            for (int i = 0; i < arr.size(); i++) {
                aDamage[arr.getJSONObject(i).getInt("type")][arr.getJSONObject(i).getInt("target")] = arr.getJSONObject(i).getDouble("damage");
            }
        } else if (key.equals("res_monster_type")) {
            JSONArray arr = JSONArray.fromObject(value);
            for (int i = 0; i < arr.size(); i++) {
                mMonsterType.put(arr.getJSONObject(i).getInt("id"), arr.getJSONObject(i).getInt("type"));
            }
        }
    }

    public static long getGoldSale(int type, int rank) {
        long rs = 0;
        for (int i = 0; i < aSaleItem.size(); i++) {
            if (aSaleItem.get(i).getType() == type && aSaleItem.get(i).getRank() == rank) {
                rs = aSaleItem.get(i).getGold();
                break;
            }
        }
        return rs;
    }

    public static void initExp(JSONArray rate, long[] arrExp) {
        double percent = rate.getDouble(0);
        double base = rate.getDouble(1);

        for (int i = 1; i < 1000; i++) {
            arrExp[i] = Math.round(base * Math.pow(percent, i));
        }
    }

    public static void init() {
        mRankPet.clear();
        for (ResPet pet : aPet) {
            if (!mRankPet.containsKey(pet.getRank())) {
                mRankPet.put(pet.getRank(), new ArrayList<ResPet>());
            }
            mRankPet.get(pet.getRank()).add(pet);
        }

        mRankAvatar.clear();
        for (ResAvatar avatar : aAvatar) {
            if (!mRankAvatar.containsKey(avatar.getRank())) {
                mRankAvatar.put(avatar.getRank(), new ArrayList<ResAvatar>());
            }
            mRankAvatar.get(avatar.getRank()).add(avatar);
        }

        mRankMaterial.clear();
        for (ResMaterial material : aMaterial) {
            if (!mRankMaterial.containsKey(material.getRank())) {
                mRankMaterial.put(material.getRank(), new ArrayList<ResMaterial>());
            }
            mRankMaterial.get(material.getRank()).add(material);
        }

        mRankAccessories.clear();
        for (ResAccessories access : aAccessories) {
            if (!mRankAccessories.containsKey(access.getRank())) {
                mRankAccessories.put(access.getRank(), new ArrayList<ResAccessories>());
            }
            mRankAccessories.get(access.getRank()).add(access);
        }

        mMonster.clear();
        mMonster.put(IMonster.CREEP1, new IMonster(0, 1, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.CREEP2, new IMonster(0, 2, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.BOSS_NAM, new BossCallMonster(0, 3, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.SNOW_BALL, new MonsterSnowBall(0, 4, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.INVISIBLE, new MonsterInvisible(0, 5, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.BOSS_EXPLODE, new BossExplode(0, 6, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.PART_IMMORTAL_BOOM, new IMonster(0, 7, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.KICK_BOMB, new IMonster(0, 8, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.BOSS_BREAK_BOMB, new BossBreakBomb(0, 9, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.MONSTER_POISON, new MonsterPoison(0, 10, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.MONSTER_SHOOTER, new MonsterShooter(0, 11, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.BOSS_POISON, new BossPoison(0, 12, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.MONSTER_IMMORTAL, new MonsterImmortal(0, 13, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.MONSTER_SHOOTER2, new MonsterShooter2(0, 14, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.BOSS_SPACE, new BossSpace(0, 15, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(16, new BossMaterial(0, 16, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(17, new BossMaterial(0, 17, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(18, new BossMaterial(0, 18, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(19, new BossMaterial(0, 19, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(20, new BossMaterial(0, 20, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.BOSS_CRAB, new BossCrab(0, 21, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.MONSTER_KILLER, new MonsterKiller(0, 22, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.PUT_BOMB, new MonsterPutBomb(0, 23, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.KILL_BOOM, new IMonster(0, 24, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.WAR_SHIP, new BossWarShip(0, 25, new MapPos(0, 0), new float[]{0f, 0f}));
        //
        mMonster.put(IMonster.MONSTER_TRAP_DAMAGE, new MonsterTrapDamage(0, 26, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.MONSTER_TRAP_FREEZE, new MonsterSnowBall(0, 27, new MapPos(0, 0), new float[]{0f, 0f}));
        mMonster.put(IMonster.BOSS_WATER_SEAL, new BossWaterSeal(0, 28, new MapPos(0, 0), new float[]{0f, 0f}));
    }

    public static Logger getLogger() {
        return slib_Logger.root();
    }
}
