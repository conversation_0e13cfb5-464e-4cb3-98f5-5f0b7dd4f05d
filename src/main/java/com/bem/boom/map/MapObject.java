package com.bem.boom.map;

import com.bem.boom.IMath;
import com.bem.boom.MapPos;
import com.bem.boom.Resources;
import com.bem.boom.SquareUnit;
import com.bem.boom.monster.IMonster;
import com.google.gson.Gson;
import com.google.protobuf.ByteString;
import com.proto.GGProto;

import java.util.List;

/**
 * Created by vieth_000 on 9/7/2016.
 */
public class MapObject {
    public int mapId, col, row, lastId;
    List<MapUnit> box;
    public List<MapPos> player_pos;
    public List<MonsterUnit> monsters;
    public List<ItemUnit> items;
    public int unlockLevel;

    public SquareUnit[][] convert() {
        SquareUnit[][] map = new SquareUnit[lengthX()][lengthY()];
        for (MapUnit unit : box) {
            map[unit.posX][unit.posY] = new SquareUnit(unit.type, new MapPos(unit.posX, unit.posY), unit.id);
            map[unit.posX][unit.posY].imgId = unit.imgId;
            map[unit.posX][unit.posY].pos = IMath.getUnit(unit.posX, unit.posY);
            map[unit.posX][unit.posY].pos.round();
        }
        for (int i = 0; i < map.length; i++) {
            for (int j = 0; j < map[i].length; j++) {
                if (map[i][j] == null) {
                    map[i][j] = new SquareUnit(0, new MapPos(i, j), ++lastId);
                    map[i][j].imgId = 0;
                    map[i][j].pos = IMath.getUnit(i, j);
                    map[i][j].pos.round();
                }
            }
        }
        return map;
    }

    public int lengthX() {
        return col;
    }

    public int lengthY() {
        return row;
    }

    class MapUnit {
        int id, type, imgId, posX, posY;
    }

    public class ItemUnit {
        public int id, itemId, posX, posY;
    }

    public class MonsterUnit {
        public int id, monsterId, posX, posY;

        public MonsterUnit() {
        }

        public IMonster convert(List<Integer> unitBoss) {
            IMonster monster = Resources.mMonster.get(monsterId).clone(id, monsterId, new MapPos(posX, posY), new float[]{0f, 0f});
            if (monster.isBoss()) {
                unitBoss.add(id);
            }
            return monster;
        }
    }

    public static ByteString protoListMonsterState(List<GGProto.ProtoMonsterState.Builder> aMonsterState) {
        GGProto.ProtoListMonsterState.Builder builder = GGProto.ProtoListMonsterState.newBuilder();
        int size = aMonsterState.size();
        for (int i = 0; i < size; i++) {
            builder.addAMonsterState(aMonsterState.get(0));
            aMonsterState.remove(0);
        }
        return builder.build().toByteString();
    }

//    public static byte[] flatListMonsterState(List<GGProto.ProtoMonsterState.Builder> aMonsterState) {
//        FlatBufferBuilder fbb = new FlatBufferBuilder(1);
//        int size = aMonsterState.size();
//        int[] arrMonster = new int[size];
//        for (int i = 0; i < size; i++) {
//            GGProto.ProtoMonsterState.Builder tmp = aMonsterState.get(i);
//            int status = com.flat.ProtoMonsterState.createStatusVector(fbb, Util.listToArray(tmp.getStatusList()));
//            int info = com.flat.ProtoMonsterState.createInfoVector(fbb, Util.listFloatToArray(tmp.getInfoList()));
//
//            ProtoMonsterState.startProtoMonsterState(fbb);
//            com.flat.ProtoMonsterState.addId(fbb, tmp.getId());
//            com.flat.ProtoMonsterState.addStatus(fbb, status);
//            com.flat.ProtoMonsterState.addInfo(fbb, info);
//            arrMonster[i] = com.flat.ProtoMonsterState.endProtoMonsterState(fbb);
//        }
//        int vector = com.flat.ProtoListMonsterState.createAMonsterStateVector(fbb, arrMonster);
//        com.flat.ProtoListMonsterState.startProtoListMonsterState(fbb);
//        com.flat.ProtoListMonsterState.addAMonsterState(fbb, vector);
//        int mon = com.flat.ProtoListMonsterState.endProtoListMonsterState(fbb);
//        fbb.finish(mon);
//        return fbb.sizedByteArray();
//    }

    public static void main(String[] args) {
        String data = "{\"background\":\"Grass\",\"Objects\":[{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":1},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":2},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":3},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":4},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":5},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":6},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":7},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":8},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":9},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":10},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":11},{\"type\":2,\"imgId\":0,\"posX\":0,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":1,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":1,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":2,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":2,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":3,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":3,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":4,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":4,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":5,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":5,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":6,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":6,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":7,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":7,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":8,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":8,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":9,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":9,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":10,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":10,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":11,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":11,\"posY\":12},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":0},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":1},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":2},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":3},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":4},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":5},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":6},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":7},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":8},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":9},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":10},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":11},{\"type\":2,\"imgId\":0,\"posX\":12,\"posY\":12},{\"type\":1,\"imgId\":6,\"posX\":4,\"posY\":1},{\"type\":1,\"imgId\":5,\"posX\":5,\"posY\":1},{\"type\":1,\"imgId\":7,\"posX\":6,\"posY\":1},{\"type\":1,\"imgId\":6,\"posX\":9,\"posY\":2},{\"type\":1,\"imgId\":5,\"posX\":8,\"posY\":1},{\"type\":2,\"imgId\":2,\"posX\":2,\"posY\":2},{\"type\":2,\"imgId\":2,\"posX\":6,\"posY\":2},{\"type\":2,\"imgId\":2,\"posX\":10,\"posY\":2},{\"type\":2,\"imgId\":2,\"posX\":10,\"posY\":4},{\"type\":2,\"imgId\":2,\"posX\":8,\"posY\":4},{\"type\":2,\"imgId\":2,\"posX\":6,\"posY\":4},{\"type\":2,\"imgId\":2,\"posX\":4,\"posY\":4},{\"type\":2,\"imgId\":2,\"posX\":2,\"posY\":4},{\"type\":2,\"imgId\":2,\"posX\":2,\"posY\":6},{\"type\":2,\"imgId\":2,\"posX\":6,\"posY\":6},{\"type\":2,\"imgId\":2,\"posX\":8,\"posY\":6},{\"type\":2,\"imgId\":2,\"posX\":10,\"posY\":6},{\"type\":2,\"imgId\":2,\"posX\":10,\"posY\":10},{\"type\":2,\"imgId\":2,\"posX\":8,\"posY\":10},{\"type\":2,\"imgId\":2,\"posX\":6,\"posY\":10},{\"type\":2,\"imgId\":2,\"posX\":2,\"posY\":10},{\"type\":1,\"imgId\":5,\"posX\":7,\"posY\":6},{\"type\":2,\"imgId\":2,\"posX\":2,\"posY\":8},{\"type\":2,\"imgId\":2,\"posX\":4,\"posY\":8},{\"type\":2,\"imgId\":2,\"posX\":6,\"posY\":8},{\"type\":2,\"imgId\":2,\"posX\":10,\"posY\":8},{\"type\":1,\"imgId\":1,\"posX\":3,\"posY\":2},{\"type\":1,\"imgId\":1,\"posX\":3,\"posY\":2},{\"type\":1,\"imgId\":6,\"posX\":5,\"posY\":2},{\"type\":1,\"imgId\":6,\"posX\":7,\"posY\":1},{\"type\":1,\"imgId\":5,\"posX\":1,\"posY\":3},{\"type\":1,\"imgId\":7,\"posX\":2,\"posY\":3},{\"type\":1,\"imgId\":7,\"posX\":3,\"posY\":3},{\"type\":1,\"imgId\":3,\"posX\":4,\"posY\":3},{\"type\":1,\"imgId\":2,\"posX\":5,\"posY\":3},{\"type\":1,\"imgId\":7,\"posX\":6,\"posY\":3},{\"type\":1,\"imgId\":7,\"posX\":7,\"posY\":3},{\"type\":1,\"imgId\":5,\"posX\":8,\"posY\":3},{\"type\":1,\"imgId\":4,\"posX\":9,\"posY\":3},{\"type\":1,\"imgId\":6,\"posX\":11,\"posY\":3},{\"type\":1,\"imgId\":3,\"posX\":11,\"posY\":4},{\"type\":1,\"imgId\":5,\"posX\":9,\"posY\":5},{\"type\":1,\"imgId\":3,\"posX\":3,\"posY\":4},{\"type\":1,\"imgId\":5,\"posX\":5,\"posY\":4},{\"type\":1,\"imgId\":6,\"posX\":2,\"posY\":5},{\"type\":1,\"imgId\":6,\"posX\":3,\"posY\":5},{\"type\":1,\"imgId\":7,\"posX\":4,\"posY\":5},{\"type\":1,\"imgId\":6,\"posX\":5,\"posY\":5},{\"type\":1,\"imgId\":6,\"posX\":6,\"posY\":5},{\"type\":1,\"imgId\":7,\"posX\":7,\"posY\":5},{\"type\":1,\"imgId\":7,\"posX\":11,\"posY\":5},{\"type\":1,\"imgId\":1,\"posX\":1,\"posY\":6},{\"type\":1,\"imgId\":6,\"posX\":3,\"posY\":6},{\"type\":1,\"imgId\":5,\"posX\":5,\"posY\":6},{\"type\":1,\"imgId\":7,\"posX\":11,\"posY\":6},{\"type\":1,\"imgId\":5,\"posX\":9,\"posY\":9},{\"type\":1,\"imgId\":6,\"posX\":8,\"posY\":9},{\"type\":1,\"imgId\":5,\"posX\":7,\"posY\":9},{\"type\":1,\"imgId\":6,\"posX\":6,\"posY\":9},{\"type\":1,\"imgId\":7,\"posX\":5,\"posY\":9},{\"type\":1,\"imgId\":6,\"posX\":4,\"posY\":9},{\"type\":1,\"imgId\":1,\"posX\":3,\"posY\":9},{\"type\":1,\"imgId\":6,\"posX\":1,\"posY\":10},{\"type\":1,\"imgId\":6,\"posX\":1,\"posY\":9},{\"type\":1,\"imgId\":3,\"posX\":1,\"posY\":8},{\"type\":1,\"imgId\":6,\"posX\":9,\"posY\":7},{\"type\":1,\"imgId\":3,\"posX\":7,\"posY\":7},{\"type\":1,\"imgId\":2,\"posX\":5,\"posY\":7},{\"type\":1,\"imgId\":5,\"posX\":4,\"posY\":7},{\"type\":1,\"imgId\":3,\"posX\":3,\"posY\":7},{\"type\":1,\"imgId\":6,\"posX\":1,\"posY\":7},{\"type\":1,\"imgId\":3,\"posX\":5,\"posY\":11},{\"type\":1,\"imgId\":4,\"posX\":8,\"posY\":5},{\"type\":1,\"imgId\":2,\"posX\":11,\"posY\":9},{\"type\":1,\"imgId\":4,\"posX\":9,\"posY\":11},{\"type\":1,\"imgId\":5,\"posX\":9,\"posY\":10},{\"type\":1,\"imgId\":6,\"posX\":7,\"posY\":10},{\"type\":2,\"imgId\":1,\"posX\":8,\"posY\":2},{\"type\":1,\"imgId\":6,\"posX\":5,\"posY\":10},{\"type\":1,\"imgId\":5,\"posX\":6,\"posY\":7},{\"type\":1,\"imgId\":1,\"posX\":9,\"posY\":6},{\"type\":1,\"imgId\":5,\"posX\":5,\"posY\":8},{\"type\":1,\"imgId\":6,\"posX\":6,\"posY\":11},{\"type\":2,\"imgId\":3,\"posX\":4,\"posY\":2},{\"type\":2,\"imgId\":3,\"posX\":4,\"posY\":6},{\"type\":1,\"imgId\":6,\"posX\":3,\"posY\":11},{\"type\":2,\"imgId\":3,\"posX\":4,\"posY\":10},{\"type\":1,\"imgId\":4,\"posX\":11,\"posY\":7},{\"type\":2,\"imgId\":3,\"posX\":8,\"posY\":8}]}";
        Gson gson = new Gson();
        MapObject abc = gson.fromJson(data, MapObject.class);
    }
}
