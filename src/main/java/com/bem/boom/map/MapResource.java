package com.bem.boom.map;

import com.bem.config.CfgCommon;
import com.bem.dao.mapping.MapEntity;
import grep.database.Database2;

import java.util.*;

/**
 * Created by vieth_000 on 9/30/2016.
 */
public class MapResource {
    static Map<Integer, MapEntity> map = new HashMap<Integer, MapEntity>();
    static List<Integer> soloMap = new ArrayList<Integer>();
    static List<Integer> zombieMap = new ArrayList<Integer>();

    public static int getSoloMap() {
        return soloMap.get(new Random().nextInt(soloMap.size()));
    }

    public static int getZombieMap() {
        int mapid = zombieMap.get(new Random().nextInt(zombieMap.size()));
        return mapid;
    }

    public static MapEntity getMap(int mapId) {
        return map.get(mapId);
    }

    public static MapEntity randomMap() {
        return map.get(new Random().nextInt(map.size()) + 1);
    }

    public static void init() {
        soloMap.clear();
        zombieMap.clear();
        map.clear();

        List<MapEntity> aMap = Database2.getList(CfgCommon.mainDb + CfgCommon.tableMap, new ArrayList<>(), "where enable=1", MapEntity.class);
        //        if (CfgServer.isTest()) {
        //            aMap = Database.getList(CfgCommon.mainDb + CfgCommon.tableMap, new ArrayList<>(), "where enable=0", MapEntity.class);
        //        }
        for (MapEntity tmp : aMap) {
            tmp.init();
            map.put(tmp.getId(), tmp);
            if (tmp.getId() >= 2000 && tmp.getId() < 3000) {
                soloMap.add(tmp.getId());
            }
            if (tmp.getId() >= 3300 && tmp.getId() < 3305) {
                zombieMap.add(tmp.getId());
            }
        }
        checkValidMap();
    }

    static void checkValidMap() {
        for (MapEntity map : map.values()) {

        }
    }
}
