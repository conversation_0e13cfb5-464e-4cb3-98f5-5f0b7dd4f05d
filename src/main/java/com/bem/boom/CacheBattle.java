package com.bem.boom;

import com.bem.boom.object.CachePlayer;
import com.bem.config.CfgServer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by vieth_000 on 4/20/2017.
 */
@Data
public class CacheBattle implements Serializable {

    public static int MODE_NORMAL = 0;
    public static int MODE_GLOBAL = 1;

    List<CachePlayer> aPlayer;
    int mapId, serverId, battleId, type, levelIndex, curStar, mode;
    long hostPlayerId;
    String key;

    public CacheBattle(List<CachePlayer> aPlayer, int mapId, String key, int type) {
        this.aPlayer = aPlayer;
        this.mapId = mapId;
        this.serverId = CfgServer.SERVER_ID;
        if (CfgServer.SERVER_ID == 124 || CfgServer.SERVER_ID == 300) {
            this.serverId = 1;
        } else if (CfgServer.SERVER_ID == 125) {
            this.serverId = 2;
        }
        this.key = key;
        this.type = type;
    }
}
