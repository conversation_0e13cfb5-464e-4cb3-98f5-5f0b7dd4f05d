package com.bem.boom;

import com.k2tek.common.slib_Logger;

/**
 * Created by vieth_000 on 7/19/2016.
 */
public class MapPos {
    public int x, y;

    public MapPos() {
    }

    public MapPos(int x, int y) {
        this.x = x;
        this.y = y;
    }

    public MapPos clone() {
        return new MapPos(x, y);
    }

    public MapPos clone(MapPos mPos) {
        return new MapPos(x + mPos.x, y + mPos.y);
    }

    public void move(MapPos mpos) {
        x += mpos.x;
        y += mpos.y;
    }

    public MapPos chaoTic(MapPos input) {
        if (input != null) {
            return new MapPos(-input.x, -input.y);
        }
        return null;
    }

    public void move(int moveX, int moveY) {
        x += moveX;
        y += moveY;
    }

    // round for jumping bomb over edge of map
    public float round(int column, int row) {
        column -= 1; //ignore vien
        row -= 1;
        float timeTravel = 0;
        while (x >= column) {
            x = x - column + 1;
            timeTravel += 1.2f;
        }
        while (x <= 0) {
            x = x + column - 1;
            timeTravel += 1.2f;
        }
        while (y >= row) {
            y = y - row + 1;
            timeTravel += 1.2f;
        }
        while (y <= 0) {
            y = y + row - 1;
            timeTravel += 1.2f;
        }
        return timeTravel;
    }

    public boolean equals(MapPos obj) {
        if (obj == null) return false;
        return obj.x == x && obj.y == y;
    }

    public int plus(MapPos mPos) {
        return x * mPos.x + y * mPos.y;
    }

    public int getIntDirection() {
        if (y < 0) return 0;
        if (x > 0) return 1;
        if (y > 0) return 2;
        return 3;
    }

    public String toString() {
        return String.format("(%s, %s)", x, y);
    }
}
