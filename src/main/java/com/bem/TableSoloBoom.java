package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.map.MapResource;
import com.bem.boom.unit.Player;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.ResShopItem;
import com.bem.util.CommonProto;
import com.google.gson.Gson;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Nguoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableSoloBoom extends AbstractTable {

    //    public TableSoloBoom(List<TeamObject> aTeam, int type, int mapId) {
//        super(aTeam, type, mapId);
//        if (type == TeamObject.TRAIN) {
//            initMap();
//        } else {
//            timer(ID_CREATE_MAP);
//        }
//    }
    public TableSoloBoom() {

    }

    public TableSoloBoom(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mapId = MapResource.getSoloMap();
//        if (CfgServer.isTest()) {
//            this.mapId = 2700 + new Random().nextInt(8);
//        }
        this.mode = cacheBattle.getType();
        delayStartGame = 5;
//        MainCache.getInstance().addBossCCU(numberPlayer, id, String.valueOf(this.hashCode()) + " " + mapId);
        initMap();
    }

    long winnerId;

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
        if (channel == null) return;
        List<Long> aBonus = new ArrayList<>();
        if (player.user.reviveItem == 2) aBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));
        aBonus.addAll(Arrays.asList((long) Bonus.GAME_NUMBER_ATK, (long) Constans.EVENT_CLAN_BATTLE, 2L, -1L, 0L));
        updateBattleBonus(player.user.id, new Gson().toJson(aBonus));

        ProtoEndgame.Builder protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(10);

        ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
        playerBuilder.setTeam(player.user.team);
        protoEndgame.addAResult(playerBuilder);

        sendPlayer(channel, IAction.LEAVE_TABLE_1, protoEndgame.build());
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
        if (!isPlay) {
            Logs.debug("endgame lan 2 rank");
            return;
        }
        isPlay = false;
        protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(teamWin);
        protoEndgame.setTimeCreated(System.currentTimeMillis());

        for (int i = 0; i < aPlayer.size(); i++) {
            Player player = aPlayer.get(i);
            resetPlayer(player);
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
            if (player.user.reviveItem == 2)
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));
            if (isMatchType(TeamObject.GAME_CLAN_BATTLE) && player.user.team == teamWin)
                playerBuilder.addAllExBonus(Arrays.asList((long) Bonus.GAME_NUMBER_ATK, (long) Constans.EVENT_CLAN_BATTLE, 2L, 1L, aPlayer.get(1 - i).user.id));
            playerBuilder.setUser(CommonProto.protoUser(player.user));
            playerBuilder.setTeam(player.user.team);
            playerBuilder.setStatus(player.leaveStatus);
            protoEndgame.addAResult(playerBuilder);
        }
        protoEndgame.setGameId(Integer.parseInt(id));
        super.endGame();
    }

    public void checkEndGame() {
        int teamAlive = -1;
        for (Player player : aPlayer) {
            if (player.isAlive()) {
                if (teamAlive == -1) {
                    teamAlive = player.user.team;
                } else if (teamAlive != player.user.team) {
                    return;
                }
            }
        }
        teamWin = teamAlive;
        gameState = STATE_END_GAME;
    }

    void doAction() {

    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableSoloBoom(cacheBattle);
    }

}
