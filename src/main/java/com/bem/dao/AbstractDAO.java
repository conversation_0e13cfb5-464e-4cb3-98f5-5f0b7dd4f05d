package com.bem.dao;

import com.bem.util.Util;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import org.hibernate.Session;

public abstract class AbstractDAO {
    public <T> boolean saveObject(T t) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.save(t);
            session.getTransaction().commit();
            return true;
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return false;
    }

    protected void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
}
