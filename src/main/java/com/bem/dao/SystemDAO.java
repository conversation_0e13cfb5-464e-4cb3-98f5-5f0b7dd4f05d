/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.dao;

import com.bem.config.GameCfgSlotMachine;
import com.bem.dao.mapping.*;
import com.bem.util.Util;
import com.k2tek.common.Logs;
import grep.database.Database2;
import grep.database.HibernateUtil;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class SystemDAO {

    public List<SystemSendMessage> getListSystemMessage() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from boom.system_send_message where time_end>now()").addEntity(SystemSendMessage.class);
            return query.getResultList();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return new ArrayList<>();
    }

    public ConfigLocalEntity getConfigLocal(String key) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return (ConfigLocalEntity) session.createSQLQuery("select * from config_local c where c.key='" + key + "'").addEntity(ConfigLocalEntity.class).uniqueResult();
        } catch (HibernateException he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public ConfigEntity getConfig(String key) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return (ConfigEntity) session.createSQLQuery("select * from boom.config c where c.key='" + key + "'").addEntity(ConfigEntity.class).uniqueResult();
        } catch (HibernateException he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public boolean sendMail(long userId, String title, String award) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            UserMessageEntity uMessage = new UserMessageEntity();
            uMessage.setTitle(title);
            uMessage.setDateCreated(new Date());
            uMessage.setUserId(userId);
            uMessage.setBonus(award);
            session.save(uMessage);
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            if (session != null) {
                session.close();
            }
        }
        return false;
    }

    public boolean sendMail(long userId, String title, String message, String award) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            UserMessageEntity uMessage = new UserMessageEntity();
            uMessage.setTitle(title);
            uMessage.setMessage(message);
            uMessage.setDateCreated(new Date());
            uMessage.setUserId(userId);
            uMessage.setBonus(award);
            session.save(uMessage);
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            if (session != null) {
                session.close();
            }
        }
        return false;
    }

    public Jackpot getJackPot() {
        Session session = null;
        try {
            int serverId = 1;
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select * from boom.jackpot where server_id=" + serverId;
            Jackpot ret = (Jackpot) session.createSQLQuery(sql).addEntity(Jackpot.class).uniqueResult();
            if (ret == null) {
                Jackpot jackpot = new Jackpot(serverId, GameCfgSlotMachine.minJackpot, 0L, "[]");
                jackpot.setLastJackpot(new Date());
//                session.beginTransaction();
//                session.save(jackpot);
//                session.getTransaction().commit();
                Database2.insert("boom.jackpot", Arrays.asList("server_id", "pot", "payout", "history", "min_receive", "max_day"), Arrays.asList(String.valueOf(jackpot.getServerId()), String.valueOf(jackpot.getPot()), String.valueOf(jackpot.getPayout()), String.valueOf(jackpot.getHistory()), String.valueOf(jackpot.getMinReceive()), String.valueOf(jackpot.getMaxDay())));
                return jackpot;
            } else {
                return ret;

            }
        } catch (Exception he) {
            he.printStackTrace();
//            getLogger().error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    private void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
}
