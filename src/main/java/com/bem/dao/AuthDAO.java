/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.dao;

import com.bem.boom.object.MyAvatar;
import com.bem.config.CfgCommon;
import com.bem.config.CfgServer;
import com.bem.dao.mapping.*;
import com.bem.monitor.Bonus;
import com.bem.monitor.BonusBuilder;
import com.bem.object.UserInfo;
import com.bem.util.Util;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import grep.helper.StringHelper;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class AuthDAO {

    private static int PAGE = 10;

    public AuthUserSession getUserSession(String username) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from boom.auth_user_session where username=:username order by id desc limit 1").addEntity(AuthUserSession.class);
            query.setString("username", username);
            List<AuthUserSession> list = query.getResultList();
            return list.isEmpty() ? null : list.get(0);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public boolean connectAccount(UserInfo user, String hpass) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query1 = session.createSQLQuery("update boom.auth_user set username=:username, hpass=:hpass where id=" + user.getId());
            query1.setString("username", user.getUsername());
            query1.setString("hpass", hpass);

            query1.executeUpdate();
            SQLQuery query2 = session.createSQLQuery("update boom.user_udid set username=:username where udid=:udid");
            query2.setString("udid", user.getAuthUser().getUdid());
            query2.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean existUsername(String username) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select count(*) from boom.auth_user where username=:username");
            query.setString("username", username);
            Integer count = Integer.parseInt(query.uniqueResult().toString());
            return count > 0;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return true;
    }

    public AuthUserEntity getUserByUsername(String username, int serverId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from " + CfgCommon.mainDb + "auth_user where username=:username").addEntity(AuthUserEntity.class);
            query.setString("username", username);
            AuthUserEntity auth = (AuthUserEntity) query.uniqueResult();
            if (auth == null) auth = new AuthUserEntity(-1);
            else if (CfgServer.isMainServer()) {
                String newServerIds = "";
                String serverIds = auth.getServerIds() == null ? "" : auth.getServerIds();
                String[] tmp = serverIds.split(",");
                for (String s : tmp) {
                    if (!s.equals(String.valueOf(serverId))) newServerIds += s + ",";
                }
                newServerIds += serverId + ",";
                session.beginTransaction();
                session.createSQLQuery("update " + CfgCommon.mainDb + "auth_user set server_ids='" + newServerIds + "' where id=" + auth.getId()).executeUpdate();
                session.getTransaction().commit();
            }
            return auth;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public AuthUserEntity getUserByUdid(String udid) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select username from boom.user_udid where udid=:udid");
            query.setString("udid", udid);
            Object result = query.uniqueResult();
            String username = result == null ? null : query.uniqueResult().toString();
            if (username != null) {
                query = session.createSQLQuery("select * from boom.auth_user where username=:username").addEntity(AuthUserEntity.class);
                query.setString("username", username);
                AuthUserEntity auth = (AuthUserEntity) query.uniqueResult();
                if (auth == null) {
                    auth = new AuthUserEntity(-1);
                }
                return auth;
            }
            return new AuthUserEntity(-1);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserEntity getLoginUserEntity(AuthUserEntity authUser, String cp, int severId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            UserEntity user = (UserEntity) session.createSQLQuery("select * from user where main_id=" + authUser.getId() + " and server = " + severId).addEntity(UserEntity.class).uniqueResult();
            if (user == null) {
                session.beginTransaction();
                user = new UserEntity();
                user.setUdidLogin("[]");
                user.setCp(cp);
                user.setDiemdanh("[0,0]");
                user.setFacebook(authUser.getFacebook() == null ? "" : authUser.getFacebook());
                user.setSettings("[0]");
                user.setMainId(authUser.getId());
                user.setBattleItem("[]");
                user.setTopBossCur("[]");
                user.setTopBossMax("[]");
                user.setUsername(severId + "_" + authUser.getUsername());
                user.setName("");
                user.setAvatar(new MyAvatar().toString());
                user.setTrophy(CfgCommon.config.newUser.trophy);
                user.setGold(CfgCommon.config.newUser.gold);
                user.setGem(CfgCommon.config.newUser.gem);
                user.setServer(severId);
                session.save(user);
                user = (UserEntity) session.createSQLQuery("select * from user where main_id=" + authUser.getId() + " and server=" + severId).addEntity(UserEntity.class).uniqueResult();
                session.saveOrUpdate(new UserDataEntity(user.getId()));

                UserMessageEntity uMessage = new UserMessageEntity();
                uMessage.setTitle("System");
                uMessage.setMessage("Account creation gift");
                uMessage.setDateCreated(new Date());
                uMessage.setUserId(user.getId());
                uMessage.setBonus(StringHelper.toDBString(Bonus.defaultBonusView(Bonus.GEM, 100)));
                session.save(uMessage);
                for (int i = 0; i < 5; i++) {
                    UserMessageEntity eventMessage = new UserMessageEntity();
                    eventMessage.setTitle("System");
                    eventMessage.setMessage("Event bonus");
                    eventMessage.setDateCreated(new Date());
                    eventMessage.setUserId(user.getId());
                    eventMessage.setBonus(BonusBuilder.newInstance().addGem(20).addGold(50000).addPetFragment(3, 5).toString());
                    session.save(eventMessage);
                }
                if (!CfgServer.isTest() && !CfgServer.isSubmit()) {
                    String serverIds = authUser.getServerIds() == null ? "" : authUser.getServerIds();
                    if (!serverIds.contains(serverIds + ",")) {
                        serverIds += serverIds + ",";
                        session.createSQLQuery("update boom.auth_user set server_ids='" + serverIds + "' where id=" + authUser.getId()).executeUpdate();
                    }
                }
                session.getTransaction().commit();
            }
            return user;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public AuthUserEntity register(String username, String hpass, String udid, String mobile, String cp, String subCp, String os, String osVersion, String clientVersion) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            //
            AuthUserEntity auth = new AuthUserEntity();
            auth.setHpass(hpass);
            auth.setUdid(udid);
            auth.setMobile(mobile);
            auth.setUsername(username);
            auth.setCp(cp);
            auth.setSubcp(subCp);
            auth.setOs(os);
            auth.setOsVersion(osVersion);
            auth.setVersion(clientVersion);
            auth.setLastLogin(new Date());
            auth.setFacebook("");
            session.save(auth);
            //
            session.getTransaction().commit();
            //
            return auth;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public AuthUserEntity saveAuthUser(String name, String fbId, String udid, String cp, String subCp, String os, String osVersion, String clientVersion) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            UserUdidEntity userUdid = new UserUdidEntity(fbId + udid);
            session.save(userUdid);
            String username = "boom_" + userUdid.getId();
            session.createSQLQuery("update boom.user_udid set username='" + username + "' where id=" + userUdid.getId()).executeUpdate();
            userUdid.setUsername(username);
            //
            AuthUserEntity auth = new AuthUserEntity();
            auth.setUdid(fbId + udid);
            auth.setUsername(username);
            auth.setCp(cp);
            auth.setSubcp(subCp);
            auth.setOs(os);
            auth.setOsVersion(osVersion);
            auth.setVersion(clientVersion);
            auth.setLastLogin(new Date());
            auth.setFacebook(fbId);
            session.save(auth);
            //
            session.getTransaction().commit();
            //
            return auth;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserEntity getUserEntity(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return (UserEntity) session.createSQLQuery("select * from user c where c.id=" + userId).addEntity(UserEntity.class).uniqueResult();
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }


    public boolean updateUserData(int userId, String key, String value) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user_data set " + key + "=? where user_id=" + userId);
            query.setString(0, value);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    private void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
}