/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.bem.dao;

import com.bem.boom.object.MyAvatar;
import com.bem.config.CfgCluster;
import com.bem.config.CfgCommon;
import com.bem.dao.mapping.*;
import com.bem.util.Util;
import com.k2tek.Xerver;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class IapDAO extends AbstractDAO {

    public IapRequest getRequest(String reqId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from boom.iap_request where req_id=:reqId")
                    .addEntity(IapRequest.class).setParameter("reqId", reqId);
            List<IapRequest> retValues = query.getResultList();
            return retValues.isEmpty() ? null : retValues.get(0);
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserEntity getUserMain(long userId, int server) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return (UserEntity) session.createCriteria(UserEntity.class, "user").add(Restrictions.eq("mainId", userId)).add(Restrictions.eq("server", server)).uniqueResult();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public int isFriend(long user1, long user2) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select count(*) from user_friend where ((user2=:id1 and user1=:id2) or (user2=:id2 and user1=:id1)) and fstatus=1");
            query.setLong("id1", user1);
            query.setLong("id2", user2);
            return Integer.parseInt(query.uniqueResult().toString());
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public int getGemInEvent(String username, String timeBegin, String timeEnd, int serverId) {
        username = CfgCluster.getRealUsername(username);
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select sum(koin_init) from boom.log_nap_koin where username=:uname and created_on>='" + timeBegin + "' and created_on<='" + timeEnd + "' and server_id =" + serverId);
            query.setString("uname", username);
            Object value = query.uniqueResult();
            if (value == null) return 0;
            return Integer.parseInt(value.toString());
        } catch (Exception he) {
            he.printStackTrace();
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return 0;
    }

    public int getNumberFriend(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select count(*) from user_friend where (user2=:id or user1=:id) and fstatus=1");
            query.setLong("id", userId);
            return Integer.parseInt(query.uniqueResult().toString());
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public List<UserFriendEntity> dbListUserFriend(long userId, int maxFriend) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_friend where (user2=:id or user1=:id) and fstatus=1 limit 0," + maxFriend).addEntity(UserFriendEntity.class);
            query.setLong("id", userId);
            return query.list();
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserFriendEntity dbGetUserFriend(long userId, long userFreinds) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_friend where (user2=:id or user1=:id) and(user2=:idF or user1=:idF ) and fstatus=1 limit 1").addEntity(UserFriendEntity.class);
            query.setLong("id", userId);
            query.setLong("idF", userFreinds);
            List<UserFriendEntity> aFriend = query.list();
            return aFriend.isEmpty() ? null : aFriend.get(0);
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserFriendEntity dbHasFamily(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_friend where (user2=:id or user1=:id) and wife_status = 1 and fstatus=1 limit 1").addEntity(UserFriendEntity.class);
            query.setLong("id", userId);

            if (query.list() != null && query.list().size() > 0) {
                return (UserFriendEntity) query.list().get(0);
            }
            return null;
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }


    public AuthUserEntity getUserByUsername(String username) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from auth_user where username=:username").addEntity(AuthUserEntity.class);
            query.setString("username", username);
            AuthUserEntity auth = (AuthUserEntity) query.uniqueResult();
            if (auth == null) {
                auth = new AuthUserEntity(-1);
            }
            return auth;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public AuthUserEntity getUserByUdid(String udid) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select id from user_udid where udid=:udid");
            query.setString("udid", udid);
            Object result = query.uniqueResult();
            Integer id = result == null ? null : Integer.parseInt(query.uniqueResult().toString());
            if (id != null) {
                query = session.createSQLQuery("select * from auth_user where username=:username").addEntity(AuthUserEntity.class);
                query.setString("username", "boom_" + id);
                AuthUserEntity auth = (AuthUserEntity) query.uniqueResult();
                if (auth == null) {
                    auth = new AuthUserEntity(-1);
                }
                return auth;
            }
            return new AuthUserEntity(-1);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public List<topTrophyDaily> getListTopTrophy(String date) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return session.createSQLQuery("select * from top_trophy_daily where date(top_trophy_daily.created_on) =  '" + date + "'").addEntity(topTrophyDaily.class).list();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return new ArrayList<topTrophyDaily>();
    }

    public List<UserEntity> getListavatar() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return session.createSQLQuery("select * from user ").addEntity(UserEntity.class).list();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return new ArrayList<UserEntity>();
    }

    public List<UserDataEntity> getListUserData() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return session.createSQLQuery("select * from boom_s1.user_data").addEntity(UserDataEntity.class).list();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return new ArrayList<UserDataEntity>();
    }

    public List<UserEntity> getListUserEntity() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return session.createSQLQuery("select * from boom_s1.user").addEntity(UserEntity.class).list();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return new ArrayList<UserEntity>();
    }

    public AuthUserEntity saveAuthUser(String name, String fbId, String udid, String cp, String subCp, String os, String osVersion, String clientVersion) {
        Session session = null;
        Session mainSession = null;
        try {
            mainSession = HibernateUtil.getSessionFactory().openSession();
            mainSession.beginTransaction();
            UserUdidEntity userUdid = new UserUdidEntity(fbId + udid);
            mainSession.save(userUdid);
            String username = "boom_" + userUdid.getId();
            //
            AuthUserEntity auth = new AuthUserEntity();
            auth.setUdid(fbId + udid);
            auth.setUsername(username);
            auth.setCp(cp);
            auth.setSubcp(subCp);
            auth.setOs(os);
            auth.setOsVersion(osVersion);
            auth.setVersion(clientVersion);
            auth.setLastLogin(new Date());
            auth.setFacebook(fbId);
            mainSession.save(auth);
            //
            mainSession.getTransaction().commit();
            //
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            UserEntity user = new UserEntity();
            user.setDiemdanh("[0,0]");
            user.setFacebook(fbId);
            user.setSettings("[0]");
            user.setId(auth.getId());
            user.setBattleItem("[]");
            user.setTopBossCur("[]");
            user.setTopBossMax("[]");

            user.setUsername(auth.getUsername());
            user.setName(name);
            user.setAvatar(new MyAvatar().toString());
            user.setTrophy(0);
            session.save(user);
            //
            session.save(new UserDataEntity(auth.getId()));
            //
            session.getTransaction().commit();
            //
            return auth;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
            closeSession(mainSession);
        }
        return null;
    }

    public UserEntity getUserEntity(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return (UserEntity) session.createSQLQuery("select * from user c where c.id=" + userId).addEntity(UserEntity.class).uniqueResult();
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public boolean changeBomb(long bomId1, long bomId2, int lv1, int lv2) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user_bomb set level=? where id=" + bomId1);
            query.setLong(0, lv1);
            query.executeUpdate();
            SQLQuery query1 = session.createSQLQuery("update user_bomb set level=? where id=" + bomId2);
            query1.setLong(0, lv2);
            query1.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean updateUserData(int userId, String key, String value) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user_data set " + key + "=? where user_id=" + userId);
            query.setString(0, value);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public String getUserData(int userId, String key) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select " + key + " from user_data where user_id=" + userId;
            Object ret = session.createSQLQuery(sql).uniqueResult();
            if (ret == null) {
                session.beginTransaction();
                session.createSQLQuery("insert user_data(user_id) values(" + userId + ")").executeUpdate();
                session.getTransaction().commit();
                return "";
            } else {
                return (String) ret;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserDataEntity getUserData(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select * from user_data where user_id=" + userId;
            Object ret = session.createSQLQuery(sql).uniqueResult();
            if (ret == null) {
                session.beginTransaction();
                session.createSQLQuery("insert user_data(user_id) values(" + userId + ")").executeUpdate();
                session.getTransaction().commit();
                return null;
            } else {
                return (UserDataEntity) ret;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public boolean updateUserGold(String username, long gold) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user set gold = gold + (:gold)  where username=:username");
            query.setLong("gold", gold);
            query.setString("username", username);
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean updateUserGem(String username, long gem) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user set gem = gem + (:gem)  where username=:username");
            query.setLong("gem", gem);
            query.setString("username", username);
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean updateUserMoney(long userId, int gem, int gold, int star) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user set gem=gem+:gem,gold=gold+:gold,star=star+:star where id=" + userId);
            query.setInteger("gem", gem);
            query.setInteger("gold", gold);
            query.setInteger("star", star);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }


    public void removeListUser(String ids) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            if (ids.length() > 0) {
                session.beginTransaction();
                ids = "(" + ids + ")";
                session.createSQLQuery("delete from user_more where id in " + ids).executeUpdate();
                session.createSQLQuery("delete from user_friend where id in " + ids + " or friend_id in " + ids).executeUpdate();
                session.createSQLQuery("delete from user where id in " + ids).executeUpdate();
                session.getTransaction().commit();
            }

        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
    }

    public int updateUserEntity(UserEntity user) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.save(user);
            session.getTransaction().commit();
            return 0;
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public void updateCacheBalance(int userId, long balance) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("update user set cache_balance=" + balance + " where id=" + userId).executeUpdate();
            session.getTransaction().commit();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
    }

    public int removeMessage(int userId, int msgId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            if (msgId == -1) {
                session.createSQLQuery("delete from user_message where user_id=" + userId).executeUpdate();
                session.createSQLQuery("update user set notify_message=0 where id=" + userId).executeUpdate();
                return 1;
            }
            Query query = session.createSQLQuery("delete from user_message where id=" + msgId + " and user_id=" + userId);
            int row = query.executeUpdate();
            if (row > 0) {
                session.createSQLQuery("update user set notify_message=notify_message-1 where id=" + userId).executeUpdate();
                return 1;
            }
            return 0;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            session.getTransaction().commit();
            closeSession(session);
        }
        return -1;
    }

    public String insertUserGiftcode(long userId, int code) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("insert into user_common_giftcode (user_id,code_id) values(" + userId + "," + code + ")").executeUpdate();
            session.getTransaction().commit();
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public int getcommonGiftcode(String code) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select id from " + CfgCommon.mainDb + "common_giftcode where code = '" + code + "' and '" + Util.formatDateSql(new Date()) + "'> date_begin and '" + Util.formatDateSql(new Date()) + "' < date_end";
            Query query = session.createSQLQuery(sql);
            Integer codeId = (Integer) query.uniqueResult();
            if (codeId == null) return -1;
            return codeId;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public int getdiemcapdoi(long userId, int event_id, int evntIndex) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select value from user_event where user_id = " + userId + " and event_index = " + evntIndex + " and event_id =" + event_id;
            Query query = session.createSQLQuery(sql);
            Long codeId = Long.parseLong(query.uniqueResult().toString());
            if (codeId == null) return -1;
            return codeId.intValue();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public String getcommonGiftcodeAward(String code) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select gift from " + CfgCommon.mainDb + "common_giftcode where code = '" + code + "' and '" + Util.formatDateSql(new Date()) + "'> date_begin and '" + Util.formatDateSql(new Date()) + "' < date_end";

            Query query = session.createSQLQuery(sql);
            //            query.getReturnAliases();
            return query.uniqueResult().toString();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public int getUsercommonGiftcode(int codeId, long user_id) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select count(*) from user_common_giftcode where user_id = " + user_id + " and code_id=" + codeId;
            Query query = session.createSQLQuery(sql);
            //            query.getReturnAliases();
            return Integer.parseInt(query.uniqueResult().toString());
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public int getTotalUserMessage(int userId, int type) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select count(*) from user_message where user_id=" + userId;
            if (type == 1) {
                sql += " and status=0";
            } else if (type == 2) {
                sql += " and status=1";
            }
            Query query = session.createSQLQuery(sql);
            return Integer.parseInt(query.uniqueResult().toString());
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public static void main(String[] args) throws Exception {
        com.k2tek.Config.load(Xerver.DEFAULT_CONFIG_FILE);
    }

    private int getIntVersion(String version) {
        try {
            return Integer.parseInt(version.replaceAll("\\.", ""));
        } catch (Exception ex) {
        }
        return 0;
    }

    public int updateResult(boolean playerQuit, List<Integer> moneys, List<Integer> ids, List<Integer> experience, List<Integer> levels, String game) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            String sql = "";
            for (int i = 0; i < ids.size(); i++) {
                boolean isWin = false;
                if (moneys.get(i) > 0) {
                    isWin = true;
                }
                if (isWin) {
                    if (!playerQuit) {
                        sql = "update user_more set " + "experience=experience+(" + experience.get(i) + "), " + "level=" + levels.get(i) + ", win=win+1 where id=" + ids.get(i) + " and game='" + game + "';";
                        SQLQuery query = session.createSQLQuery(sql);
                        query.executeUpdate();
                    }
                } else {
                    sql = "update user_more set " + "experience=experience+(" + experience.get(i) + "), " + "level=" + levels.get(i) + ", loose=loose+1 where id=" + ids.get(i) + " and game='" + game + "';";
                    SQLQuery query = session.createSQLQuery(sql);
                    query.executeUpdate();
                }
            }
            session.getTransaction().commit();
            return 1;
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public int updateItem(int userId, int itemID, int quan) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user_item set quantity=quantity+(" + quan + ") where user_id=" + userId + " and item_id =" + itemID);
            int rowCount = query.executeUpdate();
            session.getTransaction().commit();
            if (rowCount > 0) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public void removeExpireAvatar(int userId, String expireAvatars) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("delete from user_avatar where user_id=" + userId + " and avatar_id in (" + expireAvatars + ")");
            query.executeUpdate();
            session.getTransaction().commit();
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
    }

}