package com.bem.dao.mapping;

import com.proto.GGProto;
import grep.helper.JsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import net.sf.json.JSONArray;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by vieth_000 on 7/10/2017.
 */
@Data
@Entity
@Table(name = "clan_battle_bonus")
public class ClanBattleBonusEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    @Column(name = "clan_id")
    private int clanId;
    private String message;
    private String bonus;
    private int number;
    private String receiver;
    @Column(name = "date_modified")
    private Date dateModified;
    @Column(name = "date_created")
    private Date dateCreated;

    public ClanBattleBonusEntity() {
        dateCreated = new Date();
        dateModified = new Date();
    }

    public int getNumberBonusAvailable() {
        if (receiver.length() == 0) return number;
        return number - receiver.split(",").length;
    }

    public void addReceiver(String name) {
        receiver += name + ",";
    }

    public GGProto.CommonVector.Builder toProto() {
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        builder.addANumber(id);
        builder.addANumber(number);
        builder.addANumber(getNumberBonusAvailable());
        builder.addAllANumber(JsonUtil.convertToListLong(JSONArray.fromObject(bonus)));
        builder.addAString(message);
        if (StringHelper.isEmpty(receiver)) builder.addAString("");
        else {
            builder.addAString("+ " + receiver.substring(0, receiver.length() - 2).replaceAll(",", "\n+ "));
        }
        return builder;
    }
}
