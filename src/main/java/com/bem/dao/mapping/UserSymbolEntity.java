package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_symbol")
@Entity
public class UserSymbolEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "symbol_id")
    private int symbolId;

    public UserSymbolEntity() {
    }

    public UserSymbolEntity(long userId, int symbolId) {
        this.userId = userId;
        this.symbolId = symbolId;
    }
}
