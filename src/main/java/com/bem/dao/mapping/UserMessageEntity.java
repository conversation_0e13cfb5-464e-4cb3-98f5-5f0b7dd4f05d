package com.bem.dao.mapping;

import lombok.Data;
import net.sf.json.JSONArray;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by vieth_000 on 11/24/2016.
 */
@Data
@Table(name = "user_message")
@Entity
public class UserMessageEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    @Column(name = "user_id")
    private long userId;
    private String title;
    private String message = "", bonus;
    private int receive;
    @Column(name = "date_created")
    private Date dateCreated;
//    private boolean bonusMsg;

    public List<Integer> getListBonus() {
        try {
            JSONArray arr = JSONArray.fromObject(bonus);
            List<Integer> aInt = new ArrayList<Integer>();
            for (int i = 0; i < arr.size(); i++) {
                aInt.add(arr.getInt(i));
            }
            return aInt;
        } catch (Exception ex) {

        }
        return new ArrayList<Integer>();
    }

}
