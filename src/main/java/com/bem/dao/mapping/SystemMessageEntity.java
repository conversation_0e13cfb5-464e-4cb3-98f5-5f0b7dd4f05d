package com.bem.dao.mapping;

import com.proto.GGProto;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by vieth_000 on 5/10/2017.
 */
@Data
@Entity
@Table(name = "system_message")
public class SystemMessageEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private int enable;
    private String title;
    private String content;
    private String os;
    private String cp;
    private String version, actionData;
    private int server;
    private int type, actionType;
    private Date dateBegin;
    private Date dateEnd;
    private Date dateCreated;

    public GGProto.CommonVector toProto() {
        GGProto.CommonVector.Builder cmm = GGProto.CommonVector.newBuilder();
        cmm.addAString(title);
        cmm.addAString(content);
        cmm.addAString(actionData);
        cmm.addANumber(type);
        cmm.addANumber(actionType);
        return cmm.build();
    }
}
