package com.bem.dao.mapping;

import com.bem.monitor.ChatMonitor;
import com.bem.object.ResIcon;
import com.bem.object.UserInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.k2tek.Constans;
import com.proto.GGProto;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by vieth_000 on 3/28/2017.
 */
@Table(name = "feedback", catalog = "boomss")
@Entity
@Data
public class FeedbackEntity {
    @Id
    @Column(name = "user_id")
    private long userId;
    private String message;
    @Column(name = "has_new")
    private boolean hasNew;

    public FeedbackEntity() {

    }

    public FeedbackEntity(long userId) {
        this.userId = userId;
        this.message = "[]";
        hasNew = false;
    }

    public GGProto.ProtoListChat toProto() {
        GGProto.ProtoListChat.Builder builder = GGProto.ProtoListChat.newBuilder();
        List<ChatEntity> aChat = new Gson().fromJson(message, new TypeToken<ArrayList<ChatEntity>>() {
        }.getType());
        aChat.forEach(chat -> builder.addAChat(chat.toProto()));
        return builder.build();
    }

    public ChatEntity newChat(String msg) {
        return new ChatEntity(msg);
    }

    public class ChatEntity {
        String msg;
        Date time;
        int admin;

        public ChatEntity(String msg) {
            this.msg = msg;
            time = new Date();
            admin = 0;
        }

        public GGProto.ProtoChat.Builder toProto() {
            GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
            if (admin > 0) {
                builder.setAvatar("1");
                builder.setName("Boom Bá Admin");
            }
            builder.setUserId(admin);
            builder.setMessage(msg);
            builder.setTime(time.getTime() / 1000);
            builder.setChannel(Constans.CHANNEL_ADMIN);
            return builder;
        }
    }

}
