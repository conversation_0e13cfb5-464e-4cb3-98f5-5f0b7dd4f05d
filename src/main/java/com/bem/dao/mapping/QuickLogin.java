package com.bem.dao.mapping;

// Generated Dec 12, 2013 5:04:10 PM by Hibernate Tools 3.4.0.CR1

import java.util.Date;

/**
 * QuickLogin generated by hbm2java
 */
public class QuickLogin implements java.io.Serializable {

	private Integer id;
	private String deviceId;
	private String deviceName;
	private String username;
	private Date dateCreated;

	public QuickLogin() {
	}

	public QuickLogin(String deviceId, String deviceName, String username, Date dateCreated) {
		this.deviceId = deviceId;
		this.deviceName = deviceName;
		this.username = username;
		this.dateCreated = dateCreated;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getDeviceId() {
		return this.deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getDeviceName() {
		return this.deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public String getUsername() {
		return this.username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public Date getDateCreated() {
		return this.dateCreated;
	}

	public void setDateCreated(Date dateCreated) {
		this.dateCreated = dateCreated;
	}

}
