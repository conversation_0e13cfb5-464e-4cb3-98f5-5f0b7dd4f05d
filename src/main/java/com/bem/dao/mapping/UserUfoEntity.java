package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_ufo")
@Entity
public class UserUfoEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "ufo_id")
    private int ufoId;
    private int atk;
    private int hp;


    public UserUfoEntity() {
    }

    public UserUfoEntity(long userId, int ufoId) {
        this.userId = userId;
        this.ufoId = ufoId;
    }

    public UserUfoEntity(long userId, int ufoId, int atk, int hp) {
        this.userId = userId;
        this.ufoId = ufoId;
        this.atk = atk;
        this.hp = hp;
    }
}
