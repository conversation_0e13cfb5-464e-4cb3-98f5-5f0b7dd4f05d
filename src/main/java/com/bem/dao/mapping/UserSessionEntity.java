package com.bem.dao.mapping;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by vieth_000 on 6/15/2017.
 */
@Data
@Entity
@Table(name = "user_session", catalog = "boom")
@AllArgsConstructor
@NoArgsConstructor
public class UserSessionEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    @Column(name = "user_id")
    private long userId;
    @Column(name = "date_created")
    private Date dateCreated;
    @Column(name = "session_id")
    private String sessionId;
}
