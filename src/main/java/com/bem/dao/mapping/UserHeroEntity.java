package com.bem.dao.mapping;

import com.bem.boom.Resources;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_hero")
@Entity
public class UserHeroEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "hero_id")
    private int heroId;
    private long exp;
    private int level;

    public UserHeroEntity() {
    }

    public UserHeroEntity(long userId, int heroId) {
        this.userId = userId;
        this.heroId = heroId;
        this.level = 1;
    }

    public void addExp(long addExp, int levelUser) {
        int maxLv = Math.min(Resources.maxHeroExp, levelUser);
        if (level > maxLv) {
            return;
        }
        exp += addExp;
        long nextExp = Resources.heroExp[level];
        while (exp > nextExp) {
            level++;
            exp -= nextExp;
            nextExp = Resources.heroExp[level];
            if (level > maxLv) {
                exp = Resources.heroExp[level - 1];
                level--;
                return;
            }
        }
    }

    public long[] addFakeExp(long addExp, int levelUser) {

        int maxLv = Math.min(Resources.maxHeroExp, levelUser);
        if (level > maxLv) {
            return new long[]{level, 0};
        }
        long curExp = exp + addExp;
        int curLevel = level;
        long nextExp = Resources.heroExp[curLevel];
        while (curExp > nextExp) {
            curLevel++;
            curExp -= nextExp;
            nextExp = Resources.heroExp[curLevel];
            if (curLevel > maxLv) {
                return new long[]{curLevel - 1, Resources.heroExp[curLevel - 1]};
            }
        }

        return new long[]{curLevel, curExp};
    }

}
