package com.bem.dao.mapping;

import com.bem.boom.BoomConfig;
import com.bem.boom.Resources;
import com.bem.object.ResPet;
import com.bem.object.Skill;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_pet")
@Entity
public class UserPetEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "pet_id")
    private int petId;
    private int level;
    private int tienhoalv = 0;
    private int fragment;
    private long exp;
    private int star, energy;

    public UserPetEntity() {
    }

    public UserPetEntity(long userId, int petId, int level) {
        this.userId = userId;
        this.petId = petId;
        this.level = level;
    }

    public UserPetEntity(long userId, int petId, int level, int star, int fragment) {
        this.userId = userId;
        this.petId = petId;
        this.level = level;
        this.star = star;
        this.fragment = fragment;
    }

    public long addExp(int addExp) {
        if (level >= Resources.maxPetExp) {
            return addExp;
        }
        int rank = 1;
        if (BoomConfig.rankpet == 0) {
            rank = Resources.mPet.get(petId).getRank();
        }
        this.exp += addExp;
        long nextExp = Resources.petExp[level] * rank;
        while (this.exp >= nextExp) {
            this.level++;
            this.exp = this.exp - nextExp;
            nextExp = Resources.petExp[level] * rank;
            if (level >= Resources.maxPetExp) {
                long rs = this.exp;
                exp = 0;
                return rs;
            }
        }
        return 0l;
    }

    public void addFragment(int value) {
        fragment += value;
    }

    public void addEnergy(int value) {
        energy += value;
    }

    public List<Skill> getSkill() {
        List<Skill> aSkill = new ArrayList<Skill>();
        ResPet rPet = Resources.getPet(petId);
        for (Skill skill : rPet.getSkills()) {
            if (skill.level <= level) {
                aSkill.add(skill);
            }
        }
        return aSkill;
    }
}
