package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by vieth_000 on 6/19/2017.
 */
@Data
@Entity
@Table(name = "log_error", catalog = "boom")
public class LogErrorEntity {
    @Id
    private int id;
    @Column(name = "server_id")
    private int serverId;
    private String message;
    private boolean notify;
    @Column(name = "date_created")
    private Date dateCreated;

    public LogErrorEntity() {
    }

    public LogErrorEntity(String msg, int serverId) {
        this.message = msg;
        this.dateCreated = new Date();
        this.serverId = serverId;
    }

}
