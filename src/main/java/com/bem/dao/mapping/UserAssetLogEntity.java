package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by vieth_000 on 5/31/2017.
 */
@Data
@Table(name = "user_asset_log")
@Entity
public class UserAssetLogEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    @Column(name = "user_id")
    private long userId;
    private String data;
    private String k;
    @Column(name = "date_created")
    private Date dateCreated;

    public UserAssetLogEntity() {
       dateCreated = new Date();
    }
}
