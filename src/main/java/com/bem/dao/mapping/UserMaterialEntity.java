package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_material")
@Entity
public class UserMaterialEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "material_id")
    private byte materialId;
    private int number;

    public UserMaterialEntity() {
    }

    public UserMaterialEntity(long userId, int materialId, int number) {
        this.userId = userId;
        this.materialId = (byte) materialId;
        this.number = number;
    }

    public void addNumber(int value) {
        number += value;
    }
}
