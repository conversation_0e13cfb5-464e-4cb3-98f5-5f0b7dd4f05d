package com.bem.dao.mapping;

import com.bem.config.CfgClan;
import com.bem.config.lang.Lang;
import com.bem.dao.ClanDAO;
import com.bem.monitor.ClanMonitor;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.UserInfo;
import com.bem.object.UserMoneys;
import com.bem.object.event.TopTrophy;
import com.bem.util.Actions;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.proto.GGProto;
import grep.database.Database2;
import grep.helper.StringHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import net.sf.json.JSONArray;

import javax.persistence.*;
import java.util.*;

/**
 * Created by vieth_000 on 4/11/2017.
 */

@Table(name = "clan")
@Entity
@Data
@AllArgsConstructor
public class ClanEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private String name;
    @Column(name = "server_id")
    private int serverId;
    private Integer avatar, member;
    private String slogan, chat;
    private Long exp;
    private Integer level;
    @Column(name = "max_member")
    private int maxMember;
    @Column(name = "join_rule")
    private int joinRule;
    @Column(name = "join_trophy")
    private int joinTrophy;
    private long trophy;
    @Column(name = "date_created")
    private Date dateCreated;

    public ClanEntity() {
        this.avatar = 1;
        this.level = 1;
        this.dateCreated = new Date();
        this.joinRule = Constans.CLAN_JOIN_OPEN;
        this.joinTrophy = 0;
        this.exp = 0L;
        this.member = 1;
        this.maxMember = CfgClan.maxMember(level);
    }

    //region Edit
    @Transient
    int messageId;
    @Transient
    List<GGProto.ProtoChat> aChat;// = new ArrayList<GGProto.ProtoChat>();
    @Transient
    List<UserEntity> aUser;
    @Transient
    Map<Long, UserEntity> mUser = new HashMap<Long, UserEntity>();
    @Transient
    long lastGetTrophy = 0;

    public long getClanTrophy() {
        if (System.currentTimeMillis() - lastGetTrophy > 300000) {
            long tmp = new ClanDAO().getClanTrophy(TopTrophy.getInstance().getEventIndex()[0], id);
            if (tmp != -1) trophy = tmp;
        }
        return trophy;
    }

    int nextMessageId() {
        return ++messageId;
    }

    public UserEntity getMemberUser(long userId) {
        return mUser.get(userId);
    }

    public synchronized List<UserEntity> getAUser() {
        if (aUser == null) {
            aUser = new ClanDAO().listMember(id);// Database.getList("user", Arrays.asList("clan", String.valueOf(id)), "", UserEntity.class);
            if (aUser != null) {
                aUser.forEach(user -> {
                    mUser.put(user.getId(), user);
                });
                member = aUser.size();
            }
        }
        return aUser;
    }

    public void addUser(UserEntity user) {
        getAUser().add(user);
        mUser.put(user.getId(), user);
        addMember(1);
    }

    List<GGProto.ProtoChat> getAChat() {
        if (aChat == null) {
            if (!StringHelper.isEmpty(chat)) {
                aChat = new Gson().fromJson(chat, new TypeToken<ArrayList<GGProto.ProtoChat>>() {
                }.getType());
            } else {
                aChat = new ArrayList<GGProto.ProtoChat>();
            }
        }
        return aChat;
    }

    public GGProto.ProtoListChat getChatHistory(long reqTime) {
        GGProto.ProtoListChat.Builder builder = GGProto.ProtoListChat.newBuilder();
        List<GGProto.ProtoChat> aChat = getAChat();
        for (int i = aChat.size() - 1; i >= 0; i--) {
            if (aChat.get(i).getTime() > reqTime) {
                builder.addAChat(aChat.get(i));
            }
        }
        return builder.build();
    }

    synchronized void addMember(int value) {
        member += value;
        dbUpdateNumberMember();
    }

    public synchronized String answerReqJoin(Lang lang, long userId, boolean isAccept) {
        List<GGProto.ProtoChat> aChat = getAChat();

        List<Integer> removeIds = new ArrayList<>();
        for (int i = aChat.size() - 1; i >= 0; i--) {
            if (aChat.get(i).getMsgType() == Constans.CLAN_CHAT_REQUEST_JOIN) {
                if (aChat.get(i).getUserId() == userId) {
                    removeIds.add(aChat.get(i).getMsgId());
                    aChat.remove(i);
                }
            }
        }
        if (!removeIds.isEmpty()) {
            addRemoveChat(new Gson().toJson(removeIds));
        }
        if (isAccept) {
            UserEntity uEntity = (UserEntity) Database2.getUnique("user", Arrays.asList("id", String.valueOf(userId)), UserEntity.class);
            if (uEntity != null && uEntity.getClan() == 0) {
                String canJoin = CfgClan.canJoin(uEntity.getId());
                if (canJoin.equals("")) {
                    String result = new ClanDAO().joinClan(lang, this, uEntity);
                    if (result.length() > 0) {
                        return result;
                    } else if (result.length() == 0) {
                        UserInfo user = UserMonitor.getUser(userId);
                        if (user != null) {
                            user.getDbUser().setClan(id);
                            user.getDbUser().setDiemdanh(uEntity.getDiemdanh());
                            Util.sendProtoData(Online.getChannel(userId), CommonProto.getCommonLongVectorProto(Arrays.asList((long) id), null), IAction.CLAN_JOIN_NOTIFY, System.currentTimeMillis());
                            addUser(user.getDbUser());
                        } else {
                            addUser(uEntity);
                        }
                        addJoinChat(uEntity);
                        Actions.save(user.getDbUser(), Actions.GCLAN, "join", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id), "action", "accept", "member", String.valueOf(member))));
                    }
                } else {
                    return "Người chới mới rời bang hội khác, chưa thể tham gia ngay được";
                }
            }
        }
        return "";
    }

    public synchronized String forceJoinClan(UserInfo user) {
        if (member >= CfgClan.maxMember(level)) {
            return "Bang hội không còn chỗ trống";
        }
        String result = new ClanDAO().joinClan(this, user);
        if (StringHelper.isEmpty(result)) {
            getAUser().add(user.getDbUser());
            mUser.put(user.getId(), user.getDbUser());
            addMember(1);
            addJoinChat(user.getDbUser());
            Actions.save(user.getDbUser(), Actions.GCLAN, "join", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id), "action", "join", "member", String.valueOf(member))));
            return "";
        }
        return result;
    }

    public synchronized String joinClan(UserInfo user) {
        if (member >= CfgClan.maxMember(level)) {
            return "Bang hội không còn chỗ trống";
        }
        if (joinRule == Constans.CLAN_JOIN_OPEN) {
            if (user.getDbUser().getTrophy() < joinTrophy) {
                return "Đạt " + joinTrophy + " cúp để tham gia bang hội";
            }
            String result = new ClanDAO().joinClan(this, user);
            if (StringHelper.isEmpty(result)) {
                getAUser().add(user.getDbUser());
                mUser.put(user.getId(), user.getDbUser());
                addMember(1);
                addJoinChat(user.getDbUser());
                Actions.save(user.getDbUser(), Actions.GCLAN, "join", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id), "action", "join", "member", String.valueOf(member))));
                return "";
            }
            return result;
        } else if (joinRule == Constans.CLAN_JOIN_CLOSED) {
            if (user.getDbUser().getTrophy() < joinTrophy) {
                return "Đạt " + joinTrophy + " cúp để được gửi đơn tham gia bang hội";
            }
            if (CfgClan.canReqJoin(user, id)) {
                addRequestJoinChat(user.getDbUser());
                CfgClan.cacheReq(user, id);
                Actions.save(user.getDbUser(), Actions.GCLAN, "join", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id), "action", "req", "member", String.valueOf(member))));
            }
            return "Gửi đơn tham gia hội thành công";
        }
        return "Không tham gia được bang hội này";
    }

    public synchronized boolean leaveClan(UserInfo user) {
        long nextLeader = -1;
        if (user.getDbUser().getClanPosition() == Constans.CLAN_LEADER) {
            for (int i = 0; i < aUser.size(); i++) {
                if (aUser.get(i).getClanPosition() == Constans.CLAN_CO_LEADER) {
                    nextLeader = aUser.get(i).getId();
                    break;
                }
            }
            if (nextLeader == -1) {
                for (int i = 0; i < aUser.size(); i++) {
                    if (aUser.get(i).getClanPosition() == Constans.CLAN_ELDER) {
                        nextLeader = aUser.get(i).getId();
                        break;
                    }
                }
            }
            if (nextLeader == -1) {
                for (int i = 0; i < aUser.size(); i++) {
                    if (aUser.get(i).getId() != user.getId()) {
                        nextLeader = aUser.get(i).getId();
                        break;
                    }
                }
            }
        }
        if (new ClanDAO().leaveClan(id, user.getId(), nextLeader, member)) {
            int oldPos = user.getDbUser().getClanPosition();
            user.getDbUser().setClan(0);
            user.getDbUser().setClanPosition(0);
            aUser.remove(mUser.get(user.getId()));
            mUser.remove(user.getId());
            addMember(-1);
            addLeaveChat(user.getDbUser());
            if (nextLeader > 0) {
                mUser.get(nextLeader).setClanPosition(Constans.CLAN_LEADER);
                addPositionChat(user.getDbUser().getName(), mUser.get(nextLeader), true);
            }
            CfgClan.cacheLeave(user.getId());
            Actions.save(user.getDbUser(), Actions.GCLAN, "leave", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id),
                    "pos", String.valueOf(oldPos), "nextLeader", String.valueOf(nextLeader), "member", String.valueOf(member))));
            if (member <= 0) {
                new ClanDAO().destroyClan(id);
                ClanMonitor.removeClan(id);
            }
            return true;
        }
        return false;
    }

    public synchronized String kick(UserInfo user, long kickId) {
        UserEntity kickUser = mUser.get(kickId);
        if (kickUser == null) {
            return "Thành viên không tồn tại";
        }
        if (user.getDbUser().getClanPosition() == Constans.CLAN_ELDER || user.getDbUser().getClanPosition() == Constans.CLAN_MEMBER) {
            return "Bạn không có quyền kick người khác";
        }
        if (user.getDbUser().getClanPosition() == Constans.CLAN_CO_LEADER && (kickUser.getClanPosition() == Constans.CLAN_LEADER || kickUser.getClanPosition() == Constans.CLAN_CO_LEADER)) {
            return "Bạn không đủ quyền để kick người chơi này";
        }
        if (new ClanDAO().leaveClan(id, kickId, -1, member)) {
            aUser.remove(mUser.get(kickId));
            mUser.remove(kickId);
            addMember(-1);
            addKickChat(user.getDbUser().getName(), kickUser);
            CfgClan.cacheLeave(kickUser.getId());
            Actions.save(user.getDbUser(), Actions.GCLAN, "kick", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id),
                    "kickId", String.valueOf(kickId), "userKick", String.valueOf(user.getId()), "member", String.valueOf(member))));
            return "";
        }
        return user.getLang().get(Lang.err_system_down);
    }

    public synchronized String setPosition(UserInfo user, long promoteId, int newPosition) {
        UserEntity promoteUser = mUser.get(promoteId);
        if (promoteUser == null) {
            return "Thành viên không tồn tại";
        }
        if (user.getDbUser().getClanPosition() <= promoteUser.getClanPosition()) {
            return "Không đủ quyền để đặt chức vụ cho thành viên này";
        }
        if (user.getDbUser().getClanPosition() < newPosition) {
            return "Không đặt được chức vụ lớn hơn bản thân";
        }
        int myPosition = newPosition == Constans.CLAN_LEADER ? Constans.CLAN_CO_LEADER : -1;
        if (new ClanDAO().promote(user.getId(), myPosition, promoteId, newPosition)) {
            int oldPosition = promoteUser.getClanPosition();
            promoteUser.setClanPosition(newPosition);
            addPositionChat(user.getDbUser().getName(), promoteUser, newPosition > oldPosition);
            if (myPosition > -1) {
                user.getDbUser().setClanPosition(myPosition);
                addPositionChat(user.getDbUser().getName(), promoteUser, false);
            }
            Actions.save(promoteUser, Actions.GCLAN, "position", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id),
                    "oldPos", String.valueOf(oldPosition), "newPos", String.valueOf(newPosition), "userId", String.valueOf(user.getId()))));
            return "";
        }
        return user.getLang().get(Lang.err_system_down);
    }

    public synchronized String promote(UserInfo user, long promoteId) {
        UserEntity promoteUser = mUser.get(promoteId);
        if (promoteUser == null) {
            return "Thành viên không tồn tại";
        }
        if (user.getDbUser().getClanPosition() == promoteUser.getClanPosition()) {
            return "Không đủ quyền để thăng cấp thành viên này";
        }
        int newPosition = Constans.CLAN_PROMOTE[promoteUser.getClanPosition()];
        int myPosition = newPosition == Constans.CLAN_LEADER ? Constans.CLAN_CO_LEADER : -1;
        if (new ClanDAO().promote(user.getId(), myPosition, promoteId, newPosition)) {
            promoteUser.setClanPosition(newPosition);
            if (myPosition > -1) {
                user.getDbUser().setClanPosition(myPosition);
            }
            return "";
        }
        return user.getLang().get(Lang.err_system_down);
    }

    public synchronized String demote(UserInfo user, long demoteId) {
        UserEntity demoteUser = mUser.get(demoteId);
        if (demoteUser == null) {
            return "Thành viên không tồn tại";
        }
        if (user.getDbUser().getClanPosition() == demoteUser.getClanPosition()) {
            return "Không đủ quyền để hạ cấp thành viên này";
        }
        if (demoteUser.getClanPosition() == Constans.CLAN_MEMBER) {
            return "Thành viên đã ở cấp thấp nhất";
        }
        int newPosition = Constans.CLAN_DEMOTE[demoteUser.getClanPosition()];
        if (new ClanDAO().demote(demoteId, newPosition)) {
            demoteUser.setClanPosition(newPosition);
            return "";
        }
        return user.getLang().get(Lang.err_system_down);
    }

    public synchronized boolean diemdanh(UserInfo user) {
        JSONArray arr = JSONArray.fromObject(user.getDbUser().getDiemdanh());
        arr.set(0, arr.getInt(0) + 1);
        arr.set(1, System.currentTimeMillis() / 1000);

        long[] newLevel = CfgClan.calculateLevel(this, CfgClan.config.diemdanh[1]);
        UserMoneys moneys = user.getUData().getUserMoneys();
        moneys.addValue(UserMoneys.CLAN, CfgClan.config.diemdanh[0]);
        if (new ClanDAO().diemdanh(user.getId(), arr.toString(), moneys.toString(), id, newLevel)) {
            user.getDbUser().setDiemdanh(arr.toString());
            level = (int) newLevel[0];
            exp = newLevel[1];
            Actions.save(user.getDbUser(), Actions.GCLAN, "diemdanh", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id),
                    "type", "clan", "value", String.valueOf(moneys.getValue(UserMoneys.CLAN)), "addValue", "1")));
            Actions.save(user.getDbUser(), Actions.GCLAN, "exp", Actions.convertToLogString(Arrays.asList("clanId", String.valueOf(id),
                    "value", String.valueOf(exp), "addValue", String.valueOf(CfgClan.config.diemdanh[1]), "level", String.valueOf(level))));
            return true;
        }
        moneys.addValue(UserMoneys.CLAN, -CfgClan.config.diemdanh[0]);
        return false;
    }

    public synchronized void updateUserEntity(UserEntity user) {
        List<UserEntity> aUser = getAUser();
        for (int i = 0; i < aUser.size(); i++) {
            if (aUser.get(i).getId() == user.getId()) {
                aUser.set(i, user);
                mUser.put(user.getId(), user);
                break;
            }
        }
    }

    public synchronized void addChat(UserInfo user, String msg) {
        GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
        builder.setUserId(user.getId());
        builder.setMessage(msg);
        builder.setAvatar(user.getDbUser().getMAvatar().getUserAvatar() + "");
        builder.setTime(System.currentTimeMillis() / 1000);
        builder.setName(user.getDbUser().getName());
        builder.setVip(user.getDbUser().getVip());
        builder.setChannel(Constans.CHANNEL_CLAN);
        builder.setMsgId(nextMessageId());

        getAChat().add(builder.build());
    }

    void addJoinChat(UserEntity user) {
        GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
        builder.setUserId(user.getId());
        builder.setMessage(String.format("%s vừa tham gia vào hội", user.getName()));
        builder.setTime(System.currentTimeMillis() / 1000);
//        builder.setAvatar(user.getDbUser().getMAvatar().getUserAvatar() + "");
//        builder.setName(user.getDbUser().getName());
//        builder.setVip(user.getDbUser().getVip());
        builder.setMsgType(Constans.CLAN_CHAT_JOIN);
        builder.setChannel(Constans.CHANNEL_CLAN);
        builder.setMsgId(nextMessageId());

        getAChat().add(builder.build());
    }

    void addKickChat(String name, UserEntity user) {
        GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
        builder.setUserId(user.getId());
        builder.setMessage(String.format("%s vừa bị kick bởi %s", user.getName(), name));
        builder.setTime(System.currentTimeMillis() / 1000);
//        builder.setAvatar(user.getMAvatar().getUserAvatar() + "");
//        builder.setName(user.getName());
//        builder.setVip(user.getVip());
        builder.setMsgType(Constans.CLAN_CHAT_LEAVE);
        builder.setChannel(Constans.CHANNEL_CLAN);
        builder.setMsgId(nextMessageId());

        getAChat().add(builder.build());
    }

    void addLeaveChat(UserEntity user) {
        GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
        builder.setUserId(user.getId());
        builder.setMessage(String.format("%s vừa rời hội", user.getName()));
        builder.setTime(System.currentTimeMillis() / 1000);
//        builder.setAvatar(user.getMAvatar().getUserAvatar() + "");
//        builder.setName(user.getName());
//        builder.setVip(user.getVip());
        builder.setMsgType(Constans.CLAN_CHAT_LEAVE);
        builder.setChannel(Constans.CHANNEL_CLAN);
        builder.setMsgId(nextMessageId());

        getAChat().add(builder.build());
    }

    void addRequestJoinChat(UserEntity user) {
        GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
        builder.setUserId(user.getId());
        builder.setMessage(String.format("%s xin vào hội", user.getName()));
        builder.setTime(System.currentTimeMillis() / 1000);
//        builder.setAvatar(user.getMAvatar().getUserAvatar() + "");
//        builder.setName(user.getName());
//        builder.setVip(user.getVip());
        builder.setMsgType(Constans.CLAN_CHAT_REQUEST_JOIN);
        builder.setChannel(Constans.CHANNEL_CLAN);
        builder.setMsgId(nextMessageId());

        getAChat().add(builder.build());
    }

    void addPositionChat(String name, UserEntity user, boolean isPromote) {
        GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
        builder.setUserId(user.getId());
        if (isPromote) {
            builder.setMessage(String.format("%s vừa được bổ nhiệm làm %s bởi %s", user.getName(), Constans.CLAN_POSITION_NAME[user.getClanPosition()], name));
        } else {
            builder.setMessage(String.format("%s vừa giáng chức làm %s bởi %s", user.getName(), Constans.CLAN_POSITION_NAME[user.getClanPosition()], name));
        }
        builder.setTime(System.currentTimeMillis() / 1000);
//        builder.setAvatar(user.getMAvatar().getUserAvatar() + "");
//        builder.setName(user.getName());
//        builder.setVip(user.getVip());
        builder.setMsgType(isPromote ? Constans.CLAN_CHAT_PROMOTE : Constans.CLAN_CHAT_DEMOTE);
        builder.setChannel(Constans.CHANNEL_CLAN);
        builder.setMsgId(nextMessageId());

        getAChat().add(builder.build());
    }

    void addRemoveChat(String removeIds) {
        GGProto.ProtoChat.Builder builder = GGProto.ProtoChat.newBuilder();
        builder.setMessage(removeIds);
        builder.setTime(System.currentTimeMillis() / 1000);
        builder.setMsgType(Constans.CLAN_CHAT_DELETE);
        builder.setChannel(Constans.CHANNEL_CLAN);
        builder.setMsgId(nextMessageId());

        getAChat().add(builder.build());
    }

    void dbUpdateNumberMember() {
        Database2.update("clan", Arrays.asList("member", String.valueOf(member)), Arrays.asList("id", String.valueOf(id)));
    }
    //endregion
}
