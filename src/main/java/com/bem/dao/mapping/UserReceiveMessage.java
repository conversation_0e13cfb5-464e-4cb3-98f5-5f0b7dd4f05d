package com.bem.dao.mapping;

// Generated Nov 14, 2013 2:59:31 PM by Hibernate Tools 3.4.0.CR1

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * AuthUserVip generated by hbm2java
 */
@Entity
@Table(name = "user_receive_message")
@NoArgsConstructor
@Data
public class UserReceiveMessage implements java.io.Serializable {

    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "system_message_id")
    private int systemMessageId;

    public UserReceiveMessage(long userId, int systemMessageId) {
        this.userId = userId;
        this.systemMessageId = systemMessageId;
    }

}
