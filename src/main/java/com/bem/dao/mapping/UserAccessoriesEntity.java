package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_accessories")
@Entity
public class UserAccessoriesEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    int level;
    @Column(name = "user_id")
    private long userId;
    @Column(name = "accessories_id")
    private int accessoriesId;

    public UserAccessoriesEntity() {
    }

    public UserAccessoriesEntity(long userId, int accessoriesId, int level) {
        this.userId = userId;
        this.accessoriesId = accessoriesId;
        this.level = level;
    }


}
