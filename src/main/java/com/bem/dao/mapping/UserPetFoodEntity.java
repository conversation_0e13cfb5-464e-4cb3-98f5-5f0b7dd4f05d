package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 2/3/2017.
 */
@Data
@Table(name = "user_pet_food")
@Entity
public class UserPetFoodEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "food_id")
    private int foodId;
    private Integer number;

    public UserPetFoodEntity() {
    }

    public UserPetFoodEntity(long userId, int foodId, int number) {
        this.userId = userId;
        this.foodId = foodId;
        this.number = number;
    }

    public void addNumber(int value) {
        number += value;
    }

}
