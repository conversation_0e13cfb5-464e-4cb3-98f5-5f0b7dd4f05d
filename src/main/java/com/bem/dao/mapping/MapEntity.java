package com.bem.dao.mapping;

import com.bem.boom.map.MapObject;
import com.bem.config.CfgAchievement;
import com.bem.monitor.Bonus;
import com.google.gson.Gson;
import lombok.Data;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import javax.persistence.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Created by vieth_000 on 9/30/2016.
 */
@Data
@Entity
@Table(name = "map")
public class MapEntity {
    @Id
    private int id;
    private int gold, exp, gem;
    private String data, effect;
    @Column(name = "data_monster")
    private String dataMonster;
    @Column(name = "drop_item")
    private String dropItem;
    @Column(name = "unlock_level")
    private int unlockLevel;
    int energy;
    @Column(name = "times_atk")
    private int timesAtk;

    @Transient
    MapObject mapObject;
    @Transient
    Map<Integer, int[]> mPoint = new HashMap<Integer, int[]>();

    public void init() {
        mapObject = new Gson().fromJson(data, MapObject.class);
        mPoint.clear();
        if (dataMonster != null) {
            JSONArray arr = JSONArray.fromObject(dataMonster);
            for (int i = 0; i < arr.size(); i++) {
                JSONObject obj = arr.getJSONObject(i);
                mPoint.put(obj.getInt("id"), new int[]{obj.getInt("hp"), obj.getInt("atk")});
            }
        }
    }

    public static JSONArray getBonus(String dropItem) {
        JSONArray bonus = new JSONArray();
        JSONArray data = JSONArray.fromObject(dropItem);
        Random ran = new Random();
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            if (ran.nextInt(100) < obj.getInt("percent")) {
                bonus.add(obj.getInt("type"));
                switch (obj.getInt("type")) {
                    case Bonus.AVATAR:
                    case Bonus.AVATAR_FRAGMENT:
                    case Bonus.BOMB:
                    case Bonus.MATERIAL:
                    case Bonus.PET_FOOD:
                        bonus.add(obj.getInt("id"));
                        bonus.add(1);
                        break;
                }
            }
        }
        return bonus;
    }
}
