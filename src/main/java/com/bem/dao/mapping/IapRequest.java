package com.bem.dao.mapping;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.time.Instant;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "iap_request", catalog = "boom")
@NoArgsConstructor
public class IapRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "username")
    private String username;

    @Column(name = "req_id")
    private String reqId;
    @Column(name = "user_id")
    private long userId;
    @Column(name = "server_id")
    private int serverId;
    @Column(name = "status")
    private int status;
    @Column(name = "cp")
    private String cp;

    @Column(name = "req_data")
    private String reqData;

    @Column(name = "date_created")
    private Date dateCreated;

    public IapRequest(UserEntity user, String reqId, String reqData) {
        this.userId = user.getId();
        this.username = user.getRealUsername();
        this.reqId = reqId;
        this.reqData = reqData;
        this.serverId = user.getServer();
        this.cp = user.getCp();
        this.dateCreated = new Date();
    }
}