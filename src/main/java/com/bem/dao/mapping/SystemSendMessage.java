package com.bem.dao.mapping;

// Generated Nov 14, 2013 2:59:31 PM by Hibernate Tools 3.4.0.CR1

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * AuthUserVip generated by hbm2java
 */
@Entity
@Table(name = "system_send_message")
@NoArgsConstructor
@Data
public class SystemSendMessage implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    @Column(name = "title")
    private String title;
    @Column(name = "message")
    private String message;
    @Column(name = "bonus")
    private String bonus;
    @Column(name = "time_end")
    private Date timeEnd;

}
