package com.bem.dao.mapping;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "log_nap_koin", catalog = "boom")
@NoArgsConstructor
public class LogNapKoin {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "int UNSIGNED not null")
    private Long id;

    @Column(name = "created_on", nullable = false)
    private Date createdOn;

    @Column(name = "type", nullable = false)
    private Integer type;

    @Column(name = "money", columnDefinition = "int UNSIGNED not null")
    private Long money;

    @Column(name = "username", nullable = false)
    private String username;

    @Column(name = "koin_init", columnDefinition = "int UNSIGNED")
    private Long koinInit;

    @Column(name = "koin", columnDefinition = "int UNSIGNED not null")
    private Long koin;

    @Column(name = "old_koin", nullable = false)
    private Long oldKoin;

    @Column(name = "new_koin", nullable = false)
    private Long newKoin;

    @Column(name = "cp")
    private String cp;

    @Column(name = "server_id", columnDefinition = "int UNSIGNED not null")
    private Long serverId;

    @Column(name = "os")
    private String os;

    @Column(name = "reason")
    private String reason;

    public LogNapKoin(UserEntity user, long addGem, long money) {
        this.username = user.getRealUsername();
        this.cp = user.getCp();
        this.serverId = (long) user.getServer();
        this.os = "ANDROID";
        this.type = 4;
        this.createdOn = new Date();
        this.koin = addGem;
        this.koinInit = addGem;
        this.oldKoin = user.getGem() - addGem;
        this.newKoin = user.getGem();
        this.money = money;
    }

}