package com.bem.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by vieth_000 on 5/15/2017.
 */
@Data
@Entity
@Table(name = "log_event")
@NoArgsConstructor
public class LogEventEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    @Column(name = "event_id")
    private int eventId;
    @Column(name = "server_id")
    private int serverId;
    @Column(name = "event_index")
    private int eventIndex;
    @Column(name = "event_year")
    private int eventYear;
    private String data, info;
    @Column(name = "date_modified")
    private Date dateModified;

    public LogEventEntity(int serverId) {
//        serverId = CfgServer.SERVER_ID;
        this.serverId = serverId;
        eventYear = Calendar.getInstance().get(Calendar.YEAR);
        dateModified = new Date();
    }

    public LogEventEntity(int id, int serverId) {
        this.id = id;
        serverId = serverId;
        eventYear = Calendar.getInstance().get(Calendar.YEAR);
    }

    public void setData(String data) {
        this.data = data;
        dateModified = new Date();
    }
}
