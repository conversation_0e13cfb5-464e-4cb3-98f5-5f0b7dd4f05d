package com.bem.dao.mapping;

// Generated Nov 14, 2013 2:59:31 PM by Hibernate Tools 3.4.0.CR1

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * AuthUserVip generated by hbm2java
 */
@Entity
@Table(name = "auth_user_vip")
public class AuthUserVip implements java.io.Serializable {

    @Id
    @Column(name = "auth_user_id")
    private long authUserId;
    @Column(name = "vip_type")
    private int vipType;
    @Column(name = "sum_money")
    private long sumMoney;
    @Column(name = "sum_exp")
    private long sumExp;

    public AuthUserVip() {
    }

    public AuthUserVip(long authUserId, int vipType, long sumMoney, long sumExp) {
        this.authUserId = authUserId;
        this.vipType = vipType;
        this.sumMoney = sumMoney;
        this.sumExp = sumExp;
    }

    public long getAuthUserId() {
        return this.authUserId;
    }

    public void setAuthUserId(long authUserId) {
        this.authUserId = authUserId;
    }

    public int getVipType() {
        return this.vipType;
    }

    public void setVipType(int vipType) {
        this.vipType = vipType;
    }

    public long getSumMoney() {
        return this.sumMoney;
    }

    public void setSumMoney(long sumMoney) {
        this.sumMoney = sumMoney;
    }

    public long getSumExp() {
        return this.sumExp;
    }

    public void setSumExp(long sumExp) {
        this.sumExp = sumExp;
    }

}
