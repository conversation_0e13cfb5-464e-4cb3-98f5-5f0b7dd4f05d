package com.bem.dao.mapping;

// Generated Jul 29, 2014 3:03:44 PM by Hibernate Tools 3.4.0.CR1

import java.util.Date;

/**
 * Feedback generated by hbm2java
 */
public class Conversation implements java.io.Serializable {

    private int userId;
    private String name;
    private String message;
    private Byte status;
    private Date dateModified;
    private Date dateCreated;

    public Conversation() {
    }

    public Conversation(int userId, String name, String message, Byte status, Date dateModified, Date dateCreated) {
        this.dateCreated = dateCreated;
        this.userId = userId;
        this.name = name;
        this.message = message;
        this.status = status;
        this.dateModified = dateModified;
    }

    public Conversation(int userId) {
        this.userId = userId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getDateModified() {
        return dateModified;
    }

    public void setDateModified(Date dateModified) {
        this.dateModified = dateModified;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }
}
