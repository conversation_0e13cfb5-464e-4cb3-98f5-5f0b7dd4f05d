package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by vieth_000 on 6/15/2017.
 */
@Data
@Entity
@Table(name = "battle_data")
public class BattleDataEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    @Column(name = "date_created")
    private Date dateCreated;
    @Column(name = "game_type")
    private int gameType;
    private String data;

    public BattleDataEntity() {
    }

    public BattleDataEntity(int gameType) {
        this.gameType = gameType;
        dateCreated = new Date();
    }

}
