package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_icon")
@Entity
public class UserIconEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "icon_id")
    private int iconId;

    public UserIconEntity() {
    }

    public UserIconEntity(long userId, int iconId) {
        this.userId = userId;
        this.iconId = iconId;
    }
}
