package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by vieth_000 on 9/29/2016.
 */
@Data
@Entity
@Table(name = "auth_user")
public class AuthUserEntity implements Serializable {
    @Id
    private int id;
    @Column(name = "created_on")
    private Timestamp createdOn;
    private String username, facebook;
    private String hpass;
    private String cp;
    private String subcp, mobile;
    private String os;
    @Column(name = "os_version")
    private String osVersion;
    @Column(name = "last_login")
    private Date lastLogin;
    private String udid;
    private String version;
    private String email;
    @Column(name = "server_ids")
    private String serverIds;

    public AuthUserEntity() {
    }

    public AuthUserEntity(int id) {
        this.id = id;
    }

}
