package com.bem.dao.mapping;

import com.bem.boom.Resources;
import com.bem.boom.object.AchievementInfo;
import com.bem.boom.object.MyAvatar;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgNotify;
import com.bem.config.CfgRankTrophy;
import com.bem.config.CfgVip;
import com.bem.dao.UserDAO;
import com.bem.monitor.Online;
import com.bem.object.UserInfo;
import com.bem.object.event.TopWin;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.cache.MainCache;
import com.k2tek.Constans;
import com.k2tek.IAction;
import grep.database.Database2;
import lombok.Data;
import net.sf.json.JSONArray;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by vieth_000 on 9/29/2016.
 */
@Data
@Entity
@Table(name = "user")
public class UserEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    @Column(name = "main_id")
    private long mainId;
    private String username;
    private String name, facebook;
    private int level = 1, clan, server;
    @Column(name = "clan_position")
    int clanPosition;
    private String avatar;
    @Column(name = "battle_item")
    String battleItem;
    private int star = 0, vip;
    @Column(name = "star_map")
    int starMap = 0;
    @Column(name = "number_boss_top")
    int numberBossTop = 0;
    @Column(name = "get_event_nap")
    int getEventNap;
    private long gold, medal;
    private long gem;
    @Column(name = "gem_nap")
    private long gemNap = 0;
    long win = 0, loose = 0;
    private long exp = 0, logout;
    private int notify, atomic;
    @Column(name = "udid_group")
    int udidGroup;
    @Column(name = "udid_login")
    String udidLogin;
    private String settings, diemdanh, cp;
    @Column(name = "top_boss_max")
    private String topBossMax = "{}";
    @Column(name = "top_boss_cur")
    private String topBossCur = "{}";
    @Column(name = "achievement_receive")
    private String achievementReceive = "[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]";
    @Column(name = "achievement_status")
    private String achievementStatus = "[0,1,0,0,0,0,0,0,0,0,0,0,0,0,0]";
    @Column(name = "last_login")
    private Date lastLogin;
    @Column(name = "date_created")
    Date dateCreated;
    @Column(name = "lock_chat")
    Date lockChat;
    int trophy;

    //region add variable
    @Transient
    private long sumGemIntEvent = -1;
    @Transient
    int eventLienthang;
    @Transient
    private long keyCapdoi = 0l;
    @Transient
    int diemcapdoi = 0;
    @Transient
    private String rankName = "";
    @Transient
    private int rankId = 0;
    @Transient
    int maxwin;
    @Transient
    int tanglv;
    @Transient
    public MyAvatar mAvatar;
    //endregion

    public UserEntity() {
        lastLogin = new Date();
        dateCreated = new Date();
        win = 0;
        loose = 0;
    }

    public String getUdidLogin() {
        return udidLogin == null ? "[]" : udidLogin;
    }

    public int getRank(List<UserEntity> luser) {
        try {
            for (int i = 0; i < luser.size(); i++) {
                if (this.id == luser.get(i).getId()) {
                    this.rankName = luser.get(i).rankName;
                    this.rankId = luser.get(i).rankId;
                    return this.getRankId();
                    //                return luser.get(i).rankId;
                }
            }
            for (int i = CfgRankTrophy.data.size() - 4; i >= 0; i--) {

                if (CfgRankTrophy.data.get(i).trophy <= this.trophy) {
                    this.rankName = CfgRankTrophy.data.get(i).name;
                    this.rankId = CfgRankTrophy.data.get(i).id;
                    //                System.out.println("this.trophy---->"+this.trophy);
                    //                System.out.println("CfgRankTrophy.data.get(i).id_-->"+CfgRankTrophy.data.get(i).id);
                    return this.getRankId();
                    //                return CfgRankTrophy.data.get(i).id;
                }
            }
            //        int number1 = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).number;
            //        int number2 = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).number;
            //        int number3 = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).number;
            //
            //        if (this.trophy >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).trophy && luser.get(0).getId() == this.getId()) {
            //            this.rankName = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).name;
            //            this.rankId = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).id;
            //            return this.getRankId();
            //        } else if (this.trophy >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).trophy && (luser.get(1).getId() == this.getId() || luser.get(2).getId() == this.getId())) {
            //            this.rankName = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).name;
            //            this.rankId = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).id;
            //            return this.getRankId();
            //        } else if (this.trophy >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).trophy) {
            //            List<Long> ids = new ArrayList<>();
            //            for (int i = 3; i < Math.min(luser.size(), CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).number + 3); i++) {
            //                ids.add(luser.get(i).getId());
            //            }
            //            if (ids.contains(this.getId())) {
            //                this.rankName = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).name;
            //                this.rankId = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).id;
            //                return this.getRankId();
            //            }
            //        }
            //
            //        for (int i = this.rankId + 1; i <= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 4).id; i++) {
            //            if (CfgRankTrophy.data.get(i - 1).trophy <= this.trophy) {
            //                this.rankName = CfgRankTrophy.data.get(i - 1).name;
            //                this.rankId = CfgRankTrophy.data.get(i - 1).id;
            //            }
            //        }
            return this.getRankId();
        } catch (Exception ex) {

        }
        return this.getRankId();
    }

    public int getRank() {
        try {
            List<UserEntity> luser = MainCache.getInstance().getAUserTrophy100().get(server);
            for (int i = 0; i < luser.size(); i++) {
                if (this.id == luser.get(i).getId()) {
                    this.rankName = luser.get(i).rankName;
                    this.rankId = luser.get(i).rankId;
                    return this.getRankId();
                    //                return luser.get(i).rankId;
                }
            }
            for (int i = CfgRankTrophy.data.size() - 4; i >= 0; i--) {
                if (CfgRankTrophy.data.get(i).trophy <= this.trophy) {
                    this.rankName = CfgRankTrophy.data.get(i).name;
                    this.rankId = CfgRankTrophy.data.get(i).id;
                    return this.getRankId();
                    //                return CfgRankTrophy.data.get(i).id;
                }
            }
        } catch (Exception e) {

        }
        return this.getRankId();
        //        if (this.trophy >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).trophy && luser.get(0).getId() == this.getId()) {
        //            this.rankName = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).name;
        //            this.rankId = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 1).id;
        //            return this.getRankId();
        //        } else if (this.trophy >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).trophy && (luser.get(1).getId() == this.getId() || luser.get(2).getId() == this.getId())) {
        //            this.rankName = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).name;
        //            this.rankId = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 2).id;
        //            return this.getRankId();
        //        } else if (this.trophy >= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).trophy) {
        //            List<Long> ids = new ArrayList<>();
        //            for (int i = 3; i < Math.min(luser.size(), CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).number + 3); i++) {
        //                ids.add(luser.get(i).getId());
        //            }
        //            if (ids.contains(this.getId())) {
        //                this.rankName = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).name;
        //                this.rankId = CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 3).id;
        //                return this.getRankId();
        //            }
        //        }
        //
        //        for (int i = this.rankId + 1; i <= CfgRankTrophy.data.get(CfgRankTrophy.data.size() - 4).id; i++) {
        //            if (CfgRankTrophy.data.get(i - 1).trophy <= this.trophy) {
        //                this.rankName = CfgRankTrophy.data.get(i - 1).name;
        //                this.rankId = CfgRankTrophy.data.get(i - 1).id;
        //            }
        //        }
        //        return this.getRankId();
    }

    //region Logic
    public MyAvatar getMAvatar() {
        if (mAvatar == null) {
            mAvatar = new MyAvatar(avatar);
        }
        return mAvatar;
    }

    public void addWinLoose(int win, int loose) {
        this.win += win;
        this.loose += loose;

    }

    public List<Integer> getListSetting() {
        List<Integer> aInt = new ArrayList<Integer>();
        JSONArray arr = JSONArray.fromObject(settings);
        for (int i = 0; i < arr.size(); i++) {
            aInt.add(arr.getInt(i));
        }
        return aInt;
    }

    public long getSettingsIndex(int index) {
        JSONArray arr = JSONArray.fromObject(settings);
        if (arr.size() <= index) {
            return 0;
        }
        return arr.getLong(index);
    }

    public void setSettingsIndex(int index, long value) {
        JSONArray arr = JSONArray.fromObject(settings);
        while (arr.size() <= index) {
            arr.add(0);
        }
        arr.set(index, value);
        settings = arr.toString();
    }

    public void setBattleItem(String battleItem) {
        this.battleItem = battleItem;
    }

    public void addMoney(long gold, long gem) {
        this.gold += gold;
        this.gem += gem;
    }

    public void addGold(long gold) {
        this.gold += gold;
    }

    public void addGem(long gem) {
        this.gem += gem;
    }

    public void addStar(int star) {
        this.star += star;
        //        if (star > 0) {
        //            addAchievementStatus(CfgAchievement.COLECT_STAR, 1);
        //        }
    }

    public void addExp(long addExp, UserInfo user) {
        if (level >= Resources.maxUserExp) {
            return;
        }
        exp += addExp;
        long nextExp = Resources.userExp[level];
        while (exp > nextExp) {
            level++;
            exp -= nextExp;
            nextExp = Resources.userExp[level];
            addAchievementStatus(CfgAchievement.ACHIEVEMENT_LV, 1, user);
            if (CfgAchievement.getAchievement(CfgAchievement.CAPDO, this).inTime()) {
                try {
                    user.getUEvent().getEvent(Constans.EVENT_TOP_CAPDO).getEvent().increaseEvent();
                } catch (Exception ex) {
                    user.loadEvent();
                }
            }
            addSymBol(user);
            if (level >= Resources.maxUserExp) {
                exp = 0;
                return;
            }
        }
    }

    public long[] addFakeExp(long addExp) {
        long curExp = exp + addExp;
        int curLevel = level;
        long nextExp = Resources.userExp[curLevel];
        while (curExp > nextExp) {
            curLevel++;
            curExp -= nextExp;
            nextExp = Resources.userExp[curLevel];
        }
        return new long[]{curLevel, curExp};
    }

    public void setTrophy(long value, UserInfo user) {
        trophy += value;
        trophy = trophy < 0 ? 0 : trophy;
        addSymBol(user);
        getRank();
        addAchievementStatus(CfgAchievement.XEP_HANG, 1, user);
    }

    public int checkHasAwardVip() {
        if (JCache.getInstance().getValue(CfgAchievement.KEYVIPDAILY + this.getUsername() + Util.formatDateDAy(new Date())) == null) {
            return -1;
        } else {
            try {
                return Integer.parseInt(JCache.getInstance().getValue(CfgAchievement.KEYVIPDAILY + this.getUsername() + Util.formatDateDAy(new Date())));
            } catch (Exception ex) {
                return -1;
            }
        }
    }

    public int checkHasAwardRotage() {
        if (JCache.getInstance().getValue(CfgAchievement.KEYAWARDROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(new Date())) == null) {
            return 0;
        } else {
            try {
                return Integer.parseInt(JCache.getInstance().getValue(CfgAchievement.KEYAWARDROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(new Date())));
            } catch (Exception ex) {
                return 0;
            }
        }
    }

    public int checkHasNumberRotage() {
        if (JCache.getInstance().getValue(CfgAchievement.KEYNUMBERROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(new Date())) == null) {
            return 0;
        } else {
            try {
                return Integer.parseInt(JCache.getInstance().getValue(CfgAchievement.KEYNUMBERROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(new Date())));
            } catch (Exception ex) {
                return 0;
            }
        }
    }

    public void addNumberRotage(int number) {
        Date date = new Date();
        if (JCache.getInstance().getValue(CfgAchievement.KEYNUMBERROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(date)) == null) {
            JCache.getInstance().setValue(CfgAchievement.KEYNUMBERROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(date), number + "");
        } else {
            try {
                int count = Integer.parseInt(JCache.getInstance().getValue(CfgAchievement.KEYNUMBERROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(new Date())));
                JCache.getInstance().setValue(CfgAchievement.KEYNUMBERROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(date), (count + number) + "");
            } catch (Exception ex) {
            }
        }
    }

    public void addNumberAwardRotage(Date date) {
        if (JCache.getInstance().getValue(CfgAchievement.KEYAWARDROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(date)) == null) {
            JCache.getInstance().setValue(CfgAchievement.KEYAWARDROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(date), 1 + "");
        } else {
            try {
                int count = Integer.parseInt(JCache.getInstance().getValue(CfgAchievement.KEYAWARDROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(new Date())));
                JCache.getInstance().setValue(CfgAchievement.KEYAWARDROTAGEJACKPOTDAILY + this.getUsername() + Util.formatDateDAy(date), (count + 1) + "");
            } catch (Exception ex) {
            }
        }
    }

    public boolean addAchievementReceiveNapLimitUser(int index, int number, UserInfo user) {
        try {
            AchievementInfo achi = CfgAchievement.getAchievement(index, this);
            if (achi == null) {
                return false;
            }
            if (achi.getStatus() <= 0 || (achi.getStatus() == 1 && !achi.inTime())) {
                return false;
            }
            //            List<Integer> special =Arrays.asList(CfgAchievement.VIP,CfgAchievement.NAP,CfgAchievement.LIENTHANG);
            if (index > CfgAchievement.CHARACTER && index < 50) {
                int eventId = Constans.EVENT_CHARACTERPLUS + index;
                user.getUEvent().getEvent(eventId).getEvent().setInfo(user.getUEvent().getEvent(eventId).getEvent().getInfo() + 1);
                int infor = user.getUEvent().getEvent(eventId).getEvent().getInfo();
                Database2.update("user_event", Arrays.asList("info", infor + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", eventId + "", "event_index", user.getUEvent().getEvent(eventId).getEvent().getThisEventIndex() + ""));
            } else if (index > CfgAchievement.EVENT_PET && index < 100) {
                int eventId = Constans.EVENT_PETPLUS + index;
                user.getUEvent().getEvent(eventId).getEvent().setInfo(user.getUEvent().getEvent(eventId).getEvent().getInfo() + 1);
                int infor = user.getUEvent().getEvent(eventId).getEvent().getInfo();
                Database2.update("user_event", Arrays.asList("info", infor + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", eventId + "", "event_index", user.getUEvent().getEvent(eventId).getEvent().getThisEventIndex() + ""));
            } else if (!CfgAchievement.newMission.contains(index)) {
                JSONArray arr = JSONArray.fromObject(achievementReceive);
                int cNumber = arr.getInt(index);
                if (cNumber < 0) {
                    cNumber = 0;
                }
                arr.set(index, cNumber + number);
                achievementReceive = arr.toString();
                Database2.update("user", Arrays.asList("achievement_receive", achievementReceive.toString()), Arrays.asList("id", String.valueOf(this.id)));
            } else {
                if (CfgAchievement.VIP == index) {
                    JCache.getInstance().setValue(CfgAchievement.KEYVIPDAILY + this.getUsername() + Util.formatDateDAy(new Date()), (checkHasAwardVip() + 1) + "", 24 * 60 * 60);
                }
                if (CfgAchievement.ROTAGEJACKPOT == index) {
                    this.addNumberAwardRotage(new Date());
                } else if (CfgAchievement.NAP == index) {
                    this.setGetEventNap(this.getGetEventNap() + 1);
                    //                    Database.update("user", Arrays.asList("get_event_nap", this.getGetEventNap() + ""), Arrays.asList("id", String.valueOf(this.id)));
                    Database2.update("user_event", Arrays.asList("info", this.getGetEventNap() + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", Constans.EVENT_NAP + "", "event_index", user.getUEvent().getEvent(Constans.EVENT_NAP).getEvent().getThisEventIndex() + ""));
                } else if (CfgAchievement.LIENTHANG == index) {
                    this.setEventLienthang(this.getEventLienthang() + 1);
                    Database2.update("user", Arrays.asList("event_lienthang", this.getEventLienthang() + ""), Arrays.asList("id", String.valueOf(this.id)));
                } else if (CfgAchievement.CAPDO == index) {
                    this.setTanglv(this.getTanglv() + 1);
                    Database2.update("user_event", Arrays.asList("info", this.getTanglv() + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", Constans.EVENT_TOP_CAPDO + "", "event_index", user.getUEvent().getEvent(Constans.EVENT_TOP_CAPDO).getEvent().getThisEventIndex() + ""));
                } else if (CfgAchievement.SAOVANG == index) {
                    user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().setInfo(user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getInfo() + 1);
                    int infor = user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getInfo();
                    Database2.update("user_event", Arrays.asList("info", infor + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", Constans.EVENT_SAOVANG + "", "event_index", user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getThisEventIndex() + ""));
                }
            }
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return false;
    }

    public void addAchievementStatusNapLimitUser(int index, int number, UserInfo user) {
        AchievementInfo achi = null;
        for (int i = 0; i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {
            if (CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).getId() == index) {
                achi = CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i);
            }
        }
        if (achi == null) {
            return;
        }
        try {
            JSONArray arr = JSONArray.fromObject(achievementStatus);
            long cNumber = CfgAchievement.getCurentNumberAchievement(user, achi, index);
            if (achi.getStatus() == CfgAchievement.ACHIEVEMENT_STATUS_NOT_HAS_TIME || achi.inTime()) {
                if (index > CfgAchievement.CHARACTER && index < 50) {
                    int eventId = Constans.EVENT_CHARACTERPLUS + index;
                    user.getUEvent().getEvent(eventId).getEvent().setValue(user.getUEvent().getEvent(eventId).getEvent().getValue() + 1);
                    long value = user.getUEvent().getEvent(eventId).getEvent().getValue();
                    Database2.update("user_event", Arrays.asList("value", value + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", eventId + "", "event_index", user.getUEvent().getEvent(eventId).getEvent().getThisEventIndex() + ""));
                } else if (index > CfgAchievement.EVENT_PET && index < 100) {
                    int eventId = Constans.EVENT_PETPLUS + index;
                    //                    System.out.println("eventId---->"+eventId);
                    user.getUEvent().getEvent(eventId).getEvent().setValue(user.getUEvent().getEvent(eventId).getEvent().getValue() + 1);
                    long value = user.getUEvent().getEvent(eventId).getEvent().getValue();
                    Database2.update("user_event", Arrays.asList("value", value + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", eventId + "", "event_index", user.getUEvent().getEvent(eventId).getEvent().getThisEventIndex() + ""));
                } else {
                    if (index >= arr.size()) {
                        return;
                    }
                    if (index != CfgAchievement.WIN_MAP && index != CfgAchievement.COLLECT_STAR && index != CfgAchievement.XEP_HANG) {
                        arr.set(index, cNumber + number);
                    } else {
                        arr.set(index, cNumber);

                    }
                    achievementStatus = arr.toString();
                    Database2.update("user", Arrays.asList("achievement_status", achievementStatus.toString()), Arrays.asList("id", String.valueOf(this.id)));
                }
                List<Long> vLong = CfgNotify.eventNotify(user.getDbUser(), user.getUData());
                Util.sendProtoData(Online.getChannel(id), CommonProto.getCommonLongVectorProto(vLong, null), IAction.LOGIN_NOTIFY, System.currentTimeMillis());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean addAchievementReceive(int index, int number, UserInfo user) {
        try {
            AchievementInfo achi = CfgAchievement.getAchievement(index, user.getDbUser());
            if (achi == null) {
                return false;
            }
            if (achi.getStatus() <= 0 || (achi.getStatus() == 1 && !achi.inTime())) {
                return false;
            }
            //            List<Integer> special =Arrays.asList(CfgAchievement.VIP,CfgAchievement.NAP,CfgAchievement.LIENTHANG);
            if (index > CfgAchievement.CHARACTER && index < 50) {
                int eventId = Constans.EVENT_CHARACTERPLUS + index;
                user.getUEvent().getEvent(eventId).getEvent().setInfo(user.getUEvent().getEvent(eventId).getEvent().getInfo() + 1);
                int infor = user.getUEvent().getEvent(eventId).getEvent().getInfo();
                Database2.update("user_event", Arrays.asList("info", infor + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", eventId + "", "event_index", user.getUEvent().getEvent(eventId).getEvent().getThisEventIndex() + ""));
            } else if (index > CfgAchievement.EVENT_PET && index < 100) {
                int eventId = Constans.EVENT_PETPLUS + index;
                user.getUEvent().getEvent(eventId).getEvent().setInfo(user.getUEvent().getEvent(eventId).getEvent().getInfo() + 1);
                int infor = user.getUEvent().getEvent(eventId).getEvent().getInfo();
                Database2.update("user_event", Arrays.asList("info", infor + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", eventId + "", "event_index", user.getUEvent().getEvent(eventId).getEvent().getThisEventIndex() + ""));
            } else if (!CfgAchievement.newMission.contains(index)) {
                JSONArray arr = JSONArray.fromObject(achievementReceive);
                int cNumber = arr.getInt(index);
                if (cNumber < 0) {
                    cNumber = 0;
                }
                arr.set(index, cNumber + number);
                achievementReceive = arr.toString();
                Database2.update("user", Arrays.asList("achievement_receive", achievementReceive.toString()), Arrays.asList("id", String.valueOf(this.id)));
            } else {
                if (CfgAchievement.VIP == index) {
                    JCache.getInstance().setValue(CfgAchievement.KEYVIPDAILY + this.getUsername() + Util.formatDateDAy(new Date()), (checkHasAwardVip() + 1) + "", 24 * 60 * 60);
                }
                if (CfgAchievement.ROTAGEJACKPOT == index) {
                    this.addNumberAwardRotage(new Date());
                } else if (CfgAchievement.NAP == index) {
                    this.setGetEventNap(this.getGetEventNap() + 1);
                    //                    Database.update("user", Arrays.asList("get_event_nap", this.getGetEventNap() + ""), Arrays.asList("id", String.valueOf(this.id)));
                    Database2.update("user_event", Arrays.asList("info", this.getGetEventNap() + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", Constans.EVENT_NAP + "", "event_index", user.getUEvent().getEvent(Constans.EVENT_NAP).getEvent().getThisEventIndex() + ""));
                } else if (CfgAchievement.LIENTHANG == index) {
                    this.setEventLienthang(this.getEventLienthang() + 1);
                    Database2.update("user", Arrays.asList("event_lienthang", this.getEventLienthang() + ""), Arrays.asList("id", String.valueOf(this.id)));
                } else if (CfgAchievement.CAPDO == index) {
                    this.setTanglv(this.getTanglv() + 1);
                    Database2.update("user_event", Arrays.asList("info", this.getTanglv() + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", Constans.EVENT_TOP_CAPDO + "", "event_index", user.getUEvent().getEvent(Constans.EVENT_TOP_CAPDO).getEvent().getThisEventIndex() + ""));
                } else if (CfgAchievement.SAOVANG == index) {
                    user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().setInfo(user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getInfo() + 1);
                    int infor = user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getInfo();
                    Database2.update("user_event", Arrays.asList("info", infor + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", Constans.EVENT_SAOVANG + "", "event_index", user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getThisEventIndex() + ""));
                }
            }
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return false;
    }

    public void addAchievementStatus(int index, int number, UserInfo user) {
        AchievementInfo achi = null;
        for (int i = 0; i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {
            if (CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).getId() == index) {
                achi = CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i);
            }
        }
        if (achi == null) {
            return;
        }
        try {
            JSONArray arr = JSONArray.fromObject(achievementStatus);
            long cNumber = CfgAchievement.getCurentNumberAchievement(user, achi, index);
            if (achi.getStatus() == CfgAchievement.ACHIEVEMENT_STATUS_NOT_HAS_TIME || achi.inTime()) {
                if (index > CfgAchievement.CHARACTER && index < 50) {
                    int eventId = Constans.EVENT_CHARACTERPLUS + index;
                    user.getUEvent().getEvent(eventId).getEvent().setValue(user.getUEvent().getEvent(eventId).getEvent().getValue() + 1);
                    long value = user.getUEvent().getEvent(eventId).getEvent().getValue();
                    Database2.update("user_event", Arrays.asList("value", value + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", eventId + "", "event_index", user.getUEvent().getEvent(eventId).getEvent().getThisEventIndex() + ""));
                } else if (index > CfgAchievement.EVENT_PET && index < 100) {
                    int eventId = Constans.EVENT_PETPLUS + index;
                    //                    System.out.println("eventId---->"+eventId);
                    user.getUEvent().getEvent(eventId).getEvent().setValue(user.getUEvent().getEvent(eventId).getEvent().getValue() + 1);
                    long value = user.getUEvent().getEvent(eventId).getEvent().getValue();
                    Database2.update("user_event", Arrays.asList("value", value + ""), Arrays.asList("user_id", String.valueOf(this.id), "event_id", eventId + "", "event_index", user.getUEvent().getEvent(eventId).getEvent().getThisEventIndex() + ""));
                } else {
                    if (index >= arr.size()) {
                        return;
                    }
                    if (index != CfgAchievement.WIN_MAP && index != CfgAchievement.COLLECT_STAR && index != CfgAchievement.XEP_HANG) {
                        arr.set(index, cNumber + number);
                    } else {
                        arr.set(index, cNumber);

                    }
                    achievementStatus = arr.toString();
                    Database2.update("user", Arrays.asList("achievement_status", achievementStatus.toString()), Arrays.asList("id", String.valueOf(this.id)));
                }
                List<Long> vLong = CfgNotify.eventNotify(user.getDbUser(), user.getUData());
                Util.sendProtoData(Online.getChannel(id), CommonProto.getCommonLongVectorProto(vLong, null), IAction.LOGIN_NOTIFY, System.currentTimeMillis());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    boolean setSymBol(int index, UserInfo user) {
        UserSymbolEntity uS = new UserSymbolEntity();
        uS.setUserId(user.getId());
        uS.setSymbolId(index);
        if (Database2.insert("user_symbol", Arrays.asList("user_id", "symbol_id"), Arrays.asList(String.valueOf(user.getId()), String.valueOf(index))) != -1) {
            user.getRes().getMSymbol().put(index, uS);
            return true;
        }
        return false;
    }

    public List<Long> process(UserInfo user) {
        List<Long> lstSymbol = new ArrayList<>();


        for (int i = 0; i < Resources.aSymbol.size(); i++) {
            if (!user.getRes().getMSymbol().containsKey(Resources.aSymbol.get(i).getId())) {
                List<Long> lstSubSym = new ArrayList<>();
                int index = Resources.aSymbol.get(i).getId();
                lstSubSym.add((long) index);
                lstSubSym.add((long) 0);
                lstSubSym.add((long) Resources.mSymbol.get(index).getRequire());
                switch (index) {
                    case CfgAchievement.LV5:

                        if (user.getDbUser().getLevel() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) user.getDbUser().getLevel());
                        }
                        break;
                    case CfgAchievement.BATTLE50:
                        if (user.getDbUser().getWin() + user.getDbUser().getLoose() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) (user.getDbUser().getWin() + user.getDbUser().getLoose()));
                        }
                        break;
                    case CfgAchievement.FREINDS30:
                        int numberFriend = new UserDAO().getNumberFriend(id);
                        if (numberFriend < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) numberFriend);

                        }
                        break;
                    case CfgAchievement.BATTLE100:
                        if (user.getDbUser().getWin() + user.getDbUser().getLoose() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) (user.getDbUser().getWin() + user.getDbUser().getLoose()));

                        }
                        break;
                    case CfgAchievement.TOP1CLANBOSSWORLD:
                        //                        lstSubSym.add((long) 0);
                        if (user.getDbUser().getNumberBossTop() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) user.getDbUser().getNumberBossTop());
                        }
                        break;
                    case CfgAchievement.TOP1CLANBOSSWORLD20TIMES:
                        if (user.getDbUser().getNumberBossTop() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) user.getDbUser().getNumberBossTop());
                        }
                        //                        lstSubSym.add((long) 0);
                        break;
                    case CfgAchievement.WINBATTLE50:
                        if (user.getDbUser().getWin() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) (user.getDbUser().getWin()));
                        }
                        break;
                    case CfgAchievement.WINBATTLE100:
                        if (user.getDbUser().getWin() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) (user.getDbUser().getWin()));
                        }
                        break;
                    case CfgAchievement.PETLV10:
                        boolean ck = false;
                        for (int j = 0; j < user.getRes().pets.size(); j++) {
                            if (user.getRes().pets.get(j).getLevel() >= Resources.mSymbol.get(index).getRequire()) {
                                ck = true;
                                break;
                            }
                        }
                        if (!ck) {
                            int max = 0;
                            for (int j = 0; j < user.getRes().pets.size(); j++) {
                                if (user.getRes().pets.get(j).getLevel() > max) {
                                    max = user.getRes().pets.get(j).getLevel();
                                }
                            }
                            lstSubSym.add((long) max);
                        }
                        break;
                    case CfgAchievement.TOP3CUP:
                        try {
                            lstSubSym.add(0l);
                            //                            String top = "";
                            //                            CfgNewMission.entryLstUserTrophy(CfgNewMission.aSet, Database.getList("user", new ArrayList<String>(), "order by trophy desc, username asc limit 100", UserEntity.class));
                            //                            top = new Gson().toJson(CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null));
                            //                            JCache.getInstance().setValue(":TOPTROPHY:", top, 5 * 60);
                            //                            if (user.getDbUser().getTrophy() >= CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null).get(Resources.mSymbol.get(index).getRequire() - 1).getTrophy()) {
                            //                                setSymBol(index, user);
                            //                            }
                        } catch (Exception ex) {

                        }
                        break;
                    case CfgAchievement.TOP1CUP:
                        try {
                            lstSubSym.add(0l);
                            //                            String top = "";
                            //                            CfgNewMission.entryLstUserTrophy(CfgNewMission.aSet, Database.getList("user", new ArrayList<String>(), "order by trophy desc, username asc limit 100", UserEntity.class));
                            //                            top = new Gson().toJson(CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null));
                            //                            JCache.getInstance().setValue(":TOPTROPHY:", top, 5 * 60);
                            //                            if (user.getDbUser().getTrophy() >= CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null).get(Resources.mSymbol.get(index).getRequire() - 1).getTrophy()) {
                            //                                setSymBol(index, user);
                            //                            }
                        } catch (Exception ex) {

                        }
                        break;
                    case CfgAchievement.TOP1LIENTHANG:
                        try {
                            lstSubSym.add(0l);
                            //                            String top = "";
                            //                            CfgNewMission.entryLstUserTrophy(CfgNewMission.aSet, Database.getList("user", new ArrayList<String>(), "order by trophy desc, username asc limit 100", UserEntity.class));
                            //                            top = new Gson().toJson(CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null));
                            //                            JCache.getInstance().setValue(":TOPTROPHY:", top, 5 * 60);
                            //                            if (user.getDbUser().getTrophy() >= CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null).get(Resources.mSymbol.get(index).getRequire() - 1).getTrophy()) {
                            //                                setSymBol(index, user);
                            //                            }
                        } catch (Exception ex) {

                        }
                        break;
                    case CfgAchievement.LIENTHANG100:
                        if (user.getDbUser().getMaxwin() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) user.getDbUser().getMaxwin());
                        }
                        break;
                    case CfgAchievement.VIP5:
                        if (user.getDbUser().getVip() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) user.getDbUser().getVip());
                        }
                        break;
                    case CfgAchievement.VIP10:
                        if (user.getDbUser().getVip() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) user.getDbUser().getVip());
                        }
                        break;
                    case CfgAchievement.VIP20:
                        if (user.getDbUser().getVip() < Resources.mSymbol.get(index).getRequire()) {
                            lstSubSym.add((long) user.getDbUser().getVip());
                        }
                        break;
                    default:
                        lstSubSym.add((long) Resources.mSymbol.get(index).getRequire());
                        break;
                }
                if (lstSubSym.size() != 4) {
                    //                    System.out.println("index--->" + index);
                }
                lstSymbol.addAll(lstSubSym);
            }
        }
        return lstSymbol;
    }


    public void addSymBol(UserInfo user) {
        try {
            //
            for (int index = 1; index <= Resources.aSymbol.size(); index++) {
                if (user.getRes().getMSymbol().containsKey(index)) {
                    continue;
                }
                switch (index) {
                    case CfgAchievement.LV5:
                        if (user.getDbUser().getLevel() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.BATTLE50:
                        if (user.getDbUser().getWin() + user.getDbUser().getLoose() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.FREINDS30:
                        int numberFriend = new UserDAO().getNumberFriend(id);
                        if (numberFriend >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.BATTLE100:
                        if (user.getDbUser().getWin() + user.getDbUser().getLoose() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.TOP1CLANBOSSWORLD:
                        if (user.getDbUser().getNumberBossTop() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.TOP1CLANBOSSWORLD20TIMES:
                        if (user.getDbUser().getNumberBossTop() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.WINBATTLE50:
                        if (user.getDbUser().getWin() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.WINBATTLE100:
                        if (user.getDbUser().getWin() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.PETLV10:
                        boolean ck = false;
                        for (int i = 0; i < user.getRes().pets.size(); i++) {
                            if (user.getRes().pets.get(i).getLevel() >= Resources.mSymbol.get(index).getRequire()) {
                                ck = true;
                                break;
                            }
                        }
                        if (ck) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.TOP3CUP:
                        //                        try {
                        //                            String top = "";
                        //                            CfgNewMission.entryLstUserTrophy(CfgNewMission.aSet, Database.getList("user", new ArrayList<String>(), "order by trophy desc, username asc limit 100", UserEntity.class));
                        //                            top = new Gson().toJson(CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null));
                        //                            JCache.getInstance().setValue(":TOPTROPHY:", top, 5 * 60);
                        //                            if (user.getDbUser().getTrophy() >= CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null).get(Resources.mSymbol.get(index).getRequire() - 1).getTrophy()) {
                        //                                setSymBol(index, user);
                        //                            }
                        //                        } catch (Exception ex) {
                        //
                        //                        }
                        break;
                    case CfgAchievement.TOP1CUP:
                        //                        try {
                        //                            String top = "";
                        //                            CfgNewMission.entryLstUserTrophy(CfgNewMission.aSet, Database.getList("user", new ArrayList<String>(), "order by trophy desc, username asc limit 100", UserEntity.class));
                        //                            top = new Gson().toJson(CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null));
                        //                            JCache.getInstance().setValue(":TOPTROPHY:", top, 5 * 60);
                        //                            if (user.getDbUser().getTrophy() >= CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null).get(Resources.mSymbol.get(index).getRequire() - 1).getTrophy()) {
                        //                                setSymBol(index, user);
                        //                            }
                        //                        } catch (Exception ex) {
                        //
                        //                        }
                        break;
                    case CfgAchievement.VIP5:
                        if (user.getDbUser().getVip() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.VIP10:
                        if (user.getDbUser().getVip() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.VIP20:
                        if (user.getDbUser().getVip() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.LIENTHANG100:
                        if (user.getDbUser().getMaxwin() >= Resources.mSymbol.get(index).getRequire()) {
                            setSymBol(index, user);
                        }
                        break;
                    case CfgAchievement.TOP1LIENTHANG:
                        if (TopWin.getTopUserLastEvent(server) != null && TopWin.getTopUserLastEvent(server).getInfo() == 0 && TopWin.getTopUserLastEvent(server).getUserId() == this.id) {
                            setSymBol(index, user);
                            TopWin.getTopUserLastEvent(server).setInfo(1);
                        }
                        break;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getRealUsername() {
        if (username.contains("_")) return username.substring(username.indexOf("_") + 1);
        return username;
    }
    //endregion
}
