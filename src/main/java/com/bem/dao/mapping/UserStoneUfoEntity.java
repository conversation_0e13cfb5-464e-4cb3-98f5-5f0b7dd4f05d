package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_stone_ufo")
@Entity
public class UserStoneUfoEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "stone_id")
    private int stoneId;
    private int number;

    public UserStoneUfoEntity() {
    }

    public UserStoneUfoEntity(long userId, int stoneId, int number) {
        this.userId = userId;
        this.stoneId = stoneId;
        this.number = number;
    }
    public void addNumber(int number){
        this.number+=number;
    }

}
