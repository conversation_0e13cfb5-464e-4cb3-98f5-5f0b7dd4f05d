package com.bem.dao.mapping;

// Generated Jul 29, 2014 3:03:44 PM by Hibernate Tools 3.4.0.CR1

import lombok.Data;

import java.util.Date;

/**
 * UserHero generated by hbm2java
 */
@Data
public class topTrophyDaily implements java.io.Serializable {
    private int id;
    private int oldId;
    private long trophy;
    private long oldTrophy;
    private String username;
    private String screenName;
    private long userId;
    private int avatar;
    private String clan;
    private Date createdOn;

}
