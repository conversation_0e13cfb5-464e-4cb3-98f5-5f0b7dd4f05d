package com.bem.dao.mapping;

import com.bem.config.*;
import com.bem.object.MapLevel;
import com.bem.object.MissionInviteFb;
import com.bem.object.UserInt;
import com.bem.object.UserMoneys;
import com.google.gson.Gson;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by vieth_000 on 12/16/2016.
 */
@Data
@Table(name = "user_data")
@Entity
public class UserDataEntity implements Serializable {

    public UserDataEntity() {
    }

    public UserDataEntity(long userId) {
        this.userId = userId;
        this.dataInt = "[]";
        this.shopSpecial = "";
    }

    @Id
    @Column(name = "user_id")
    private long userId;
    @Column(name = "data_int")
    private String dataInt;
    @Column(name = "shop_special")
    private String shopSpecial;
    private String energy;
    private String attendance;
    @Column(name = "rank_trophy")
    private String rankTrophy;
    @Column(name = "map_level")
    private String mapLevel;
    @Column(name = "map_award")
    private String mapAward;
    private String moneys;
    @Column(name = "mission_invite_fb")
    private String missionInviteFb;

    @Transient
    public CfgShop.MyShop myShop;
    @Transient
    UserInt uInt;
    @Transient
    public CfgEnergy.MyEnergy myEnergy;
    @Transient
    public CfgDiemdanh.MyAttendance myAttendance;
    //    public CfgRankTrophy.MyRankTrophy myRankTrophy;
    @Transient
    public CfgMapAward.MyAward myAward;
    @Transient
    MissionInviteFb inviteFb;

    public MissionInviteFb getInviteFb() {
        if (inviteFb == null) {
            try {
                inviteFb = new Gson().fromJson(missionInviteFb, MissionInviteFb.class);
            } catch (Exception ex) {
            }
        }
        if (inviteFb == null) inviteFb = new MissionInviteFb();
        return inviteFb;
    }

    @Transient
    MapLevel map;

    public MapLevel getMap() {
        if (map == null) map = new MapLevel(mapLevel);
        return map;
    }

    @Transient
    UserMoneys userMoneys;

    public UserMoneys getUserMoneys() {
        if (userMoneys == null) userMoneys = new UserMoneys(moneys);
        return userMoneys;
    }

    public UserInt getInitUInt(UserEntity user) {
        if (uInt == null) {
            uInt = new UserInt(dataInt);
        }
        while (uInt.aInt.size() < uInt.NUMBER_VALUE) {
            uInt.aInt.add(0);
        }
        user.setVip(CfgVip.getVip(user.getGemNap()));

        //        boolean hasUpdate = false;
        uInt.setValue(UserInt.NUMBER_EXCHANGE_ATOMIC, CfgVip.config.get(user.getVip()).numberBuyAtomic);
        uInt.setValue(UserInt.NUMBER_EXCHANGE_ENERGY, CfgVip.config.get(user.getVip()).numBuyEnergy);
        uInt.setValue(UserInt.NUMBER_EXCHANGE_GOLD, CfgVip.config.get(user.getVip()).numBuyGold);
        uInt.setValue(UserInt.NUMBER_FRIENDS, CfgVip.config.get(user.getVip()).numMaxFriends);
        //
        uInt.setValue(UserInt.NUMBER_EXCHANGE_GAME_GOLD, CfgVip.config.get(user.getVip()).numJoinMapGold);
        uInt.setValue(UserInt.NUMBER_EXCHANGE_GAME_MATERIAL, CfgVip.config.get(user.getVip()).numJoinMapMaterial);
        uInt.setValue(UserInt.NUMBER_EXCHANGE_GAME_BOSS, CfgVip.config.get(user.getVip()).numRevivalBoss);
        uInt.setValue(UserInt.NUMBER_RESET_MAP_MAO_HIEM, CfgVip.config.get(user.getVip()).resetMapAdventure);

        int bonusEnergy = CfgVip.config.get(user.getVip()).bonusEnergyMax;
        if (bonusEnergy + CfgEnergy.config.maxEnergy != myEnergy.maxEnergy) {
            myEnergy.maxEnergy = bonusEnergy + CfgEnergy.config.maxEnergy;
            myEnergy.update(user.getId());
        }

        //        if (hasUpdate) {
        //            uInt.update(userId);
        //        }
        return uInt;
    }

    public UserInt getUInt() {
        if (uInt == null) {
            uInt = new UserInt(dataInt);
        }
        return uInt;
    }
}
