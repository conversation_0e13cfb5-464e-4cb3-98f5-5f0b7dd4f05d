package com.bem.dao.mapping;

import lombok.Data;
import net.sf.json.JSONArray;

import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 5/8/2017.
 */
@Data
public class LotteryEntity {
    private Date date;
    private String data;

    List<Integer> aInt = new ArrayList<>();
    Map<Integer, Integer> mInt = new HashMap<>();

    List<Integer> nextDayInt = new ArrayList<>();
    Map<Integer, Integer> nextDayMap = new HashMap<>();

    public void init() {
        aInt.clear();
        JSONArray arr = JSONArray.fromObject(data);
        for (int i = 0; i < arr.size(); i++) {
            JSONArray aI = arr.getJSONArray(i);
            for (int j = 0; j < aI.size(); j++) {
                int number = Integer.parseInt(aI.getString(j).substring(aI.getString(j).length() - 2, aI.getString(j).length()));
                if (!aInt.contains(number)) {
                    aInt.add(number);
                }
            }
        }
        mInt.clear();
        for (Integer value : aInt) {
            mInt.put(value, 1);
        }
    }

}
