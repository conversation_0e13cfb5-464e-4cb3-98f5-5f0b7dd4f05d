package com.bem.dao.mapping;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Table(name = "user_item")
@Entity
public class UserItemEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "item_id")
    private byte itemId;
    private int number;

    public UserItemEntity() {
    }

    public UserItemEntity(long userId, byte itemId, int number) {
        this.userId = userId;
        this.itemId = itemId;
        this.number = number;
    }

    public void addNumber(int value) {
        number += value;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public byte getItemId() {
        return itemId;
    }

    public void setItemId(byte itemId) {
        this.itemId = itemId;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UserItemEntity that = (UserItemEntity) o;

        if (userId != that.userId) return false;
        if (itemId != that.itemId) return false;
        if (number != that.number) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (userId ^ (userId >>> 32));
        result = 31 * result + (int) itemId;
        result = 31 * result + number;
        return result;
    }
}
