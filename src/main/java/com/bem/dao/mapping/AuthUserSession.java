package com.bem.dao.mapping;

// Generated Nov 14, 2013 2:59:31 PM by Hibernate Tools 3.4.0.CR1

import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.signature.qual.Identifier;

import javax.persistence.*;
import java.util.Date;

/**
 * AuthUserVip generated by hbm2java
 */
@Entity
@Table(name = "auth_user_session")
@NoArgsConstructor
@Data
public class AuthUserSession implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    @Column(name = "username")
    private String username;
    @Column(name = "session_id")
    private String sessionId;
    @Column(name = "date_created")
    private Date dateCreated;

}
