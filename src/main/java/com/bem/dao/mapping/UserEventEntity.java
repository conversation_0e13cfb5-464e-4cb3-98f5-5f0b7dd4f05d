package com.bem.dao.mapping;

import grep.database.Database2;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;

/**
 * Created by vieth_000 on 5/18/2017.
 */
@Data
@Entity
@Table(name = "user_event")
public class UserEventEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "event_id")
    private int eventId;
    @Id
    @Column(name = "event_index")
    int eventIndex;
    @Column(name = "server_id")
    private int serverId;
    private int info;
    private long value;
    @Column(name = "send_bonus")
    private boolean sendBonus;
    @Column(name = "date_created")
    private Date dateCreated;

    public UserEventEntity() {

    }

    public UserEventEntity(long userId, int eventId, int eventIndex) {
        this.userId = userId;
        this.eventId = eventId;
        this.eventIndex = eventIndex;
        value = 0;
        info = -1;
        sendBonus = false;
        dateCreated = new Date();
    }

    public int getThisEventIndex() {
        return eventIndex;
    }

    public void addValue(long value) {
        this.value += value;
    }

    public void increaseEvent() {
        if (this.getValue() < 0) {
            this.setValue(0);
        }
        this.setValue(this.getValue() + 1);
        Database2.update("user_event", Arrays.asList("value", this.getValue() + ""), Arrays.asList("user_id", String.valueOf(this.userId), "event_id", this.eventId + "", "event_index", this.eventIndex + ""));
    }

    public void increaseRecieveEvent() {
        if (this.getValue() < 0) {
            this.setInfo(0);
        }
        this.setInfo(this.getInfo() + 1);
        Database2.update("user_event", Arrays.asList("info", this.getValue() + ""), Arrays.asList("user_id", String.valueOf(this.userId), "event_id", this.eventId + "", "event_index", this.eventIndex + ""));
    }
}
