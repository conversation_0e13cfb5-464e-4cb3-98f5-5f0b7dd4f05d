package com.bem.dao.mapping;

// Generated Jan 9, 2013 3:15:13 PM by Hibernate Tools 3.4.0.CR1

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * UserBlogWall generated by hbm2java
 */
@Table(name = "user_mission")
@Entity
public class UserMission implements java.io.Serializable {

	@Id
	@Column(name = "user_id")
	private Integer userId;
	private String infor;

	public UserMission() {
	}

	public UserMission(String infor) {
		this.infor = infor;
	}

	public UserMission(Integer userId, String infor) {
		this.userId = userId;
		this.infor = infor;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getInfor() {
		return infor;
	}

	public void setInfor(String infor) {
		this.infor = infor;
	}

}
