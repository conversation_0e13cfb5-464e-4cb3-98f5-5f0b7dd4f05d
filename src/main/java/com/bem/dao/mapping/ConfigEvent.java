package com.bem.dao.mapping;

// Generated Jan 9, 2013 3:15:13 PM by Hibernate Tools 3.4.0.CR1

import com.bem.boom.object.AchievementInfo;
import com.google.gson.JsonArray;
import grep.helper.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * User generated by hbm2java
 */
@Entity
@Table(name = "config_event", catalog = "boom")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigEvent implements java.io.Serializable {

    @Id
    private int id;
    String name, title;
    int status;
    @Column(name="server_ids")
    String serverIds;
    @Column(name="date_begin")
    Date dateBegin;
    @Column(name="date_end")
    Date dateEnd;
    String description;
    String required;
    String award;
    int image, time1,time2,time3;
    @Column(name="is_release")
    int isRelease;

    public AchievementInfo toAchievement() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        AchievementInfo achievementInfo = new AchievementInfo();
        achievementInfo.setId(id);
        achievementInfo.setStatus(status);
        achievementInfo.setName(name);
        achievementInfo.setDesc(description);
        achievementInfo.setImage(image);
        achievementInfo.setDateBegin(sdf.format(dateBegin));
        achievementInfo.setDateEnd(sdf.format(dateEnd));
        achievementInfo.setARequired(GsonUtil.parse(int[].class, required));
        achievementInfo.setABonus(GsonUtil.strTo2ListInt(award));
        achievementInfo.setTime1(time1);
        achievementInfo.setTime2(time2);
        achievementInfo.setTime3(time3);
        return achievementInfo;
    }
}
