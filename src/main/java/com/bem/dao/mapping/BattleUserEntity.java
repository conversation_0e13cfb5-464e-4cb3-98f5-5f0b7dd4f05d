package com.bem.dao.mapping;

import com.bem.boom.Resources;
import com.bem.config.CfgAchievement;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.UserInfo;
import com.bem.object.event.ArenaInfo;
import com.bem.util.Util;
import com.cache.MCache;
import com.cache.MainCache;
import com.google.gson.Gson;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import grep.database.HibernateUtil;
import grep.helper.JsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import net.sf.json.JSONArray;
import org.hibernate.Session;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by vieth_000 on 6/15/2017.
 */
@Data
@Entity
@Table(name = "battle_user")
public class BattleUserEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    @Column(name = "user_id")
    private long userId;
    private byte receive;
    @Column(name = "has_action")
    private byte hasAction;
    @Column(name = "bonus_ex")
    private String bonusEx;
    @Column(name = "bonus_battle_ex")
    String bonusBattleEx;
    String data;
    @Column(name = "date_created")
    private Date dateCreated;
    @Column(name = "battle_id")
    private int battleId;
    @Column(name = "game_type")
    int gameType;

    public BattleUserEntity() {
    }

    public BattleUserEntity(int battleId, long userId, int gameType) {
        this.battleId = battleId;
        this.userId = userId;
        this.bonusEx = "[]";
        this.bonusBattleEx = "[]";
        this.gameType = gameType;
        this.dateCreated = new Date();
    }

    public BattleUserEntity(int battleId, long userId, String bonusEx, int gameType) {
        this.battleId = battleId;
        this.userId = userId;
        this.bonusEx = bonusEx;
        this.bonusBattleEx = "[]";
        this.gameType = gameType;
        this.dateCreated = new Date();
    }

    //
    @Transient
    static BattleUserEntity instance;
    @Transient
    String keyType;
    @Transient
    long totalDamage;

    public static BattleUserEntity getInstance() {
        if (instance == null) instance = new BattleUserEntity();
        return instance;
    }

    public BattleBonusResult getBattleBonus(UserInfo user) {
        //        System.out.println("username------------->"+user.getUsername());
        int rankId = user.getDbUser().getRank(MainCache.getInstance().getAUserTrophy100().get(user.getDbUser().getServer()));
        int lastGameType = -1;
        int oldTrophy = user.getDbUser().getTrophy();
        List<Long> aBonus = new ArrayList<>();
        long totalDamage = 0;

        List<BattleUserEntity> aBattle = dbGetBattleResult(user.getId());
        System.out.println("aBatle size------>" + aBattle.size());
        if (aBattle != null) {
            String debug = user.getId() + "->";
            try {
                for (BattleUserEntity battle : aBattle) {
                    debug += battle.getId() + " ";
                    System.out.println("bbattle.getBattleId()--->" + battle.getBattleId());
                    System.out.println("battle.getId()---->" + battle.getId());
                    System.out.println("receive----->" + receive);
                    System.out.println("has_action----->" + hasAction);

                    if (battle.updateReceive()) {
                        List<Long> bonus = battle.getBonus(rankId);
                        System.out.println("bonus after get----------->" + bonus);
                        //                        System.out.println("username------------->"+user.getUsername());
                        //                        System.out.println("bonus after get-->"+bonus);
                        if (bonus != null && !bonus.isEmpty()) {

                            aBonus.addAll(Bonus.receiveListItem(user, JSONArray.fromObject(bonus), battle.getKeyType()));
                        }
                        if (lastGameType == -1) lastGameType = battle.getGameType();
                        if (battle.getGameType() == TeamObject.GAME_BOSS || battle.getGameType() == TeamObject.ARENA)
                            totalDamage += battle.getTotalDamage();
                        //                        try {
                        //                            if (battle.gameType == TeamObject.ZOMBIE) {
                        //                                JSONObject data = JSONObject.fromObject(battle.getData());
                        //                                String rs = "";
                        //                                if (data.getInt("teamWin_") == 0) {
                        //                                    rs = "{'zombie':1}";
                        //                                } else {
                        //                                    rs = "{'human':1}";
                        //                                }
                        //                                Actions.save(user.getDbUser(), "zombiewin", "zombiewin", rs);
                        //                            }
                        //                        }catch (Exception ex){
                        //                        }
                    }

                }
            } catch (Exception ex) {
                Logs.error(debug + " -> " + Util.exToString(ex));
            }
        }
        if (user.getDbUser().getTrophy() > oldTrophy) {
            //            System.out.println("add achievement------->"+(CfgAchievement.CHARACTER + user.getDbUser().getMAvatar().getHero()));
            // su kien theo thoi gian mo
            user.getDbUser().addAchievementStatus(CfgAchievement.CHARACTER + user.getDbUser().getMAvatar().getHero(), 1, user);
            user.getDbUser().addAchievementStatus(CfgAchievement.EVENT_PET + user.getDbUser().getMAvatar().getPet(), 1, user);


            // bang xep hang
            Bonus.receiveListItem(user, JSONArray.fromObject(Arrays.asList((long) Bonus.USER_EVENT, (long) Constans.EVENT_PET - 1 + user.getMAvatar().getPet(), (long) 1)), "top sieu thu");
            Bonus.receiveListItem(user, JSONArray.fromObject(Arrays.asList((long) Bonus.USER_EVENT, (long) Constans.EVENT_CHARACTER_LOOP - 1 + user.getMAvatar().getHero(), (long) 1)), "top thong thao");

            //            Bonus.receiveListItem(user, JSONArray.fromObject(Arrays.asList((long) Bonus.USER_EVENT, (long) Constans.EVENT_SAOVANG, (long) 1)), "top sieu thu");

            //Arrays.asList((long) Bonus.USER_EVENT, (long) Constans.EVENT_PET -1 + user.getMAvatar().getPet(), (long) 1);
        }

        return new BattleBonusResult(lastGameType, aBonus, totalDamage);
    }

    List<Long> getBonus(int rankId) {
        try {
            if (hasAction == 1) {
                List<Long> exBonus = JsonUtil.convertToListLong(JSONArray.fromObject(bonusEx));
                List<Long> exBattleBonus = JsonUtil.convertToListLong(JSONArray.fromObject(bonusBattleEx));

                if (!StringHelper.isEmpty(data)) {
                    GGProto.ProtoEndgame protoEndgame = new Gson().fromJson(data, GGProto.ProtoEndgame.class);
                    List<GGProto.ProtoPlayerResult> aResult = protoEndgame.getAResultList();
                    for (int i = 0; i < aResult.size(); i++) {
                        if (aResult.get(i).getUser().getId() == userId) {
                            List<Long> results = new ArrayList<>();
                            //                            System.out.println("resul1--->"+results);
                            if (aResult.get(i).getBonusList() != null) results.addAll(aResult.get(i).getBonusList());
                            //                            System.out.println("resul12--->"+results);

                            if (aResult.get(i).getExBonusList() != null) {
                                if (gameType == TeamObject.GAME_BOSS || gameType == TeamObject.ARENA)
                                    totalDamage = aResult.get(i).getExBonus(0);
                                else results.addAll(aResult.get(i).getExBonusList());
                            }
                            if (gameType == TeamObject.ARENA && totalDamage < 0) {
                                totalDamage = 0;
                                MCache.getInstance().set(ArenaInfo.ARENADELAY + userId, (System.currentTimeMillis() / 1000) + "", MCache.EXPIRE_1M);
                            }
                            this.keyType = aResult.get(i).getStatus() == Constans.PLAYER_STATUS_ENDGAME ? Constans.GAME_LOG_ENDGAME[protoEndgame.getType()] : Constans.GAME_LOG_LEAVE[protoEndgame.getType()];
                            if (gameType == TeamObject.BOSS && protoEndgame.getTeamWin() == -1) {
                                if (exBonus.get(1) == -1) results.addAll(exBonus);
                            }

                            //                            else if (gameType == TeamObject.ARENA) {
                            //                                results.addAll(exBonus);
                            //                            }
                            else if (gameType != TeamObject.ARENA) {
                                results.addAll(exBonus);
                            }
                            //                            System.out.println("resul3--->"+results);

                            return results;
                        }
                    }
                } else if (System.currentTimeMillis() - dateCreated.getTime() > 0) { // luôn luôn tính điểm
                    this.keyType = Constans.GAME_LOG_LEAVE[gameType];
                    List<Long> results = new ArrayList<>();
                    //                    results.addAll(exBonus);
                    results.addAll(exBattleBonus);
                    if (gameType == TeamObject.RANK) {
                        results.addAll(Bonus.defaultLongBonusView(Bonus.TROPHY, Resources.getRankTrophy(rankId, false)));
                        results.addAll(Arrays.asList((long) Bonus.WIN_RATE, 0L, 1L));
                    }

                    return results;
                } else if (gameType == TeamObject.RANK) {
                    List<Long> results = new ArrayList<>();
                    results.addAll(Arrays.asList((long) Bonus.WIN_RATE, 0L, 1L));
                    return results;
                }
            } else {
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        }
        return null;
    }

    List<BattleUserEntity> dbGetBattleResult(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            List<BattleUserEntity> aBattle = session.createSQLQuery("select u.id,u.game_type,u.battle_id,u.user_id,u.receive,u.has_action,d.data,u.bonus_ex,u.bonus_battle_ex,u.date_created from " +
                    "battle_user u join " +
                    "battle_data d " +
                    "where u.battle_id=d.id and u.user_id=" + userId + " and u.receive=0 order by d.date_created desc limit 0,2").addEntity(BattleUserEntity.class).list();
            return aBattle;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    boolean updateReceive() {
        return Database2.update("battle_user", Arrays.asList("receive", "1"), Arrays.asList("battle_id", String.valueOf(battleId), "user_id", String.valueOf(userId)));
    }

    void closeSession(Session session) {
        try {
            if (session != null) session.close();
        } catch (Exception ex) {
        }
    }

    public class BattleBonusResult {
        public List<Long> aBonus;
        public long totalDamage;
        public int lastGameType;

        public BattleBonusResult(int lastGameType, List<Long> aBonus, long totalDamage) {
            this.lastGameType = lastGameType;
            this.aBonus = aBonus;
            this.totalDamage = totalDamage;
        }
    }
}
