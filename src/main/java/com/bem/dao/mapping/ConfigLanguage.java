package com.bem.dao.mapping;

// Generated Jan 9, 2013 3:15:13 PM by Hibernate Tools 3.4.0.CR1

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * User generated by hbm2java
 */
@Entity
@Table(name = "config_language", catalog = "boom")
public class ConfigLanguage implements java.io.Serializable {

    @Id
    private String k;
    private String vi;
    private String en;

    public ConfigLanguage() {
    }

    public ConfigLanguage(String k, String vi, String en) {
        this.k = k;
        this.vi = vi;
        this.en = en;
    }

    public String getK() {
        return k;
    }

    public void setK(String k) {
        this.k = k;
    }

    public String getVi() {
        return vi;
    }

    public void setVi(String vi) {
        this.vi = vi;
    }

    public String getEn() {
        return en;
    }

    public void setEn(String en) {
        this.en = en;
    }
}
