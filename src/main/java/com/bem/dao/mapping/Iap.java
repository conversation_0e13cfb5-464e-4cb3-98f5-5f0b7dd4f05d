package com.bem.dao.mapping;

import com.bem.config.CfgPayment;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "iap", catalog = "boom")
@NoArgsConstructor
public class Iap {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "int UNSIGNED not null")
    private Long id;

    @Column(name = "created_on", nullable = false)
    private Date createdOn;

    @Column(name = "trans_id", nullable = false)
    private String transId;

    @Column(name = "money", columnDefinition = "int UNSIGNED not null")
    private Long money;

    @Column(name = "username", nullable = false)
    private String username;

    @Column(name = "cp")
    private String cp;

    @Column(name = "server_id", columnDefinition = "int UNSIGNED not null")
    private Long serverId;

    @Lob
    @Column(name = "response", nullable = false)
    private String response;

    @Column(name = "os", columnDefinition = "int UNSIGNED not null")
    private Long os;

    @Column(name = "sandbox", nullable = false)
    private Boolean sandbox = false;

    public Iap(UserEntity user, String transId, String productId, String response, boolean sandbox) {
        this.createdOn = new Date();
        this.transId = transId;
        this.username = user.getRealUsername();
        this.cp = user.getCp();
        this.serverId = (long) user.getServer();
        this.response = response;
        this.os = 1L;

        String[] tmp = productId.split("\\.");
        int id = Integer.parseInt(tmp[tmp.length - 1]);
        this.money = CfgPayment.getGemNapMoney(id - 1);
        this.sandbox = sandbox;
    }

}