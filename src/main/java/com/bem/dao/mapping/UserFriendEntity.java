package com.bem.dao.mapping;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 10/6/2016.
 */
@Table(name = "user_friend")
@Entity
public class UserFriendEntity implements Serializable {
    @Id
    private long user1;
    @Id
    private long user2;
    private byte fstatus;
    @Column(name = "wife_status")
    private byte wifeStatus;
    @Column(name = "point_friend")
    private long point;

    public byte getWifeStatus() {
        return wifeStatus;
    }

    public void setWifeStatus(byte wifeStatus) {
        this.wifeStatus = wifeStatus;
    }

    public long getPoint() {
        return point;
    }

    public void setPoint(long point) {
        this.point = point;
    }

    public UserFriendEntity() {
        fstatus = -1;
    }

    public long getUser1() {
        return user1;
    }

    public void setUser1(long user1) {
        this.user1 = user1;
    }

    public long getUser2() {
        return user2;
    }

    public void setUser2(long user2) {
        this.user2 = user2;
    }

    public byte getFstatus() {
        return fstatus;
    }

    public void setFstatus(byte fstatus) {
        this.fstatus = fstatus;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UserFriendEntity that = (UserFriendEntity) o;

        if (user1 != that.user1) return false;
        if (user2 != that.user2) return false;
        if (fstatus != that.fstatus) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (user1 ^ (user1 >>> 32));
        result = 31 * result + (int) (user2 ^ (user2 >>> 32));
        result = 31 * result + (int) fstatus;
        return result;
    }
}
