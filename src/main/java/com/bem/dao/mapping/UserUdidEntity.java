package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Created by vieth_000 on 9/29/2016.
 */
@Data
@Table(name = "user_udid")
@Entity
public class UserUdidEntity {
    @Id
    private long id;
    private String udid;
    private String username;

    public UserUdidEntity() {
    }

    public UserUdidEntity(String udid) {
        this.udid = udid;
        this.username = "";
    }

}
