package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_bomb")
@Entity
public class UserBombEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    @Column(name = "user_id")
    private long userId;
    @Column(name = "bomb_id")
    private int bombId;
    private int level;

    public UserBombEntity() {

    }

    public UserBombEntity(long userId, int bombId) {
        this.userId = userId;
        this.bombId = bombId;
        this.level = 1;
    }

    public UserBombEntity(long userId, int bombId, int level) {
        this.userId = userId;
        this.bombId = bombId;
        this.level = level;
    }
}
