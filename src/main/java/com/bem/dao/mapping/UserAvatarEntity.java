package com.bem.dao.mapping;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
@Table(name = "user_avatar")
@Entity
public class UserAvatarEntity implements Serializable {
    @Id
    @Column(name = "user_id")
    private long userId;
    @Id
    @Column(name = "avatar_id")
    private int avatarId;
    private int level;
    private int fragment;

    public UserAvatarEntity() {
    }

    public UserAvatarEntity(long userId, int avatarId) {
        this.userId = userId;
        this.avatarId = avatarId;
        this.level = 1;
    }

    public UserAvatarEntity(long userId, int avatarId, int level) {
        this.userId = userId;
        this.avatarId = avatarId;
        this.level = level;
    }

    public UserAvatarEntity(long userId, int avatarId, int level, int fragment) {
        this.userId = userId;
        this.avatarId = avatarId;
        this.level = level;
        this.fragment = fragment;
    }

    public void addFragment(int value) {
        fragment += value;
    }

}
