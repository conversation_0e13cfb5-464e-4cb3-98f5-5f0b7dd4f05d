package com.bem.dao.mapping;

import com.bem.object.UserInfo;
import lombok.Data;

import java.util.Date;

@Data
public class MonthlyCardReceivedEntity {
    private int id;
    private int serverId;
    private int userId;
    private String username;
    private int cardType;
    private Date dateCreated;

    public MonthlyCardReceivedEntity() {

    }

    public MonthlyCardReceivedEntity(UserInfo user) {
//        serverId = CfgServer.SERVER_ID;
        serverId = user.getDbUser().getServer();
        dateCreated = new Date();
        userId = (int) user.getId();
        username = user.getUsername();
    }
}
