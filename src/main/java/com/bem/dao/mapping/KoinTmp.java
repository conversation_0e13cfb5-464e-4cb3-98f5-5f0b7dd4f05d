package com.bem.dao.mapping;

// Generated Jan 9, 2013 3:15:13 PM by Hibernate Tools 3.4.0.CR1

import java.util.Date;

/**
 * KoinTmp generated by hbm2java
 */
public class KoinTmp implements java.io.Serializable {

	private int id;
	private int userId;
	private String username;
	private Date dateCreated;
	private Long koin;
	private Long balance;
	private String type;

	public KoinTmp() {
	}

	public KoinTmp(int id, int userId, String username, Date dateCreated) {
		this.id = id;
		this.userId = userId;
		this.username = username;
		this.dateCreated = dateCreated;
	}

	public KoinTmp(int id, int userId, String username, Date dateCreated, Long koin, Long balance, String type) {
		this.id = id;
		this.userId = userId;
		this.username = username;
		this.dateCreated = dateCreated;
		this.koin = koin;
		this.balance = balance;
		this.type = type;
	}

	public int getId() {
		return this.id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getUserId() {
		return this.userId;
	}

	public void setUserId(int userId) {
		this.userId = userId;
	}

	public String getUsername() {
		return this.username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public Date getDateCreated() {
		return this.dateCreated;
	}

	public void setDateCreated(Date dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Long getKoin() {
		return this.koin;
	}

	public void setKoin(Long koin) {
		this.koin = koin;
	}

	public Long getBalance() {
		return this.balance;
	}

	public void setBalance(Long balance) {
		this.balance = balance;
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
