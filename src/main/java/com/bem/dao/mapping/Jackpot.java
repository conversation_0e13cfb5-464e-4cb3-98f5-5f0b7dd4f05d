package com.bem.dao.mapping;

import lombok.Data;
import lombok.NonNull;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 12/19/2014.
 */
@Data
@Entity
@Table(name = "jackpot", catalog = "boom")
public class Jackpot {
    @Id
    @NonNull
    @Column(name = "server_id")
    int serverId;
    @NonNull
    long pot;
    @NonNull
    long payout;
    @NonNull
    String history;
    @Column(name = "max_day")
    int maxDay;
    @Column(name = "min_receive")
    int minReceive;
    @Column(name = "last_jackpot")
    Date lastJackpot;
    @Column(name = "easy_date")
    Date easyDate;

    public Jackpot() {
    }

    public Jackpot(int serverId, long pot, long payout, String history) {
        this.serverId = serverId;
        this.pot = pot;
        this.payout = payout;
        this.history = history;
    }

    public void addPot(long value) {
        pot += value;
    }

    public void addPayout(long value) {
        payout += value;
    }
}
