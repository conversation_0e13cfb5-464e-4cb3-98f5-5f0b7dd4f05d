/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.dao;

import com.bem.dao.mapping.UserAssetLogEntity;
import com.bem.util.Util;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class CacheDAO {

    public boolean logAsset(UserAssetLogEntity assEntity) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.save(assEntity);
            session.getTransaction().commit();
            return true;
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean updateFirstLogin(int userId, String screenname, String avatar, int gender) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user set gender=:gender, screen_name=:name, avatar=:avatar where id=" + userId);
            query.setString("name", screenname);
            query.setString("avatar", avatar);
            query.setInteger("gender", gender);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public int checkScreenname(String screenname) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select count(*) from user where screen_name=:screenname");
            query.setString("screenname", screenname);
            return Integer.parseInt(query.uniqueResult().toString());
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    private void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
}
