/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.dao;

import com.bem.config.GameCfgSlotMachine;
import com.bem.dao.mapping.*;
import com.bem.util.Util;
import com.k2tek.common.Logs;
import grep.database.Database2;
import grep.database.HibernateUtil;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class EventDAO extends AbstractDAO {

    public List<ConfigEvent> getListConfigEvent() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from boom.config_event").addEntity(ConfigEvent.class);
            return query.getResultList();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return new ArrayList<>();
    }

}
