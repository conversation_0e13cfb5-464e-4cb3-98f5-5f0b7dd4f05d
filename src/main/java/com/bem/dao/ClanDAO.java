/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.dao;

import com.bem.config.CfgClan;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.ClanEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.object.UserInfo;
import com.bem.object.event.TopTrophy;
import com.bem.util.Util;
import com.cache.MCache;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import net.sf.json.JSONArray;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.List;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class ClanDAO {

    public List<UserEntity> listMember(int clanId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            int eventIndex = TopTrophy.getInstance().getEventIndex()[0];
            SQLQuery query = session.createSQLQuery("select ifnull((select value from user_event where user_id=u.id and event_index=" + eventIndex + " and event_id=" + Constans.EVENT_TOP_TROPHY +
                    "), 0) as trophy, u.* from user u where u.clan=" + clanId).addEntity(UserEntity.class);
            return query.list();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public boolean changeName(int clanId, String name) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update clan set name=:name where id=" + clanId);
            query.setString("name", name);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public void destroyClan(int clanId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("delete from clan where id=" + clanId).executeUpdate();
            session.createSQLQuery("update user set clan=0, clan_position=0 where clan=" + clanId).executeUpdate();
            session.getTransaction().commit();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
    }

    public Long getClanTrophy(int eventIndex, int clanId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select sum(user_event.value) AS trophy from user,user_event where clan=" + clanId +
                    " and user.id = user_event.user_id and user_event.event_index=" + eventIndex;
            long trophy = Long.parseLong(session.createSQLQuery(sql).uniqueResult().toString());
            return trophy;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public ClanEntity getClan(int clanId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            ClanEntity clan = (ClanEntity) session.createSQLQuery("select * from clan where id=" + clanId).addEntity(ClanEntity.class).uniqueResult();
            if (clan != null)
                clan.setMember(Integer.parseInt(session.createSQLQuery("select count(*) from user where clan=" + clanId).uniqueResult().toString()));
//            clan.setTrophy(Long.parseLong(session.createSQLQuery("select sum(user_event.value) AS trophy from user,user_event where clan=" + clanId + " and user.id = user_event.user_id").uniqueResult().toString()));
            return clan;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public int numberMember(int clanId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            return Integer.parseInt(session.createSQLQuery("select count(*) from user where clan=" + clanId).uniqueResult().toString());
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public String joinClan(ClanEntity clan, UserInfo user) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            int numberMember = Integer.parseInt(session.createSQLQuery("select count(*) from user where clan=" + clan.getId()).uniqueResult().toString());
            clan.setMember(numberMember);
            if (numberMember >= CfgClan.maxMember(clan.getLevel())) {
                return user.getLang().get(Lang.clan_max_member);
            }
            session.beginTransaction();
            JSONArray arr = JSONArray.fromObject(user.getDbUser().getDiemdanh());
            arr.set(0, 0);
            session.createSQLQuery("update user set clan=" + clan.getId() + ", diemdanh='" + arr.toString() + "' where id=" + user.getId()).executeUpdate();
            session.getTransaction().commit();
            user.getDbUser().setClan(clan.getId());
            user.getDbUser().setDiemdanh(arr.toString());
            MCache.getInstance().set("joinclan:" + user.getId(), "1", MCache.EXPIRE_1D);
            return "";
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return user.getLang().get(Lang.err_system_down);
    }

    public String joinClan(Lang lang, ClanEntity clan, UserEntity uEntity) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            int numberMember = Integer.parseInt(session.createSQLQuery("select count(*) from user where clan=" + clan.getId()).uniqueResult().toString());
            clan.setMember(numberMember);
            if (numberMember >= CfgClan.maxMember(clan.getLevel())) {
                return "1";
            }
            session.beginTransaction();
            session.createSQLQuery("update clan set member=" + numberMember + " where id=" + clan.getId()).executeUpdate();
            JSONArray arr = JSONArray.fromObject(uEntity.getDiemdanh());
            arr.set(0, 0);
            session.createSQLQuery("update user set clan=" + clan.getId() + ", diemdanh='" + arr.toString() + "' where id=" + uEntity.getId()).executeUpdate();
            session.getTransaction().commit();
            uEntity.setDiemdanh(arr.toString());
            MCache.getInstance().set("joinclan:" + uEntity.getId(), "1", MCache.EXPIRE_1D);
            return "";
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return lang.get(Lang.err_system_down);
    }

    public boolean leaveClan(int clanId, long userId, long nextLeader, int numberMember) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("update user set clan=0, clan_position=0 where id=" + userId).executeUpdate();
            if (nextLeader != -1) {
                session.createSQLQuery("update user set clan_position=" + Constans.CLAN_LEADER + " where id=" + nextLeader).executeUpdate();
            }
            if (numberMember - 1 <= 0) session.createSQLQuery("delete from clan where id=" + clanId).executeUpdate();
            else session.createSQLQuery("update clan set member=" + (numberMember - 1) + " where id=" + clanId).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean promote(long userId, int myPosition, long promoteId, int newPosition) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("update user set clan_position=" + newPosition + " where id=" + promoteId).executeUpdate();
            if (myPosition != -1) {
                session.createSQLQuery("update user set clan_position=" + myPosition + " where id=" + userId).executeUpdate();
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean demote(long demoteId, int newPosition) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("update user set clan_position=" + newPosition + " where id=" + demoteId).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean diemdanh(long userId, String diemdanh, String moneys, int clanId, long[] newLevel) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("update clan set level=" + newLevel[0] + ", max_member=" + CfgClan.maxMember((int) newLevel[0]) + ", exp=" + newLevel[1] + " where id=" + clanId).executeUpdate();
            session.createSQLQuery("update user_data set moneys='" + moneys + "' where user_id=" + userId).executeUpdate();
            session.createSQLQuery("update user set diemdanh='" + diemdanh + "' where id=" + userId).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    private void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
}