package com.bem.object;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.Database2;
import grep.helper.DateTime;
import lombok.Data;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by vieth_000 on 3/9/2017.
 */
@Data
public class MapLevel {
    public static final int MODE_NORMAL = 1;
    public static final int MODE_INSANE = 2;

    public static final int NUMBER_MAP = 70;

    List<Integer> normal, insane, numberAtk;
    List<Integer> fakeNormal, fakeInsane, fakeNumberAtk;
    int curDate;

    public int getStar(){
       int  cNumber = 0;
        for (int i = 0; i < getNormal().size(); i++) {
            cNumber += getNormal().get(i);
        }
        for (int j = 0; j < getInsane().size(); j++) {
            cNumber += getInsane().get(j);
        }
        return cNumber;
    }

    public MapLevel(String data) {
        if (data == null || data.length() == 0) {
            normal = new ArrayList<Integer>();
            insane = new ArrayList<Integer>();
            numberAtk = new ArrayList<>();
            for (int i = 0; i < NUMBER_MAP; i++) {
                numberAtk.add(0);
            }
            curDate = Integer.parseInt(DateTime.getDateyyyyMMdd(new Date()));
        } else {
            JSONArray arr = JSONArray.fromObject(data);
            normal = new Gson().fromJson(arr.getJSONArray(0).toString(), new TypeToken<ArrayList<Integer>>() {
            }.getType());
            insane = new Gson().fromJson(arr.getJSONArray(1).toString(), new TypeToken<ArrayList<Integer>>() {
            }.getType());
            if (arr.size() <= 2) {
                numberAtk = new ArrayList<>();
                for (int i = 0; i < NUMBER_MAP; i++) {
                    numberAtk.add(0);
                }
            } else {
                numberAtk = new Gson().fromJson(arr.getJSONArray(2).toString(), new TypeToken<ArrayList<Integer>>() {
                }.getType());
                while (numberAtk.size() < NUMBER_MAP) {
                    numberAtk.add(0);
                }
            }
            if (arr.size() <= 3) {
                curDate = Integer.parseInt(DateTime.getDateyyyyMMdd(new Date()));
            } else {
                curDate = arr.getInt(3);
            }
        }
    }

    public List<Integer> getKindOfMap(int mode) {
        return mode == MODE_NORMAL ? normal : insane;
    }

    public int getNumberAtk(int mapId) {
        if (!String.valueOf(curDate).equals(DateTime.getDateyyyyMMdd(new Date()))) {
            curDate = Integer.parseInt(DateTime.getDateyyyyMMdd(new Date()));
            for (int i = 0; i < NUMBER_MAP; i++) {
                numberAtk.set(i, 0);
            }
        }
        mapId = mapId % 1000;
        return numberAtk.get(mapId - 1);
    }

    public void setNumberAtk(int mapId, int numberAtk) {
        mapId = mapId % 1000;
        this.numberAtk.set(mapId - 1, numberAtk);
    }

    public int getCurMap(int mode) {
        return mode == MODE_NORMAL ? normal.size() : insane.size();
    }

    public int getStar(int mode, int mapId) {
        try {
            mapId = mapId % 1000;
            return mode == MODE_NORMAL ? normal.get(mapId - 1) : insane.get(mapId - 1);
        } catch (Exception ex) {
            return 0;
        }
    }

    public void setReal(int mode, int mapId, int star) {
        mapId = mapId % 1000;
        if (star > 0) {
            List<Integer> tmp = mode == MODE_NORMAL ? normal : insane;
            if (tmp.size() < mapId) {
                tmp.add(star);
            } else if (tmp.get(mapId - 1) < star) {
                tmp.set(mapId - 1, star);
            }
        }
        if (mode == MODE_INSANE) {
            numberAtk.set(mapId - 1, numberAtk.get(mapId - 1) + 1);
        }
    }

    public void setFake(int mode, int mapId, int star) {
        mapId = mapId % 1000;
        fakeNormal = new ArrayList<Integer>(normal);
        fakeInsane = new ArrayList<Integer>(insane);
        fakeNumberAtk = new ArrayList<>(numberAtk);
        if (star > 0) {
            List<Integer> tmp = mode == MODE_NORMAL ? fakeNormal : fakeInsane;
            if (tmp.size() < mapId) {
                tmp.add(star);
            } else if (tmp.get(mapId - 1) < star) {
                tmp.set(mapId - 1, star);
            }
        }
        if (mode == MODE_INSANE) {
            fakeNumberAtk.set(mapId - 1, fakeNumberAtk.get(mapId - 1) + 1);
        }
    }

    public boolean update(long userId, boolean isReal) {
        return Database2.update("user_data", Arrays.asList("map_level", toString(isReal)), Arrays.asList("user_id", String.valueOf(userId)));
    }

    public String toString(boolean isReal) {
        if (isReal)
            return String.format("[%s,%s,%s,%s]", new Gson().toJson(normal), new Gson().toJson(insane), new Gson().toJson(numberAtk), String.valueOf(curDate));
        return String.format("[%s,%s,%s,%s]", new Gson().toJson(fakeNormal), new Gson().toJson(fakeInsane), new Gson().toJson(fakeNumberAtk), String.valueOf(curDate));
    }

}
