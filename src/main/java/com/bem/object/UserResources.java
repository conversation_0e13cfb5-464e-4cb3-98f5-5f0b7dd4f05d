package com.bem.object;

import com.bem.dao.mapping.*;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 1/19/2017.
 */
@Data
public class UserResources implements Serializable {
    public List<UserAvatarEntity> avatars;
    public List<UserItemEntity> items;
    public List<UserBombEntity> bombs;
    public List<UserHeroEntity> heroes;
    public List<UserMaterialEntity> materials;
    public List<UserPetEntity> pets;
    public List<UserPetFoodEntity> petFoods;
    public List<UserIconEntity> icons;
    public List<UserSymbolEntity> symbols;
    public List<UserUfoEntity> ufos;
    public List<UserStoneUfoEntity> stoneUfos;

    public List<UserAccessoriesEntity> accessories;

    //
    public Map<Integer, UserAvatarEntity> mAvatar = new HashMap<Integer, UserAvatarEntity>();
    public Map<Integer, UserItemEntity> mItem = new HashMap<Integer, UserItemEntity>();
    public Map<Integer, UserBombEntity> mBomb = new HashMap<Integer, UserBombEntity>();
    public Map<Integer, UserHeroEntity> mHero = new HashMap<Integer, UserHeroEntity>();
    public Map<Integer, UserMaterialEntity> mMaterial = new HashMap<Integer, UserMaterialEntity>();
    public Map<Integer, UserPetEntity> mPet = new HashMap<Integer, UserPetEntity>();
    public Map<Integer, UserPetFoodEntity> mPetFood = new HashMap<Integer, UserPetFoodEntity>();
    public Map<Integer, UserIconEntity> mIcon = new HashMap<Integer, UserIconEntity>();
    public Map<Integer, UserAccessoriesEntity> mAccessories = new HashMap<Integer, UserAccessoriesEntity>();
    public Map<Integer, UserSymbolEntity> mSymbol = new HashMap<Integer, UserSymbolEntity>();
    public Map<Integer, UserUfoEntity> mUfo = new HashMap<Integer, UserUfoEntity>();
    public Map<Integer, UserStoneUfoEntity> mStoneUfo = new HashMap<Integer, UserStoneUfoEntity>();

    public void addStar(int star, int fragment, int idPet) {
        mPet.get(idPet).setStar(mPet.get(idPet).getStar() + star);
        mPet.get(idPet).setFragment(mPet.get(idPet).getFragment() + fragment);
        if (mPet.get(idPet).getStar() < 0) {
            mPet.get(idPet).setStar(0);
        }
        if (mPet.get(idPet).getFragment() < 0) {
            mPet.get(idPet).setFragment(0);
        }
    }

    boolean isOk() {
        if (pets != null && avatars != null && items != null && bombs != null && heroes != null && materials != null && petFoods != null && icons != null && accessories != null && symbols != null && ufos != null && stoneUfos != null) {
            for (UserAvatarEntity avatar : avatars) {
                mAvatar.put(avatar.getAvatarId(), avatar);
            }
            for (UserItemEntity item : items) {
                mItem.put((int) item.getItemId(), item);
            }
            for (UserBombEntity bomb : bombs) {
                mBomb.put(bomb.getId(), bomb);
            }
            for (UserHeroEntity hero : heroes) {
                mHero.put(hero.getHeroId(), hero);
            }
            for (UserMaterialEntity material : materials) {
                mMaterial.put((int) material.getMaterialId(), material);
            }
            for (UserPetEntity pet : pets) {
                mPet.put(pet.getPetId(), pet);
            }
            for (UserPetFoodEntity petFood : petFoods) {
                mPetFood.put(petFood.getFoodId(), petFood);
            }
            for (UserIconEntity icon : icons) {
                mIcon.put(icon.getIconId(), icon);
            }
            for (UserAccessoriesEntity acc : accessories) {
                mAccessories.put(acc.getId(), acc);
            }
            for (UserSymbolEntity sysbol : symbols) {
                mSymbol.put(sysbol.getSymbolId(), sysbol);
            }
            for (UserUfoEntity ufo : ufos) {
                mUfo.put(ufo.getUfoId(), ufo);
            }
            for (UserStoneUfoEntity stone : stoneUfos) {
                mStoneUfo.put(stone.getStoneId(), stone);
            }
            return true;
        }
        return false;
    }

    public String toString() {
        Iterator<Integer> keys = mAvatar.keySet().iterator();
        while (keys.hasNext()) {
            int key = keys.next();
        }
        return "";
    }
}
