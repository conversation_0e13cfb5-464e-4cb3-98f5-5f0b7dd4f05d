package com.bem.object;

import com.k2tek.Constans;
import com.proto.GGProto;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by vieth_000 on 6/27/2017.
 */
@Data
public class ChatObject implements Serializable {
    long userId, time;
    String msg, name;
    int avatar, msgId, channel, msgType, vip;

    public ChatObject(long userId, String msg, int msgType) {
        this.userId = userId;
        this.msg = msg;
        this.time = System.currentTimeMillis() / 1000;
        this.msgType = msgType;
    }

    public GGProto.ProtoChat.Builder toProto() {
        GGProto.ProtoChat.Builder tmp = GGProto.ProtoChat.newBuilder();
        tmp.setUserId(userId);
        tmp.setMessage(msg);
        tmp.setAvatar(avatar + "");
        tmp.setTime(time);
        if (name != null) tmp.setName(name);
        tmp.setVip(vip);
        tmp.setChannel(channel);
        tmp.setMsgType(msgType);
        tmp.setMsgId(msgId);
        return tmp;
    }
}
