package com.bem.object;

import com.bem.boom.Resources;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgShop;
import com.bem.monitor.Bonus;
import com.k2tek.Constans;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 2/3/2017.
 */
@Data
public class ResAvatar {
    int id, characterId, imageId, rank, hp, sHp, atk, sAtk;
    int lock;

    public static List<List<Integer>> get2AvatarFragment(Map<String, CfgShop.ShopSpecial> mShopSpecial, int type, int priceType) {
        List<List<Integer>> aInt = new ArrayList<>();
        List<Integer> aId = new ArrayList<>();
        while (aInt.size() < 2) {
            ResAvatar avatar = Resources.getRandomAvatar();
            CfgShop.ShopSpecial shop = mShopSpecial.get(type + "_" + avatar.rank);
            if (priceType == Constans.PRICE_NONE) {
                if ((shop.Gold != -1 || shop.Gem != -1) && !aId.contains(avatar.id)) {
                    int[] price = mShopSpecial.get(type + "_" + avatar.rank).getPriceThanbi();
                    aInt.add(Arrays.asList(Bonus.AVATAR_FRAGMENT, avatar.id, price[0], price[1], 1));
                    aId.add(avatar.id);
                }
            } else if (priceType == Constans.PRICE_MEDAL && shop.Medal != -1 && !aId.contains(avatar.id)) {
                aInt.add(Arrays.asList(Bonus.AVATAR_FRAGMENT, avatar.id, Constans.PRICE_MEDAL, shop.Medal, 1));
                aId.add(avatar.id);
            } else if (priceType == Constans.PRICE_MEDAL_BOSS && shop.Boss != -1 && !aId.contains(avatar.id)) {
                aInt.add(Arrays.asList(Bonus.AVATAR_FRAGMENT, avatar.id, Constans.PRICE_MEDAL_BOSS, shop.Boss, 1));
                aId.add(avatar.id);
            }
        }
        return aInt;
    }

    public static List<List<Integer>> get2Avatar(Map<String, CfgShop.ShopSpecial> mShopSpecial, int type, int priceType) {
        List<List<Integer>> aInt = new ArrayList<>();
        List<Integer> aId = new ArrayList<>();
        while (aInt.size() < 8) {
            ResAvatar avatar = Resources.getRandomAvatar();
            CfgShop.ShopSpecial shop = mShopSpecial.get(type + "_" + avatar.rank);
            if (priceType == Constans.PRICE_NONE) {
                if ((shop.Gold != -1 || shop.Gem != -1) && !aId.contains(avatar.id)) {
                    int[] price = mShopSpecial.get(type + "_" + avatar.rank).getPriceThanbi();
                    aInt.add(Arrays.asList(Bonus.AVATAR, avatar.id, price[0], price[1], 1));
                    aId.add(avatar.id);
                }
            } else if (priceType == Constans.PRICE_MEDAL && shop.Medal != -1 && !aId.contains(avatar.id)) {
                aInt.add(Arrays.asList(Bonus.AVATAR, avatar.id, Constans.PRICE_MEDAL, shop.Medal, 1));
                aId.add(avatar.id);
            } else if (priceType == Constans.PRICE_MEDAL_BOSS && shop.Boss != -1 && !aId.contains(avatar.id)) {
                aInt.add(Arrays.asList(Bonus.AVATAR, avatar.id, Constans.PRICE_MEDAL_BOSS, shop.Boss, 1));
                aId.add(avatar.id);
            }
        }
        return aInt;
    }

    public static List<List<Integer>> get10Avatar(Map<String, CfgShop.ShopSpecial> mShopSpecial, int type, int priceType) {
        List<List<Integer>> aInt = new ArrayList<>();
        List<Integer> aId = new ArrayList<>();
        while (aInt.size() < 10) {
            ResAvatar avatar = Resources.getRandomAvatar();
            CfgShop.ShopSpecial shop = mShopSpecial.get(type + "_" + avatar.rank);
            if (priceType == Constans.PRICE_NONE) {
                if ((shop.Gold != -1 || shop.Gem != -1) && !aId.contains(avatar.id)) {
                    int[] price = mShopSpecial.get(type + "_" + avatar.rank).getPriceThanbi();
                    aInt.add(Arrays.asList(Bonus.AVATAR, avatar.id, price[0], price[1] / 2, 1));
                    aId.add(avatar.id);
                }
            } else if (priceType == Constans.PRICE_MEDAL && shop.Medal != -1 && !aId.contains(avatar.id)) {
                aInt.add(Arrays.asList(Bonus.AVATAR, avatar.id, Constans.PRICE_MEDAL, shop.Medal / 2, 1));
                aId.add(avatar.id);
            } else if (priceType == Constans.PRICE_MEDAL_BOSS && shop.Boss != -1 && !aId.contains(avatar.id)) {
                aInt.add(Arrays.asList(Bonus.AVATAR, avatar.id, Constans.PRICE_MEDAL_BOSS, shop.Boss / 2, 1));
                aId.add(avatar.id);
            }
        }
        return aInt;
    }
}
