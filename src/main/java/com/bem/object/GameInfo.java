package com.bem.object;

import com.proto.GGProto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 5/8/2017.
 */
public class GameInfo implements Serializable {

    public GameInfo() {
        status = new EventStatus(-1, -1);
        topPlayer = new ArrayList<>();
        bossId = 3;
        sendBonus = false;
    }

    public EventStatus status;
    public int bossId, mapId;
    public boolean sendBonus = false;
    public List<GGProto.ProtoUser> topPlayer = new ArrayList<GGProto.ProtoUser>();
}
