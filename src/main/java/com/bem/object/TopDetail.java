package com.bem.object;

import com.bem.dao.mapping.UserEntity;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class TopDetail {
    public int id;
    public String keyId="";
    public String hostName;
    public List<UserEntity> user;
    public  int week=0;
    public int time;
    public TopDetail() {
    }


    public List<TopDetail> getListTopBoss(String input){
        List<TopDetail> rs = new ArrayList<TopDetail>();
        try {
            rs = new Gson().fromJson(input, new TypeToken<List<TopDetail>>() {
            }.getType());
        }catch (Exception ex){
            ex.printStackTrace();
        }
        return rs;
    }
    public TopDetail getTopBoss(String input){
        TopDetail rs = new TopDetail();
        try {
            rs = new Gson().fromJson(input, new TypeToken<TopDetail>() {
            }.getType());
        }catch (Exception ex){
            ex.printStackTrace();
        }
        return rs;
    }
    public String toString(List<TopDetail> input){
        try {
            return new Gson().toJson(input);
//            return input.toString();
        }catch (Exception ex){
        }
        return "";
    }
    public String toString(TopDetail input){
        try {
            return new Gson().toJson(input);
//            return input.toString();
        }catch (Exception ex){
        }
        return "";
    }

}
