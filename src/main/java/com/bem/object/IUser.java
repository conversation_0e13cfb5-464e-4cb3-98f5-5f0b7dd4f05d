package com.bem.object;


import com.bem.boom.CacheBattle;
import com.bem.boom.Resources;
import com.bem.boom.object.CachePlayer;
import com.bem.boom.object.MyAvatar;
import com.bem.boom.object.Point;
import com.bem.dao.mapping.UserPetEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

//import com.bem.dao.mapping.HeroEntity;

/**
 * Created by vieth_000 on 7/18/2016.
 */
public class IUser {
    public long id;
    public int team, trophy, userTrophy, rankId;
    public String name, username;
    public List<Integer> avatars = new ArrayList<>();
    public List<UsedItem> aUsedItem = new ArrayList<>();
    public Point point;
    public Skill userSkill, petSkill;
    public int reviveItem;

    public IUser(UserInfo user, int team) {
        this.id = user.getId();
        this.name = user.getDbUser().getName();
        this.team = team;
        //
        UserPetEntity uPet = user.getRes().getMPet().get(user.getMAvatar().getPet());
        if (uPet != null) {
            List<Skill> aSkill = uPet.getSkill();
            if (!aSkill.isEmpty()) {
                petSkill = aSkill.get(0);
            }
        }
        user.getPoint().reloadPoint(user.getRes(), user.getMAvatar(), user.getDbUser().getLevel());
        this.point = user.getPoint().clone();
        this.userSkill = Resources.getHero(user.getMAvatar().getHero()).getSkill();
    }

    public IUser(CachePlayer cachePlayer, int mode) {
        this.id = cachePlayer.getUserId();
        this.name = cachePlayer.getName();
        this.team = cachePlayer.getTeam();
        this.userTrophy = cachePlayer.getTrophy();
        this.username = cachePlayer.getUsername();
        this.avatars = cachePlayer.getAvatars();
        this.rankId = cachePlayer.getRankId();
        //
        this.petSkill = cachePlayer.getPetSkill();
        this.userSkill = cachePlayer.getUserSkill();
        if (cachePlayer.getPoint().isZombie()) {
            try {
                this.userSkill = Resources.getHero(Point.zombieSkill[(int) cachePlayer.getPoint().typeZombie()]).getSkill();
            } catch (Exception ex) {
                this.userSkill = Resources.getHero(5).getSkill();

            }
            this.petSkill = null;
        }
        this.point = cachePlayer.getPoint().clone();
        this.reviveItem = cachePlayer.getReviveItem();
        for (int i = 0; i < cachePlayer.getBattleItems().size(); i += 2) {
            if (cachePlayer.getBattleItems().get(i) > 0 && cachePlayer.getBattleItems().get(i + 1) > 0) {
                this.aUsedItem.add(new UsedItem(cachePlayer.getBattleItems().get(i), cachePlayer.getBattleItems().get(i + 1)));
            }
        }
        if (mode == CacheBattle.MODE_GLOBAL) {
            this.point.point[Point.HP] = 1;
            this.point.point[Point.CUR_HP] = 1;
        }
    }

    public boolean hasItem(int itemId) {
        for (UsedItem item : aUsedItem) {
            if (item.itemId == itemId && item.totalNumber > item.number) return true;
        }
        return false;
    }

    public void useItem(int itemId) {
        for (UsedItem item : aUsedItem) {
            if (item.itemId == itemId && item.totalNumber > item.number) {
                item.number++;
                return;
            }
        }
    }

    public int getPet() {
        return avatars.get(MyAvatar.PET);
    }

    public int getHero() {
        return avatars.get(MyAvatar.HERO);
    }

    public int getBombImage() {
        return avatars.get(MyAvatar.BOMB_IMAGE);
    }

    public float getPoint(int index) {
        return point.point[index];
    }

    public int getIntPoint(int index) {
        return (int) point.point[index];
    }

    public void setPoint(int index, float value) {
        point.point[index] = value;
    }

    public void addHp(int value) {
        point.addHp(value);
    }

    public void addAtk(int value) {
        point.addAtk(value);
    }

    public void addDecreaseDamage(int value) {
        point.addDecreaseDamage(value);
    }

    @Data
    public class UsedItem {
        int itemId, totalNumber;
        int number;

        public UsedItem(int itemId, int totalNumber) {
            this.itemId = itemId;
            this.totalNumber = totalNumber;
        }
    }
}
