package com.bem.object;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class CPInfor {
	JSONArray cpName;
	JSONObject sms, version;
	String parentCP;

	public String getParentCP() {
		return parentCP;
	}

	public void setParentCP(String parentCP) {
		this.parentCP = parentCP;
	}

	public JSONArray getCpName() {
		return cpName;
	}

	public void setCpName(JSONArray cpName) {
		this.cpName = cpName;
	}

	public JSONObject getSms() {
		return sms;
	}

	public void setSms(JSONObject sms) {
		this.sms = sms;
	}

	public JSONObject getVersion() {
		return version;
	}

	public void setVersion(JSONObject version) {
		this.version = version;
	}

}
