package com.bem.object;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by vieth_000 on 7/12/2017.
 */
@Data
public class EventStatus implements Serializable {
    int id, index;

    public EventStatus() {
        this.id = -1;
        this.index = -1;
    }

    public EventStatus(int id, int index) {
        this.id = id;
        this.index = index;
    }

    //
    int lastEventId, nextEventId, curTime, nextTime;

    public EventStatus(int lastEventId, int nextEventId, int curTime, int nextTime) {
        this.lastEventId = lastEventId;
        this.nextEventId = nextEventId;
        this.curTime = curTime;
        this.nextTime = nextTime;
    }
}
