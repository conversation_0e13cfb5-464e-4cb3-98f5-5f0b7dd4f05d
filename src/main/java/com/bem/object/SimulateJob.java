package com.bem.object;

import com.bem.AbstractTable;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * Created by vieth_000 on 3/31/2017.
 */
public class Simulate<PERSON>ob implements Job {

    public SimulateJob() {
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        AbstractTable table = (AbstractTable) context.getJobDetail().getJobDataMap().get("table");
        int intervals = context.getJobDetail().getJobDataMap().getInt("intervals");
        if (intervals < 30)
            table.server_update_physics();
        else
            table.server_update();
    }
}
