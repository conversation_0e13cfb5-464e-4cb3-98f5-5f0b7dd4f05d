package com.bem.object;

import com.bem.boom.Resources;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgShop;
import com.bem.config.lang.Lang;
import com.bem.monitor.Bonus;
import com.k2tek.Constans;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 2/3/2017.
 */
@Data
public class ResBomb {
    int id;
    String name;
    int atk, sAtk, gold, rank;
    int lock, type, price;

    public String getName() {
        return Lang.getClientLanguage(name);
    }

    public static List<List<Integer>> get2Bomb(Map<String, CfgShop.ShopSpecial> mShopSpecial, int type, int priceType) {
        List<List<Integer>> aInt = new ArrayList<>();
        List<Integer> aId = new ArrayList<>();
        while (aInt.size() < 2) {
            ResBomb bomb = Resources.getRandomBomb();
            CfgShop.ShopSpecial shop = mShopSpecial.get(type + "_" + bomb.rank);
            if (priceType == Constans.PRICE_NONE) {
                if ((shop.Gold != -1 || shop.Gem != -1) && !aId.contains(bomb.id)) {
                    int[] price = mShopSpecial.get(type + "_" + bomb.rank).getPriceThanbi();
                    aInt.add(Arrays.asList(Bonus.BOMB, bomb.id, price[0], price[1], 1));
                    aId.add(bomb.id);
                }
            } else if (priceType == Constans.PRICE_MEDAL && shop.Medal != -1 && !aId.contains(bomb.id)) {
                aInt.add(Arrays.asList(Bonus.BOMB, bomb.id, Constans.PRICE_MEDAL, shop.Medal, 1));
                aId.add(bomb.id);
            } else if (priceType == Constans.PRICE_MEDAL_BOSS && shop.Boss != -1 && !aId.contains(bomb.id)) {
                aInt.add(Arrays.asList(Bonus.BOMB, bomb.id, Constans.PRICE_MEDAL_BOSS, shop.Boss, 1));
                aId.add(bomb.id);
            }
        }
        return aInt;
    }
}
