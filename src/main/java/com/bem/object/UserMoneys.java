package com.bem.object;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.Database2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 2/22/2017.
 */
public class UserMoneys {
    public static final int NUMBER_VALUE = 3;
    //
    public static final int CLAN = 0;
    public static final int MEDAL_BOSS = 1;
    public static final int MEDAL = 2;
    //
    public List<Long> aLong;

    public UserMoneys(String moneys) {
        moneys = moneys == null ? "[]" : moneys;
        aLong = new Gson().fromJson(moneys, new TypeToken<ArrayList<Long>>() {
        }.getType());
        while (aLong.size() < NUMBER_VALUE) {
            aLong.add(0L);
        }
    }

    public void addValue(int index, long value) {
        aLong.set(index, aLong.get(index) + value);
    }

    public long getValue(int index) {
        return aLong.get(index);
    }

    public void setValue(int index, long value) {
        aLong.set(index, value);
    }

    public String toString() {
        return new Gson().toJson(aLong);
    }

    public boolean update(long userId) {
        return Database2.update("user_data", Arrays.asList("moneys", aLong.toString()), Arrays.asList("user_id", String.valueOf(userId)));
    }

}
