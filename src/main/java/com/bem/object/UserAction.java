package com.bem.object;

import com.bem.config.lang.Lang;
import com.bem.dao.mapping.UserItemEntity;
import com.bem.monitor.Bonus;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.handler.AHandler;
import com.k2tek.Constans;
import com.k2tek.IAction;
import grep.database.Database2;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 3/16/2015.
 */
public class UserAction implements Serializable {

    UserInfo user;

    public UserAction(UserInfo user) {
        this.user = user;
    }

    public synchronized int doAction(Channel channel, int action, byte[] srcRequest) {
        switch (action) {
        }
        return 0;
    }

    public synchronized boolean addItem(int itemId, int addValue) {
        return addItem(Arrays.asList(itemId), addValue);
    }

    public synchronized boolean addItem(List<Integer> itemIds, int addValue) {
        String moreDetails = "and item_id in (";
        List<UserItemEntity> aItem = new ArrayList<UserItemEntity>();
        for (int i = 0; i < itemIds.size(); i++) {
            UserItemEntity uItem = user.getRes().getMItem().get(itemIds.get(i));
            if (uItem == null || uItem.getNumber() <= 0) {
                return false;
            }
            aItem.add(uItem);
            if (i == 0) {
                moreDetails += itemIds.get(i);
            } else {
                moreDetails += "," + itemIds.get(i);
            }
        }
        moreDetails += ")";

        if (Database2.addNumber("user_item", Arrays.asList("number"), Arrays.asList(addValue), Arrays.asList("user_id", String.valueOf(user.getId())), moreDetails) >= 1) {
            for (UserItemEntity uItem : aItem) {
                uItem.addNumber(addValue);
            }
            return true;
        }
        return false;
    }

    public synchronized JSONArray checkMoney(AHandler handler, int priceType, long value) {
        UserMoneys moneys = user.getUData().getUserMoneys();
        switch (priceType) {
            case Constans.PRICE_GOLD:
                if (user.getDbUser().getGold() + value < 0) {
//                    handler.addErrMessage("Không đủ vàng");
                    handler.addErrMessage(user.getLang().get(Lang.err_not_enough_gold));
                    return null;
                }
                return Bonus.defaultBonusView(Bonus.GOLD, value);
            case Constans.PRICE_GEM:
                if (user.getDbUser().getGem() + value < 0) {
//                    handler.addErrMessage("Không đủ ngọc");
                    handler.addErrMessage(user.getLang().get(Lang.err_not_enough_gem));
                    return null;
                }
                return Bonus.defaultBonusView(Bonus.GEM, value);
            case Constans.PRICE_MEDAL_BOSS:
                if (moneys.getValue(UserMoneys.MEDAL_BOSS) + value < 0) {
//                    handler.addErrMessage("Không đủ huy hiệu boss");
                    handler.addErrMessage(user.getLang().get(Lang.err_not_enough_boss_medal));
                    return null;
                }
                return Bonus.defaultBonusView(Bonus.MEDAL_BOSS, value);
            case Constans.PRICE_MEDAL:
                if (moneys.getValue(UserMoneys.MEDAL) + value < 0) {
//                    handler.addErrMessage("Không đủ huy hiệu");
                    handler.addErrMessage(user.getLang().get(Lang.err_not_enough_medal));
                    return null;
                }
                return Bonus.defaultBonusView(Bonus.MEDAL, value);
        }
        return null;
    }

    public synchronized JSONArray addMoney(AHandler handler, int priceType, long value) {
        long addGold = 0, addGem = 0;
        switch (priceType) {
            case Constans.PRICE_GOLD:
                addGold += value;
                break;
            case Constans.PRICE_GEM:
                addGem += value;
                break;
        }
        return addMoney(handler, addGold, addGem);
    }

    public synchronized JSONArray addMoney(AHandler handler, long addGold, long addGem) {
        if (user.getDbUser().getGold() + addGold < 0) {
//            handler.addErrMessage("Không đủ vàng");
            handler.addErrMessage(user.getLang().get(Lang.err_not_enough_gold));
            return null;
        } else if (user.getDbUser().getGem() + addGem < 0) {
//            handler.addErrMessage("Không đủ ngọc");
            handler.addErrMessage(user.getLang().get(Lang.err_not_enough_gem));
            return null;
        }
        JSONArray arr = new JSONArray();
        if (addGem != 0) {
            arr.addAll(Bonus.defaultBonusView(Bonus.GEM, addGem));
        }
        if (addGold != 0) {
            arr.addAll(Bonus.defaultBonusView(Bonus.GOLD, addGold));
        }
        return arr;
    }

    void sendErrMsg(Channel channel, String msg) {
        Util.sendProtoData(channel, CommonProto.getCommonVectorProto(null, Arrays.asList(msg)), IAction.MSG_TOAST, System.currentTimeMillis());
    }
}
