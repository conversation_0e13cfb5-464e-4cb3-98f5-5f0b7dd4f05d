package com.bem.object.event;

import com.bem.object.EventStatus;
import com.bem.object.UserArena;
import com.bem.object.UserInfo;
import com.proto.GGProto;
import io.netty.channel.Channel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 5/8/2017.
 */
public class ArenaInfo implements Serializable {
    public final static int TYPENOTTIME = -1, TYPEGROUPTIME = 0, TYPEWAITTIME = 1, TYPEKNOCKOUTTIME = 2;
    public final static int STATUSLOSE = 0, STATUSPLAY =1,STATUSWIN = 2, STATUSREADY = 3,STATUSWAIT = -1;
    public final static int VONG1_16 = 0, TUKET =1,BANKET = 2, CHUNGKET = 3;

    public final static String ARENADELAY=":ARENADELAY:";
    public ArenaInfo() {
        status = new EventStatus(-1, -1);
        topPlayer = new ArrayList<>();
//        lstUserKnockout = new ArrayList<>();
        lstHistoryUserKnockout = new ArrayList<>();
        type = -1;
        sendBonus = false;
    }

    public EventStatus status;
    int type;// =0vong bang ;=1 waitknockout; = 2 vong knockout ; -1 ko dien ra
    public boolean sendBonus = false;
    public List<UserArena> topPlayer = new ArrayList<UserArena>();
//    public List<UserArena> lstUserKnockout = new ArrayList<UserArena>();
    public List<List<UserArena>> lstHistoryUserKnockout = new ArrayList<>();
    int round =-1;

    public synchronized int getRound() {
        return round;
    }

    public synchronized void setRound(int Round) {
        this.round = Round;
    }


    public synchronized int getType() {
        return type;
    }

    public synchronized void setType(int type) {
        this.type = type;
    }

    public GGProto.ProtoUser changeToProtoUser(UserArena user,UserInfo userI) {
        GGProto.ProtoUser.Builder builder = GGProto.ProtoUser.newBuilder();
        builder.setUsername(user.getUsername());
        builder.setName(user.getName());
        builder.setId(user.getUserId());
        builder.setGold(user.getPoint());
        builder.setGem(user.getPoint());
        builder.setLvl(user.getLevel());
        builder.addAllAvatar(user.getAvatar());
        builder.setRank(user.getRank());
        return builder.build();
    }

    //region proto
    public UserArena protoArenaUser(UserInfo user, long point, int status, int rourd ) {
        UserArena uArena = new UserArena();
        uArena.setUsername(user.getUsername());
        uArena.setName(user.getDbUser().getName());
        uArena.setUserId(user.getId());
        uArena.setRoundKnockout(rourd);
        uArena.setPoint((int)point);
        uArena.setStatus((int)status);
        uArena.setAvatar(user.getMAvatar().toList());
        uArena.setRank(user.getDbUser().getRankId());
        uArena.setLevel(user.getDbUser().getLevel());
        return uArena;
    }



}
