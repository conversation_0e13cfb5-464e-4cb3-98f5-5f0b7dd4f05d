package com.bem.object.event;

import com.bem.dao.mapping.UserDataEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.UserEventEntity;
import com.bem.object.UserInt;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import grep.database.Database2;
import grep.database.HibernateUtil;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 5/18/2017.
 */
public class TopWin extends AbstractEvent {

    static TopWin instance;
    private static Map<Integer, UserEventEntity> topUserLastEvent = new HashMap<>();

    public static synchronized TopWin getInstance() {
        if (instance == null) {
            instance = new TopWin();
        }
        return instance;
    }

    public static synchronized void setTopUserLastEvent(int serverId, UserEventEntity user) {
        topUserLastEvent.put(serverId, user);
    }

    public static synchronized UserEventEntity getTopUserLastEvent(int serverId) {
        return topUserLastEvent.get(serverId);
    }

    public TopWin() {
        this.eventId = Constans.EVENT_TOP_WIN;
    }

    public TopWin(long userId, UserDataEntity uData, UserEntity user) {
        this.eventId = Constans.EVENT_TOP_WIN;
        this.userId = userId;
        dbLoadData(userId, getEventIndex(), uData, user);
    }

    //region Database
    public boolean dbLoadData(long userId, int[] eventIndex, UserDataEntity uData, UserEntity user) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_event where user_id=" + userId + " and event_id=" + eventId
                    + " and (event_index=" + eventIndex[0] + " or event_index=" + eventIndex[1] + ")");
            List<UserEventEntity> aEvent = query.addEntity(UserEventEntity.class).list();
            boolean hasCurIndex = false;
            UserEventEntity lastEvent = null;
            for (UserEventEntity event : aEvent) {
                if (event.getEventIndex() == eventIndex[0]) {
                    hasCurIndex = true;
                    this.event = event;
                } else if (event.getEventIndex() == eventIndex[1]) { // event cu chua trao thuong
                    if (getTopUserLastEvent(user.getServer()) == null || getTopUserLastEvent(user.getServer()).getThisEventIndex() != eventIndex[1]) {
                        List<UserEventEntity> aUser = Database2.getList("user_event", Arrays.asList("event_id", String.valueOf(Constans.EVENT_TOP_WIN),
                                "event_index", String.valueOf(eventIndex[1]), "server_id", String.valueOf(user.getServer())), "order by value desc limit 0, 1", UserEventEntity.class);
                        if (aUser != null && aEvent.size() > 0) {
                            setTopUserLastEvent(user.getServer(), new UserEventEntity());
                            getTopUserLastEvent(user.getServer()).setInfo(0);
                            getTopUserLastEvent(user.getServer()).setUserId(aUser.get(0).getUserId());
                            getTopUserLastEvent(user.getServer()).setValue(aUser.get(0).getValue());
                            getTopUserLastEvent(user.getServer()).setEventIndex(eventIndex[1]);
                        }
                    }
                }
            }
            if (!hasCurIndex || lastEvent != null) {
                session.beginTransaction();
                if (!hasCurIndex) {
                    UserEventEntity event = new UserEventEntity(userId, eventId, eventIndex[0]);
                    uData.getUInt().setValue(UserInt.NUMBER_WIN, 0);
                    event.setServerId(user.getServer());
                    SQLQuery query1 = session.createSQLQuery("update user_data set data_int=:dataInt where user_id=" + userId);
                    query1.setString("dataInt", new Gson().toJson(uData.getUInt().aInt));
                    query1.executeUpdate();
                    session.save(event);
                    this.event = event;
                }
                session.getTransaction().commit();
            }
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }
    //endregion
}
