package com.bem.object.event;

import com.bem.boom.Resources;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.UserEventEntity;
import com.bem.dao.mapping.UserMessageEntity;
import com.bem.object.ITimer;
import com.bem.object.ResRank;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.k2tek.Config;
import com.k2tek.Constans;
import com.k2tek.Xerver;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by vieth_000 on 5/18/2017.
 */
public class TopTrophy extends AbstractEvent implements ITimer {

    static TopTrophy instance;

    public static synchronized TopTrophy getInstance() {
        if (instance == null) {
            instance = new TopTrophy();
        }
        return instance;
    }

    public TopTrophy() {
        this.eventId = Constans.EVENT_TOP_TROPHY;
    }

    public TopTrophy(long userId, UserEntity user) {
        this.eventId = Constans.EVENT_TOP_TROPHY;
        this.userId = userId;
        dbLoadData(userId, getEventIndex(), user);
    }

//    public TopTrophy(long userId, int index) {
//        this.userId = userId;
//        this.eventId = index;
//        dbLoadData(userId, getEventIndexMaxWin());
//    }
//
//    public static int[] getEventIndexMaxWin() {
//        return CfgEvent.getEvent(Constans.EVENT_TOP_WIN).eventIndex();
//    }
//
//    public static long nextSessionWin() {
//        return CfgEvent.getEvent(Constans.EVENT_TOP_WIN).nextSeason();
//    }
//
//    public long getMaxWin() {
//        if (event == null) {
//            return 0;
//        }
//        if (getEventIndexMaxWin()[0] == event.getEventIndex()) return event.getValue();
//        return 0;
//    }

    //region Database
    public boolean dbLoadData(long userId, int[] eventIndex, UserEntity user) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_event where user_id=" + userId + " and event_id=" + eventId
                    + " and (event_index=" + eventIndex[0] + " or event_index=" + eventIndex[1] + ")");
            List<UserEventEntity> aEvent = query.addEntity(UserEventEntity.class).list();
            boolean hasCurIndex = false;
            UserEventEntity lastEvent = null;
            for (UserEventEntity event : aEvent) {
                if (event.getEventIndex() == eventIndex[0]) {
                    hasCurIndex = true;
                    this.event = event;
                } else if (event.getEventIndex() == eventIndex[1] && !event.isSendBonus() && event.getInfo() > -1) { // event cu chua trao thuong
                    lastEvent = event;
                }
            }
            if (!hasCurIndex || lastEvent != null) {
                session.beginTransaction();
                if (!hasCurIndex) {
                    UserEventEntity event = new UserEventEntity(userId, eventId, eventIndex[0]);
                    event.setServerId(user.getServer());
                    session.save(event);
                    this.event = event;
                }
                if (lastEvent != null) {
                    // send bonus here
                    int rank = lastEvent.getInfo();
                    if (rank == 1000) {
                        for (int i = Resources.aRankTrophy.size() - 4; i >= 0; i--) {
                            if (lastEvent.getValue() >= Resources.aRankTrophy.get(i).trophy) {
                                rank = Resources.aRankTrophy.get(i).id;
                                break;
                            }
                        }
                    }
                    if (rank >= 0 && rank < 1000 && this.eventId == Constans.EVENT_TOP_TROPHY) {
                        ResRank resRank = Resources.aRankTrophy.get(rank - 1);
                        UserMessageEntity uMessage = new UserMessageEntity();
                        uMessage.setTitle("Ranking reward " + resRank.name);
                        uMessage.setDateCreated(new Date());
                        uMessage.setUserId(userId);
                        uMessage.setBonus(new Gson().toJson(resRank.reward));
                        session.save(uMessage);
                        lastEvent.setSendBonus(true);
                        session.update(lastEvent);
                    }
                }
                session.getTransaction().commit();
            }
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }
    //endregion

    public boolean setRewards(int rewardEventIndex) {
        getLogger().warn("check reward eventIndex = " + rewardEventIndex);
        for (int serverId = 1; serverId <= 2; serverId++) {
            Session session = null;
            try {
                session = HibernateUtil.getSessionFactory().openSession();
                SQLQuery query = session.createSQLQuery("select * from user_event where event_id=" + Constans.EVENT_TOP_TROPHY + " and event_index=" + rewardEventIndex + " and server_id=" + serverId + " order by value desc limit 0,23");
                List<UserEventEntity> aEvent = query.addEntity(UserEventEntity.class).list();
                if (aEvent.isEmpty()) {
                    break;
                }
                if (aEvent.get(0).getInfo() >= 0) {
                    break;
                }
                getLogger().warn("Rewards top trophy serverId=" + serverId + " - size=" + aEvent.size());
                List<String> aSql = new ArrayList<>();
                if (aEvent != null) {
                    session.beginTransaction();
                    int number1 = Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 1).number;
                    int number2 = Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 2).number;
                    int number3 = Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 3).number;
                    for (int i = 0; i < aEvent.size(); i++) {
                        if (number1 > 0 && aEvent.get(i).getValue() >= Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 1).trophy) {
                            aEvent.get(i).setInfo(Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 1).id);
                            getLogger().warn(aEvent.get(i).getUserId() + " " + aEvent.get(i).getInfo());
                            session.update(aEvent.get(i));
                            number1 = 0;
                            aSql.add(String.format("insert ignore user_symbol(user_id,symbol_id) values(%s,%s)", aEvent.get(i).getUserId(), "10"));
                            aSql.add(String.format("insert ignore user_symbol(user_id,symbol_id) values(%s,%s)", aEvent.get(i).getUserId(), "11"));
                        } else if (number2 > 0 && aEvent.get(i).getValue() >= Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 2).trophy) {
                            aEvent.get(i).setInfo(Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 2).id);
                            getLogger().warn(aEvent.get(i).getUserId() + " " + aEvent.get(i).getInfo());
                            session.update(aEvent.get(i));
                            number2--;
                            aSql.add(String.format("insert ignore user_symbol(user_id,symbol_id) values(%s,%s)", aEvent.get(i).getUserId(), "10"));
                        } else if (number3 > 0 && aEvent.get(i).getValue() >= Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 3).trophy) {
                            aEvent.get(i).setInfo(Resources.aRankTrophy.get(Resources.aRankTrophy.size() - 3).id);
                            getLogger().warn(aEvent.get(i).getUserId() + " " + aEvent.get(i).getInfo());
                            session.update(aEvent.get(i));
                            number3--;
                        }
                    }
                    for (String sql : aSql) session.createSQLQuery(sql).executeUpdate();
                    session.createSQLQuery("update user_event set info=1000 where info=-1 and event_id=" + Constans.EVENT_TOP_TROPHY + " and event_index=" + rewardEventIndex + " and server_id=" + serverId).executeUpdate();
                    session.getTransaction().commit();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                Logs.error(Util.exToString(ex));
                return false;
            } finally {
                closeSession(session);
            }
        }
        return true;
    }

    int count = 0;

    @Override
    public void doExpireTurn(int turnId) {
        if (++count == 30) {
            count = 0;
            setRewards(getEventIndex()[1]);
        }
        timer();
    }

    void timer() {
        try {
            Xerver.timer(this, 0, 30);
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
    }
}
