package com.bem.object.event;

import com.bem.config.CfgEvent;
import com.bem.dao.mapping.UserEventEntity;
import com.k2tek.Constans;
import com.k2tek.common.slib_Logger;
import lombok.Data;
import org.hibernate.Session;
import org.slf4j.Logger;

/**
 * Created by vieth_000 on 5/18/2017.
 */
@Data
public abstract class AbstractEvent {
    long userId;
    int eventId;
    UserEventEntity event;

    public int[] getEventIndex() {
        return CfgEvent.getEventRepeat(eventId).eventIndex();
    }

    public long nextSession() {

        Long rs = null;
                try {
                  rs =  CfgEvent.getEventRepeat(eventId).nextSeason();
                }catch (Exception ex) {
                    if (rs == null && eventId >= Constans.EVENT_PET && eventId < Constans.EVENT_PET + 11) {
                        rs = CfgEvent.getEventRepeat(Constans.EVENT_PET).nextSeason();
                    }
                }
        return rs;
    }

    public long getValue() {
        if (event == null) {
            return 0;
        }
        if (getEventIndex()[0] == event.getEventIndex()) return event.getValue();
        return 0;
    }

    protected static Logger getLogger() {
        return slib_Logger.root();
    }

    protected void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
}
