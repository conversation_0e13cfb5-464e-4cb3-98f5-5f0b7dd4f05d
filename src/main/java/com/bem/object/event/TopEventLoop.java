package com.bem.object.event;

import com.bem.dao.mapping.UserDataEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.UserEventEntity;
import com.bem.util.Util;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 5/18/2017.
 */
public class TopEventLoop extends AbstractEvent {

    static Map<Integer, TopEventLoop> instance;
    private static Map<Integer, List<UserEventEntity>> mapTopUserLastEvent = new HashMap<>();

    public static synchronized TopEventLoop getInstance(int eventId) {
        if (instance == null) {
            instance = new HashMap<>();
        }
        return instance.get(eventId) != null ? instance.get(eventId) : new TopEventLoop(eventId);
    }

    public static synchronized void setTopUserLastEvent(List<UserEventEntity> user, int eventId) {
        mapTopUserLastEvent.put(eventId, user);
    }

    public static synchronized List<UserEventEntity> getTopUserLastEvent(int eventId) {
        return mapTopUserLastEvent.get(eventId);
    }

    public TopEventLoop(int eventId) {
        this.eventId = eventId;
    }

    public TopEventLoop(long userId, UserDataEntity uData, UserEntity user, int eventId) {
        this.eventId = eventId;
        this.userId = userId;
        dbLoadData(userId, getEventIndex(), uData, user);
    }

    //region Database
    public boolean dbLoadData(long userId, int[] eventIndex, UserDataEntity uData, UserEntity user) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_event where user_id=" + userId + " and event_id=" + eventId
                    + " and (event_index=" + eventIndex[0] + " or event_index=" + eventIndex[1] + ")");
            List<UserEventEntity> aEvent = query.addEntity(UserEventEntity.class).list();
            boolean hasCurIndex = false;
            for (UserEventEntity event : aEvent) {
                if (event.getEventIndex() == eventIndex[0]) {
                    hasCurIndex = true;
                    this.event = event;
                }
                //                else if (event.getEventIndex() == eventIndex[1]) { // event cu
                ////                    if(getTopUserLastEvent(eventId) == null){
                ////                        System.out.println("nulllllllllllllllllllllllllllllll");
                ////                    }
                ////                    if(( getTopUserLastEvent(eventId).get(0).getThisEventIndex() != eventIndex[1])){
                ////                        System.out.println("su kien moi");
                ////                    }
                //                    if (getTopUserLastEvent(eventId) == null || ( getTopUserLastEvent(eventId).get(0).getThisEventIndex() != eventIndex[1])) {//event cu chua trao thuong
                //                        List<UserEventEntity> aUser = Database.getList("user_event", Arrays.asList("event_id", String.valueOf(this.eventId), "event_index", String.valueOf(eventIndex[1])), "order by value desc limit 0, 1", UserEventEntity.class);
                //                        if (aUser != null && aEvent.size() > 0) {
                //                            UserEventEntity topUser = new UserEventEntity();
                //                            topUser.setInfo(0);
                //                            topUser.setUserId(aUser.get(0).getUserId());
                //                            topUser.setValue(aUser.get(0).getValue());
                //                            topUser.setEventIndex(eventIndex[1]);
                //                            List<UserEventEntity> lstTopUser= new ArrayList<>();
                //                            lstTopUser.add(topUser);
                //                            setTopUserLastEvent(lstTopUser,this.eventId);
                //                        }
                //                    }
            }
            if (!hasCurIndex) {// tao curevent
                session.beginTransaction();
                UserEventEntity event = new UserEventEntity(userId, eventId, eventIndex[0]);
                event.setInfo(0);
                event.setServerId(user.getServer());
                //                    uData.getUInt().setValue(UserInt.NUMBER_WIN, 0);
                //                    SQLQuery query1 = session.createSQLQuery("update user_data set data_int=:dataInt where user_id=" + userId);
                //                    query1.setString("dataInt", new Gson().toJson(uData.getUInt().aInt));
                //                    query1.executeUpdate();
                session.save(event);
                this.event = event;
                session.getTransaction().commit();
            }
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }
    //endregion
}
