package com.bem.object.event;

import com.bem.dao.mapping.UserDataEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.UserEventEntity;
import com.bem.util.Util;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.List;

/**
 * Created by vieth_000 on 5/18/2017.
 */
public class TopEvent extends AbstractEvent {

    static TopEvent instance;

    public static synchronized TopEvent getInstance(int eventId) {
        if (instance == null) {
            instance = new TopEvent(eventId);
        }
        return instance;
    }

    public TopEvent(int eventId) {
        this.eventId = eventId;
    }

    public TopEvent(long userId, int eventId, UserDataEntity uData, UserEntity user, int eventIndex) {
        this.eventId = eventId;
        this.userId = userId;
        dbLoadData(userId, uData, user, eventIndex);
    }


    //region Database
    public boolean dbLoadData(long userId, UserDataEntity uData, UserEntity user, int eventIndex) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_event where user_id=" + userId + " and event_id=" + eventId
                    + " and event_index=" + eventIndex);
            List<UserEventEntity> aEvent = query.addEntity(UserEventEntity.class).list();
//            System.out.println(eventId);
            if (aEvent != null && aEvent.size() > 0) {
                this.event = aEvent.get(0);
            } else {
                session.beginTransaction();
                UserEventEntity event = new UserEventEntity(userId, eventId, eventIndex);
                event.setServerId(user.getServer());
                event.setInfo(0);
                session.save(event);
                this.event = event;
                session.getTransaction().commit();
            }
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    //endregion
}
