package com.bem.object.clanbattle;

import com.proto.GGProto;

import java.io.Serializable;

/**
 * Created by vieth_000 on 7/31/2017.
 */
public class PlayerPoint implements Serializable {
    public long id, lastSearch;
    public int point, winStraight, bonusPoint, clanId;
    public String name, clanName;

    public PlayerPoint(long id, String name, int clanId, String clanName) {
        this.id = id;
        this.clanId = clanId;
        this.name = name;
        this.clanName = clanName;
        this.lastSearch = System.currentTimeMillis();
    }

    public GGProto.CommonVector.Builder toProto(int index) {
        GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
        tmp.addANumber(point);
        tmp.addANumber(id);
        tmp.addAString(String.valueOf(index));
        tmp.addAString(name);
        return tmp;
    }
}
