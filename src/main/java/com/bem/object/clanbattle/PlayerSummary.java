package com.bem.object.clanbattle;

import com.proto.GGProto;

import java.io.Serializable;

/**
 * Created by vieth_000 on 7/31/2017.
 */
public class PlayerSummary implements Serializable {
    public long id;
    public int point, numberRoom;
    public String name, clanName;

    public PlayerSummary(long userId, String name) {
        this.id = userId;
        this.name = name;
    }

    public PlayerSummary(long userId, String name, String clanName) {
        this.id = userId;
        this.name = name;
        this.clanName = clanName;
    }

    public void addPoint(int point) {
        this.point += point;
    }

    public GGProto.CommonVector.Builder toProto(int index) {
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        builder.addANumber(id);
        builder.addANumber(point);
        builder.addAString(String.valueOf(index));
        builder.addAString(name);
        builder.addAString(clanName);
        return builder;
    }
}
