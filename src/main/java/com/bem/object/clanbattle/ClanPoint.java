package com.bem.object.clanbattle;

import com.proto.GGProto;

import java.io.Serializable;

/**
 * Created by vieth_000 on 7/31/2017.
 */
public class ClanPoint implements Serializable {
    public int id, point;
    public String name;

    public ClanPoint(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public GGProto.CommonVector.Builder toProto(int index) {
        GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
        tmp.addANumber(point);
        tmp.addANumber(id);
        tmp.addAString(String.valueOf(index));
        tmp.addAString(name);
        return tmp;
    }
}
