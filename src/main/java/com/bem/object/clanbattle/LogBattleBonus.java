package com.bem.object.clanbattle;

import com.bem.config.GameCfgClanWar;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 7/31/2017.
 */
public class LogBattleBonus {
    public List<Integer> aId = new ArrayList<>();
    public List<String> aName = new ArrayList<>();
    public List<Integer> topClan = new ArrayList<>();
    public List<List<Long>> topPlayer = new ArrayList<>();
    public List<Long> topMap = new ArrayList<>();
    public List<Long> topEasyMap = new ArrayList<>();

    public void setTopMap(List<PlayerSummary> aPlayer) {
        topMap.clear();
        for (int i = 0; i < GameCfgClanWar.config.topMapPlayerBonus.size(); i++) {
            if (aPlayer.size() > i && aPlayer.get(i).point > 0) {
                topMap.add(aPlayer.get(i).id);
            }
        }
    }

    public void setTopEasyMap(List<PlayerSummary> aPlayer) {
        topEasyMap.clear();
        for (int i = 0; i < GameCfgClanWar.config.topEasyMapPlayerBonus.size(); i++) {
            if (aPlayer.size() > i && aPlayer.get(i).point > 0) {
                topEasyMap.add(aPlayer.get(i).id);
            }
        }
    }
}
