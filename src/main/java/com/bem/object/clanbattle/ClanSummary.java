package com.bem.object.clanbattle;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by vieth_000 on 7/31/2017.
 */
public class ClanSummary implements Serializable {
    public int id;
    public Map<Long, PlayerSummary> mPlayer = new HashMap<>();

    public void playerJoinNewRoom(long userId, String name) {
        if (!mPlayer.containsKey(userId)) mPlayer.put(userId, new PlayerSummary(userId, name));
        mPlayer.get(userId).numberRoom++;
    }

    public void addPoint(long userId, int point) {
        if (mPlayer.containsKey(userId)) mPlayer.get(userId).point += point;
    }
}
