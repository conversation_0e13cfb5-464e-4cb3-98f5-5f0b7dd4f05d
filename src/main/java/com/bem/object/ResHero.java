package com.bem.object;

import com.bem.config.lang.Lang;
import lombok.Data;

/**
 * Created by vieth_000 on 2/3/2017.
 */
@Data
public class ResHero {
    int id;
    String name, content;
    int boom_min, boom_max, length_min, length_max, speed_min, speed_max;
    int hp, atk, shp, satk;
    int price, price_type, avatarId;
    public Skill skill;

    public String getName() {
        return Lang.getClientLanguage(name);
    }

    public void init() {
        //        skillObj = new Gson().fromJson(skill, Skill.class);
    }
}
