package com.bem.object;

import com.cache.MCache;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by vieth_000 on 4/7/2017.
 */
public class UserMCache implements Serializable {

    Map<String, Object> mCache = new HashMap<String, Object>();
    long userId;

    public UserMCache(long userId) {
        this.userId = userId;
    }

    public Object get(String key) {
        Object tmp = mCache.get(key);
        if (tmp == null) {
            tmp = MCache.getInstance().get(key);
            if (tmp != null) {
                mCache.put(key, tmp);
            }
        }
        return tmp;
    }

    public void set(String key, Object obj) {
        mCache.put(key, obj);
        MCache.getInstance().set(key, obj, MCache.EXPIRE_1H);
    }

    public void set(String key, Object obj, int expire) {
        mCache.put(key, obj);
        MCache.getInstance().set(key, obj, expire);
    }
}
