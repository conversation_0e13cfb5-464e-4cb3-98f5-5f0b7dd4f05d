package com.bem.object;

import com.bem.boom.Resources;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgShop;
import com.bem.monitor.Bonus;
import com.k2tek.Constans;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 2/3/2017.
 */
@Data
public class ResPetFood {
    int id, exp, rank;
    String name;

    public static List<List<Integer>> get2PetFood(Map<String, CfgShop.ShopSpecial> mShopSpecial, int type, int priceType) {
        List<List<Integer>> aInt = new ArrayList<>();
        List<Integer> aId = new ArrayList<>();
        while (aInt.size() < 2) {
            ResPetFood petFood = Resources.getRandomPetFood();
            CfgShop.ShopSpecial shop = mShopSpecial.get(type + "_" + petFood.rank);
            if (priceType == Constans.PRICE_NONE) {
                if ((shop.Gold != -1 || shop.Gem != -1) && !aId.contains(petFood.id)) {
                    int[] price = mShopSpecial.get(type + "_" + petFood.rank).getPriceThanbi();
                    aInt.add(Arrays.asList(Bonus.PET_FOOD, petFood.id, price[0], price[1], 1));
                    aId.add(petFood.id);
                }
            } else if (priceType == Constans.PRICE_MEDAL && shop.Medal != -1 && !aId.contains(petFood.id)) {
                aInt.add(Arrays.asList(Bonus.PET_FOOD, petFood.id, Constans.PRICE_MEDAL, shop.Medal, 1));
                aId.add(petFood.id);
            } else if (priceType == Constans.PRICE_MEDAL_BOSS && shop.Boss != -1 && !aId.contains(petFood.id)) {
                aInt.add(Arrays.asList(Bonus.PET_FOOD, petFood.id, Constans.PRICE_MEDAL_BOSS, shop.Boss, 1));
                aId.add(petFood.id);
            }
        }
        return aInt;
    }
}
