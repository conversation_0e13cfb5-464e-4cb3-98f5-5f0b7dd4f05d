package com.bem.object;

import com.bem.dao.mapping.UserEventEntity;
import com.bem.object.event.AbstractEvent;
import com.bem.object.event.TopTrophy;
import com.k2tek.Constans;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by vieth_000 on 5/18/2017.
 */
public class UserEventMonitor {
    Map<Integer, AbstractEvent> mEvent = new HashMap<>();

    public void add(int id, AbstractEvent event) {
        mEvent.put(id, event);
    }
//    public void remove(int id) {
//        mEvent.remove(id);
//    }

    public boolean isOk() {
        Iterator<AbstractEvent> iter = mEvent.values().iterator();
        while (iter.hasNext()) if (iter.next().getEvent() == null) return false;
        return true;
    }

    public AbstractEvent getEvent(int id) {
        return mEvent.get(id);
    }

}
