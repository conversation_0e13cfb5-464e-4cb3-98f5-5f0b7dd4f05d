package com.bem.object;

import org.slf4j.Logger;

import com.bem.util.Util;
import com.k2tek.common.slib_Logger;

public abstract class TaskSchedule implements Runnable {

	protected long SLEEP_TIME = 3000;
	int gap;
	boolean start = false;

	// protected JobSchedule timeCounter;

	public TaskSchedule(long jobInterval) {
		this.SLEEP_TIME = jobInterval;
		gap = (int) SLEEP_TIME / 3000;
	}

	public void run() {
		start = true;
		try {
			doJob();
		} catch (Exception ex) {
			getLogger().info(Util.exToString(ex));
		}
		start = false;
	}

	public boolean isStart() {
		return start;
	}

	protected abstract void doJob();

	public int getGap() {
		return gap;
	}

	public long getSleepTime() {
		return SLEEP_TIME;
	}

	protected Logger getLogger() {
		return slib_Logger.root();
	}
}
