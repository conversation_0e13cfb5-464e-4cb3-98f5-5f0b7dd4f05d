package com.bem.object;

import com.cache.JCache;
import com.cache.MCache;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by vieth_000 on 4/7/2017.
 */
public class UserJCache implements Serializable {

    Map<String, String> mCache = new HashMap<>();
    long userId;

    public UserJCache(long userId) {
        this.userId = userId;
    }

    public String get(String key) {
        String tmp = mCache.get(key);
        if (tmp == null) {
            tmp = JCache.getInstance().getValue(key);
            if (tmp != null) {
                mCache.put(key, tmp);
            }
        }
        return tmp;
    }

    public void set(String key, String obj) {
        mCache.put(key, obj);
        JCache.getInstance().setValue(key, obj, MCache.EXPIRE_1H);
    }

    public void set(String key, String obj, int expire) {
        mCache.put(key, obj);
        JCache.getInstance().setValue(key, obj, expire);
    }

    public Integer getInt(String key) {
        String value = get(key);
        if (value == null) return null;
        return Integer.parseInt(value);
    }
}
