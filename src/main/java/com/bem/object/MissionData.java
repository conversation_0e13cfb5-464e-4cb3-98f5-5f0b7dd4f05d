package com.bem.object;

import lombok.Data;
import lombok.NonNull;

/**
 * Created by <PERSON><PERSON> on 12/11/2014.
 */
@Data
public class MissionData {
    public static int ONGOING = 0, COMPLETED = 1;
    public static String DAILY = "DAILY", UNIQUE = "UNIQUE";

    @NonNull
    String type, modified;
    @NonNull
    int number, status;

    public void addNumber() {
        number++;
    }

    public void addNumber(int value) {
        number += value;
    }

	public static int getONGOING() {
		return ONGOING;
	}

	public static void setONGOING(int oNGOING) {
		ONGOING = oNGOING;
	}

	public static int getCOMPLETED() {
		return COMPLETED;
	}

	public static void setCOMPLETED(int cOMPLETED) {
		COMPLETED = cOMPLETED;
	}

	public static String getDAILY() {
		return DAILY;
	}

	public static void setDAILY(String dAILY) {
		DAILY = dAILY;
	}

	public static String getUNIQUE() {
		return UNIQUE;
	}

	public static void setUNIQUE(String uNIQUE) {
		UNIQUE = uNIQUE;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getModified() {
		return modified;
	}

	public void setModified(String modified) {
		this.modified = modified;
	}

	public int getNumber() {
		return number;
	}

	public void setNumber(int number) {
		this.number = number;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

}
