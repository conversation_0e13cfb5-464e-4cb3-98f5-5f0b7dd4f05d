package com.bem.object;

import com.bem.boom.Resources;
import com.bem.config.CfgShop;
import com.bem.config.lang.Lang;
import com.bem.monitor.Bonus;
import com.k2tek.Constans;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 2/3/2017.
 */
@Data
public class ResPet {
    public int id, hp, sHp, atk, sAtk, rank;
    public String name;
    public List<Skill> skills;
    public int lock;
    public List<Integer> food, evolutionAvatar, evolutionStat, evolutionMega, evolutionStar;

    public String getName() {
        return Lang.getClientLanguage(name);
    }

    public static List<List<Integer>> get2PetFragment(Map<String, CfgShop.ShopSpecial> mShopSpecial, int type, int priceType) {
        List<List<Integer>> aInt = new ArrayList<>();
        List<Integer> aId = new ArrayList<>();
        while (aInt.size() < 2) {
            ResPet pet = Resources.getRandomPet();
            CfgShop.ShopSpecial shop = mShopSpecial.get(type + "_" + pet.rank);
            if (priceType == Constans.PRICE_NONE) {
                if ((shop.Gold != -1 || shop.Gem != -1) && !aId.contains(pet.id)) {
                    int[] price = mShopSpecial.get(type + "_" + pet.rank).getPriceThanbi();
                    aInt.add(Arrays.asList(Bonus.PET_FRAGMENT, pet.id, price[0], price[1], 10));
                    aId.add(pet.id);
                }
            } else if (priceType == Constans.PRICE_MEDAL && shop.Medal != -1 && !aId.contains(pet.id)) {
                aInt.add(Arrays.asList(Bonus.PET_FRAGMENT, pet.id, Constans.PRICE_MEDAL, shop.Medal, 10));
                aId.add(pet.id);
            } else if (priceType == Constans.PRICE_MEDAL_BOSS && shop.Boss != -1 && !aId.contains(pet.id)) {
                aInt.add(Arrays.asList(Bonus.PET_FRAGMENT, pet.id, Constans.PRICE_MEDAL_BOSS, shop.Boss, 10));
                aId.add(pet.id);
            }
        }
        return aInt;
    }

    public void init() {
        if (skills == null) {
            skills = new ArrayList<Skill>();
        }
    }
}
