package com.bem.object;

import com.proto.GGProto;
import lombok.Data;

/**
 * Created by vieth_000 on 2/8/2017.
 */
@Data
public class ResShopItem {
    //
    public static final int EXP_X2_10 = 1;
    public static final int EXP_X2_100 = 2;
    public static final int GOLD_CHEST = 3;
    public static final int GOLD_KEY = 4;
    public static final int LUCKY_AMULET = 5;
    public static final int ATOMIC_BAG = 6;
    public static final int EGG = 7;
    public static final int ENERGY_FOR_PLAYER = 8;
    public static final int CHEST_MATERIAL_LVL1 = 9;
    public static final int CHEST_MATERIAL_LVL2 = 10;
    public static final int CHEST_MATERIAL_LVL3 = 11;
    public static final int CHEST_AVATAR_LVL1 = 12;
    public static final int CHEST_AVATAR_LVL2 = 13;
    public static final int CHEST_AVATAR_LVL3 = 14;
    public static final int ATOM_BOMB = 15;
    public static final int SPEAKER = 16;
    public static final int CHANGE_NAME = 17;
    public static final int REVIVE = 18;
    public static final int CHANGE_BOMB = 19;
    public static final int CHANGE_ACCESSORY = 20;

    public static final int RANDOM_BOM = 21;
    public static final int RANDOM_PETFOOD = 22;
    public static final int RANDOM_ACCESSORY = 23;
    public static final int RANDOM_STONE = 24;

    public static final int CHOSE_CLOTHING = 25;
    public static final int CHOSE_BOOM = 26;
    public static final int CHOSE_XBOX = 27;
    public static final int GEM_PET_UPGRADE = 28;
    public static final int CHOSE_MARY_BOX = 37;
    //    public static final int NHAN_VANG = 29;
//    public static final int NHAN_KIM_CUONG = 30;
    public static final int IPHONE = 31;
    public static final int IPAB = 32;
    public static final int MAC = 33;
    public static final int NUOC_HOA = 34;
    public static final int HOA = 35;

    public static final int BATTLE_ITEM_MERMAID = 38;
    public static final int BATTLE_ITEM_SHIELD = 39;
    public static final int BATTLE_ITEM_JUMP_SHOES = 40;
    public static final int BATTLE_ITEM_FLASH_SHOES = 41;
    public static final int BATTLE_ITEM_FREEZE = 42;
    public static final int BATTLE_ITEM_MAGNIFY = 43;
    public static final int BATTLE_ITEM_REMOVE_BOMB = 44;
    public static final int BATTLE_ITEM_RADA = 45;
    public static final int BATTLE_ITEM_INVISIBLE = 46;
    public static final int BATTLE_ITEM_CURE = 47;

    public static final int PHAO_HOA = 109;
    public static final int KEO = 108;
    public static final int HOA_HONG = 101;

    // item ghep do
    public static final int BOTMY = 110;
    public static final int BANH_EGG = 111;
    public static final int HAT_SEN = 112;
    public static final int SUGAR = 113;
    public static final int DAU_XANH = 114;
    public static final int MUT = 115;
    public static final int GIAY_THUONG = 116;
    public static final int GIAY_THUONG_HANG = 117;
    public static final int BANH_THAP_CAM = 118;
    public static final int BANH_DEO = 119;
    public static final int BANH_DAU_XANH = 120;
    public static final int BANH_PIA = 121;
    public static final int HOP_BANH_THUONG = 122;
    public static final int HOP_BANH_THUONG_HANG = 123;


    int id, pricetype, price;
    String name, note;

    public void proto(GGProto.CommonVector.Builder builder) {
        builder.addANumber(id);
        builder.addANumber(pricetype);
        builder.addANumber(price);
        builder.addAString(name);
        builder.addAString(note);
    }
}
