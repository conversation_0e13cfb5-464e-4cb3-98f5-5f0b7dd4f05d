package com.bem.object;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.Database2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 2/22/2017.
 */
public class UserInt {
    public static final int NUMBER_VALUE = 20;
    //
    public static final int NUMBER_EXCHANGE_GOLD = 0;
    public static final int NUMBER_EXCHANGE_ENERGY = 1;
    public static final int NUMBER_EXCHANGE_ATOMIC = 2;
    public static final int NUMBER_FRIENDS = 3;
    public static final int HERO_EXP_X2 = 4;
    public static final int CURRENT_DATE = 5;
    public static final int NUMBER_EXCHANGE_GAME_GOLD = 6;
    public static final int NUMBER_ATK_GAME_GOLD = 7;
    public static final int NUMBER_EXCHANGE_GAME_MATERIAL = 8;
    public static final int NUMBER_ATK_GAME_MATERIAL = 9;
    public static final int NUMBER_EXCHANGE_GAME_BOSS = 10;
    public static final int NUMBER_ATK_GAME_BOSS = 11;
    public static final int NUMBER_RESET_MAP_MAO_HIEM = 12;
    public static final int NUMBER_WIN = 13;
    //
    public static final int BATTLE_ITEM_1 = 14;
    public static final int BATTLE_ITEM_2 = 15;

    //
    public static final int TYPE_SET = 0;
    public static final int TYPE_ADD = 1;
    //
    public List<Integer> aInt;

    public UserInt(String dataInt) {
        aInt = new Gson().fromJson(dataInt, new TypeToken<ArrayList<Integer>>() {
        }.getType());
        while (aInt.size() < NUMBER_VALUE) {
            aInt.add(0);
        }
    }

    public void addValue(int index, int type, int value) {
        if (type == TYPE_SET) { // set
            aInt.set(index, value);
        } else { // add
            aInt.set(index, aInt.get(index) + value);
        }
    }

    public int getValue(int index) {
        return aInt.get(index);
    }

    public void setValue(int index, int value) {
        aInt.set(index, value);
    }

    public String toString() {
        return new Gson().toJson(this);
    }

    public boolean update(long userId) {
        return Database2.update("user_data", Arrays.asList("data_int", aInt.toString()), Arrays.asList("user_id", String.valueOf(userId)));
    }

}
