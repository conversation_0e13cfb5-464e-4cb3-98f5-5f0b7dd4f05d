package com.bem.object;

import com.google.gson.Gson;
import grep.database.Database2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 2/22/2017.
 */
public class MissionInviteFb {
    //
    public int receive;
    public List<String> invites;

    public MissionInviteFb() {
        invites = new ArrayList<>();
    }

    public boolean update(long userId) {
        return Database2.update("user_data", Arrays.asList("mission_invite_fb", new Gson().toJson(this)), Arrays.asList("user_id", String.valueOf(userId)));
    }

}
