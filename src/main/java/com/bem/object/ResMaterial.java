package com.bem.object;

import com.bem.boom.Resources;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgShop;
import com.bem.monitor.Bonus;
import com.k2tek.Constans;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by vieth_000 on 2/3/2017.
 */
@Data
public class ResMaterial {
    int id;
    String name;
    int point, atomic, rank, type, price, lock = 0;

    public static List<List<Integer>> get2Material(Map<String, CfgShop.ShopSpecial> mShopSpecial, int type, int priceType) {
        List<List<Integer>> aInt = new ArrayList<>();
        List<Integer> aId = new ArrayList<>();
        while (aInt.size() < 2) {
            ResMaterial material = Resources.getRandomMaterial();
            CfgShop.ShopSpecial shop = mShopSpecial.get(type + "_" + material.rank);
            if (priceType == Constans.PRICE_NONE) {
                if ((shop.Gold != -1 || shop.Gem != -1) && !aId.contains(material.id)) {
                    int[] price = mShopSpecial.get(type + "_" + material.rank).getPriceThanbi();
                    aInt.add(Arrays.asList(Bonus.MATERIAL, material.id, price[0], price[1], 1));
                    aId.add(material.id);
                }
            } else if (priceType == Constans.PRICE_MEDAL && shop.Medal != -1 && !aId.contains(material.id)) {
                aInt.add(Arrays.asList(Bonus.MATERIAL, material.id, Constans.PRICE_MEDAL, shop.Medal, 1));
                aId.add(material.id);
            } else if (priceType == Constans.PRICE_MEDAL_BOSS && shop.Boss != -1 && !aId.contains(material.id)) {
                aInt.add(Arrays.asList(Bonus.MATERIAL, material.id, Constans.PRICE_MEDAL_BOSS, shop.Boss, 1));
                aId.add(material.id);
            }
        }
        return aInt;
    }
}
