/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.object;

import com.bem.boom.Resources;
import com.bem.boom.object.InviteBattle;
import com.bem.boom.object.MyAvatar;
import com.bem.boom.object.Point;
import com.bem.config.*;
import com.bem.config.lang.Lang;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.*;
import com.bem.matcher.TeamObject;
import com.bem.object.event.TopEvent;
import com.bem.object.event.TopEventLoop;
import com.bem.object.event.TopTrophy;
import com.bem.object.event.TopWin;
import com.bem.util.CommonProto;
import com.handler.AHandler;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.proto.GGProto;
import grep.database.Database2;
import io.netty.channel.Channel;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
@Data
public class UserInfo implements Serializable {

    Lang lang;
    AuthUserEntity authUser;
    UserEntity dbUser;
    UserAction action = new UserAction(this);
    List<InviteBattle> aInvite = new ArrayList<InviteBattle>();
    UserResources res = new UserResources();
    UserMCache mCache;
    UserJCache jCache;
    UserDataEntity uData;
    Point point;
    long lastChat = 0;
    String session;
    String os, version, cp;
    List<List<Long>> syncBonus = new ArrayList<>();
    boolean endgame = false, isVN;
    UserEventMonitor uEvent = new UserEventMonitor();
    TeamObject team;
    long timeLastReceiveSlide = 0;
    int hasArena = -1;

    public UserInfo() {
    }

    public UserInfo(Channel channel, AuthUserEntity authUser, UserEntity dbUser) {
        this.authUser = authUser;
        this.dbUser = dbUser;
    }

    public MyAvatar getMAvatar() {
        return dbUser.getMAvatar();
    }

    public String getUsername() {
        return dbUser.getUsername();
    }

    public long getId() {
        return dbUser.getId();
    }

    public List<Long> syncMoneys(AHandler handler) {
        List<Long> aLong = new ArrayList<Long>();
        aLong.add(getDbUser().getGold());
        aLong.add(getDbUser().getGem());
        UserMoneys moneys = getUData().getUserMoneys();
        aLong.add(moneys.getValue(UserMoneys.MEDAL_BOSS));
        aLong.add(moneys.getValue(UserMoneys.MEDAL));
        aLong.add(moneys.getValue(UserMoneys.CLAN));
        if (handler != null)
            handler.addResponse(IAction.SYNC_MONEYS, CommonProto.getCommonLongVectorProto(aLong, null));
        return aLong;
    }

    public void loadEvent() {
        UserFriendEntity uf = new UserDAO().dbHasFamily(dbUser.getId());
        if (uf != null && uf.getUser1() == dbUser.getId()) {
            try {
//                System.out.println("aaaaaaaaaaaaaaaaaaaaaa");
                uEvent.add(Constans.EVENT_TOP_CAP_DOI, new TopEventLoop(getId(), uData, dbUser, Constans.EVENT_TOP_CAP_DOI));
                dbUser.setDiemcapdoi((int) uEvent.getEvent(Constans.EVENT_TOP_CAP_DOI).getEvent().getValue());
                dbUser.setKeyCapdoi(uEvent.getEvent(Constans.EVENT_TOP_CAP_DOI).getEvent().getUserId());
            } catch (Exception ex) {

            }
        } else {
//            System.out.println("bbbbbbbbbbbbbbbbbbbbb");
            try {
//                UserFriendEntity ufamily = new UserDAO().dbHasFamily(this.getId());
                if (uf != null) {
                    dbUser.setKeyCapdoi(uf.getUser1());
                    dbUser.setDiemcapdoi(new UserDAO().getdiemcapdoi(uf.getUser1(), Constans.EVENT_TOP_CAP_DOI, CfgEvent.getEventRepeat(Constans.EVENT_TOP_CAP_DOI).eventIndex()[0]));
                    System.out.println(new UserDAO().getdiemcapdoi(uf.getUser1(), Constans.EVENT_TOP_CAP_DOI, CfgEvent.getEventRepeat(Constans.EVENT_TOP_CAP_DOI).eventIndex()[0]));

                }
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
        }
        try {
            uEvent.add(Constans.EVENT_ZOMBIE, new TopEventLoop(getId(), uData, dbUser, Constans.EVENT_ZOMBIE));
        } catch (Exception ex) {
        }
//        try {
//            uEvent.add(Constans.EVENT_TOP_CAP_DOI, new TopEventLoop(getId(), uData, dbUser, Constans.EVENT_ZOMBIE));
//        } catch (Exception ex) {
//        }

        int[] arr = {Constans.EVENT_PET};
        int numberPet = 11;
        for (int eventId = arr[0]; eventId < arr[arr.length - 1] + numberPet; eventId++) {
            try {
                int eventIndex = TopEventLoop.getInstance(eventId).getEventIndex()[0];
                uEvent.add(eventId, new TopEventLoop(getId(), uData, dbUser, eventId));
            } catch (Exception ex) {
            }
        }


        int[] arrHero = {Constans.EVENT_CHARACTER_LOOP};
        int numberHero = 6;
        for (int eventId = arrHero[0]; eventId < arrHero[arrHero.length - 1] + numberPet; eventId++) {
            try {
                int eventIndex = TopEventLoop.getInstance(eventId).getEventIndex()[0];
                uEvent.add(eventId, new TopEventLoop(getId(), uData, dbUser, eventId));
            } catch (Exception ex) {
            }
        }

        try {
            if (CfgAchievement.getAchievement(CfgAchievement.CAPDO, this.getDbUser()) != null && CfgAchievement.getAchievement(CfgAchievement.CAPDO, this.getDbUser()).inTime()) {
//                System.out.println("(CfgAchievement.getAchievement(CfgAchievement.CAPDO).getMiliStart() / (1000 * 60))--->"+(CfgAchievement.getAchievement(CfgAchievement.CAPDO).getMiliStart() / (1000 * 60)));
                uEvent.add(Constans.EVENT_TOP_CAPDO, new TopEvent(getId(), Constans.EVENT_TOP_CAPDO, uData, dbUser, (int) (CfgAchievement.getAchievement(CfgAchievement.CAPDO, this.getDbUser()).getMiliStart() / (1000 * 60)) - 25040340));
                dbUser.setTanglv(uEvent.getEvent(Constans.EVENT_TOP_CAPDO).getEvent().getInfo());
            }
        } catch (Exception ex) {
        }

        try {
//            System.out.println("111111111113333");
            if (CfgAchievement.getAchievement(CfgAchievement.SAOVANG, this.getDbUser()) != null && CfgAchievement.getAchievement(CfgAchievement.SAOVANG, this.getDbUser()).inTime()) {
//                System.out.println("(CfgAchievement.getAchievement(CfgAchievement.CAPDO).getMiliStart() / (1000 * 60))--->"+(CfgAchievement.getAchievement(CfgAchievement.CAPDO).getMiliStart() / (1000 * 60)));
                uEvent.add(Constans.EVENT_SAOVANG, new TopEvent(getId(), Constans.EVENT_SAOVANG, uData, dbUser, (int) (CfgAchievement.getAchievement(CfgAchievement.SAOVANG, this.getDbUser()).getMiliStart() / (1000 * 60))));
//                System.out.println("222222222222233333");
//                dbUser.setTanglv(uEvent.getEvent(Constans.EVENT_SAOVANG).getEvent().getInfo());
            } else {
//                System.out.println("232323545654");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        try {
            if (CfgAchievement.getAchievement(CfgAchievement.NAP, this.getDbUser()) != null && CfgAchievement.getAchievement(CfgAchievement.NAP, this.getDbUser()).inTime()) {
                uEvent.add(Constans.EVENT_NAP, new TopEvent(getId(), Constans.EVENT_NAP, uData, dbUser, (int) (CfgAchievement.getAchievement(CfgAchievement.NAP, this.getDbUser()).getMiliStart() / (1000 * 60))));
                dbUser.setGetEventNap(uEvent.getEvent(Constans.EVENT_NAP).getEvent().getInfo());
            }
        } catch (Exception ex) {
        }
        for (int i = 0; i < Resources.aHero.size(); i++) {
            try {
                int eventId = Constans.EVENT_CHARACTERPLUS + Resources.aHero.get(i).getId() + CfgAchievement.CHARACTER;
                int achievmentId = CfgAchievement.CHARACTER + Resources.aHero.get(i).getId();
                if (CfgAchievement.getAchievement(achievmentId, this.getDbUser()) != null && CfgAchievement.getAchievement(achievmentId, this.getDbUser()).inTime()) {
                    uEvent.add(eventId, new TopEvent(getId(), eventId, uData, dbUser, (int) (CfgAchievement.getAchievement(achievmentId, this.getDbUser()).getMiliStart() / (1000 * 60))));
                }
            } catch (Exception ex) {
                continue;
            }
        }
        for (int i = 0; i < Resources.aPet.size(); i++) {
            try {
                int eventId = Constans.EVENT_PETPLUS + Resources.aPet.get(i).getId() + CfgAchievement.EVENT_PET;
                int achievmentId = CfgAchievement.EVENT_PET + Resources.aPet.get(i).getId();
                if (CfgAchievement.getAchievement(achievmentId, this.getDbUser()) != null && CfgAchievement.getAchievement(achievmentId, this.getDbUser()).inTime()) {
                    uEvent.add(eventId, new TopEvent(getId(), eventId, uData, dbUser, (int) (CfgAchievement.getAchievement(achievmentId, this.getDbUser()).getMiliStart() / (1000 * 60))));
                }
            } catch (Exception ex) {
                continue;
            }
        }
        for (int i = 0; i < CfgEvent.config.eventForLitmitUser.size(); i++) {
            if (CfgEvent.config.eventForLitmitUser.get(i).enable == 1 && CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id, this.getDbUser()) != null && CfgEvent.getEventForLimitUserInTime(CfgEvent.config.eventForLitmitUser.get(i).id, this, (int) (CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id, this.getDbUser()).getMiliStart() / (1000 * 60)))) {
                uEvent.add(CfgEvent.config.eventForLitmitUser.get(i).id, new TopEvent(getId(), CfgEvent.config.eventForLitmitUser.get(i).id, uData, dbUser, (int) (CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id, this.getDbUser()).getMiliStart() / (1000 * 60))));
            }
        }
    }

    public boolean loadInventory() {
        res.avatars = Database2.getList("user_avatar", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserAvatarEntity.class);
        res.heroes = Database2.getList("user_hero", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserHeroEntity.class);
        res.items = Database2.getList("user_item", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserItemEntity.class);
        res.bombs = Database2.getList("user_bomb", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserBombEntity.class);
        res.materials = Database2.getList("user_material", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserMaterialEntity.class);
        res.pets = Database2.getList("user_pet", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserPetEntity.class);
        res.petFoods = Database2.getList("user_pet_food", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserPetFoodEntity.class);
        res.icons = Database2.getList("user_icon", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserIconEntity.class);
        uData = (UserDataEntity) Database2.getUnique("user_data", Arrays.asList("user_id", String.valueOf(dbUser.getId())), UserDataEntity.class);
        if (uData == null) {
            UserDataEntity createUserData = new UserDataEntity(dbUser.getId());
            if (Database2.save(createUserData)) uData = createUserData;
        }
        res.accessories = Database2.getList("user_accessories", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserAccessoriesEntity.class);
        res.symbols = Database2.getList("user_symbol", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserSymbolEntity.class);
        res.ufos = Database2.getList("user_ufo", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserUfoEntity.class);
        res.stoneUfos = Database2.getList("user_stone_ufo", Arrays.asList("user_id", String.valueOf(dbUser.getId())), "", UserStoneUfoEntity.class);

        CfgDiemdanh.getMyAttendance(this);
        CfgMapAward.getMyAward(this);
        CfgEnergy.getMyEnergy(this);
        getUData().getInitUInt(dbUser);

        uEvent.add(Constans.EVENT_TOP_TROPHY, new TopTrophy(getId(), getDbUser()));
        dbUser.setTrophy((int) uEvent.getEvent(Constans.EVENT_TOP_TROPHY).getValue());

        uEvent.add(Constans.EVENT_TOP_WIN, new TopWin(getId(), uData, dbUser));
        dbUser.setMaxwin((int) uEvent.getEvent(Constans.EVENT_TOP_WIN).getValue());

//        uEvent.add(Constans.EVENT_TOP_CAPDO, new TopEvent(getId(), Constans.EVENT_TOP_CAPDO, uData, dbUser, 0));
//        dbUser.setTanglv(uEvent.getEvent(Constans.EVENT_TOP_CAPDO).getEvent().getInfo());

        loadEvent();
        mCache = new UserMCache(getId());
        jCache = new UserJCache(getId());
        if (res.isOk() && uData != null && dbUser.getMAvatar() != null && uEvent.isOk()) {
            dbUser.getMAvatar().checkDefaultAvatar(getId(), res);
//            if (dbUser.getMAvatar().getUserAvatar() > 5) dbUser.getMAvatar().setAvatar(MyAvatar.USER_AVATAR, 5);
            point = new Point(this);
            Database2.rawSQL("insert into user_power(user_id, atk, hp, hero, pet) values(" + getId() + "," + point.point[Point.ATK] + "," + point.point[Point.HP] + ", " + getMAvatar().getHero() + "," +
                    getMAvatar().getPet() + ") ON DUPLICATE KEY UPDATE atk=" + point.point[Point.ATK] + ", hp=" + point.point[Point.HP] + ", hero=" + getMAvatar().getHero() + ", pet=" + getMAvatar().getPet());
            return true;
        }
        return false;
    }

    public GGProto.CommonVector protoInfor() {
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        builder.addANumber(dbUser.getGold());
        builder.addANumber(dbUser.getGem());
        builder.addANumber(dbUser.getMedal());
        return builder.build();
    }

    public static final int NUMBER_MATCH_REJECT = 3;
    Map<Long, Integer> lastMatchPlayer = new HashMap<>();

    public void countMatch(List<Long> oppIds) {
        for (Long oppId : oppIds)
            if (!lastMatchPlayer.containsKey(oppId)) lastMatchPlayer.put(oppId, 0);
        Long[] keys = lastMatchPlayer.keySet().toArray(new Long[0]);
        for (Long key : keys) {
            lastMatchPlayer.put(key, lastMatchPlayer.get(key) + 1);
            if (lastMatchPlayer.get(key) > NUMBER_MATCH_REJECT) lastMatchPlayer.remove(key);
        }
    }

    public boolean justMatch(List<Long> oppIds) {
        for (Long oppId : oppIds) {
            Integer number = lastMatchPlayer.get(oppId);
            if (number != null && number <= NUMBER_MATCH_REJECT) return true;
        }
        return false;
    }
}
