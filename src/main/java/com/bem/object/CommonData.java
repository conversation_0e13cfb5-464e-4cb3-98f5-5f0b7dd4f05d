package com.bem.object;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 3/14/2017.
 */
public class CommonData {
    List<String> aStr;
    List<Long> aLong;
    List<Integer> aInt;

    public CommonData() {
        aStr = new ArrayList<String>();
        aLong = new ArrayList<Long>();
        aInt = new ArrayList<Integer>();
    }

    public Integer getInt(int index) {
        return aInt.get(index);
    }

    public String getStr(int index) {
        return aStr.get(index);
    }

    public Long getLong(int index) {
        return aLong.get(index);
    }

    public void addInt(int value) {
        aInt.add(value);
    }

    public void addStr(String value) {
        aStr.add(value);
    }

    public void addLong(long value) {
        aLong.add(value);
    }

    public void addInt(int index, int value) {
        aInt.add(index, value);
    }

    public void addStr(int index, String value) {
        aStr.add(index, value);
    }

    public void addLong(int index, long value) {
        aLong.add(index, value);
    }
}
