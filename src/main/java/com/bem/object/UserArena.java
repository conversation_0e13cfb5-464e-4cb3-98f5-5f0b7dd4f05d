package com.bem.object;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by vieth_000 on 7/12/2017.
 */
@Data
public class UserArena implements Serializable {
    public UserArena() {
        status = -1;
//        usersKnockout = new ArrayList<>();
    }

    public UserArena(UserArena u) {
        status = u.status;
        userId = u.userId;
        point = u.point;
        roundKnockout = u.roundKnockout;
        name = u.name;
        username = u.username;
        avatar = u.avatar;
        rank = u.rank;
        level= u.level;

//        usersKnockout = new ArrayList<>();
    }

    long userId;
    int point = 0, status, roundKnockout, rank, level;
    String name, username;
    List<Integer> avatar;
    //        Channel channel;
}
