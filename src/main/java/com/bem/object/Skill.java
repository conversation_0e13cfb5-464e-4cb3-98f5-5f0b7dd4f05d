package com.bem.object;

import com.bem.boom.unit.Item;

import java.io.Serializable;
import java.util.List;

/**
 * Created by vieth_000 on 1/3/2017.
 */
public class Skill implements Serializable {
    public String desc;
    public int time_recover, level, effect;
    public List<SkillDetail> data;

    public class SkillDetail implements Serializable {
        public int id, target, duration;
        public int[] value;
    }

    public boolean canRecovery() {
        for (SkillDetail detail : data) {
            if (detail.id == Item.ITEM_RECOVERY)
                return false;
        }
        return true;
    }
}
