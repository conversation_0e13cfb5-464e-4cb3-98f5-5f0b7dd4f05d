package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.IMath;
import com.bem.boom.PlayerStatus;
import com.bem.boom.unit.Player;
import com.bem.matcher.TeamObject;
import com.bem.object.UserInfo;
import com.k2tek.IAction;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Nguoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableTutorial extends AbstractTable {

    public TableTutorial(List<TeamObject> aTeam, int type, int mapId) {
        super(aTeam, type, mapId);
        initMap();
    }

    public TableTutorial(CacheBattle cacheBattle) {
    }

    long winnerId;

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
    }

//    public TableBoom(List<List<TeamObject>> lstaTeam, int type) {
//        super();
//        gameState = STATE_JOIN_PLAYER;
//        timer(ID_CREATE_MAP);
//        //        initPlayer(aTeam);
//        // schedule_server_update();
//        // create_physics_simulation();
//    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    protected void initPlayer() {
        aPlayer = new ArrayList<Player>();
        int posIndex = 0;
        for (int i = 0; i < aTeam.size(); i++) {
            List<UserInfo> aUser = aTeam.get(i).aUser;
            for (UserInfo user : aUser) {
                Player player = new Player(user, mode == TeamObject.BOSS ? 0 : posIndex, map.getNextUnitId());
                player.table = this;
                player.map = map;
                player.mPos = map.getStartPos().get(posIndex);
                player.pos = IMath.getUnit(player.mPos);

                aPlayer.add(player);
//                if (user.getChannel() != null) {
//                    ChUtil.set(user.getChannel(), KEY_TABLE, this);
//                    ChUtil.set(user.getChannel(), KEY_PLAYER, player);
//                }
                posIndex++;
            }
        }
        Player userPlayer = aPlayer.get(0);
    }

    void endGame() {
        isPlay = false;
        int teamWin = -1;
        for (Player player : aPlayer) {
            if (player.isAlive()) {
                if (teamWin == -1) {
                    teamWin = player.user.team;
                } else if (teamWin != player.user.team) {
                    teamWin = -1;
                    break;
                }
            }
        }
        //
        ProtoEndgame.Builder builder = ProtoEndgame.newBuilder();
        builder.setType(mode);
        for (Player player : aPlayer) {
            if (player.channel == null) continue;
            player.aStatus = new ArrayList<PlayerStatus>();
            player.mStatus = new HashMap<Integer, PlayerStatus>();
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
//            if (type == TeamObject.SOLO && teamWin != -1) {
//                int exp = player.isAlive() ? 20 : 2;
//                player.dbUser.addExp(exp);
//                playerBuilder.setExp(exp);
//            }
//            playerBuilder.setGold(player.bonusGold);
//            GGProto.ProtoUser.Builder user = CommonProto.protoUser(player.dbUser).toBuilder();
//            playerBuilder.setUser(user);
//            builder.addAResult(playerBuilder);
//            //
//            ChUtil.set(player.channel, "table", null);
//            ChUtil.set(player.channel, Constans.KEY_USER_TEAM, null);
        }
        builder.setTeamWin(teamWin);
        sendAllPlayer(IAction.END_GAME, builder.build());
        cancelTask();
    }

    public void checkEndGame() {
        int numberAlive = 0;
        for (Player player : aPlayer) {
            if (player.isAlive()) numberAlive++;
        }
        if (numberAlive <= 1) {
            gameState = STATE_END_GAME;
        }
//        else if (server_time > timeTable) {
//            gameState = STATE_END_GAME;
//        }
    }

    void doAction() {

    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableTutorial(cacheBattle);
    }
}
