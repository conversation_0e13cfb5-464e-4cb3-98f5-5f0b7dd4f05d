package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.Resources;
import com.bem.boom.SquareUnit;
import com.bem.boom.monster.IMonster;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.config.GameCfgHunting;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.ResShopItem;
import com.bem.util.CommonProto;
import com.k2tek.Constans;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;

import java.util.List;

/**
 * Nguoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableGoldHunting extends AbstractTable {
    static final int STATE_ALERT_MONSTER = 0;
    static final int STATE_ADD_MONSTER = 1;
    static final int STATE_PLAY = 2;
    static final int STATE_COLLECT_GOLD = 3;

    GameCfgHunting.Map mapHunting;
    int level = 1, state = 0, bonusGold = 0, oldPlayerGold, totalCollectGold = 0;
    float timeAddMonster = 0;
    List<Integer> aMonsterId;
    List<SquareUnit> aSquare;

    public TableGoldHunting() {

    }

    public TableGoldHunting(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mode = TeamObject.GAME_HUNTING;
        delayStartGame = 1;
        mapHunting = GameCfgHunting.getMap();
        mapId = mapHunting.id;
        state = STATE_PLAY;
        timeAddMonster = 3;
        initMap();
    }

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
//        TeamObject team = (TeamObject) ChUtil.get(channel, Constans.KEY_USER_TEAM);
//        if (team != null) {
//            for (int i = 0; i < team.aUser.size(); i++) {
//                if (team.aUser.get(i).getId() == player.user.id) {
//                    team.removeUser(team.aUser.get(i).getId());
//                }
//            }
//        }
//        ChUtil.remove(channel, Constans.KEY_USER_TEAM);
        player.channel = channel;
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
        isPlay = false;
        //
        protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(teamWin);
        protoEndgame.setTimeCreated(System.currentTimeMillis());
        //
        boolean quickResult = false;
        for (Player player : aPlayer) {
            resetPlayer(player);
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
            if (totalCollectGold > 0) {
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.GOLD, totalCollectGold));
            }
            if (player.user.reviveItem == 2)
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));
            playerBuilder.setUser(CommonProto.protoUser(player.user).toBuilder());
            playerBuilder.setTeam(player.user.team);
            playerBuilder.setStatus(player.leaveStatus);
            protoEndgame.addAResult(playerBuilder);
            if (player.leaveStatus == Constans.PLAYER_STATUS_LEAVE) quickResult = true;
        }
        protoEndgame.setGameId(Integer.parseInt(id));
        super.endGame();
    }

    public void checkEndGame() {
        boolean isEndgame = true;
        for (Player player : aPlayer) {
            if (player.isAlive()) {
                isEndgame = false;
            }
        }
        if (isEndgame || server_time >= 180) {
            gameState = STATE_END_GAME;
            teamWin = 10;
        } else if (state == STATE_PLAY && numberMonsterDie == aMonster.size()) {
            timeAddMonster = server_time + 1;
            if (level < 6) {
                state = STATE_ALERT_MONSTER;
            } else {
                state = STATE_COLLECT_GOLD;
                timeAddMonster = server_time + 10;
            }
            // add item here
            int numberChest = level > 1 ? level - 1 : 0;
            int[] itemIds = level >= 6 ? new int[0] : GameCfgHunting.getSupportItem(level);
            List<SquareUnit> aSquare = map.getRandomSquare(map.getSquareEmpty(), itemIds.length + numberChest);
            bonusGold = 0;
            oldPlayerGold = aPlayer.get(0).bonusGold;
            for (int i = 0; i < aSquare.size(); i++) {
                Item tmpItem = null;
                if (i < numberChest) { // add gold
                    tmpItem = new Item(map.getNextUnitId(), Item.UNKNOWN_GOLD);
                    tmpItem.value = GameCfgHunting.getGold(cacheBattle.getLevelIndex());
                    bonusGold += tmpItem.value;
                    totalCollectGold += tmpItem.value;
                } else {
                    tmpItem = new Item(map.getNextUnitId(), itemIds[i - numberChest]);
                }
                if (tmpItem != null) {
                    aProtoAdd.add(tmpItem.protoAdd(aSquare.get(i).pos));
//                    aProtoJumpPos.add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, tmpItem.getId(), aSquare.get(i).pos));
                    aSquare.get(i).aItem.add(tmpItem);
                }
            }
        } else if (state == STATE_ALERT_MONSTER && server_time >= timeAddMonster) {
            timeAddMonster = server_time + 2;
            state = STATE_ADD_MONSTER;
            // add monster here
            aMonsterId = mapHunting.getMonsterId(level);
            aSquare = map.getRandomSquare(map.getSquareEmpty(), aMonsterId.size());
            for (int i = 0; i < aSquare.size(); i++) {
                getAProtoAdd().add(CommonProto.protoAddAlertDanger(aSquare.get(i).pos));
            }
        } else if (state == STATE_ADD_MONSTER && server_time >= timeAddMonster) {
            state = STATE_PLAY;
            int hp = GameCfgHunting.getHp(cacheBattle.getLevelIndex(), level), atk = GameCfgHunting.getAtk(cacheBattle.getLevelIndex(), level);
            for (int i = 0; i < aSquare.size(); i++) {
                IMonster mU = Resources.mMonster.get(aMonsterId.get(i)).clone(map.getNextUnitId(), aMonsterId.get(i), aSquare.get(i).mPos.clone(), new float[]{hp, atk});
                mU.map = map;
                aMonster.add(mU);
                aProtoAdd.add(mU.protoAdd());
                aSquare.get(i).aMonster.add(mU);
            }
            level++;
        } else if (state == STATE_COLLECT_GOLD) {
            if (server_time >= timeAddMonster || aPlayer.get(0).bonusGold - oldPlayerGold == bonusGold) {
                gameState = STATE_END_GAME;
                teamWin = 0;
            }
        }
    }

    void doAction() {
    }

    @Override
    void initMap() {
        super.initMap();
    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableGoldHunting(cacheBattle);
    }
}
