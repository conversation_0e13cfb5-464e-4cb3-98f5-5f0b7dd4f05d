package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.Resources;
import com.bem.boom.effect.EffectDropBox;
import com.bem.boom.effect.EffectSuddenItem;
import com.bem.boom.map.MapResource;
import com.bem.boom.unit.Player;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgBonusPvp;
import com.bem.config.GameCfgArena;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.ResShopItem;
import com.bem.util.CommonProto;
import com.handler.game.Arena;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

/**
 * <PERSON>uoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableArena extends AbstractTable {
    int round =-1;
    public TableArena() {

    }
    long userleave =-1;
    public TableArena(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mapId = MapResource.getSoloMap();
        this.mode = TeamObject.ARENA;
        delayStartGame = 5;
//        System.out.println("round-->"+round);
        round = cacheBattle.getLevelIndex();
//        System.out.println("round--------------->"+round);
        initMap();
    }

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
        if (channel == null) return;
        ProtoEndgame.Builder protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(10);
        userleave=player.user.id;
        sendPlayer(channel, IAction.LEAVE_TABLE_1, protoEndgame.build());
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
        isPlay = false;
        protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(teamWin);
        protoEndgame.setTimeCreated(System.currentTimeMillis());

//        boolean quickResult = false;
        for (Player player : aPlayer) {
//            System.out.println("player.totalDamage = " + player.totalDamage);
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
            if(player.user.team==teamWin) {
//                System.out.println("win----");
                playerBuilder.addExBonus(1);//add point
                playerBuilder.addAllBonus(GameCfgArena.config.getBonus().get(round+1).getWin());
            }else {
//                System.out.println("lose----");
                playerBuilder.addAllBonus(GameCfgArena.config.getBonus().get(round+1).getLose());
                if(player.user.id!=userleave) {
                    playerBuilder.addExBonus(0);//add point
//                    System.out.println("00000000000");
                }else{
//                    System.out.println("-1111111111111111");
                    playerBuilder.addExBonus(-1);//add point
                }

            }

//            playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.MEDAL_BOSS, 1));
            playerBuilder.setUser(CommonProto.protoUser(player.user).toBuilder());
            playerBuilder.setTeam(player.user.team);
            playerBuilder.setStatus(player.leaveStatus);
            if (player.user.reviveItem == 2)
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));

            protoEndgame.addAResult(playerBuilder);
//            if (player.leaveStatus == Constans.PLAYER_STATUS_LEAVE) quickResult = true;
        }
        super.endGame();
    }

    public void checkEndGame() {
        int teamAlive = -1;
        for (Player player : aPlayer) {
            if (player.isAlive()) {
                if (teamAlive == -1) {
                    teamAlive = player.user.team;
                } else if (teamAlive != player.user.team) {
                    return;
                }
            }
        }
        teamWin = teamAlive;
        gameState = STATE_END_GAME;
    }


    void doAction() {

    }

    @Override
    void initMap() {
        super.initMap();
        aEffect.add(new EffectDropBox(this, map));
        aEffect.add(new EffectSuddenItem(this, map));
//        System.out.println("init arena");
    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableArena(cacheBattle);
    }
}
