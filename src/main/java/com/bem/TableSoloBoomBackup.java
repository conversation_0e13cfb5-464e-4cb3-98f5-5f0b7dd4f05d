package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.unit.Player;
import com.bem.matcher.TeamObject;
import io.netty.channel.Channel;

import java.util.List;

/**
 * Nguoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableSoloBoomBackup extends AbstractTable {

    public TableSoloBoomBackup(List<TeamObject> aTeam, int type, int mapId) {
        super(aTeam, type, mapId);
        if (type == TeamObject.TRAIN) {
            initMap();
        } else {
            timer(ID_CREATE_MAP);
        }
    }

    public TableSoloBoomBackup(CacheBattle cacheBattle) {}

    long winnerId;

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
    }

//    public TableBoom(List<List<TeamObject>> lstaTeam, int type) {
//        super();
//        gameState = STATE_JOIN_PLAYER;
//        timer(ID_CREATE_MAP);
//        //        initPlayer(aTeam);
//        // schedule_server_update();
//        // create_physics_simulation();
//    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
//        isPlay = false;
//        debug("endgame solo | train ket thuc");
//        //
//        protoEndgame = ProtoEndgame.newBuilder();
//        protoEndgame.setType(type);
//        protoEndgame.setTimeCreated(System.currentTimeMillis());
//        protoEndgame.setTeamWin(teamWin);
//        //
//        int totalTrophy = 0;
//        for (Player player : aPlayer) {
//            if (player.user.team != teamWin) {
//                totalTrophy += player.user.trophy;
//            }
//        }
//        totalTrophy = totalTrophy < 5 ? 5 : totalTrophy;
//        debug("----------------------");
//        debug("totalTrophy = " + totalTrophy);
//        List<GameBonus> aBonus = new ArrayList<GameBonus>();
//        for (Player player : aPlayer) {
//            GameBonus bonus = new GameBonus();
//            player.aStatus = new ArrayList<PlayerStatus>();
//            player.mStatus = new HashMap<Integer, PlayerStatus>();
//            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
//            if (teamWin != -1) {
//                if (type == TeamObject.SOLO) {
//                    int exp = player.user.team == teamWin ? 20 : 2;
//                    int trophy = player.user.team == teamWin ? totalTrophy : -player.user.trophy;
//                    debug("trophy = " + trophy + " " + player.user.team + " " + teamWin);
//                    //
//                    bonus.setExp(exp);
//                    player.dbUser.addExp(exp,player.userInfo);
//                    playerBuilder.addAllBonus(Arrays.asList((long) CfgAchievement.EXP, (long) player.dbUser.getLevel(), player.dbUser.getExp(), (long) exp));
//                    //
//                    bonus.setTrophy(trophy);
//                    player.dbUser.addTrophy(trophy,player.userInfo);
//                    playerBuilder.addAllBonus(Arrays.asList((long) CfgAchievement.TROPHY, (long) player.dbUser.getTrophy(), (long) trophy));
//                    //
//                    player.dbUser.addWinRate(TeamObject.SOLO, player.user.team == teamWin);
//                }
//                if (player.user.team == teamWin) {
//                    if (isMatchType(TeamObject.SOLO)) {
////                        CfgNewMission.addMission(player.dbUser, player.channel, MissionObject.SOLOPLAYMISSION, 1);
////                        CfgNewMission.addMission(player.dbUser, player.channel, MissionObject.MISSIONCHARACTER + player.dbUser.getMAvatar().getHero(), 1);
//
////                        player.dbUser.addAchievementStatus(CfgAchievement.ACHIEVEMENT_SOLO, 1);
//
//                        player.dbUser.addAchievementStatus(CfgAchievement.CHARACTER + player.dbUser.getMAvatar().getHero(), 1,player.userInfo);
//
//                    } else if (isMatchType(TeamObject.TRAIN)) {
////                        CfgNewMission.addMission(player.dbUser, player.channel, MissionObject.TRAINPLAYMISSION, 1);
////                        player.dbUser.addAchievementStatus(CfgAchievement.ACHIEVEMENT_TRAIN, 1);
//                    }
//                }
//            }
//            playerBuilder.setTeam(player.user.team);
//            playerBuilder.addAllBonus(Arrays.asList((long) CfgAchievement.GOLD, player.dbUser.getGold(), (long) player.bonusGold));
//            GGProto.ProtoUser.Builder user = CommonProto.protoUser(player.dbUser).toBuilder();
//            playerBuilder.setUser(user);
//            protoEndgame.addAResult(playerBuilder);
//            //
//            ChUtil.set(player.channel, "table", null);
//            TeamObject team = (TeamObject) ChUtil.get(player.channel, Constans.KEY_USER_TEAM);
//            if (type == TeamObject.SOLO) {
//                ChUtil.set(player.channel, Constans.KEY_USER_TEAM, null);
//            } else {
//                team.userReady = new ArrayList<String>();
//                team.status = TeamObject.STATUS_NONE;
//            }
//            //
//            bonus.setUserId(player.dbUser.getId());
//            bonus.setGold(player.bonusGold);
//            bonus.setWinRate(player.dbUser.getWinRate());
//            aBonus.add(bonus);
//        }
//        updateHistory(protoEndgame);
////        sendAllPlayer(IAction.END_GAME, builder.build());
//        timeEndgame = server_time + 3;
    }

    public void checkEndGame() {
        int numberAlive = 0;
        for (Player player : aPlayer) {
            if (player.isAlive()) {
                numberAlive++;
                teamWin = player.user.team;
            }
        }
        if (numberAlive <= 1) {
            gameState = STATE_END_GAME;
//        } else if (server_time > timeTable) {
//            gameState = STATE_END_GAME;
        } else {
            teamWin = -1;
        }
    }

    void doAction() {

    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableSoloBoomBackup(cacheBattle);
    }
}
