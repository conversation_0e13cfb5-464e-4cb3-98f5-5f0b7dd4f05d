package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.effect.EffectSuddenItem;
import com.bem.boom.unit.Player;
import com.bem.config.CfgCommon;
import com.bem.config.CfgNewDropItem;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.MapEntity;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.MapLevel;
import com.bem.object.ResShopItem;
import com.bem.object.TopDetail;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MCache;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import grep.database.Database2;
import grep.helper.JsonUtil;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Nguoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableBossBoom extends AbstractTable {

    public TableBossBoom() {
    }

    public TableBossBoom(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mode = TeamObject.BOSS;
        this.isTableBoss = true;
        this.mapId = cacheBattle.getMapId() > 0 ? cacheBattle.getMapId() : 1;
//        if (CfgServer.isTest()) this.mapId = 10001;
        delayStartGame = 1;
//        MainCache.getInstance().addBossCCU(numberPlayer, id, String.valueOf(this.hashCode()) + " " + mapId);
        initMap();
    }

//    public TableBossBoom(List<CachePlayer> aPlayer, int type, int mapId) {
//        super(null, type, mapId);
//        this.isTableBoss = true;
//        delayStartGame = 1;
//        MainCache.getInstance().addBossCCU(numberPlayer, id, String.valueOf(this.hashCode()) + " " + mapId);
//        initMap();
//    }

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
        if (channel == null) return;
//        TeamObject team = (TeamObject) ChUtil.get(channel, Constans.KEY_USER_TEAM);
//        if (team != null) {
//            for (int i = 0; i < team.aUser.size(); i++) {
//                if (team.aUser.get(i).getId() == player.user.id) {
//                    team.removeUser(team.aUser.get(i).getId());
//                }
//            }
//        }
//        ChUtil.remove(channel, Constans.KEY_USER_TEAM);

        ProtoEndgame.Builder protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(10);

        JSONArray bonus = new JSONArray();
        bonus.addAll(Arrays.asList(Bonus.USER_ENERGY, -1));

        ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
        playerBuilder.setUser(CommonProto.protoUser(player.user));
        playerBuilder.setTeam(player.user.team);
        protoEndgame.addAResult(playerBuilder);

        sendPlayer(channel, IAction.LEAVE_TABLE_1, protoEndgame.build());
        MCache.getInstance().deleteNormal(cacheBattle.getKey());
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    public void insertTopAward(List<TopDetail> preTop, int preWeek) {
        List<Long> goldAward = new ArrayList<Long>(Arrays.asList(400000l, 360000l, 320000l, 280000l, 240000l, 200000l, 160000l, 120000l, 80000l, 40000l, 0l, 0l));
        List<String> lstUsername = new ArrayList<String>();
        List<Long> award = new ArrayList<Long>();
        List<Integer> number = new ArrayList<Integer>();

        for (int i = 0; i < preTop.size(); i++) {
            for (int j = 0; j < preTop.get(i).user.size(); j++) {
                if (!lstUsername.contains(preTop.get(i).user.get(j).getUsername())) {
                    lstUsername.add(preTop.get(i).user.get(j).getUsername());
                    award.add(goldAward.get(i));
                    number.add(preTop.get(i).id + 1);
                }
            }
        }
        for (int i = 0; i < lstUsername.size(); i++) {
//            Database.update("newUser", Arrays.asList("gold", "gold +" + award), Arrays.asList("username", String.valueOf(lstUsername.get(i))));
            UserDAO uDao = new UserDAO();
            uDao.updateUserGold(lstUsername.get(i), award.get(i));
            getLoggerMoney().info("lstUsername.get(i)Thưởng xếp hạng " + number.get(i) + "Add----> " + award.get(i) + "Koin");
        }
    }

    public void insertBossTop(boolean updatePretop, String top, String topPre, int time, int curWeek, int mapId) {
        try {
            if (updatePretop) {
                Database2.update(CfgCommon.mainDb + "map", Arrays.asList("top", top, "top_pre", topPre, "time_max_top", time + "", "cur_week", curWeek + ""), Arrays.asList("id", String.valueOf(mapId)));
            } else {
                Database2.update(CfgCommon.mainDb + "map", Arrays.asList("top", top, "time_max_top", time + "", "cur_week", curWeek + ""), Arrays.asList("id", String.valueOf(mapId)));
            }
        } catch (Exception ex) {

        }
    }

    public TopDetail getMytop(TableBossBoom table) {
//        try {
//            TopDetail myTop = new TopDetail();
//            TeamObject team = (TeamObject) ChUtil.get(table.aPlayer.get(0).channel, Constans.KEY_USER_TEAM);
//            String hostName = "";
//            List<UserEntity> lstUser = new ArrayList<UserEntity>();
//            List<String> lstUserName = new ArrayList<String>();
//            for (int i = 0; i < table.aPlayer.size(); i++) {
//                lstUserName.add(table.aPlayer.get(i).dbUser.getUsername());
//                if (team.hostId == table.aPlayer.get(i).dbUser.getId()) {
//                    hostName = table.aPlayer.get(i).dbUser.getName();
//                }
//                UserEntity user = new UserEntity();
//                user.setName(table.aPlayer.get(i).dbUser.getName());
//                user.setUsername(table.aPlayer.get(i).dbUser.getUsername());
//                user.setId(table.aPlayer.get(i).dbUser.getId());
//                user.setBattleItem(table.aPlayer.get(i).dbUser.getBattleItem());
//                user.setAvatar(table.aPlayer.get(i).dbUser.getAvatar());
//                user.setExp(table.aPlayer.get(i).dbUser.getExp());
//                user.setLevel(table.aPlayer.get(i).dbUser.getLevel());
//                user.setLogout(table.aPlayer.get(i).dbUser.getLogout());
//                user.setTopBossCur("{}");
//                user.setTopBossMax("{}");
//                user.setGold(table.aPlayer.get(i).dbUser.getGold());
//                user.setGem(table.aPlayer.get(i).dbUser.getGem());
//                lstUser.add(user);
//            }
//            for (int i = 0; i < lstUserName.size() - 1; i++) {
//                for (int j = i + 1; j < lstUserName.size(); j++) {
//                    if (lstUserName.get(i).compareToIgnoreCase(lstUserName.get(j)) > 0) {
////                        String tg = new String(lstUserName.get(i).toString());
////                        lstUserName.set(i,new String(lstUserName.get(j).toString()));
////                        lstUserName.set(j,new String(tg.toString()));
//                        String tg = lstUserName.get(i);
//                        lstUserName.set(i, lstUserName.get(j));
//                        lstUserName.set(j, tg);
//                    }
//                }
//            }
//            for (int i = 0; i < lstUserName.size(); i++) {
//                myTop.keyId += lstUserName.get(i);
//            }
//            myTop.hostName = hostName;
//            SimpleDateFormat sdf = new SimpleDateFormat("w");
//            Date currDate = new Date();
//            String curDateString = sdf.format(currDate);
//            myTop.week = Integer.parseInt(curDateString);
//            myTop.time = (int) table.kill_boss_time * 1000 / 1000;
//            myTop.user = lstUser;
//            return myTop;
//        } catch (Exception ex) {
//
//        }
        return new TopDetail();
    }

    public void checkCanHaveTop() {
//        try {
//            int mapId = (((map.getMapId() - 1) / 3) * 3) + 1;
//            SimpleDateFormat sdf = new SimpleDateFormat("w");
//            Date currDate = new Date();
//            String curDateString = sdf.format(currDate);
//            int curWeed = Integer.parseInt(curDateString);
//
//            // check my record
//            for (int i = 0; i < aPlayer.size(); i++) {
//                int weekRecord = -1;
//                Gson gson = new Gson();
//                TopDetail topCur = null;
//                TopDetail topBest = null;
//
//                try {
//                    topCur = gson.fromJson(aPlayer.get(i).dbUser.getTopBossCur(), new TypeToken<TopDetail>() {
//                    }.getType());
//                    weekRecord = topCur.week;
//                } catch (Exception ex) {
//                }
//                try {
//                    topBest = gson.fromJson(aPlayer.get(i).dbUser.getTopBossMax(), new TypeToken<TopDetail>() {
//                    }.getType());
//                    debug("aPlayer.get(i).dbUser.getTopBossMax()---->" + aPlayer.get(i).dbUser.getTopBossMax());
//                } catch (Exception ex) {
//                }
//                TopDetail myTop = getMytop(this);
//                boolean haveUpdateWeek = false;
//
//                if (weekRecord < curWeed) {//recordWeek moi
//                    haveUpdateWeek = true;
//                } else {
//                    if (topCur == null || myTop.time < topCur.time || topCur.time == 0) {
//                        haveUpdateWeek = true;
//                    }
//                }
//                if (haveUpdateWeek) {
//                    aPlayer.get(i).dbUser.setTopBossCur(myTop.toString(myTop));
//                    Database.update("user", Arrays.asList("top_boss_cur", aPlayer.get(i).dbUser.getTopBossCur()), Arrays.asList("id", String.valueOf(aPlayer.get(i).dbUser.getId())));
//                }
//
//                if (topBest == null || myTop.time < topBest.time || topBest.time == 0) {
//                    aPlayer.get(i).dbUser.setTopBossMax(myTop.toString(myTop));
//                    Database.update("user", Arrays.asList("top_boss_max", aPlayer.get(i).dbUser.getTopBossMax()), Arrays.asList("id", String.valueOf(aPlayer.get(i).dbUser.getId())));
//                }
//            }
//
//
//            int curWeedDb = -1;
//            try {
//                curWeedDb = Integer.parseInt(Database.getUniqueColumn(CfgCommon.mainDb + "map", Arrays.asList("id", String.valueOf(mapId)), "cur_week"));
//            } catch (Exception ex) {
//            }
//            int time = -1;
//            if (curWeed == curWeedDb && curWeedDb > 0) {// dang trong tuan update
//                try {
//                    time = Integer.parseInt(Database.getUniqueColumn(CfgCommon.mainDb + "map", Arrays.asList("id", String.valueOf(mapId)), "time_max_top"));
//                } catch (Exception ex) {
//                }
//            }
//            if (time == -1 || time > kill_boss_time) {
//                topBossHistory(this);
//            }
//        } catch (Exception ex) {
//            ex.printStackTrace();
//
//        }
    }

    public static synchronized void topBossHistory(TableBossBoom table) {
        //top = Database.getUniqueColumn("map", Arrays.asList("id", String.valueOf(team.map)), "top");
        int mapId = (((table.map.getMapId() - 1) / 3) * 3) + 1;
        SimpleDateFormat sdf = new SimpleDateFormat("w");
        Date currDate = new Date();
//                String curDateString = sdf.format(Calendar.getInstance().getTime());
        String curDateString = sdf.format(currDate);
        int curWeed = Integer.parseInt(curDateString);
        List<TopDetail> preTop = new ArrayList<TopDetail>();
        boolean hasUpdatePre = false;
        int curWeedDb = -1;
        try {
            curWeedDb = Integer.parseInt(Database2.getUniqueColumn(CfgCommon.mainDb + "map", Arrays.asList("id", String.valueOf(mapId)), "cur_week"));
        } catch (Exception ex) {

        }
        int time = -1;
        if (curWeed == curWeedDb && curWeedDb > 0) {// dang trong tuan update
            try {
                time = Integer.parseInt(Database2.getUniqueColumn(CfgCommon.mainDb + "map", Arrays.asList("id", String.valueOf(mapId)), "time_max_top"));
                hasUpdatePre = true;
            } catch (Exception ex) {
            }
        } else if (curWeed > curWeedDb) {
            String top = Database2.getUniqueColumn(CfgCommon.mainDb + "map", Arrays.asList("id", String.valueOf(mapId)), "top");
            if (top != null && !top.trim().equalsIgnoreCase("")) {
                preTop = new TopDetail().getListTopBoss(top);
            } else {
                preTop = new ArrayList<TopDetail>();
            }
        }
        if (time == -1 || time > table.kill_boss_time) {
            String top = "[]";
            if (time > -1) {
                top = Database2.getUniqueColumn(CfgCommon.mainDb + "map", Arrays.asList("id", String.valueOf(mapId)), "top");
            }
            List<TopDetail> lstTop = new TopDetail().getListTopBoss(top);
            if (lstTop == null) {
                lstTop = new ArrayList<TopDetail>();
            }
            TopDetail myTop = table.getMytop(table);
            if (lstTop.size() == 0) {
                myTop.id = 0;
                lstTop.add(myTop);
            } else {
                int ck = 0;//0 insert , 1 update , 2 koupdate
                for (int i = 0; i < lstTop.size(); i++) {
                    if (lstTop.get(i).keyId.trim().equalsIgnoreCase(myTop.keyId.trim())) {
                        if (myTop.time < lstTop.get(i).time) {
                            ck = 1;
                        } else {
                            ck = 2;
                        }
                        break;
                    }
                }
                if (ck == 0) {
                    int index = -1;
                    for (int i = 0; i < lstTop.size(); i++) {
                        if (index == -1) {
                            if (lstTop.get(i).time > myTop.time) {
                                index = lstTop.get(i).id;
                                lstTop.get(i).id += 1;
                            }
                        } else {
                            lstTop.get(i).id += 1;
                        }
                    }
                    if (index > -1) {
                        myTop.id = index;
                        lstTop.add(index, myTop);
                        for (int i = lstTop.size(); i > 10; i++) {
                            lstTop.remove(lstTop.size() - 1);
                        }
                    } else {
                        if (lstTop.size() < 10) {
                            index = lstTop.size();
                            myTop.id = index;
                            lstTop.add(myTop);

                        }
                    }
                } else if (ck == 1) {
                    for (int i = 0; i < lstTop.size(); i++) {
                        if (lstTop.get(i).keyId.trim().equalsIgnoreCase(myTop.keyId.trim())) {
                            lstTop.get(i).time = myTop.time;
                        }
                    }
                }
            }
            if (lstTop.size() >= 10) {
                time = lstTop.get(lstTop.size() - 1).time;
            } else {
                time = (int) (table.server_time * 1000);
            }
            String outPut = new TopDetail().toString(lstTop);
            String outPutPre = new TopDetail().toString(preTop);
            table.insertBossTop(hasUpdatePre, outPut, outPutPre, time, curWeed, mapId);
            if (time == -1) {
                table.insertTopAward(preTop, curWeed - 1);
            }
        }
    }

    void endGame() {
        String debug = String.valueOf(isPlay) + " " + this.hashCode() + " " + mapId;
        if (!isPlay) {
            return;
        }
        try {
            isPlay = false;
            int star = 0;
            protoEndgame = ProtoEndgame.newBuilder();
            protoEndgame.setType(mode);
            protoEndgame.setTeamWin(teamWin);
            protoEndgame.setTimeCreated(System.currentTimeMillis());
            if (teamWin == 0) {
                float timeRemain = 183 - server_time;
                star = numberPlayer > 1 ? 1 : (timeRemain > 90 ? 3 : (timeRemain > 60 ? 2 : 1));
                for (Player player : aPlayer) {
                    if (player.user.id == cacheBattle.getHostPlayerId()) {
                        player.bonusGold += map.getGold();
                        player.bonusExp += map.getExp();
                        if (cacheBattle.getHostPlayerId() == player.user.id && cacheBattle.getCurStar() < star && star == 3) {
                            player.bonusGem += map.getGem();
                        }
                    } else {
                        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
                    }
//                    if(player.userInfo.getUData().getMap().getStar(aTeam.get(0).mode, mapId)<3 && star ==3){
//                        player.bonusGem += map.getGem();
//                    }
//                    CfgNewMission.addMission(player.dbUser, player.channel, MissionObject.BOSSPLAYMISSION, 1);
//                    player.dbUser.addAchievementStatus(CfgAchievement.WIN_MAP, 1, player.userInfo);
//                    player.dbUser.addAchievementStatus(CfgAchievement.COLLECT_STAR, 1, player.userInfo);
                }
//                checkCanHaveTop();
            }
            protoEndgame.setStar(star);
            for (Player player : aPlayer) {
                resetPlayer(player);
                debug += ", " + player.channel + " " + player.user.name + " " + teamWin;
                ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
                if (player.user.id == cacheBattle.getHostPlayerId()) {
                    playerBuilder.addAllBonus(teamWin == player.user.team ? JsonUtil.convertToListLong(MapEntity.getBonus(map.dropItem)) : new ArrayList<>());
                    if (player.bonusGold > 0) {
                        playerBuilder.addAllBonus(Arrays.asList((long) Bonus.GOLD, (long) player.bonusGold));
                    }
                    if (player.bonusGem > 0) {
                        playerBuilder.addAllBonus(Arrays.asList((long) Bonus.GEM, (long) player.bonusGem));
                    }
                    if (player.bonusExp > 0) {
                        playerBuilder.addAllBonus(Arrays.asList((long) Bonus.EXP, (long) player.bonusExp));
                        playerBuilder.addAllBonus(Arrays.asList((long) Bonus.HERO_EXP, (long) player.user.getHero(), (long) player.bonusExp));
                    }
                }
                if (teamWin == 0) playerBuilder.addAllBonus(CfgNewDropItem.getDropItem(mode));
                if (player.user.reviveItem == 2)
                    playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));

                playerBuilder.addAllExBonus(Arrays.asList((long) Bonus.UPDATE_MAP_LEVEL, map.getMapId() >= 1000 ? (long) MapLevel.MODE_INSANE : (long) MapLevel.MODE_NORMAL,
                        (long) map.getMapId() % 1000, (long) star));
                playerBuilder.setUser(CommonProto.protoUser(player.user));
                playerBuilder.setTeam(player.user.team);
                playerBuilder.setStatus(player.leaveStatus);
                protoEndgame.addAResult(playerBuilder);
            }
            protoEndgame.setGameId(Integer.parseInt(id));
            timeEndgame = server_time + 3;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
        super.endGame();
    }

    public synchronized void checkEndGame() {
        numberPlayerDie = 0;
        for (Player player : aPlayer) {
            if (!player.isAlive()) numberPlayerDie++;
        }
        if (server_time >= 180) {
            gameState = STATE_END_GAME;
        } else if (numberPlayerDie >= 1) {
            gameState = STATE_END_GAME;
        } else if (numberMonsterDie == aMonster.size()) {
            gameState = STATE_END_GAME;
            teamWin = 0;
        }
//        else if (numberBossDie > 0) {
//            for (IMonster monster : aMonster) {
//                if (monster.isAlive()) {
////                    monster.addDamage(Integer.MAX_VALUE);
//                    monster.setHp(0);
//                }
//            }
//        }
    }

    @Override
    void initMap() {
        super.initMap();
        aEffect.add(new EffectSuddenItem(this, map));
    }

    void doAction() {
    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableBossBoom(cacheBattle);
    }
}
