package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.Resources;
import com.bem.boom.effect.EffectDropBox;
import com.bem.boom.effect.EffectSuddenItem;
import com.bem.boom.map.MapResource;
import com.bem.boom.unit.Player;
import com.bem.config.CfgBonusPvp;
import com.bem.config.CfgNewDropItem;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.IUser;
import com.bem.object.ResShopItem;
import com.bem.util.CommonProto;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

/**
 * <PERSON>uoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableTeamBoom extends AbstractTable {

    //    public TableTeamBoom(List<TeamObject> aTeam, int type, int mapId) {
//        super(aTeam, type, mapId);
//        this.mapId = MapResource.getSoloMap();
//        delayStartGame = 5;
//        MainCache.getInstance().addRankCCU(numberPlayer, id);
//        initMap();
//    }
    public TableTeamBoom() {

    }

    public TableTeamBoom(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mapId = MapResource.getSoloMap();
        this.mode = TeamObject.RANK;
        delayStartGame = 5;
//        MainCache.getInstance().addBossCCU(numberPlayer, id, String.valueOf(this.hashCode()) + " " + mapId);
        initMap();
    }

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
        if (channel == null) return;
//        TeamObject team = (TeamObject) ChUtil.get(channel, Constans.KEY_USER_TEAM);
//        if (team != null) {
//            for (int i = 0; i < team.aUser.size(); i++) {
//                if (team.aUser.get(i).getId() == player.user.id) {
//                    team.removeUser(team.aUser.get(i).getId());
//                }
//            }
//        }
//        ChUtil.remove(channel, Constans.KEY_USER_TEAM);

        ProtoEndgame.Builder protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(10);

//        ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
//        playerBuilder.setTeam(player.user.team);
//        protoEndgame.addAResult(playerBuilder);
//
        sendPlayer(channel, IAction.LEAVE_TABLE_1, protoEndgame.build());
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
        if (!isPlay) {
            Logs.debug("endgame lan 2 rank");
            return;
        }
        isPlay = false;
        protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(teamWin);
        protoEndgame.setTimeCreated(System.currentTimeMillis());

        botUsername = new ArrayList<String>();
        for (Player player : aPlayer) {
            resetPlayer(player);
            botUsername.add(player.user.username);
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
            if (teamWin != -1) {
                int exp = player.user.team == teamWin ? 20 : 10;
                int trophy = Resources.getRankTrophy(player.user.rankId, player.user.team == teamWin);
//                if(trophy > 0){
//                    playerBuilder.addAllBonus(Arrays.asList((long)Bonus.UPDATE_DATA_INT, (long)UserInt.NUMBER_WIN, (long)UserInt.TYPE_ADD , (long)1));
//
//                }else if(trophy< 0){
//                    playerBuilder.addAllBonus(Arrays.asList((long)Bonus.UPDATE_DATA_INT, (long)UserInt.NUMBER_WIN, (long)UserInt.TYPE_SET , (long)0));
//
//                }
                if (trophy > 0 && new Random().nextInt(100) < 15) {
                    if (player.user.id > 0) {
                        playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.GOLD_KEY, 1));
                    }
                }
                if (player.user.id > 0 && player.user.team == teamWin) {
                    playerBuilder.addAllBonus(CfgNewDropItem.getDropItem(mode));
                    try {
                        int hasBonus = new Random().nextInt(100);
//                    List<Long> aBonusPvp = new ArrayList<>();
                        if (CfgBonusPvp.percent > hasBonus) {
                            int index = new Random().nextInt(CfgBonusPvp.bonus.size());
//                        aBonusPvp.addAll(Bonus.receiveListItem(player.user, JSONArray.fromObject(CfgBonusPvp.bonus.get(index)), "bonus stone ufo"));
                            for (int i = 0; i < JSONArray.fromObject(CfgBonusPvp.bonus.get(index)).size(); i++) {
                                playerBuilder.addBonus((long) JSONArray.fromObject(CfgBonusPvp.bonus.get(index)).getLong(i));

                            }
                        }
                    } catch (Exception ex) {

                    }
                }


                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.TROPHY, trophy));
                playerBuilder.addAllBonus(Arrays.asList((long) Bonus.HERO_EXP, (long) player.user.getHero(), (long) exp));
                if (player.bonusGold > 0) {
                    playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.GOLD, player.bonusGold));
                }
                if (trophy > 0) playerBuilder.addAllExBonus(Arrays.asList((long) Bonus.WIN_RATE, 1L, 0L));
                else playerBuilder.addAllExBonus(Arrays.asList((long) Bonus.WIN_RATE, 0L, 1L));
            }
            if (player.user.reviveItem == 2)
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));

            for (IUser.UsedItem item : player.user.aUsedItem) {
                if (item.getNumber() > 0)
                    playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, item.getItemId(), -item.getNumber()));
            }
            if (player.user.team == teamWin && mode == TeamObject.RANK) {
//                player.dbUser.addAchievementStatus(CfgAchievement.ACHIEVEMENT_TEAM, 1, player.userInfo);
//                CfgNewMission.addMission(player.dbUser, player.channel, MissionObject.SOLOPLAYMISSION, 1);
//                player.dbUser.addAchievementStatus(CfgAchievement.CHARACTER + player.dbUser.getMAvatar().getHero(), 1, player.userInfo);
            }
            playerBuilder.setUser(CommonProto.protoUser(player.user));
            playerBuilder.setTeam(player.user.team);
            playerBuilder.setStatus(player.leaveStatus);
            protoEndgame.addAResult(playerBuilder);
            System.out.println("bonus sau tran dau------->"+playerBuilder.getBonusList().toString());
        }
        protoEndgame.setGameId(Integer.parseInt(id));
        super.endGame();
    }

    public void checkEndGame() {
        int teamAlive = -1;
        for (Player player : aPlayer) {
            if (player.isAlive()) {
                if (teamAlive == -1) {
                    teamAlive = player.user.team;
                } else if (teamAlive != player.user.team) {
                    return;
                }
            }
        }
        teamWin = teamAlive;
        gameState = STATE_END_GAME;
    }

//    @Override
//    protected void initPlayer() {
//        debug("reset aplayer Team");
//        aPlayer = new ArrayList<Player>();
//        int count1 = 0, count2 = 0;
//        for (int i = 0; i < aTeam.size(); i++) {
//            debug("aTeam.get(i).aUser.size() = " + i + " " + aTeam.get(i).aUser.size());
//            if (aTeam.get(i).aUser.size() == 1) {
//                count1++;
//            } else {
//                count2++;
//            }
//        }
//        debug(count1 + " " + count2);
//        int posIndex = 0, teamIndex = 0, count = 0;
//        if (count1 == 2 && count2 == 0) {
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                for (UserInfo user : aUser) {
//                    createPlayer(user, teamIndex, posIndex);
//                    posIndex++;
//                }
//                posIndex++;
//                teamIndex++;
//            }
//        } else if (count1 == 4) {
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                if (aUser.size() == 1) {
//                    for (UserInfo user : aUser) {
//                        createPlayer(user, teamIndex, posIndex);
//                        posIndex++;
//                    }
//                    if (++count == 2) teamIndex++;
//                }
//            }
//        } else if (count1 == 2 && count2 == 1) {
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                teamIndex = aUser.size() == 1 ? 0 : 1;
//                for (UserInfo user : aUser) {
//                    createPlayer(user, teamIndex, posIndex);
//                    posIndex++;
//                }
//            }
//        } else if (count2 == 2) {
//            //  slib_Logger.root().warn(Xerver.mCounter.get(3) + " " + this + " " + Util.exToString(ex));
//            String debug = "";
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                debug += "+size=" + aTeam.get(i).aUser.size() + " - ";
//                for (UserInfo user : aUser) {
//                    debug += posIndex + " " + user.getDbUser().getUsername() + " ";
//                    posIndex++;
//                }
//                teamIndex++;
//            }
//            slib_Logger.root().warn(debug);
//            posIndex = 0;
//            teamIndex = 0;
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                for (UserInfo user : aUser) {
//                    createPlayer(user, teamIndex, posIndex);
//                    posIndex++;
//                }
//                teamIndex++;
//            }
//        }
//    }

    void doAction() {

    }

    @Override
    void initMap() {
        super.initMap();
        aEffect.add(new EffectDropBox(this, map));
        aEffect.add(new EffectSuddenItem(this, map));
    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableTeamBoom(cacheBattle);
    }
}
