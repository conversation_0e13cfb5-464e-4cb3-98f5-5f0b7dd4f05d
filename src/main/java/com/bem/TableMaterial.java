package com.bem;

import com.bem.boom.*;
import com.bem.boom.effect.EffectPoison;
import com.bem.boom.monster.IMonster;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.config.CfgAchievement;
import com.bem.config.GameCfgBoss;
import com.bem.config.GameCfgMaterial;
import com.bem.matcher.TableMonitor;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.ResMaterial;
import com.bem.object.ResShopItem;
import com.bem.util.CommonProto;
import com.k2tek.Constans;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * <PERSON><PERSON><PERSON> choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableMaterial extends AbstractTable {

    static final int STATE_PLAY = 0;
    static final int STATE_COLLECT_MATERIAL = 1;

    GameCfgMaterial.Map mapMaterial;
    List<Integer> addMaterials = new ArrayList<>();
    int state = 0;
    float timeCollect;

    public TableMaterial() {

    }

    public TableMaterial(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mode = TeamObject.GAME_MATERIAL;
        this.mapMaterial = GameCfgMaterial.getMap();
        this.mapId = mapMaterial.id;
        delayStartGame = 2;
        initMap();
    }

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
        player.channel = channel;
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
        isPlay = false;
        protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(teamWin);
        protoEndgame.setTimeCreated(System.currentTimeMillis());
        boolean quickResult = false;
        for (Player player : aPlayer) {
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
            if (teamWin == player.user.team)
                for (Integer material : addMaterials) {
                    playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.MATERIAL, material, 1));
                }
            if (player.user.reviveItem == 2)
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));
            playerBuilder.setUser(CommonProto.protoUser(player.user).toBuilder());
            playerBuilder.setTeam(player.user.team);
            playerBuilder.setStatus(player.leaveStatus);
            protoEndgame.addAResult(playerBuilder);
            if (player.leaveStatus == Constans.PLAYER_STATUS_LEAVE) quickResult = true;
        }
        super.endGame();
    }

    public synchronized void checkEndGame() {
        numberPlayerDie = 0;
        for (Player player : aPlayer) {
            if (!player.isAlive()) numberPlayerDie++;
        }
        if (server_time >= 180 || numberPlayerDie == aPlayer.size()) {
            gameState = STATE_END_GAME;
            teamWin = 10;
        } else if (numberMonsterDie == aMonster.size()) {
            if (state == STATE_PLAY) {
                timeCollect = server_time + 10;
                state = STATE_COLLECT_MATERIAL;
                // add material
                List<SquareUnit> aSquare = map.getRandomSquare(map.getSquareEmpty(), addMaterials.size());
                for (int i = 0; i < aSquare.size(); i++) {
                    Item item = new Item(map.getNextUnitId(), Item.UNKNOWN_MATERIAL);
                    item.value = addMaterials.get(i);
                    aProtoAdd.add(item.protoAdd(lastBossPos));
                    aProtoJumpPos.add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, item.getId(), aSquare.get(i).pos));
                    aSquare.get(i).aItem.add(item);
                }
            } else if (state == STATE_COLLECT_MATERIAL) {
                if (server_time >= timeCollect || aPlayer.get(0).materials.size() == addMaterials.size()) {
                    gameState = STATE_END_GAME;
                    teamWin = 0;
                }
            }
        } else if (numberBossDie > 0) {
            for (IMonster monster : aMonster) {
                if (monster.isAlive()) {
                    monster.setHp(0);
                }
            }
        }
    }

    @Override
    void initMap() {
        super.initMap();
        map.poisonDamage = GameCfgMaterial.getPoison(cacheBattle.getLevelIndex());
        //
        aEffect.add(new EffectPoison(this, map));
        // prepare material
        addMaterials = new ArrayList<>();
        Random ran = new Random();
        List<Integer> materials = GameCfgMaterial.getMaterials(cacheBattle.getLevelIndex());
        for (int i = 0; i < materials.size(); i += 3) {
            int numberItem = ran.nextInt(materials.get(i + 2) - materials.get(i + 1)) + materials.get(i + 1);
            List<ResMaterial> tmp = Resources.getRandomMaterial(materials.get(i), mapMaterial.materialType, numberItem);
            for (int j = 0; j < tmp.size(); j++) {
                addMaterials.add(tmp.get(j).getId());
            }
        }
        // set hp atk monster
        int hp = GameCfgMaterial.getHp(cacheBattle.getLevelIndex(), false), atk = GameCfgMaterial.getAtk(cacheBattle.getLevelIndex());
        int bossHp = GameCfgMaterial.getHp(cacheBattle.getLevelIndex(), true);
        for (IMonster monster : aMonster) {
            monster.setHp(monster.isBoss() ? bossHp : hp);
            monster.setAtk(atk);
            protoInit.addAUnitAdd(monster.protoAdd());
        }
    }

    void doAction() {
    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableMaterial(cacheBattle);
    }
}
