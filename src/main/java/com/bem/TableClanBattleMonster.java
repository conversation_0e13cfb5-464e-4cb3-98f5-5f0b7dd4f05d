package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.Resources;
import com.bem.boom.SquareUnit;
import com.bem.boom.monster.IMonster;
import com.bem.boom.object.Point;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.config.GameCfgBoss;
import com.bem.config.GameCfgClanWar;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.ResShopItem;
import com.bem.util.CommonProto;
import com.k2tek.Constans;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * Nguoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableClanBattleMonster extends AbstractTable {

    public TableClanBattleMonster() {

    }

    public TableClanBattleMonster(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mode = TeamObject.GAME_CLAN_BATTLE_MONSTER;
        this.mapId = cacheBattle.getMapId();
        delayStartGame = 2;
        initMap();
    }

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
        player.channel = channel;
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
        isPlay = false;
        protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(teamWin);
        protoEndgame.setTimeCreated(System.currentTimeMillis());

        for (int i = 0; i < aPlayer.size(); i++) {
            Player player = aPlayer.get(i);
            resetPlayer(player);
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
            if (player.user.reviveItem == 2)
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));
            if (player.user.team == teamWin)
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.GOLD, GameCfgClanWar.config.bonusGold));
            playerBuilder.addAllExBonus(Arrays.asList((long) Bonus.GAME_NUMBER_ATK, (long) Constans.EVENT_CLAN_BATTLE, 2L, player.user.team == teamWin ? 1L : -1L, 0L));
            playerBuilder.setUser(CommonProto.protoUser(player.user));
            playerBuilder.setTeam(player.user.team);
            playerBuilder.setStatus(player.leaveStatus);
            protoEndgame.addAResult(playerBuilder);
        }
        protoEndgame.setGameId(Integer.parseInt(id));
        super.endGame();
        timeEndgame = server_time;
    }

    public synchronized void checkEndGame() {
        numberPlayerDie = 0;
        for (Player player : aPlayer) {
            if (!player.isAlive()) numberPlayerDie++;
        }
        if (server_time >= 60) {
            gameState = STATE_END_GAME;
            teamWin = 10;
        } else if (numberPlayerDie == aPlayer.size()) {
            gameState = STATE_END_GAME;
            teamWin = 10;
        } else if (numberMonsterDie == aMonster.size()) {
            gameState = STATE_END_GAME;
            teamWin = 0;
        }
    }

    @Override
    void initMap() {
        super.initMap();
        int[] itemIds = GameCfgBoss.getSupportItem();
        List<SquareUnit> aSquare = map.getRandomSquare(map.getSquareEmpty(), itemIds.length);
        for (int i = 0; i < aSquare.size(); i++) {
            Item tmpItem = new Item(map.getNextUnitId(), itemIds[i]);
            aProtoAdd.add(tmpItem.protoAdd(aSquare.get(i).pos));
//                    aProtoJumpPos.add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, tmpItem.getId(), aSquare.get(i).pos));
            aSquare.get(i).aItem.add(tmpItem);
        }
        Player player = aPlayer.get(0);
        int numberHit = new Random().nextInt(10) + 1, numberBeAtk = new Random().nextInt(5) + 5;
        int atk = (int) (player.user.getPoint(Point.HP) / numberBeAtk), hp = (int) player.user.getPoint(Point.ATK) * numberHit;
        aMonster.forEach(iMonster -> {
            iMonster.setAtk(atk);
            iMonster.setHp(hp);
            iMonster.getMPoint().forEach((key, value) -> {
                iMonster.getMPoint().put(key, new int[]{atk, atk});
            });
        });
        aMonster.forEach(iMonster -> protoInit.addAUnitAdd(iMonster.protoAdd()));
    }

    void doAction() {
    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableClanBattleMonster(cacheBattle);
    }

}
