package com.bem;

import com.bem.boom.*;
import com.bem.boom.effect.EffectPoison;
import com.bem.boom.monster.IMonster;
import com.bem.boom.unit.Item;
import com.bem.boom.unit.Player;
import com.bem.config.CfgAchievement;
import com.bem.config.GameCfgBoss;
import com.bem.config.GameCfgHunting;
import com.bem.config.GameCfgMaterial;
import com.bem.matcher.TableMonitor;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.ResShopItem;
import com.bem.util.CommonProto;
import com.k2tek.Constans;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;

import java.util.*;

/**
 * Nguoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableBossGlobal extends AbstractTable {

    GameCfgBoss.Map mapBoss;

    public TableBossGlobal() {

    }

    public TableBossGlobal(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mode = TeamObject.GAME_BOSS;
        this.mapBoss = GameCfgBoss.getMap();
        this.mapId = this.mapBoss.id;
        delayStartGame = 2;
        initMap();
    }

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
        player.channel = channel;
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
        isPlay = false;
        protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(teamWin);
        protoEndgame.setTimeCreated(System.currentTimeMillis());
        boolean quickResult = false;
        for (Player player : aPlayer) {
//            System.out.println("player.totalDamage = " + player.totalDamage);
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
            playerBuilder.addExBonus(player.totalDamage);
            playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.MEDAL_BOSS, 1));
            playerBuilder.setUser(CommonProto.protoUser(player.user).toBuilder());
            playerBuilder.setTeam(player.user.team);
            playerBuilder.setStatus(player.leaveStatus);
            if (player.user.reviveItem == 2)
                playerBuilder.addAllBonus(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, -1));

            protoEndgame.addAResult(playerBuilder);
            if (player.leaveStatus == Constans.PLAYER_STATUS_LEAVE) quickResult = true;
        }
        super.endGame();
    }

    public synchronized void checkEndGame() {
        numberPlayerDie = 0;
        for (Player player : aPlayer) {
            if (!player.isAlive()) numberPlayerDie++;
        }
        if (server_time >= 60) {
            gameState = STATE_END_GAME;
            teamWin = 10;
        } else if (numberPlayerDie == aPlayer.size()) {
            gameState = STATE_END_GAME;
            teamWin = 10;
        }
    }

    @Override
    void initMap() {
        super.initMap();
        int[] itemIds = GameCfgBoss.getSupportItem();
        List<SquareUnit> aSquare = map.getRandomSquare(map.getSquareEmpty(), itemIds.length);
        for (int i = 0; i < aSquare.size(); i++) {
            Item tmpItem = new Item(map.getNextUnitId(), itemIds[i]);
            aProtoAdd.add(tmpItem.protoAdd(aSquare.get(i).pos));
//                    aProtoJumpPos.add(CommonProto.protoJumpPos(BoomConfig.JUMP_NORMAL, tmpItem.getId(), aSquare.get(i).pos));
            aSquare.get(i).aItem.add(tmpItem);
        }

//        System.out.println("mapBoss.boss[0] = " + mapBoss.boss[0]);
//        Map<Integer, int[]> mPoint = new HashMap<Integer, int[]>();
//        for (int i = 0; i < 50; i++) {
//            mPoint.put(i, new int[]{5000, 5000});
//        }
//        map.map.setMPoint(mPoint);
//
//        IMonster mU = Resources.mMonster.get(mapBoss.boss[0]).clone(map.getNextUnitId(), mapBoss.boss[0], new MapPos(6, 6), new float[]{0f, 0f});
//        mU.initPoint(Integer.MAX_VALUE, Integer.MAX_VALUE);
//        mU.setMPoint(mPoint);
//        mU.map = map;
//        aMonster.add(mU);
//        aProtoAdd.add(mU.protoAdd());
    }

    void doAction() {
    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableBossGlobal(cacheBattle);
    }
}
