package com.bem.matcher;

import com.bem.boom.CacheBattle;
import com.bem.boom.object.CachePlayer;
import com.bem.config.CfgEnergy;
import com.bem.config.CfgServer;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.BotBomb;
import com.bem.object.ITimer;
import com.bem.object.MapLevel;
import com.bem.object.UserInfo;
import com.bem.util.Actions;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.google.protobuf.AbstractMessage;
import com.k2tek.IAction;
import com.k2tek.Xerver;
import com.k2tek.common.slib_Logger;
import com.proto.GGProto;
import net.sf.json.JSONArray;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * Created by vieth_000 on 9/6/2016.
 */
public class TeamObject implements ITimer {
    public static final int SOLO = 0;
    //
    public static final int RANK = 1;
    public static final int BOSS = 2;
    public static final int TRAIN = 3;
    public static final int ZOMBIE = 10;
    public static final int ARENA = 11;
    public static final int GLOBAL = 12;
    //
    public static final int TUTORIAL = 4;
    public static final int GAME_HUNTING = 5;
    public static final int GAME_MATERIAL = 6;
    public static final int GAME_BOSS = 7;
    public static final int GAME_CLAN_BATTLE = 8;
    public static final int GAME_CLAN_BATTLE_MONSTER = 9;
    public static final List<Integer> PLAY_TYPE = Arrays.asList(RANK, BOSS, TRAIN);
    //
    public static final int STATUS_NONE = 0;
    public static final int STATUS_SEARCH = 1;
    public static final int STATUS_PLAY = 2;
    //
    public static final List<Integer> TYPE_TIMER = Arrays.asList(RANK, BOSS, TRAIN);
    public int type, id, status;
    public long timeSearch, hostId;
    public List<String> userReady = new ArrayList<String>();
    public List<UserInfo> aUser = new ArrayList<>();
    public int map = 0, mode = 1;
    public int maxPlayer = 2;
    public List<BotBomb> aBot = new ArrayList<BotBomb>();
    public long lastAction = System.currentTimeMillis();
    static int counterId = 0;

    static synchronized int getCounterId() {
        if (++counterId == 100000) counterId = 1;
        return counterId;
    }

    public TeamObject(int type, UserInfo hostUser) {
        this.id = getCounterId();
        this.type = type;
        this.aUser.add(hostUser);
        this.hostId = hostUser.getDbUser().getId();
        this.status = STATUS_NONE;
        this.map = 0;
        hostUser.setTeam(this);
        if (TYPE_TIMER.contains(type)) doExpireTurn(0);
    }

    public void removeUser(long userId) {
        for (int i = 0; i < aUser.size(); i++) {
            if (aUser.get(i).getId() == userId) {
                userReady.remove(aUser.get(i).getUsername());
                aUser.get(i).setTeam(null);
                aUser.remove(i);
                break;
            }
        }
        if (userId == hostId && !aUser.isEmpty()) {
            hostId = aUser.get(0).getId();
        }
        if (aUser.isEmpty()) {
            TeamMatcher.removeTeam(id);
        }
    }

    public synchronized int addUser(UserInfo user) {
        if (aUser.size() == maxPlayer) {
            return 3;
        }
        try {
            for (int i = 0; i < aUser.size(); i++) {
                if (aUser.get(i).getUsername().equals(user.getUsername())) {
                    return 0;
                }
            }
            user.setTeam(this);
            aUser.add(user);
            return 1;
        } catch (Exception ex) {
        }
        return 2;
    }

    public GGProto.ProtoTeamStatus protoTeamStatus() {
        GGProto.ProtoTeamStatus.Builder builder = GGProto.ProtoTeamStatus.newBuilder();
        builder.setId(id);
        builder.setType(type);
        builder.setStatus(status);
        builder.setHostId(hostId);
        builder.setCurMap(map);
        for (int i = 0; i < aUser.size(); i++) {
            builder.addAUser(CommonProto.protoUser(aUser.get(i).getDbUser(), aUser.get(i).getUData()));
        }
        return builder.build();
    }

    public boolean isHost(UserInfo user) {
        return user.getId() == hostId;
    }

    public void sendBattleInfo(String battleKey) {
        if (aBot.isEmpty()) {
            for (int i = aUser.size() - 1; i >= 0; i--) {
                UserInfo user = UserMonitor.getUser(aUser.get(i).getId());
                if (user != null && user.getTeam() != null && user.getTeam().id == id)
                    Util.sendProtoData(Online.getChannel(aUser.get(i).getId()), CfgServer.getBattleServer(aUser.get(i).isVN(), battleKey), IAction.SERVER_BATTLE_INFO, System.currentTimeMillis());
                else aUser.remove(i);
            }
        } else {
            for (int i = 0; i < aBot.size(); i++) {
                Util.sendProtoData(aBot.get(i).getChannel(), CfgServer.getBattleServer(false, battleKey), IAction.SERVER_BATTLE_INFO, System.currentTimeMillis());
            }
        }
    }

    public void sendBattleInfoArena(String battleKey) {
        getLogger().warn("ARENA SEND INFOR NEW ---auser.size--->" + aUser.size());
        for (int i = aUser.size() - 1; i >= 0; i--) {
            UserInfo user = UserMonitor.getUser(aUser.get(i).getId());
            if (user != null && user.getTeam() != null && user.getTeam().id == id) {
                Util.sendProtoData(Online.getChannel(aUser.get(i).getId()), CfgServer.getBattleServer(aUser.get(i).isVN(), battleKey), IAction.SERVER_BATTLE_INFO, System.currentTimeMillis());
                getLogger().warn("ARENA_SEND_BATTLE_INFOR--index:" + i + "->" + user.getId() + "|" + user.getUsername() + "|" + user.getDbUser().getName());
            } else {
                if (user == null) {
                    getLogger().warn("ARENA SEND INFOR NEW ---user null--->");
                } else if (user.getTeam() == null) {
                    getLogger().warn("ARENA SEND INFOR NEW ---user team null--->");
                } else if (user.getTeam().id != id) {
                    getLogger().warn("ARENA SEND INFOR NEW ---user team diff--user.getTeam().id->" + user.getTeam().id + "| id-->" + id);
                } else {
                    getLogger().warn("ARENA SEND INFOR NEW ERROR SAO LAI VAO DAY NHI");
                }
                aUser.remove(i);
            }
        }
    }

    protected Logger getLogger() {
        return slib_Logger.root();
    }

    public void sendMessage(int service, AbstractMessage data) {
        for (int i = aUser.size() - 1; i >= 0; i--) {
            TeamObject uTeam = aUser.get(i).getTeam();
            if (uTeam != null && uTeam.id == id)
                Util.sendProtoData(Online.getChannel(aUser.get(i).getId()), data, service, System.currentTimeMillis());
            else aUser.remove(i);
        }
    }

    public void sendMessage(int service, AbstractMessage data, long ignoreId) {
        for (UserInfo user : aUser) {
            if (user.getId() != ignoreId) {
                Util.sendProtoData(Online.getChannel(user.getId()), data, service, System.currentTimeMillis());
            }
        }
    }

    public String isEnoughEnergy(int requiredEnergy) {
        for (UserInfo user : aUser) {
            if (CfgEnergy.getMyEnergy(user).energy < requiredEnergy) {
                return user.getDbUser().getName();
            }
        }
        return "";
    }

    public String isEnoughNumberAtk(int mode, int mapId, int maxNumberAtk) {
        if (mode == MapLevel.MODE_INSANE) {
            for (UserInfo user : aUser) {
                int numberAtk = user.getUData().getMap().getNumberAtk(mapId % 1000);
                if (numberAtk >= maxNumberAtk) {
                    return user.getDbUser().getName();
                }
            }
        }
        return "";
    }

    public void setLastMatchPlayer(List<Long> aPlayerId, int matchType) {
        List<Long> oppIds = new ArrayList<>();

        aPlayerId.forEach(playerId -> {
            boolean oppTeam = true;
            for (UserInfo user : aUser) {
                if (user.getId() == playerId) oppTeam = false;
            }
            if (oppTeam) oppIds.add(playerId);
        });
        userReady.clear();

        for (UserInfo user : aUser) {
            user.countMatch(oppIds);
        }

        JSONArray arr = new JSONArray();
        for (UserInfo user : aUser) arr.add(user.getId());
        for (UserInfo user : aUser)
            Actions.save(user.getDbUser(), Actions.GUSER, Actions.DBATTLE, String.format("{\"type\":%s,\"id\":%s,\"my\":%s,\"opp\":%s}", matchType, id, arr.toString(), new Gson().toJson(oppIds)));
    }

    public String isEnoughMapLevel(int mode, int mapId) {
        for (UserInfo user : aUser) {
            int curMap = user.getUData().getMap().getCurMap(mode);
            if (curMap + 1 < mapId % 1000) {
                return user.getDbUser().getName();
            }
        }
        return "";
    }

    public int getMaxTrophy() {
        int maxTrophy = 0;
        List<UserInfo> tmpUser = new ArrayList<>(aUser);
        for (UserInfo user : tmpUser) {
            if (maxTrophy < user.getDbUser().getTrophy()) maxTrophy = user.getDbUser().getTrophy();
        }
        return maxTrophy;
    }

    public int getRank() {
        int maxRank = 0;
        List<UserInfo> tmpUser = new ArrayList<>(aUser);
        for (UserInfo user : tmpUser) {
            if (maxRank < user.getDbUser().getRankId()) maxRank = user.getDbUser().getRankId();
        }
        return maxRank >= 31 ? 31 : maxRank;
    }

    public CacheBattle getCacheValue(int battleId) {
        List<CachePlayer> aCache = new ArrayList<>();
        for (UserInfo user : aUser) {
            aCache.add(new CachePlayer(user, 0));
        }
        String key = String.valueOf(CfgServer.SERVER_ID) + "_" + type + "_" + new Random().nextInt(100);
        for (UserInfo user : aUser) {
            key += "_" + user.getId();
        }
        CacheBattle cache = new CacheBattle(aCache, map, key, type);
        cache.setBattleId(battleId);
        cache.setHostPlayerId(hostId);
        cache.setServerId(CfgServer.getServerIdForBattle());
        return cache;
    }

    public List<CachePlayer> getCachePlayer(int team) {
        List<CachePlayer> aCache = new ArrayList<>();
        List<UserInfo> tmpUser = new ArrayList<>(aUser);
        for (UserInfo user : tmpUser) {
            aCache.add(new CachePlayer(user, team));
        }
        return aCache;
    }

    public boolean justMatch(List<TeamObject> aTeam) {
        List<Long> oppIds = new ArrayList<>();
        for (TeamObject teamObject : aTeam) {
            for (UserInfo userInfo : teamObject.aUser) oppIds.add(userInfo.getId());
        }
        for (UserInfo user : aUser) {
            if (user.justMatch(oppIds)) return true;
        }
        return false;
    }

    @Override
    public void doExpireTurn(int turnId) {
        if (System.currentTimeMillis() - lastAction > 30 * 60 * 1000) {
            TeamMatcher.removeTeam(id);
            slib_Logger.root().info("remove team idle " + id);
        } else {
            try {
                Xerver.timer(this, turnId, 60);
            } catch (Exception ex) {
                slib_Logger.root().warn(Xerver.mCounter.get(3) + " " + Util.exToString(ex));
            }
        }
    }

}
