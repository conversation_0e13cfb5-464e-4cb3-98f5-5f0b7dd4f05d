package com.bem.matcher;

import com.bem.boom.CacheBattle;
import com.bem.boom.object.CachePlayer;
import com.bem.config.CfgBot;
import com.bem.config.CfgCommon;
import com.bem.config.CfgServer;
import com.bem.dao.AuthDAO;
import com.bem.dao.mapping.AuthUserEntity;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.monitor.BotMonitor;
import com.bem.object.BotBomb;
import com.bem.object.UserInfo;
import com.bem.util.Util;
import com.cache.MCache;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * Created by vieth_000 on 9/7/2016.
 */
public class SoloQueue extends SearchQueue {

    int gameType;
    RankGroup[] aGroup;

    public SoloQueue(int gameType) {
        super();
        this.gameType = gameType;
        aGroup = new RankGroup[CfgCommon.config.rankTrophyGroup.length + 1];
        for (int i = 0; i < aGroup.length; i++) {
            aGroup[i] = new RankGroup(i);
        }
    }

    @Override
    public synchronized int doAction(int action, TeamObject team) {
        try {
            switch (action) {
                case ACTION_ADD:
                    aTeam.add(team);
                    break;
                case ACTION_REMOVE:
                    aTeam.remove(team);
                    break;
                case ACTION_MATCH:
                    checkQueue();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
        return 0;
    }

    @Override
    void checkQueue() {
        if (isEmptyQueue()) {
            return;
        }
        resetGroup();
        for (int i = 0; i < getTeamSize(); i++) {
            aGroup[getRankIndex(aTeam.get(i))].add(aTeam.get(i));
        }
        aGroup[0].lowRank();
        aGroup[1].highRank();
    }

    void resetGroup() {
        for (int i = 0; i < aGroup.length; i++) {
            aGroup[i].reset();
        }
    }

    void testBot() {
        while (true) {
            List<BotBomb> aBot = BotMonitor.getBot(2);
            if (aBot != null && aBot.size() == 2) {
                AuthDAO aDAO = new AuthDAO();
                AuthUserEntity authUser = aDAO.getUserByUsername("nhdang", 1);
                UserEntity uEntity = aDAO.getLoginUserEntity(authUser, authUser.getCp(), 1);
                UserInfo user = new UserInfo(null, authUser, uEntity);
                user.loadInventory();
                //
                TeamObject team = new TeamObject(TeamObject.RANK, CfgBot.reloadBot(aBot.get(0).getUser(), user));
                team.aBot = new ArrayList<>();
                team.aBot.add(aBot.get(0));
                //                ChUtil.set(aBot.get(0).getChannel(), Constans.KEY_USER_TEAM, team);

                TeamObject team1 = new TeamObject(TeamObject.RANK, CfgBot.reloadBot(aBot.get(1).getUser(), user));
                team1.aBot = new ArrayList<>();
                team1.aBot.add(aBot.get(1));
                //                ChUtil.set(aBot.get(1).getChannel(), Constans.KEY_USER_TEAM, team1);

                List<TeamObject> aTeam = new ArrayList<>();
                aTeam.add(team);
                aTeam.add(team1);
                //                createTableOld(aTeam);
            } else {
                break;
            }
        }
    }

    TeamObject createFakeTeam(int numberBot, List<UserInfo> aUser) {
        List<BotBomb> aBot = BotMonitor.getBot(numberBot);
        if (aBot != null && aBot.size() == numberBot) {
            TeamObject team = new TeamObject(TeamObject.RANK, CfgBot.reloadBot(aBot.get(0).getUser(), aUser.get(0)));
            team.aBot = aBot;
            //            ChUtil.set(aBot.get(0).getChannel(), Constans.KEY_USER_TEAM, team);
            if (numberBot == 2) {
                team.addUser(CfgBot.reloadBot(aBot.get(1).getUser(), aUser.get(1)));
                //                ChUtil.set(aBot.get(1).getChannel(), Constans.KEY_USER_TEAM, team);
            }
            return team;
        }
        return null;
    }

    void createTableOld(List<TeamObject> tmp, int numberPlayer) {
        List<CachePlayer> aCachePlayer = new ArrayList<>();
        boolean cancel = false;
        for (int i = tmp.size() - 1; i >= 0; i--) {
            List<CachePlayer> aCache = tmp.get(i).getCachePlayer(0);
            if (aCache.size() < numberPlayer) {
                if (aCache.isEmpty()) aTeam.remove(tmp.get(i));
                cancel = true;
            }
            aCachePlayer.addAll(aCache);
        }
        if (cancel) return;
        List<Long> aPlayerId = new ArrayList<>();
        for (int i = 0; i < aCachePlayer.size(); i++) {
            if (i < aCachePlayer.size() / 2) aCachePlayer.get(i).setTeam(0);
            else aCachePlayer.get(i).setTeam(1);
            aPlayerId.add(aCachePlayer.get(i).getUserId());
        }
        //        if (aCachePlayer.size() > 4) {
        //            String debug = "";
        //            for (int i = 0; i < tmp.size(); i++) {
        //                debug += "|" + i + ":";
        //                for (int j = 0; j < tmp.get(i).aUser.size(); j++) {
        //                    debug += "-" + tmp.get(i).aUser.get(j).getUsername();
        //                }
        //            }
        //            getLogger().warn(debug);
        //        }
        BattleDataEntity battle = prepareBattle(aCachePlayer);
        //        BattleDataEntity battle = prepareBattleZombie(aCachePlayer);

        if (battle != null) {
            CacheBattle cacheBattle = new CacheBattle(aCachePlayer, 0, getKey(aCachePlayer), gameType);
            cacheBattle.setBattleId(battle.getId());
            cacheBattle.setMode(CacheBattle.MODE_GLOBAL);
            MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
            for (int i = tmp.size() - 1; i >= 0; i--) {
                tmp.get(i).status = TeamObject.STATUS_PLAY;
                tmp.get(i).sendBattleInfo(cacheBattle.getKey());
                tmp.get(i).setLastMatchPlayer(aPlayerId, SOLO_MATCH);
                aTeam.remove(tmp.get(i));
            }
        }
    }

    void createTableOld(List<TeamObject> aloneTeam, TeamObject doubleTeam) {
        List<CachePlayer> aCachePlayer = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            List<CachePlayer> aCache = aloneTeam.get(i).getCachePlayer(0);
            if (aCache.isEmpty()) aTeam.remove(aloneTeam.get(i));
            aCachePlayer.addAll(aCache);
        }
        List<CachePlayer> aCache = doubleTeam.getCachePlayer(1);
        if (aCache.isEmpty()) aTeam.remove(doubleTeam);
        aCachePlayer.addAll(aCache);
        if (aCachePlayer.size() < 4) return;
        List<Long> aPlayerId = new ArrayList<>();
        for (int i = 0; i < aCachePlayer.size(); i++) {
            aPlayerId.add(aCachePlayer.get(i).getUserId());
        }
        //        if (aCachePlayer.size() > 4) {
        //            String debug = "alone->";
        //            for (int i = 0; i < 2; i++) {
        //                debug += "|" + i + ":";
        //                for (int j = 0; j < aloneTeam.get(i).aUser.size(); j++) {
        //                    debug += "-" + aloneTeam.get(i).aUser.get(j).getUsername();
        //                }
        //            }
        //            debug += "double->";
        //            for (int j = 0; j < doubleTeam.aUser.size(); j++) {
        //                debug += "-" + doubleTeam.aUser.get(j).getUsername();
        //            }
        //            getLogger().warn(debug);
        //        }
        BattleDataEntity battle = prepareBattle(aCachePlayer);
        //        BattleDataEntity battle = prepareBattleZombie(aCachePlayer);

        if (battle != null) {
            CacheBattle cacheBattle = new CacheBattle(aCachePlayer, 0, getKey(aCachePlayer), gameType);
            cacheBattle.setBattleId(battle.getId());
            MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
            for (int i = 0; i < 2; i++) {
                aloneTeam.get(i).status = TeamObject.STATUS_PLAY;
                aloneTeam.get(i).sendBattleInfo(cacheBattle.getKey());
                aloneTeam.get(i).setLastMatchPlayer(aPlayerId, SOLO_MATCH);
                aTeam.remove(aloneTeam.get(i));
            }

            doubleTeam.status = TeamObject.STATUS_PLAY;
            doubleTeam.sendBattleInfo(cacheBattle.getKey());
            doubleTeam.setLastMatchPlayer(aPlayerId, SOLO_MATCH);
            aTeam.remove(doubleTeam);
        }
    }

    int resetLoop(List<TeamObject> aloneTeam, List<TeamObject> doubleTeam) {
        aloneTeam.clear();
        doubleTeam.clear();
        return 0;
    }

    boolean waitEnough(TeamObject team) {
        if (team.getMaxTrophy() >= 800)
            return System.currentTimeMillis() - team.timeSearch >= CfgBot.config.timeWait * 1000 * 2;
        else return System.currentTimeMillis() - team.timeSearch >= CfgBot.config.timeWait * 1000;
    }

    int getRankIndex(TeamObject team) {
        int trophy = team.getMaxTrophy();
        for (int i = CfgCommon.config.rankTrophyGroup.length - 1; i >= 0; i--) {
            if (trophy > CfgCommon.config.rankTrophyGroup[i]) {
                return i + 1;
            }
        }
        return 0;
    }

    //region prepare battle
    String getKey(List<CachePlayer> aCachePlayer) {
        String key = String.valueOf(CfgServer.SERVER_ID) + "_" + TeamObject.RANK + "_" + new Random().nextInt(100);
        for (int i = 0; i < aCachePlayer.size(); i++) {
            key += "_" + aCachePlayer.get(i).getUserId();
        }
        return key;
    }

    BattleDataEntity prepareBattle(List<CachePlayer> aCachePlayer) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(gameType);
            session.save(battle);

            for (CachePlayer cachePlayer : aCachePlayer) {
                session.save(new BattleUserEntity(battle.getId(), cachePlayer.getUserId(), gameType));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
    //endregion

    //
    class RankGroup {
        int rankIndex;
        List<TeamObject> aloneTeam = new ArrayList<>();
        List<TeamObject> doubleTeam = new ArrayList<>();

        public RankGroup(int rankIndex) {
            this.rankIndex = rankIndex;
        }

        void reset() {
            aloneTeam.clear();
            doubleTeam.clear();
        }

        void add(TeamObject team) {
            if (team.aUser.size() == 1) aloneTeam.add(team);
            else doubleTeam.add(team);
            //
            //            if (rankIndex == 0) {
            //                if (aloneTeam.size() == 4) {
            //                    createTableOld(aloneTeam, 1);
            //                    return true;
            //                } else if (doubleTeam.size() == 2) {
            //                    createTableOld(doubleTeam, 2);
            //                    return true;
            //                } else if (aloneTeam.size() >= 2 && doubleTeam.size() == 1) {
            //                    createTableOld(aloneTeam, doubleTeam.get(0));
            //                    return true;
            //                } else if (aloneTeam.size() >= 2 && waitEnough(aloneTeam.get(0))) {
            //                    createTableOld(aloneTeam.subList(0, 2), 1);
            //                    return true;
            //                }
            //                return false;
            //            }
            //            return highRank();
        }

        void lowRank() {
            //            while (doubleTeam.size() >= 2) {
            //                createTableOld(Arrays.asList(doubleTeam.get(0), doubleTeam.get(1)), 2);
            //                removeList(doubleTeam, 2);
            //            }
            //            if (doubleTeam.size() == 1 && aloneTeam.size() >= 2) {
            //                createTableOld(Arrays.asList(aloneTeam.get(0), aloneTeam.get(1)), doubleTeam.get(0));
            //                removeList(aloneTeam, 2);
            //                removeList(doubleTeam, 1);
            //            }
            while (aloneTeam.size() >= 2) {
                createTableOld(Arrays.asList(aloneTeam.get(0), aloneTeam.get(1)), 1);
                removeList(aloneTeam, 2);
            }
            checkLongWait();
        }

        boolean highRank() {
            checkLongWaitHighRank();
            // double team
            //            int index = 0;
            //            while (index < doubleTeam.size() - 1) {
            //                int addIndex = 1;
            //                int rank = doubleTeam.get(index).getRank();
            //                for (int i = index + 1; i < doubleTeam.size(); i++) {
            //                    if (Math.abs(rank - doubleTeam.get(i).getRank()) <= CfgBot.config.matchRank
            //                            && !doubleTeam.get(index).justMatch(Arrays.asList(doubleTeam.get(i)))) {
            //                        createTableOld(Arrays.asList(doubleTeam.get(index), doubleTeam.get(i)), 2);
            //                        doubleTeam.remove(i);
            //                        doubleTeam.remove(index);
            //                        addIndex = 0;
            //                        break;
            //                    }
            //                }
            //                index += addIndex;
            //            }

            // alone team
            {
                List<Integer> useIndex = new ArrayList<>();
                List<List<TeamObject>> sameRank = new ArrayList<>();
                int index = 0;
                while (index < aloneTeam.size()) {
                    if (!useIndex.contains(index)) {
                        int rank = aloneTeam.get(index).getRank();
                        for (int i = index + 1; i < aloneTeam.size(); i++) {
                            if (!useIndex.contains(i) && Math.abs(rank - aloneTeam.get(i).getRank()) <= CfgBot.config.matchRank && !aloneTeam.get(index).justMatch(Arrays.asList(aloneTeam.get(i)))) {
                                useIndex.add(index);
                                useIndex.add(i);
                                sameRank.add(Arrays.asList(aloneTeam.get(index), aloneTeam.get(i)));
                                break;
                            }
                        }
                    }
                    index++;
                }
                while (!sameRank.isEmpty()) {
                    List<TeamObject> sameRank1 = sameRank.get(0);
                    createTableOld(Arrays.asList(sameRank1.get(0), sameRank1.get(1)), 1);
                    for (TeamObject teamObject : sameRank1) aloneTeam.remove(teamObject);
                    sameRank.remove(0);
                }
            }

            // double + alone
            //            index = 0;
            //            while (index < doubleTeam.size()) {
            //                TeamObject team = doubleTeam.get(0);
            //                int[] aloneIndex = {-1, -1};
            //                for (int i = 0; i < aloneTeam.size(); i++) {
            //                    boolean isUse = false;
            //                    if (aloneIndex[0] == -1 && Math.abs(team.aUser.get(0).getDbUser().getRankId() - aloneTeam.get(i).getRank()) <= CfgBot.config.matchRank
            //                            && !team.justMatch(Arrays.asList(aloneTeam.get(i)))) {
            //                        aloneIndex[0] = i;
            //                        isUse = true;
            //                    }
            //                    if (!isUse && aloneIndex[1] == -1 && Math.abs(team.aUser.get(1).getDbUser().getRankId() - aloneTeam.get(i).getRank()) <= CfgBot.config.matchRank
            //                            && !team.justMatch(Arrays.asList(aloneTeam.get(i)))) {
            //                        aloneIndex[1] = i;
            //                    }
            //                }
            //                if (aloneIndex[0] >= 0 && aloneIndex[1] >= 0) {
            //                    TeamObject aloneTeam1 = aloneTeam.get(aloneIndex[0]);
            //                    TeamObject aloneTeam2 = aloneTeam.get(aloneIndex[1]);
            //                    createTableOld(Arrays.asList(aloneTeam1, aloneTeam2), team);
            //                    doubleTeam.remove(team);
            //                    aloneTeam.remove(aloneTeam1);
            //                    aloneTeam.remove(aloneTeam2);
            //                } else index++;
            //            }
            return false;
        }

        void checkLongWaitHighRank() {
            //            while (!doubleTeam.isEmpty()) {
            //                if (waitEnough(doubleTeam.get(0))) {
            //                    if (doubleTeam.size() > 1) {
            //                        createTableOld(Arrays.asList(doubleTeam.get(0), doubleTeam.get(1)), 2);
            //                        removeList(doubleTeam, 2);
            //                    } else if (aloneTeam.size() > 2) {
            //                        createTableOld(aloneTeam, doubleTeam.get(0));
            //                        removeList(aloneTeam, 2);
            //                        removeList(doubleTeam, 1);
            //                    } else break;
            //                } else break;
            //            }
            while (aloneTeam.size() >= 2) {
                if (waitEnough(aloneTeam.get(0))) {
                    if (aloneTeam.size() >= 2) {
                        createTableOld(Arrays.asList(aloneTeam.get(0), aloneTeam.get(1)), 1);
                        removeList(aloneTeam, 2);
                    } else break;
                } else break;
            }
        }

        void checkLongWait() {
            // cac truong hop can bot
            if (!aloneTeam.isEmpty() && !doubleTeam.isEmpty()) {
                if (waitEnough(aloneTeam.get(0)) || waitEnough(doubleTeam.get(0))) {
                    try {
                        TeamObject fakeTeam = createFakeTeam(1, Arrays.asList(doubleTeam.get(0).aUser.get(0)));
                        if (fakeTeam != null) {
                            createTableOld(Arrays.asList(aloneTeam.get(0), fakeTeam), doubleTeam.get(0));
                            aloneTeam.remove(0);
                            doubleTeam.remove(0);
                        }
                    } catch (Exception ex) {
                        Logs.error(Util.exToString(ex));
                        if (doubleTeam.get(0).aUser.isEmpty()) doubleTeam.remove(0);
                    }
                }
            } else if (!aloneTeam.isEmpty() && waitEnough(aloneTeam.get(0))) {
                if (aloneTeam.size() == 1 && aloneTeam.get(0).aUser.get(0).getDbUser().getRankId() >= CfgCommon.config.limitRank) {

                } else {
                    if (aloneTeam.size() == 1 || aloneTeam.size() == 3) {
                        try {
                            TeamObject fakeTeam = createFakeTeam(1, Arrays.asList(aloneTeam.get(0).aUser.get(0)));
                            if (fakeTeam != null) {
                                aloneTeam.add(fakeTeam);
                                createTableOld(aloneTeam, 1);
                                aloneTeam.clear();
                            }
                        } catch (Exception ex) {
                            Logs.error(Util.exToString(ex));
                            if (aloneTeam.get(0).aUser.isEmpty()) aloneTeam.remove(0);
                        }
                    } else if (aloneTeam.size() == 2) {
                        createTableOld(aloneTeam, 1);
                        aloneTeam.clear();
                    }
                }
            } else if (!doubleTeam.isEmpty() && waitEnough(doubleTeam.get(0))) {
                if (doubleTeam.get(0).aUser.isEmpty()) doubleTeam.remove(0);
                else {
                    try {
                        if (doubleTeam.get(0).aUser.get(0).getDbUser().getRankId() >= CfgCommon.config.limitRank &&
                                doubleTeam.get(0).aUser.get(1).getDbUser().getRankId() >= CfgCommon.config.limitRank) {
                        } else {
                            TeamObject fakeTeam = createFakeTeam(2, Arrays.asList(doubleTeam.get(0).aUser.get(0), doubleTeam.get(0).aUser.get(1)));
                            if (fakeTeam != null) {
                                createTableOld(Arrays.asList(doubleTeam.get(0), fakeTeam), 2);
                                doubleTeam.remove(0);
                            }
                        }
                    } catch (Exception ex) {
                        Logs.error(Util.exToString(ex));
                        if (doubleTeam.get(0).aUser.isEmpty()) doubleTeam.remove(0);
                    }
                }
            }
        }

        void removeList(List<TeamObject> aTeam, int numberRemove) {
            for (int i = 0; i < numberRemove; i++) {
                aTeam.remove(0);
            }
        }

    }
}