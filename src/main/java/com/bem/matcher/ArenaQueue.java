package com.bem.matcher;

import com.bem.boom.CacheBattle;
import com.bem.boom.object.CachePlayer;
import com.bem.config.CfgCommon;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.monitor.Online;
import com.bem.util.Util;
import com.cache.MCache;
import com.handler.game.Arena;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * Created by vieth_000 on 9/7/2016.
 */
public class ArenaQueue extends SearchQueue {

    int gameType;
    RankGroup[] aGroup;
    int serverId;

    public ArenaQueue(int gameType,int serverId) {
        super();
        this.gameType = gameType;
        aGroup = new RankGroup[CfgCommon.config.rankTrophyGroup.length + 1];
        for (int i = 0; i < aGroup.length; i++) {
            aGroup[i] = new RankGroup(i);
        }
    }

    @Override
    public synchronized int doAction(int action, TeamObject team) {
        try {
            switch (action) {
                case ACTION_ADD:
                    aTeam.add(team);
                    break;
                case ACTION_REMOVE:
                    aTeam.remove(team);
                    break;
                case ACTION_MATCH:
                    checkQueue();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
        return 0;
    }

    @Override
    void checkQueue() {
        if (isEmptyQueue()) {
            return;
        }
        resetGroup();
        for (int i = 0; i < getTeamSize(); i++) {
            aGroup[getRankIndex(aTeam.get(i))].add(aTeam.get(i));
        }
        aGroup[0].lowRank2(serverId);
//        aGroup[1].highRank();
    }

    void resetGroup() {
        for (int i = 0; i < aGroup.length; i++) {
            aGroup[i].reset();
        }
    }

    public  void removeQueueAllArena() {
        for (int i = 0; i <aTeam.size() ; i++) {
            try {
                Util.sendProtoData(Online.getChannel(aTeam.get(i).aUser.get(0).getId()), null, IAction.ARENA_OUT_BATTLE, System.currentTimeMillis());
                aTeam.get(i).status = TeamObject.STATUS_NONE;
            }catch (Exception ex){

            }
        }
    }

    void createTableOld(List<TeamObject> tmp, int numberPlayer,int serverId) {
        List<CachePlayer> aCachePlayer = new ArrayList<>();
        boolean cancel = false;
        for (int i = tmp.size() - 1; i >= 0; i--) {
            List<CachePlayer> aCache = tmp.get(i).getCachePlayer(0);
            if (aCache.size() < numberPlayer) {
                if (aCache.isEmpty()) aTeam.remove(tmp.get(i));
                cancel = true;
            }
            aCachePlayer.addAll(aCache);
        }
        if (cancel) return;
            List<Long> aPlayerId = new ArrayList<>();
            for (int i = 0; i < aCachePlayer.size(); i++) {
                if (i < aCachePlayer.size() / 2) aCachePlayer.get(i).setTeam(0);
                else aCachePlayer.get(i).setTeam(1);
                aPlayerId.add(aCachePlayer.get(i).getUserId());
            }
            BattleDataEntity battle = prepareBattle(aCachePlayer);
            if (battle != null) {
                CacheBattle cacheBattle = new CacheBattle(aCachePlayer, 0, getKey(aCachePlayer), gameType);
                cacheBattle.setBattleId(battle.getId());
                cacheBattle.setLevelIndex(Arena.getRound(serverId));
                MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
                for (int i = tmp.size() - 1; i >= 0; i--) {
                    tmp.get(i).status = TeamObject.STATUS_PLAY;
                    tmp.get(i).sendBattleInfo(cacheBattle.getKey());
                    tmp.get(i).setLastMatchPlayer(aPlayerId, ARENA_MATCH);
                    aTeam.remove(tmp.get(i));
                }
            }
    }


    int getRankIndex(TeamObject team) {
        return 0;
    }

    //region prepare battle
    String getKey(List<CachePlayer> aCachePlayer) {
        String key = String.valueOf(serverId) + "_" + TeamObject.ARENA + "_" + new Random().nextInt(100);
        for (int i = 0; i < aCachePlayer.size(); i++) {
            key += "_" + aCachePlayer.get(i).getUserId();
        }
        return key;
    }

    BattleDataEntity prepareBattle(List<CachePlayer> aCachePlayer) {
        Session session = null;
        try {
            gameType = TeamObject.ARENA;
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(gameType);
            session.save(battle);

            for (CachePlayer cachePlayer : aCachePlayer) {
                session.save(new BattleUserEntity(battle.getId(), cachePlayer.getUserId(), gameType));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
    //endregion

    //
    class RankGroup {
        int rankIndex;
        List<TeamObject> aloneTeam = new ArrayList<>();
        List<TeamObject> doubleTeam = new ArrayList<>();

        public RankGroup(int rankIndex) {
            this.rankIndex = rankIndex;
        }

        void reset() {
            aloneTeam.clear();
            doubleTeam.clear();
        }

        void add(TeamObject team) {
            if (team.aUser.size() == 1) aloneTeam.add(team);
            else doubleTeam.add(team);
        }

//        void lowRank() {
//            while (aloneTeam.size() >= 4) {
//                createTableOld(Arrays.asList(aloneTeam.get(0), aloneTeam.get(1), aloneTeam.get(2), aloneTeam.get(3)), 1);
//                removeList(aloneTeam, 4);
//            }
////            checkLongWait();
//        }

        void lowRank2(int serverId) {
            while (aloneTeam.size() >= 2) {
                createTableOld(Arrays.asList(aloneTeam.get(0), aloneTeam.get(1)), 1,serverId);
                removeList(aloneTeam, 2);
            }
//            checkLongWait();
        }


        void removeList(List<TeamObject> aTeam, int numberRemove) {
            for (int i = 0; i < numberRemove; i++) {
                aTeam.remove(0);
            }
        }

    }
}