package com.bem.matcher;

import com.bem.config.CfgServer;
import com.k2tek.Config;
import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by vieth_000 on 9/7/2016.
 */
public class TeamMatcher {
    static private Map<Integer, TeamObject> mTeam = new HashMap<Integer, TeamObject>();

    static public Map<Integer, SearchQueue> availableQueue = new HashMap<Integer, SearchQueue>() {{
        put(TeamObject.RANK, new zombieQueue(TeamObject.RANK));
        put(TeamObject.TRAIN, new TrainQueue(TeamObject.TRAIN));
        put(TeamObject.SOLO, new SoloQueue(TeamObject.RANK));
        put(TeamObject.GLOBAL, new GlobalQueue(TeamObject.RANK));
//        for (int i = 0; i < Config.lstServerId.size(); i++) {
//            put(TeamObject.ARENA, new ArenaQueue(TeamObject.ARENA,Config.lstServerId.get(i)));
//        }
//        put(TeamObject.ARENA, new ArenaQueue(TeamObject.ARENA));
//        put(TeamObject.GLOBAL, new GlobalQueue(TeamObject.RANK));
//        put(TeamObject.ARENA, new ArenaQueue(TeamObject.ARENA));
    }};

    static public Map<Integer, SearchQueue> mQueue = new HashMap<Integer, SearchQueue>() {
        {
            if (CfgServer.SERVER_ID == 2) put(TeamObject.RANK, new TeamQueueNoRank(TeamObject.RANK));
            else put(TeamObject.RANK, new zombieQueue(TeamObject.RANK));
//            else put(TeamObject.RANK, new TeamQueueNew(TeamObject.RANK));

            put(TeamObject.TRAIN, new TrainQueue(TeamObject.TRAIN));

//            if (CfgServer.isTest()) {
            put(TeamObject.RANK, new GlobalQueue(TeamObject.RANK));
//                put(TeamObject.ARENA, new ArenaQueue(TeamObject.ARENA));
//            }
//            for (int i = 0; i < Config.lstServerId.size(); i++) {
//                put(TeamObject.ARENA, new ArenaQueue(TeamObject.ARENA, Config.lstServerId.get(i)));
//            }
        }
    };

    public static JSONObject monitorValue() {
        JSONObject obj = new JSONObject();
//        obj.put("solo", mQueue.get(TeamObject.SOLO).getTeamSize());
        obj.put("team", mQueue.get(TeamObject.RANK).getTeamSize());
        obj.put("team", mQueue.get(TeamObject.TRAIN).getTeamSize());
        return obj;
    }

    public static synchronized void removeTeam(int teamId) {
        mTeam.remove(teamId);
    }

    public static synchronized boolean addTeam(TeamObject team) {
        if (mTeam.containsValue(team)) {
            return false;
        }
        mTeam.put(team.id, team);
        return true;
    }

    public static TeamObject getTeam(int teamId) {
        return mTeam.get(teamId);
    }

    public static void addQueue(TeamObject team) {
        team.status = TeamObject.STATUS_SEARCH;
        team.timeSearch = System.currentTimeMillis();
        mQueue.get(team.type).addQueue(team);

//        if (mTeam.containsKey(team.id)) {
//            team.status = TeamObject.STATUS_SEARCH;
//            team.timeSearch = System.currentTimeMillis();
//            mQueue.get(team.type).addQueue(team);
//        }
    }

    public static int removeQueue(TeamObject team) {
        mQueue.get(team.type).removeQueue(team);
        return 0;
//        if (mQueue.get(team.type).containQueue(team)) {
//            if (team.status == TeamObject.STATUS_SEARCH) {
//                team.status = TeamObject.STATUS_NONE;
////                mTeam.put(team.id, team);
//                mQueue.get(team.type).reMoveQueue(team);
//                return 0;
//            } else {
//                return 1;
//            }
//        } else {
//            return 2;
//        }
    }

    public static void removeQueueAll() {
        ArenaQueue arena = (ArenaQueue) mQueue.get(TeamObject.ARENA);
        arena.removeQueueAllArena();
    }
}
