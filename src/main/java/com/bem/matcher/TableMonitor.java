package com.bem.matcher;

import com.bem.*;
import com.bem.boom.CacheBattle;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by vieth_000 on 9/7/2016.
 */
public class TableMonitor {
    static private Map<Integer, AbstractTable> mInstanceTable = new HashMap<Integer, AbstractTable>() {{
        put(TeamObject.BOSS, new TableBossBoom());
        put(TeamObject.RANK, new TableTeamBoom());
        put(TeamObject.TRAIN, new TableTrain());
        put(TeamObject.GAME_HUNTING, new TableGoldHunting());
        put(TeamObject.GAME_MATERIAL, new TableMaterial());
        put(TeamObject.GAME_BOSS, new TableBossGlobal());
        put(TeamObject.GAME_CLAN_BATTLE, new TableClanBattlePlayer());
        put(TeamObject.GAME_CLAN_BATTLE_MONSTER, new TableClanBattleMonster());
        put(TeamObject.ZOMBIE, new TableZombieBoom());
        put(TeamObject.ARENA, new TableArena());

    }};

    static private Map<String, AbstractTable> mTable = new HashMap<String, AbstractTable>();

    public static synchronized AbstractTable getTable(String key, CacheBattle cBattle) {
        AbstractTable table = mTable.get(key);
        if (table == null && mInstanceTable.containsKey(cBattle.getType())) {
//            System.out.println("cBattle.getType()------------->"+cBattle.getType());
            table = mInstanceTable.get(cBattle.getType()).getNewInstance(cBattle);
            if (table != null) mTable.put(key, table);
        }
        return table;
    }

    public static synchronized void removeTable(String key) {
        mTable.remove(key);
    }

    public static synchronized boolean addTable(TeamObject team) {
//        if (mTeam.containsValue(team)) {
//            return false;
//        }
//        mTeam.put(team.id, team);
//        return true;
        return false;
    }

}
