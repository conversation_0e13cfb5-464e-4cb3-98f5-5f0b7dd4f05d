package com.bem.matcher;

import com.bem.object.ITimer;
import com.bem.object.TurnInfor;
import com.bem.util.Util;
import com.k2tek.Xerver;
import com.k2tek.common.slib_Logger;
import io.netty.channel.Channel;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 9/7/2016.
 */
public abstract class SearchQueue implements ITimer {
    static final int ACTION_ADD = 1;
    static final int ACTION_REMOVE = 2;
    static final int ACTION_MATCH = 3;
    static final int NORMAL_MATCH = 0, FORCE_MATCH = 1, ZOMBIE_MATCH = 2;
    static final int SOLO_MATCH = 3, ARENA_MATCH = 4;

    protected List<TeamObject> aTeam = new ArrayList<TeamObject>();

    public SearchQueue() {
        timer();
    }

    public void addQueue(TeamObject team) {
        doAction(ACTION_ADD, team);
    }

    public void removeQueue(TeamObject team) {
        doAction(ACTION_REMOVE, team);
    }

//    public synchronized void reMoveQueue(TeamObject team) {
//        aTeam.remove(team);
//    }

    public synchronized boolean containQueue(TeamObject team) {
        for (int i = aTeam.size() - 1; i >= 0; i--) {
            if (aTeam.get(i) == null) {
                aTeam.remove(i);
            } else if (aTeam.get(i).id == team.id) {
                return true;
            }
        }
        return false;
    }

    public boolean isEmptyQueue() {
        return aTeam.isEmpty();
    }

    public int getTeamSize() {
        return aTeam.size();
    }

    public void doExpireTurn(int turnId) {
        doAction(ACTION_MATCH, null);
        timer();
    }

    abstract void checkQueue();

    public abstract int doAction(int action, TeamObject team);

    void timer() {
        try {
            Xerver.mCounter.get(3).addQueue(new TurnInfor(this, 0, 3));
        } catch (Exception ex) {
            getLogger().warn(Xerver.mCounter.get(3) + " " + Util.exToString(ex));
        }
    }

    protected Logger getLogger() {
        return slib_Logger.root();
    }
}
