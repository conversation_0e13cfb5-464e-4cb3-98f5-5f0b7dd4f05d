package com.bem.matcher;

import com.bem.boom.CacheBattle;
import com.bem.boom.object.CachePlayer;
import com.bem.config.CfgBot;
import com.bem.config.CfgCommon;
import com.bem.config.CfgServer;
import com.bem.dao.AuthDAO;
import com.bem.dao.mapping.AuthUserEntity;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.monitor.BotMonitor;
import com.bem.object.BotBomb;
import com.bem.object.UserInfo;
import com.bem.util.Util;
import com.cache.MCache;
import grep.database.HibernateUtil;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 9/7/2016.
 */
public class TrainQueue extends SearchQueue {

    int gameType;

    public TrainQueue(int gameType) {
        super();
        this.gameType = gameType;
    }

    @Override
    public synchronized int doAction(int action, TeamObject team) {
        try {
            switch (action) {
                case ACTION_ADD:
                    aTeam.add(team);
                    break;
                case ACTION_REMOVE:
                    aTeam.remove(team);
                    break;
                case ACTION_MATCH:
                    checkQueue();
                    break;
            }
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
        return 0;
    }

    @Override
    void checkQueue() {
        if (isEmptyQueue()) {
            return;
        }
        List<TeamObject> aloneTeam = new ArrayList<TeamObject>();
        List<TeamObject> doubleTeam = new ArrayList<TeamObject>();
        int index = 0;
        while (index < getTeamSize()) {
            TeamObject team = aTeam.get(index);
            if (team.aUser.size() == 1) aloneTeam.add(team);
            else doubleTeam.add(team);
            if (aloneTeam.size() == 4) {
                createTableOld(aloneTeam, 1);
                index = resetLoop(aloneTeam, doubleTeam);
            } else if (doubleTeam.size() == 2) {
                createTableOld(doubleTeam, 2);
                index = resetLoop(aloneTeam, doubleTeam);
            } else if (aloneTeam.size() >= 2 && doubleTeam.size() == 1) {
                createTableOld(aloneTeam, doubleTeam.get(0));
                index = resetLoop(aloneTeam, doubleTeam);
                ;
            } else {
                index++;
            }
        }
        // cac truong hop can bot
        if (!aloneTeam.isEmpty() && !doubleTeam.isEmpty()) {
//            if (waitEnough(aloneTeam.get(0)) || waitEnough(doubleTeam.get(0))) {
//                TeamObject fakeTeam = createFakeTeam(1, Arrays.asList(doubleTeam.get(0).aUser.get(0)));
//                if (fakeTeam != null) {
//                    createTableOld(Arrays.asList(aloneTeam.get(0), fakeTeam), doubleTeam.get(0));
//                    aloneTeam.remove(0);
//                    doubleTeam.remove(0);
//                }
//            }
        } else if (!aloneTeam.isEmpty() && waitEnough(aloneTeam.get(0))) {
            if (aloneTeam.size() == 1 && aloneTeam.get(0).aUser.get(0).getDbUser().getRankId() >= CfgCommon.config.limitRank) {

            } else {
                if (aloneTeam.size() == 1 || aloneTeam.size() == 3) {
//                    TeamObject fakeTeam = createFakeTeam(1, Arrays.asList(aloneTeam.get(0).aUser.get(0)));
//                    if (fakeTeam != null) {
//                        aloneTeam.add(fakeTeam);
//                        createTableOld(aloneTeam);
//                        aloneTeam.clear();
//                    }
                } else if (aloneTeam.size() == 2) {
                    createTableOld(aloneTeam, 1);
                    aloneTeam.clear();
                }
            }
        } else if (!doubleTeam.isEmpty() && waitEnough(doubleTeam.get(0))) {
//            if (doubleTeam.get(0).aUser.get(0).getDbUser().getRankId() >= CfgCommon.config.limitRank &&
//                    doubleTeam.get(0).aUser.get(1).getDbUser().getRankId() >= CfgCommon.config.limitRank) {
//            } else {
//                TeamObject fakeTeam = createFakeTeam(2, Arrays.asList(doubleTeam.get(0).aUser.get(0), doubleTeam.get(0).aUser.get(1)));
//                if (fakeTeam != null) {
//                    createTableOld(Arrays.asList(doubleTeam.get(0), fakeTeam));
//                    doubleTeam.remove(0);
//                }
//            }
        }
    }

    void testBot() {
        while (true) {
            List<BotBomb> aBot = BotMonitor.getBot(2);
            if (aBot != null && aBot.size() == 2) {
                AuthDAO aDAO = new AuthDAO();
                AuthUserEntity authUser = aDAO.getUserByUsername("nhdang",1);
                UserEntity uEntity = aDAO.getLoginUserEntity(authUser, authUser.getCp(),1);
                UserInfo user = new UserInfo(null, authUser, uEntity);
                user.loadInventory();
                //
                TeamObject team = new TeamObject(TeamObject.RANK, CfgBot.reloadBot(aBot.get(0).getUser(), user));
                team.aBot = new ArrayList<>();
                team.aBot.add(aBot.get(0));
//                ChUtil.set(aBot.get(0).getChannel(), Constans.KEY_USER_TEAM, team);

                TeamObject team1 = new TeamObject(TeamObject.RANK, CfgBot.reloadBot(aBot.get(1).getUser(), user));
                team1.aBot = new ArrayList<>();
                team1.aBot.add(aBot.get(1));
//                ChUtil.set(aBot.get(1).getChannel(), Constans.KEY_USER_TEAM, team1);

                List<TeamObject> aTeam = new ArrayList<>();
                aTeam.add(team);
                aTeam.add(team1);
//                createTableOld(aTeam);
            } else {
                break;
            }
        }
    }

    TeamObject createFakeTeam(int numberBot, List<UserInfo> aUser) {
        List<BotBomb> aBot = BotMonitor.getBot(numberBot);
        if (aBot != null && aBot.size() == numberBot) {
            TeamObject team = new TeamObject(TeamObject.RANK, CfgBot.reloadBot(aBot.get(0).getUser(), aUser.get(0)));
            team.aBot = aBot;
//            ChUtil.set(aBot.get(0).getChannel(), Constans.KEY_USER_TEAM, team);
            if (numberBot == 2) {
                team.addUser(CfgBot.reloadBot(aBot.get(1).getUser(), aUser.get(1)));
//                ChUtil.set(aBot.get(1).getChannel(), Constans.KEY_USER_TEAM, team);
            }
            return team;
        }
        return null;
    }

    void createTableOld(List<TeamObject> tmp, int numberPlayer) {
        List<CachePlayer> aCachePlayer = new ArrayList<>();
        boolean cancel = false;
        for (int i = tmp.size() - 1; i >= 0; i--) {
            List<CachePlayer> aCache = tmp.get(i).getCachePlayer(0);
            if (aCache.size() < numberPlayer) {
                if (aCache.isEmpty()) aTeam.remove(tmp.get(i));
                cancel = true;
            }
            aCachePlayer.addAll(aCache);
        }
        if (cancel) return;
        for (int i = 0; i < aCachePlayer.size(); i++) {
            if (i < aCachePlayer.size() / 2) aCachePlayer.get(i).setTeam(0);
            else aCachePlayer.get(i).setTeam(1);
        }
//        if (aCachePlayer.size() > 4) {
//            String debug = "";
//            for (int i = 0; i < tmp.size(); i++) {
//                debug += "|" + i + ":";
//                for (int j = 0; j < tmp.get(i).aUser.size(); j++) {
//                    debug += "-" + tmp.get(i).aUser.get(j).getUsername();
//                }
//            }
//            getLogger().warn(debug);
//        }
        BattleDataEntity battle = prepareBattle(aCachePlayer);
        if (battle != null) {
            CacheBattle cacheBattle = new CacheBattle(aCachePlayer, 0, getKey(aCachePlayer), gameType);
            cacheBattle.setBattleId(battle.getId());
            MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
            for (int i = tmp.size() - 1; i >= 0; i--) {
                tmp.get(i).status = TeamObject.STATUS_PLAY;
                tmp.get(i).sendBattleInfo(cacheBattle.getKey());
                aTeam.remove(tmp.get(i));
            }
        }
    }

    void createTableOld(List<TeamObject> aloneTeam, TeamObject doubleTeam) {
        List<CachePlayer> aCachePlayer = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            List<CachePlayer> aCache = aloneTeam.get(i).getCachePlayer(0);
            if (aCache.isEmpty()) aTeam.remove(aloneTeam.get(i));
            aCachePlayer.addAll(aCache);
        }
        List<CachePlayer> aCache = doubleTeam.getCachePlayer(1);
        if (aCache.isEmpty()) aTeam.remove(doubleTeam);
        aCachePlayer.addAll(aCache);
        if (aCachePlayer.size() < 4) return;
//        if (aCachePlayer.size() > 4) {
//            String debug = "alone->";
//            for (int i = 0; i < 2; i++) {
//                debug += "|" + i + ":";
//                for (int j = 0; j < aloneTeam.get(i).aUser.size(); j++) {
//                    debug += "-" + aloneTeam.get(i).aUser.get(j).getUsername();
//                }
//            }
//            debug += "double->";
//            for (int j = 0; j < doubleTeam.aUser.size(); j++) {
//                debug += "-" + doubleTeam.aUser.get(j).getUsername();
//            }
//            getLogger().warn(debug);
//        }
        BattleDataEntity battle = prepareBattle(aCachePlayer);
        if (battle != null) {
            CacheBattle cacheBattle = new CacheBattle(aCachePlayer, 0, getKey(aCachePlayer), gameType);
            cacheBattle.setBattleId(battle.getId());
            MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
            for (int i = 0; i < 2; i++) {
                aloneTeam.get(i).status = TeamObject.STATUS_PLAY;
                aloneTeam.get(i).sendBattleInfo(cacheBattle.getKey());
                aTeam.remove(aloneTeam.get(i));
            }

            doubleTeam.status = TeamObject.STATUS_PLAY;
            doubleTeam.sendBattleInfo(cacheBattle.getKey());
            aTeam.remove(doubleTeam);
        }
    }

    int resetLoop(List<TeamObject> aloneTeam, List<TeamObject> doubleTeam) {
        aloneTeam.clear();
        doubleTeam.clear();
        return 0;
    }

    boolean waitEnough(TeamObject team) {
        return System.currentTimeMillis() - team.timeSearch >= CfgBot.config.timeWait * 1000;
    }

    //region prepare battle
    String getKey(List<CachePlayer> aCachePlayer) {
        String key = String.valueOf(CfgServer.SERVER_ID);
        for (int i = 0; i < aCachePlayer.size(); i++) {
            key += "_" + aCachePlayer.get(i).getUserId();
        }
        return key;
    }

    BattleDataEntity prepareBattle(List<CachePlayer> aCachePlayer) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(gameType);
            session.save(battle);

            for (CachePlayer cachePlayer : aCachePlayer) {
                session.save(new BattleUserEntity(battle.getId(), cachePlayer.getUserId(), gameType));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
    //endregion
}