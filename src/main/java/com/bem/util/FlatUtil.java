//package com.bem.util;
//
//import com.bem.boom.BoomConfig;
//import com.flat.*;
//import com.google.flatbuffers.FlatBufferBuilder;
//import com.proto.GGProto;
//
//import java.nio.ByteBuffer;
//import java.util.List;
//
//public class FlatUtil {
//
//    int[] abc = new int[4];
//
//    public static void main(String[] args) throws Exception {
//        FlatBufferBuilder fbb = new FlatBufferBuilder(1);
//        //
//        int[] off = new int[2];
//        ProtoUnitAdd.startProtoUnitAdd(fbb);
//        ProtoUnitAdd.addId(fbb, 10);
//        off[0] = ProtoUnitAdd.endProtoUnitAdd(fbb);
//        ProtoUnitAdd.startProtoUnitAdd(fbb);
//        ProtoUnitAdd.addId(fbb, 20);
//        off[1] = ProtoUnitAdd.endProtoUnitAdd(fbb);
//        int sortMons = fbb.createSortedVectorOfTables(new ProtoUnitAdd(), off);
//
//        ProtoTableState.startProtoTableState(fbb);
//        ProtoTableState.addAUnitAdd(fbb, sortMons);
//        int mon = ProtoTableState.endProtoTableState(fbb);
//        fbb.finish(mon);
//        ByteBuffer buffer = fbb.dataBuffer();
//        byte[] a = buffer.array();
//        debug(a.length);
//
//        ProtoTableState table = ProtoTableState.getRootAsProtoTableState(buffer);
//        debug(table.aUnitAddLength());
//        debug(table.aUnitAdd(0).id());
//        debug(table.aUnitAdd(1).id());
//
////        int fred = fbb.createString("a");
////        GambRoom.startGambRoom(fbb);
////        GambRoom.addName(fbb, fred);
////        int mon = GambRoom.endGambRoom(fbb);
////        fbb.finish(mon);
////        ByteBuffer buffer = fbb.dataBuffer();
////        byte[] a = buffer.array();
////        for (byte b : a) {
////            System.out.print(b + " ");
////        }
////        debug();
////        GambRoom gamb = GambRoom.getRootAsGambRoom(buffer);
////        debug(gamb.name() + " " + (int) gamb.name().charAt(0));
//    }
//
//    public int[] getabc() {
//        return abc;
//    }
//
//    public static byte[] convertProtoBuffToFlatBuffer(GGProto.ProtoTableState proto, GGProto.ProtoPlayerState playerState) {
//        FlatBufferBuilder fbb = new FlatBufferBuilder(1);
//        boolean unitAdd = true;
//        boolean unitPos = true;
//        boolean unitState = true;
//        //484
//        // ProtoUnitAdd
//
//        List<GGProto.ProtoUnitAdd> aUnitAdd = proto.getAUnitAddList();
//        int[] arrUnitAdd = new int[aUnitAdd.size()];
//        if (unitAdd) {
//            debug(aUnitAdd.size());
//            for (int i = 0; i < aUnitAdd.size(); i++) {
//                GGProto.ProtoUnitAdd tmp = aUnitAdd.get(i);
//                int playerInfor = tmp.getItemId() == BoomConfig.TYPE_PLAYER ? createPlayerInfor(fbb, tmp.getPlayerInfo()) : 0;
////                int offset = Util.flatPos(fbb, tmp.getPos().getX(), tmp.getPos().getY());
//                int avatars = ProtoUnitAdd.createAvatarVector(fbb, Util.listToArray(tmp.getAvatarList()));
//
//                ProtoUnitAddNew.startProtoUnitAddNew(fbb);
//                ProtoUnitAddNew.addAvatar(fbb, avatars);
//                ProtoUnitAddNew.addId(fbb, tmp.getId());
//                ProtoUnitAddNew.addType(fbb, tmp.getItemId());
//                ProtoUnitAddNew.addAdd(fbb, tmp.getAdd());
//                ProtoUnitAddNew.addPosX(fbb, tmp.getPos().getX());
//                ProtoUnitAddNew.addPosY(fbb, tmp.getPos().getY());
//                if (tmp.getItemId() == BoomConfig.TYPE_PLAYER) {
//                    ProtoUnitAddNew.addPlayerInfo(fbb, playerInfor);
//                }
//                arrUnitAdd[i] = ProtoUnitAddNew.endProtoUnitAddNew(fbb);
//            }
//        }
//        //
//        List<GGProto.ProtoUnitPos> aUnitPos = proto.getAUnitPosList();
//        int[] arrUnitPos = new int[aUnitPos.size()];
//        if (unitPos) {
//            for (int i = 0; i < arrUnitPos.length; i++) {
//                GGProto.ProtoUnitPos tmp = aUnitPos.get(i);
//                ProtoUnitPos.startProtoUnitPos(fbb);
//                ProtoUnitPos.addId(fbb, tmp.getId());
//                ProtoUnitPos.addPosX(fbb, tmp.getPosX());
//                ProtoUnitPos.addPosY(fbb, tmp.getPosY());
//                ProtoUnitPos.addLastInputSeq(fbb, tmp.getLastInputSeq());
//                arrUnitPos[i] = ProtoUnitPos.endProtoUnitPos(fbb);
//            }
//        }
//        //
//        int[] arrPlayerState = new int[1];
//        if (unitState && playerState != null) {
//            int status = com.flat.ProtoPlayerState.createStatusVector(fbb, Util.listToArray(playerState.getStatusList()));
//            int info = com.flat.ProtoPlayerState.createInfoVector(fbb, Util.listFloatToArray(playerState.getInfoList()));
//
//            com.flat.ProtoPlayerState.startProtoPlayerState(fbb);
//            com.flat.ProtoPlayerState.addId(fbb, playerState.getId());
//            com.flat.ProtoPlayerState.addStatus(fbb, status);
//            com.flat.ProtoPlayerState.addInfo(fbb, info);
//            arrPlayerState[0] = com.flat.ProtoPlayerState.endProtoPlayerState(fbb);
//        }
//        //
//        int vectorAdd = 0;
//        if (unitAdd) ProtoTableStateNew.createAUnitAddVector(fbb, arrUnitAdd);
//        int vectorPos = 0;
//        if (unitPos) ProtoTableStateNew.createAUnitPosVector(fbb, arrUnitPos);
//        int vectorUpdate = 0;
//        if (unitState) ProtoTableStateNew.createAPlayerStateVector(fbb, arrPlayerState);
//        //
//        ProtoTableStateNew.startProtoTableStateNew(fbb);
//        ProtoTableStateNew.addServerTime(fbb, proto.getServerTime());
//        if (unitAdd)
//            ProtoTableStateNew.addAUnitAdd(fbb, vectorAdd);
//        if (unitPos)
//            ProtoTableStateNew.addAUnitPos(fbb, vectorPos);
//        if (unitState)
//            ProtoTableStateNew.addAPlayerState(fbb, vectorUpdate);
//
//        int mon = ProtoTableState.endProtoTableState(fbb);
//        fbb.finish(mon);
//        byte[] retData = fbb.sizedByteArray();
//        debug(String.format("new=%s, old=%s", retData.length, proto.toByteArray().length));
//        return retData;
//    }
//
//    public static byte[] convertProtoBuffToFlatBuffer(GGProto.ProtoTableState proto,
//                                                      List<byte[]> aByte) {
//        FlatBufferBuilder fbb = new FlatBufferBuilder(1);
//        // ProtoUnitAdd
//        List<GGProto.ProtoUnitAdd> aUnitAdd = proto.getAUnitAddList();
//        int[] arrUnitAdd = new int[aUnitAdd.size()];
//        for (int i = 0; i < aUnitAdd.size(); i++) {
//            GGProto.ProtoUnitAdd tmp = aUnitAdd.get(i);
//            int playerInfor = tmp.getItemId() == BoomConfig.TYPE_PLAYER ? createPlayerInfor(fbb, tmp.getPlayerInfo()) : 0;
//            int offset = Util.flatPos(fbb, tmp.getPos().getX(), tmp.getPos().getY());
//            int avatars = ProtoUnitAdd.createAvatarVector(fbb, Util.listToArray(tmp.getAvatarList()));
//
//            ProtoUnitAdd.startProtoUnitAdd(fbb);
//            ProtoUnitAdd.addAvatar(fbb, avatars);
//            ProtoUnitAdd.addId(fbb, tmp.getId());
//            ProtoUnitAdd.addType(fbb, tmp.getItemId());
//            ProtoUnitAdd.addAdd(fbb, tmp.getAdd());
//            ProtoUnitAdd.addPos(fbb, offset);
//            if (tmp.getItemId() == BoomConfig.TYPE_PLAYER) {
//                ProtoUnitAdd.addPlayerInfo(fbb, playerInfor);
//            }
//            arrUnitAdd[i] = ProtoUnitAdd.endProtoUnitAdd(fbb);
//        }
//        //
//        List<GGProto.ProtoUnitPos> aUnitPos = proto.getAUnitPosList();
//        int[] arrUnitPos = new int[aUnitPos.size()];
//        for (int i = 0; i < arrUnitPos.length; i++) {
//            GGProto.ProtoUnitPos tmp = aUnitPos.get(i);
//            ProtoUnitPos.startProtoUnitPos(fbb);
//            ProtoUnitPos.addId(fbb, tmp.getId());
//            ProtoUnitPos.addPosX(fbb, tmp.getPosX());
//            ProtoUnitPos.addPosY(fbb, tmp.getPosY());
//            ProtoUnitPos.addLastInputSeq(fbb, tmp.getLastInputSeq());
//            arrUnitPos[i] = ProtoUnitPos.endProtoUnitPos(fbb);
//        }
//        //
//        int size = 0;
//        for (byte[] bytes : aByte) {
//            if (bytes.length > 0) {
//                size++;
//            }
//        }
//        int[] arrByte = new int[size];
//        int index = 0;
//        for (int i = 0; i < aByte.size(); i++) {
//            if (aByte.get(i).length > 0) {
//                int vector = ProtoUnitUpdate.createDataVector(fbb, aByte.get(i));
//                ProtoUnitUpdate.startProtoUnitUpdate(fbb);
//                ProtoUnitUpdate.addType(fbb, i + 1);
//                ProtoUnitUpdate.addData(fbb, vector);
//                arrByte[index] = ProtoUnitUpdate.endProtoUnitUpdate(fbb);
//                index++;
//            }
//        }
//
//        //
//        int vectorAdd = ProtoTableState.createAUnitAddVector(fbb, arrUnitAdd);
//        int vectorPos = ProtoTableState.createAUnitPosVector(fbb, arrUnitPos);
//        int vectorUpdate = ProtoTableState.createAUnitUpdateVector(fbb, arrByte);
//        //
//        ProtoTableState.startProtoTableState(fbb);
//        ProtoTableState.addServerTime(fbb, proto.getServerTime());
//        ProtoTableState.addAUnitAdd(fbb, vectorAdd);
//        ProtoTableState.addAUnitPos(fbb, vectorPos);
//        ProtoTableState.addAUnitUpdate(fbb, vectorUpdate);
//
//        int mon = ProtoTableState.endProtoTableState(fbb);
//        fbb.finish(mon);
//        byte[] retData = fbb.sizedByteArray();
////        debug(String.format("new=%s, old=%s", retData.length, proto.toByteArray().length));
//        return retData;
//    }
//
//    static int createPlayerInfor(FlatBufferBuilder fbb, GGProto.ProtoPlayerInfo playerInfo) {
//        int name = fbb.createString(playerInfo.getName());
//        int aItem = ProtoPlayerInfo.createAItemVector(fbb, Util.listToArray(playerInfo.getAItemList()));
//        int aAvatar = ProtoPlayerInfo.createAvatarVector(fbb, Util.listToArray(playerInfo.getAvatarList()));
//        ProtoPlayerInfo.startProtoPlayerInfo(fbb);
//        ProtoPlayerInfo.addAItem(fbb, aItem);
//        ProtoPlayerInfo.addAvatar(fbb, aAvatar);
//        ProtoPlayerInfo.addName(fbb, name);
//        ProtoPlayerInfo.addId(fbb, playerInfo.getId());
//        ProtoPlayerInfo.addTeam(fbb, playerInfo.getTeam());
////        ProtoPlayerInfo.addSpeed(fbb, playerInfo.getSpeed());
//        return ProtoPlayerInfo.endProtoPlayerInfo(fbb);
//    }
//
//}
