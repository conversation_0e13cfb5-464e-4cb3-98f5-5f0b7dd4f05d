package com.bem.util;

import io.netty.channel.Channel;
import io.netty.util.AttributeKey;

/**
 * Created by vieth_000 on 8/18/2016.
 */
public class ChUtil {
    public static Integer getInteger(Channel channel, String key) {
        try {
            return (Integer) channel.attr(AttributeKey.valueOf(key)).get();
        } catch (Exception ex) {
        }
        return null;
    }

    public static Object get(Channel channel, String key) {
        return channel.attr(AttributeKey.valueOf(key)).get();
    }

    public static void set(Channel channel, String key, Object value) {
        channel.attr(AttributeKey.valueOf(key)).set(value);
    }

    public static void remove(Channel channel, String key) {
        if (channel != null) channel.attr(AttributeKey.valueOf(key)).remove();

    }
}
