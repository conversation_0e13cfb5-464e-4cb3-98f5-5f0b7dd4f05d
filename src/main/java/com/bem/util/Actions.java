package com.bem.util;

import com.bem.dao.CacheDAO;
import com.bem.dao.mapping.UserAssetLogEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.object.UserInfo;
import com.google.gson.Gson;
import com.k2tek.common.slib_Logger;
import grep.helper.DateTime;
import grep.helper.Filer;
import net.sf.json.JSONObject;
import org.slf4j.Logger;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 8/18/2014.
 */
public class Actions {

    // Group Action
    public static String GUSER = "user";
    public static String GCLAN = "clan";

    // Detail action
    public static String DLOGIN = "login";

    public static String GITEM = "item";
    public static String GUSED_ITEM = "useditem";
    public static String GHERO = "hero";
    public static String GGEM = "gem";
    public static String GKUNGFU = "kungfu";
    public static String GGAME = "game";

    public static String GTOP = "top";
    public static String GMAP = "map";
    public static String GRECEIVE = "receive";
    public static String GWAR = "war";
    public static String GAMULET = "amulet";


    public static String DJACKPOT = "jackpot";
    public static String DQUAY = "quay";
    public static String DFOOD = "freefood";
    public static String DMARKET = "market";
    public static String DFORGE = "forge";
    public static String DCREATE = "create";
    public static String DRULE = "rule";
    public static String DREQ = "req";
    public static String DSUPPORT = "spp";
    public static String DAREQ = "areq";
    public static String DMERGE = "merge";
    public static String DROBBER = "robber";
    public static String DKICK = "kick";
    public static String DDESTROY = "destroy";
    public static String DSTATUS = "status";
    public static String DSEARCH = "search";
    public static String DLEAVE = "leave";
    public static String DBUY = "buy";
    public static String DSELL = "sell";
    public static String DUPGRADE = "upgrade";
    public static String DEQUIP = "equip";
    public static String DSORT = "sort";
    public static String DGIFT_BOX = "giftbox";
    public static String DGIFT_CODE = "giftcode";
    public static String DEVENT_CODE = "eventcode";
    public static String DGIFT_ACHIEVE = "achievement";
    public static String DINFO = "info";
    public static String DREF = "ref";
    public static String DCHAT = "chat";
    public static String DPVPLOG = "pvp_log";
    public static String DPVP = "pvp";
    public static String DFEEPVP = "feepvp";
    public static String DSOUL = "soul";
    public static String DUSE = "use";
    public static String DAUTO = "auto";
    public static String DBATTLE = "battle";
    public static String DARENA = "arena";
    public static String DKINGDOM = "kingdom";
    public static String DBOSS = "boss";
    public static String DSILVER_TROPHY = "silver_trophy";
    public static String DAUTO_RESULT = "auto_result";

    //    static String path = "../activity/s" + CfgServer.SERVER_ID + "/";
    static String path = "../activity/s";


    public static String getPath(UserEntity u) {
        return path + u.getServer() + "/";
    }

    public static void save(UserEntity uEntity, String gAction, String dAction, String desc) {
        long userId = uEntity.getId();
        if (!uEntity.getCp().equalsIgnoreCase("test"))
            getLogger().info(uEntity.getServer() + "u" + userId + " " + gAction + " " + dAction + " " + desc);
        //
        String curDate = DateTime.getDateyyyyMMdd(new Date());
        File file = new File(getPath(uEntity) + curDate);
        if (!file.exists()) file.mkdirs();
        //
        Filer.append(getPath(uEntity) + DateTime.getDateyyyyMMdd(new Date()) + "/" + userId + ".log",
                DateTime.getFullDate(new Date()) + "\t" + uEntity.getServer() + "u" + userId + " " + gAction + " " + dAction + " " + desc);
    }

    public static void saveUserAsset(String key, UserInfo user) {
        try {
            JSONObject obj = new JSONObject();
            obj.put("user", new Gson().toJson(user.getDbUser()));
            obj.put("data", new Gson().toJson(user.getRes()));
            UserAssetLogEntity assEntity = new UserAssetLogEntity();
            assEntity.setUserId(user.getId());
            assEntity.setData(obj.toString());
            assEntity.setK(key);
            new CacheDAO().logAsset(assEntity);
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
        }
    }

    public static String arrIntToLog(List<Integer> aInt) {
        String tmp = "";
        for (int i = 0; i < aInt.size(); i++) {
            tmp += "_" + aInt.get(i);
        }
        tmp = tmp.length() > 0 ? tmp.substring(1) : tmp;
        return tmp;
    }

    public static String arrLongToLog(List<Long> aLong) {
        String tmp = "";
        for (int i = 0; i < aLong.size(); i++) {
            tmp += "_" + aLong.get(i);
        }
        tmp = tmp.length() > 0 ? tmp.substring(1) : tmp;
        return tmp;
    }

    public static String convertToLogString(List<String> values) {
        JSONObject obj = new JSONObject();
        for (int i = 0; i < values.size() / 2; i++) {
            obj.put(values.get(i * 2), values.get(i * 2 + 1));
        }
        return obj.toString();
    }

    public static Logger getLogger() {
        return slib_Logger.act();
    }
}
