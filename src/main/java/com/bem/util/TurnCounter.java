package com.bem.util;

import java.util.*;

import org.slf4j.Logger;

import com.bem.object.TurnInfor;
import com.k2tek.common.slib_Logger;

/**
 * <AUTHOR>
 */
public class TurnCounter extends Thread {
    Map<Integer, Queue<TurnInfor>> mTurn = new HashMap<Integer, Queue<TurnInfor>>();
    List<Integer> aQueue = new ArrayList<Integer>();
    long waitTime;
    int seconds;

    public TurnCounter(int waitTime) {
        this.waitTime = waitTime * 1000;
    }

    @Override
    public void run() {
        while (true) {
            try {
                long curTime = System.currentTimeMillis();
                TurnInfor nearestTurn = null;
                for (int i = 0; i < aQueue.size(); i++) {
                    seconds = aQueue.get(i);
                    Queue<TurnInfor> queue = mTurn.get(seconds);
                    if (!queue.isEmpty()) {
                        TurnInfor turn = peek(queue);
                        while (turn.getExpire() <= curTime) {
                            turn.getTimer().doExpireTurn(turn.getTurnId());
                            queue.poll();
                            turn = peek(queue);
                            if (turn == null) {
                                break;
                            }
                        }
                        if (turn != null && (nearestTurn == null || nearestTurn.getExpire() > turn.getExpire())) {
                            nearestTurn = turn;
                        }
                    }
                }
                Thread.yield();
                long sleepTime = 1000;
                if (nearestTurn != null) {
                    sleepTime = nearestTurn.getExpire() - curTime;
                    if (sleepTime > waitTime)
                        sleepTime = waitTime;
                }
                sleep(sleepTime);
            } catch (Exception ex) {
                try {
                    Queue<TurnInfor> queue = mTurn.get(seconds);
                    if (queue != null) {
                        getLogger().warn("seconds=" + seconds + ", size=" + queue.size() + " -> " + Util.exToString(ex));
                        if (!queue.isEmpty()) {
                            poll(queue);
                        }
                    } else {
                        getLogger().warn("seconds=" + seconds + ", size=null -> " + Util.exToString(ex));
                        queue = new LinkedList<TurnInfor>();
                        mTurn.put(seconds, queue);
                    }
                } catch (Exception ex1) {
                    getLogger().warn("Exception again " + Util.exToString(ex1));
                }
            }
        }
    }

    public synchronized TurnInfor peek(Queue<TurnInfor> queue) { // Retrieves, but does not remove, the head of this queue,
        return queue.peek();
    }

    public synchronized TurnInfor poll(Queue<TurnInfor> queue) { // Retrieves and removes the head of this queue
        return queue.poll();
    }

    public synchronized void addQueue(TurnInfor turn) {
        Queue<TurnInfor> queue = mTurn.get(turn.getSeconds());
        if (queue == null) {
            queue = new LinkedList<TurnInfor>();
            mTurn.put(turn.getSeconds(), queue);
            aQueue.add(turn.getSeconds());
        }
        queue.add(turn);
    }

    public static Logger getLogger() {
        return slib_Logger.root();
    }
}
