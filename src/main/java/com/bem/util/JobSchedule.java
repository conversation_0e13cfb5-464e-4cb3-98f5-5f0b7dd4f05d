package com.bem.util;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;

import com.bem.object.TaskSchedule;
import com.k2tek.common.slib_Logger;

/**
 * 
 * <AUTHOR>
 * @description Common time counter for Task Monitors (Reduce thread)
 */
public class JobSchedule extends Thread {
	long interval = 3000;
	final static int MAX_COUNTER = 10000;
	List<TaskSchedule> mQueue = new ArrayList<TaskSchedule>();
	int counter = 0;

	public JobSchedule(long interval) {
		this.interval = interval;
	}

	@Override
	public void run() {
		while (true) {
			try {
				if (++counter == MAX_COUNTER) {
					counter = 0;
				}

				for (TaskSchedule task : mQueue) {
					if (counter % task.getGap() == 0 && !task.isStart()) {
						new Thread(task).start();
					}
				}

				Thread.yield();
				sleep(interval);
			} catch (Exception ex) {
				getLogger().warn("JobSchedule Exception " + Util.exToString(ex));
			}
		}
	}

	public void addQueue(TaskSchedule task) {
		mQueue.add(task);
	}

	public static Logger getLogger() {
		return slib_Logger.root();
	}
}
