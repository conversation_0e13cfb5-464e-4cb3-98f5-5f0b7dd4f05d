/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.util;

import com.bem.boom.BoomConfig;
import com.bem.config.CfgEnergy;
import com.bem.dao.mapping.ClanEntity;
import com.bem.dao.mapping.UserDataEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.monitor.ClanMonitor;
import com.bem.object.IUser;
import com.bem.object.TopDetail;
import com.bem.object.UserInt;
import com.google.protobuf.AbstractMessage;
import com.google.protobuf.ByteString;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.proto.GGProto;
import com.proto.GGProto.*;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Vector;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"unchecked", "rawtypes"})
public class CommonProto {

    public static String protoUpdateBattle(List<Long> userIds, List<String> results, boolean endgame) {
        JSONArray arr = new JSONArray();
        for (int i = 0; i < userIds.size(); i++) {
            JSONObject obj = new JSONObject();
            obj.put("id", userIds.get(i));
            obj.put("result", results.get(i));
            obj.put("endgame", endgame);
            arr.add(obj);
        }
        return IAction.UPDATE_BATTLE_RESULT + "@" + arr.toString();
    }

    public static Action getCommonVectorAction(int service, List<Long> aLong, List<String> aStr) {
        Action.Builder builder = Action.newBuilder();
        builder.setService(service);
        builder.setData(getCommonLongVectorProto(aLong, aStr).toByteString());
        return builder.build();
    }

    public static ProtoUnitAdd.Builder protoAddAlertDanger(com.bem.boom.Pos pos) {
        GGProto.ProtoUnitAdd.Builder builder = GGProto.ProtoUnitAdd.newBuilder();
        builder.setId(0);
        builder.setType(BoomConfig.TYPE_ALERT_DANGER);
        builder.setAdd(true);
        builder.setPos(CommonProto.protoPos(pos.x, pos.y));
        return builder;
    }

    public static ByteString protoListJump(List<GGProto.ProtoUnitJumpPos.Builder> aJump) {
        GGProto.ProtoListUnitJumpPos.Builder builder = GGProto.ProtoListUnitJumpPos.newBuilder();
        int size = aJump.size();
        for (int i = 0; i < size; i++) {
            builder.addAJump(aJump.get(0));
            aJump.remove(0);
        }
        return builder.build().toByteString();
    }

    public static ProtoUnitJumpPos.Builder protoJumpPos(int typeJump, int unitId, com.bem.boom.Pos pos) {
        ProtoUnitJumpPos.Builder builder = ProtoUnitJumpPos.newBuilder();
        builder.setId(unitId);
        builder.setPosX(pos.x);
        builder.setPosY(pos.y);
        builder.setType(typeJump);
        return builder;
    }

    public static ProtoInviteBattle protoInviteBattle(long userId, String name, int teamId) {
        ProtoInviteBattle.Builder builder = ProtoInviteBattle.newBuilder();
        builder.setUserId(userId);
        builder.setName(name);
        builder.setTeamId(teamId);
        return builder.build();
    }

    public static ProtoUser protoUser(IUser user) {
        ProtoUser.Builder builder = ProtoUser.newBuilder();
        builder.setId(user.id);
        builder.setName(user.name);
        builder.setUsername(user.username);
        builder.addAllAvatar(user.avatars);
        builder.setRank(user.rankId);
        return builder.build();
    }

    public static ProtoListUser ListProtoUser(List<ProtoUser> luser) {
        ProtoListUser.Builder builder = ProtoListUser.newBuilder();
        builder.addAllAUser(luser);
        return builder.build();
    }

    public static ProtoUser protoUser(UserEntity user, UserDataEntity uData) {
        return protoUser(user, null, uData);
    }

    public static ProtoUser protoGetTopUser(UserEntity user, long number, List<UserEntity> luser, boolean haveTophy) {
        return protoTopUser(user, number, luser, haveTophy);
    }


    public static ProtoUser protoTopUser(UserEntity user, long number, List<UserEntity> luser, boolean haveTophy) {
        ProtoUser.Builder builder = ProtoUser.newBuilder();
        builder.setId(user.getId());
        builder.setName(user.getName());
        builder.setUsername(user.getUsername());
//        builder.setGem(user.getGem());
//        builder.setGold(user.getGold());
        builder.addAllAvatar(user.getMAvatar().toList());
        builder.setLvl(user.getLevel());
//        builder.setExp(user.getExp());
//        builder.setUnreadMail(user.getNotify());
//        builder.addAllSettings(user.getListSetting());
        if (haveTophy) {
            builder.setRank(user.getRank(luser));
        } else {
            builder.setRank(0);
        }
        builder.addInfor(user.getVip()); // vip
        builder.addInfor(user.getGemNap()); // number gem charge
        builder.addInfor(user.getMaxwin()); // lien thang
//        System.out.println("user------>"+user.getRank(luser));
        builder.setTrophy(number);
//        builder.setAtomic(user.getAtomic());

//        if (energy != null) {
//            builder.addAllEnergy(energy.info());
//        }
//        builder.setMedal(user.getMedal());
//        if (uInfor!=null&&uInfor.getUData() != null) {
//            builder.addInfor(uInfor.getUData().getUInt().getValue(UserInt.HERO_EXP_X2)); // so tran x2 con lai
//        }
//        if (user.getWinRate() == null) {
//            builder.addAllWinRate(Arrays.asList(0, 0));
//        } else {
//            JSONArray arr = JSONArray.fromObject(user.getWinRate());
//            builder.addAllWinRate(Arrays.asList(arr.getInt(0), arr.getInt(1)));
//        }
        // clan
//        builder.addClan(user.getClan());
//        builder.addClan(user.getClanPosition());
//        if (user.getClan() > 0) {
//            ClanEntity clan = ClanMonitor.getClan(user.getClan());
//            if (clan != null) {
//                builder.addClan(clan.getAvatar());
//                builder.addClan(JSONArray.fromObject(user.getDiemdanh()).getInt(0));
//                builder.setClanName(clan.getName());
//            }
//        } else {
//            builder.addClan(0);
//            builder.addClan(0);
//        }

        return builder.build();
    }

    public static ProtoUser protoUser(UserEntity user, CfgEnergy.MyEnergy energy, UserDataEntity uData) {
        ProtoUser.Builder builder = ProtoUser.newBuilder();
        builder.setId(user.getId());
        builder.setName(user.getName());
        builder.setUsername(user.getUsername());
        builder.setGem(user.getGem());
        builder.setGold(user.getGold());
        builder.addAllAvatar(user.getMAvatar().toList());
        builder.setLvl(user.getLevel());
        builder.setExp((int) user.getExp());
        builder.setUnreadMail(user.getNotify());
        builder.addAllSettings(user.getListSetting());
        builder.setTrophy(user.getTrophy());
        builder.setAtomic(user.getAtomic());
        builder.setRank(user.getRank());
        if (energy != null) {
            builder.addAllEnergy(energy.info());
        }
        builder.setMedal(user.getMedal());
        {
            builder.addInfor(user.getVip()); // vip
            builder.addInfor(user.getGemNap()); // number gem charge
            builder.addInfor(uData != null ? uData.getUInt().getValue(UserInt.NUMBER_WIN) : 0); // lien thang
            builder.addInfor(uData != null ? uData.getUInt().getValue(UserInt.BATTLE_ITEM_1) : 0);
            builder.addInfor(uData != null ? uData.getUInt().getValue(UserInt.BATTLE_ITEM_2) : 0);
        }
        builder.addAllWinRate(Arrays.asList((int) user.getWin(), (int) user.getLoose()));

        // clan
        builder.addClan(user.getClan());
        builder.addClan(user.getClanPosition());
        if (user.getClan() > 0) {
            ClanEntity clan = ClanMonitor.getClan(user.getClan());
            if (clan != null) {
                builder.addClan(clan.getAvatar());
                builder.addClan(JSONArray.fromObject(user.getDiemdanh()).getInt(0));
                builder.setClanName(clan.getName());
            }
        } else {
            builder.addClan(0);
            builder.addClan(0);
        }
//        System.out.println("builder-->"+builder.getInforList());
        return builder.build();
    }

    public static ProtoTopBossDetail protoTopBoss(TopDetail top) {
        ProtoTopBossDetail.Builder builder = ProtoTopBossDetail.newBuilder();
        try {
            builder.setId(top.id);
            for (int i = 0; i < top.user.size(); i++) {
                builder.addAUser(protoUser(top.user.get(i), null));
            }
            builder.setHostName(top.hostName);
            builder.setTime(top.time);
        } catch (Exception ex) {

        }
        return builder.build();
    }

    public static ProtoTopListBossDetail protoListTopBoss(List<TopDetail> top) {
        ProtoTopListBossDetail.Builder builder = ProtoTopListBossDetail.newBuilder();
        for (int i = 0; i < top.size(); i++) {
            builder.addATop(protoTopBoss(top.get(i)));
        }
        return builder.build();
    }


    public static ProtoUnitUpdate.Builder protoUnitUpdate(int type, ByteString data) {
        ProtoUnitUpdate.Builder builder = ProtoUnitUpdate.newBuilder();
        builder.setType(type);
        builder.addData(data);
        return builder;
    }

    public static MapPos protoMPos(int x, int y) {
        MapPos.Builder builder = MapPos.newBuilder();
        builder.setX(x);
        builder.setY(y);
        return builder.build();
    }

    public static Pos protoPos(float x, float y) {
        Pos.Builder builder = Pos.newBuilder();
        builder.setX(x);
        builder.setY(y);
        return builder.build();
    }

    public static ProtoInput parseProtoInput(byte[] data) {
        if (data != null) {
            try {
                ProtoInput cmm = ProtoInput.parseFrom(data);
                return cmm;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return null;
    }

    public static CommonVector parseCommonVector(byte[] data) {
        if (data != null) {
            try {
                CommonVector cmm = CommonVector.parseFrom(data);
                return cmm;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return null;
    }

    public static CommonVector getErrorMsg(String msg) {
        try {
            CommonVector.Builder cmm = CommonVector.newBuilder();
            cmm.addAString(msg);
            return cmm.build();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static CommonVector getCommonLongVectorProto(List<Long> listLong, List<String> listString) {
        try {
            CommonVector.Builder cmm = CommonVector.newBuilder();
            if (listLong != null) {
                for (Long i : listLong) {
                    cmm.addANumber(i);
                }
            }
            if (listString != null) {
                for (String str : listString) {
                    cmm.addAString(str);
                }
            }
            return cmm.build();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static ListCommonVector getListCommonLongVectorProto(List<CommonVector> listLong) {
        try {
            ListCommonVector.Builder cmm = ListCommonVector.newBuilder();
            if (listLong != null) {
                for (CommonVector i : listLong) {
                    cmm.addAVector(i);
                }
            }
            return cmm.build();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static CommonVector getCommonVectorProto(List<Integer> listInt, List<String> listString) {
        try {
            CommonVector.Builder cmm = CommonVector.newBuilder();
            if (listInt != null) {
                for (Integer i : listInt) {
                    cmm.addANumber(i);
                }
            }
            if (listString != null) {
                for (String str : listString) {
                    cmm.addAString(str);
                }
            }
            return cmm.build();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static CommonVector getUserLeaveProto(int isKick, long looseMoney, String leaveUsername, String hostName, String nextUsername, String message) {
        try {
            CommonVector.Builder cmm = CommonVector.newBuilder();
            cmm.addANumber(isKick);
            cmm.addANumber(looseMoney);
            cmm.addAString(leaveUsername);
            cmm.addAString(hostName);
            cmm.addAString(nextUsername);
            cmm.addAString(message);
            return cmm.build();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static Action getAction(int service, AbstractMessage data) {
        Action.Builder builder = Action.newBuilder();
        builder.setService(service);
        if (data != null) {
            builder.setData(data.toByteString());
        }
        return builder.build();
    }

    public static Vector getStealCard(Channel channel) {
        try {
            return new Vector((List<Integer>) ChUtil.get(channel, Constans.KEY_USER_CARDS_STEAL));
        } catch (Exception ex) {
        }
        return new Vector();
    }

    public static Vector getStealCard(Map<String, Object> map) {
        try {
            return new Vector((List<Integer>) map.get(Constans.KEY_USER_CARDS_STEAL));
        } catch (Exception ex) {
        }
        return new Vector();
    }

    public static Vector getPlayCard(Channel channel) {
        try {
            return new Vector((List<Integer>) ChUtil.get(channel, Constans.KEY_USER_CARDS_PLAY));
        } catch (Exception ex) {
        }
        return new Vector();
    }

    public static Vector getPlayCard(Map<String, Object> map) {
        try {
            return new Vector((List<Integer>) map.get(Constans.KEY_USER_CARDS_PLAY));
        } catch (Exception ex) {
        }
        return new Vector();
    }

    public static Vector getUserCard(Channel channel) {
        try {
            return new Vector((List<Integer>) ChUtil.get(channel, Constans.KEY_USER_CARDS));
        } catch (Exception ex) {
        }
        return new Vector();
    }

    public static Vector getUserCard(Map<String, Object> map) {
        try {
            return new Vector((List<Integer>) map.get(Constans.KEY_USER_CARDS));
        } catch (Exception ex) {
        }
        return new Vector();
    }

    public static Vector getShowCard(Channel channel) {
        try {
            return new Vector((List<Integer>) ChUtil.get(channel, Constans.KEY_USER_CARDS_SHOW));
        } catch (Exception ex) {
        }
        return new Vector();
    }

    public static Vector getShowCard(Map<String, Object> map) {
        try {
            return new Vector((List<Integer>) map.get(Constans.KEY_USER_CARDS_SHOW));
        } catch (Exception ex) {
        }
        return new Vector();
    }

    public static Integer getBlind(Channel channel) {
        try {
            return Integer.parseInt((String) ChUtil.get(channel, Constans.KEY_USER_BLIND));
        } catch (Exception ex) {
        }
        return 0;
    }

    public static Integer getBlind(Map<String, Object> map) {
        try {
            return Integer.parseInt((String) map.get(Constans.KEY_USER_BLIND));
        } catch (Exception ex) {
        }
        return 0;
    }

    public static int getReady(Channel channel) {
        try {
            boolean isReady = Util.getBooleanAttribute(channel, Constans.KEY_USER_READY);
            return isReady ? 1 : 0;
        } catch (Exception ex) {
        }
        return 0;
    }

    public static int getReady(Map<String, Object> map) {
        try {
            boolean isReady = Util.getBooleanAttribute(map, Constans.KEY_USER_READY);
            return isReady ? 1 : 0;
        } catch (Exception ex) {
        }
        return 0;
    }

    public static Action parseAction(byte[] data) {
        try {
            return Action.parseFrom(data);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static RequestData parseRequestData(byte[] data) {
        try {
            return RequestData.parseFrom(data);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static ResponseData parseResponseData(byte[] data) {
        try {
            return ResponseData.parseFrom(data);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }
}
