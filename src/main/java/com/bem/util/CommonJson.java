/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.util;

import com.bem.object.UserInfo;
import com.k2tek.common.slib_Logger;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CommonJson {

    public static Logger getLogger() {
        return slib_Logger.root();
    }

    public static JSONObject sendMessage(String sender, String message) {
        JSONObject json = new JSONObject();
        json.put("sender", sender);
        json.put("message", message);
        return json;
    }

    public static JSONObject getSendCard(String username, String preUsername, List<Integer> cards, Integer card) {
        JSONObject json = new JSONObject();
        json.put("username", username);
        json.put("receiver", preUsername);
        json.put("card", card);
        JSONArray arrCard = new JSONArray();
        for (Integer c : cards) {
            arrCard.add(c);
        }
        json.put("cards", arrCard);
        return json;
    }

    public static JSONObject kickPlayer(String username) {
        JSONObject json = new JSONObject();
        json.put("username", username);
        return json;
    }

    public static JSONObject getGroup(JSONArray arrGroups, String username, String nextPlayer) {
        JSONObject json = new JSONObject();
        json.put("username", username);
        json.put("cards", arrGroups);
        json.put("turn", nextPlayer);
        return json;
    }

    public static JSONObject requestSendCard(String username) {
        JSONObject json = new JSONObject();
        json.put("username", username);
        json.put("turn", username);
        return json;
    }

    public static JSONObject userShowHand(String username) {
        JSONObject json = new JSONObject();
        json.put("showhand", username);
        json.put("turn", username);
        return json;
    }

    public static JSONObject playerReady(List<String> username, boolean ready) {
        JSONObject rdyJson = new JSONObject();
        JSONArray arr = new JSONArray();
        for (String str : username) {
            JSONObject json = new JSONObject();
            json.put("username", str);
            json.put("ready", ready);
            arr.add(json);
        }
        rdyJson.put("ready", arr);
        return rdyJson;
    }

    public static JSONObject getPlayCard(String userPlay, String nextUser, int card, boolean isSteal) {
        JSONObject json = new JSONObject();
        json.put("username", userPlay);
        json.put("card", card);
        json.put("turn", nextUser);
        if (isSteal) {
            json.put("eat", true);
        }
        return json;
    }

    public static JSONObject getCard(String username, int card, boolean isReceiver) {
        JSONObject json = new JSONObject();
        json.put("turn", username);
        if (isReceiver) {
            json.put("card", card);
        }
        return json;
    }

    public static JSONObject getStartGame(String activePlayer, List<Integer> cards) {
        JSONObject json = new JSONObject();
        json.put("turn", activePlayer);
        JSONArray arr = new JSONArray();
        arr.addAll(cards);
        json.put("cards", arr);
        return json;
    }

    public static JSONObject answerRequestFriend(String username, String message, String answer) {
        JSONObject json = new JSONObject();
        json.put("message", message);
        json.put("receiver", username);
        json.put("answer", answer);
        return json;
    }

    public static JSONObject requestFriend(String username, String message) {
        JSONObject json = new JSONObject();
        json.put("message", message);
        json.put("sender", username);
        return json;
    }

    public static JSONObject getMessage(String message) {
        JSONObject json = new JSONObject();
        json.put("message", message);
        return json;
    }

    public static JSONObject getErrorMessage(String message) {
        JSONObject json = new JSONObject();
        json.put("error", message);
        return json;
    }

    public static JSONObject getChatMessage(UserInfo user, String message) {
        JSONObject json = new JSONObject();
        if (user != null) {
            json.put("username", user.getUsername());
            json.put("message", message);
        }
        return json;
    }

    public static JSONObject getResult(boolean result, String message) {
        JSONObject json = new JSONObject();
        json.put("status", result);
        json.put("message", message);
        return json;
    }

    public static JSONObject removePlayer(String username, String hostPlayer, String nextPlayer, int kick, int chickenMoney, int looseMoney) {
        JSONObject json = new JSONObject();
        json.put("username", username);
        json.put("hostname", hostPlayer);
        json.put("turn", nextPlayer);
        json.put("status", kick);
        json.put("chicken", chickenMoney);
        json.put("money", looseMoney);
        return json;
    }
}
