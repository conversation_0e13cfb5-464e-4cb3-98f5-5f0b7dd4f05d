package com.bem.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 12/5/2014.
 */
public class DateUtil {
    public static long DAY = 24 * 3600000L;
    public static long DAY7 = 7 * 24 * 3600000L;
    public static long YEAR10 = 10 * 365 * 24 * 3600000L;
    public static long YEAR5 = 5 * 365 * 24 * 3600000L;

    static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    public static String getDateYMD(Date date) {
        return sdf.format(date);
    }

}
