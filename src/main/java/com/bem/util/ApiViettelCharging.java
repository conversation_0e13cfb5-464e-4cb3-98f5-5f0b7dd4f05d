package com.bem.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.PostMethod;
import org.slf4j.Logger;

import com.bem.config.CfgServer;
import com.k2tek.common.slib_Logger;

public class ApiViettelCharging {

	private static String USERNAME = "beme", PASSWORD = "bembem2012zxop";
	public static int BONUS_KOIN = 0;
	public static String MESSAGE = "", TITLE = "";
	private static String URL_GET_MOBILE = "http://pm.mvdigital.vn/chargingapi/index.php";
	private static String URL_CHARGING_REGISTER = "http://pm.mvdigital.vn/chargingapi/index.php";
	private static String URL_CHARGING_UNREGISTER = "http://pm.mvdigital.vn/chargingapi/index.php";
	private static String URL_CHECK_REGISTER_INFOR = "http://pm.mvdigital.vn/chargingapi/index.php";
	private static String URL_UPDATE_CHARGING_MOBILE = "http://pm.mvdigital.vn/chargingapi/index.php";
	private static List<String> CPS = new ArrayList<String>();

	public static String getMobile(String ip) {
		String url = String.format(URL_GET_MOBILE, USERNAME, PASSWORD, ip);
		try {
			long l = System.currentTimeMillis();
			String ret = getPostContent(url, null, true);
			getLogger().info(url + " -> " + (System.currentTimeMillis() - l) + " -> " + ret);
			if (ret != null && ret.length() > 0) {
				JSONObject obj = JSONObject.fromObject(ret);
				if (obj.getInt("status") == 0) {
					String mobile = obj.getString("data");
					if (mobile.startsWith("84")) {
						mobile = "0" + mobile.substring(2);
					}
					return mobile;
				}
			}
		} catch (Exception ex) {
			getLogger().error(url + " -> " + Util.exToString(ex));
		}
		return null;
	}

	public static boolean registerCharging(String username, String mobile) {
		String url = String.format(URL_CHARGING_REGISTER, USERNAME, PASSWORD, mobile, username);
		try {
			long l = System.currentTimeMillis();
			String ret = getPostContent(url, null, true);
			getLogger().info(url + " -> " + (System.currentTimeMillis() - l) + " -> " + ret);
			if (ret != null && ret.length() > 0) {
				JSONObject obj = JSONObject.fromObject(ret);
				if (obj.getInt("status") == 0) {
					return true;
				}
			}
		} catch (Exception ex) {
			getLogger().error(url + " -> " + Util.exToString(ex));
		}
		return false;
	}

	public static boolean unRegisterCharging(String username) {
		String url = String.format(URL_CHARGING_UNREGISTER, USERNAME, PASSWORD, username);
		try {
			long l = System.currentTimeMillis();
			String ret = getPostContent(url, null, true);
			getLogger().info(url + " -> " + (System.currentTimeMillis() - l) + " -> " + ret);
			if (ret != null && ret.length() > 0) {
				JSONObject obj = JSONObject.fromObject(ret);
				if (obj.getInt("status") == 0) {
					return true;
				}
			}
		} catch (Exception ex) {
			getLogger().error(url + " -> " + Util.exToString(ex));
		}
		return false;
	}

	public static JSONObject getRegisterInfor(String username, String mobile) {
		String url = String.format(URL_CHECK_REGISTER_INFOR, USERNAME, PASSWORD, mobile, username);
		try {
			long l = System.currentTimeMillis();
			String ret = getPostContent(url, null, true);
			getLogger().info(url + " -> " + (System.currentTimeMillis() - l) + " -> " + ret);
			if (ret != null && ret.length() > 0) {
				JSONObject obj = JSONObject.fromObject(ret);
				if (obj.getInt("status") == 0) {
					return obj.getJSONObject("data");
				}
			}
		} catch (Exception ex) {
			getLogger().error(url + " -> " + Util.exToString(ex));
		}
		return null;
	}

	public static boolean updateMobileCharging(String username, String mobile) {
		String url = String.format(URL_UPDATE_CHARGING_MOBILE, USERNAME, PASSWORD, mobile, username);
		try {
			long l = System.currentTimeMillis();
			String ret = getPostContent(url, null, true);
			getLogger().info(url + " -> " + (System.currentTimeMillis() - l) + " -> " + ret);
			if (ret != null && ret.length() > 0) {
				JSONObject obj = JSONObject.fromObject(ret);
				if (obj.getInt("status") == 0) {
					return true;
				}
			}
		} catch (Exception ex) {
			getLogger().error(url + " -> " + Util.exToString(ex));
		}
		return false;
	}

	public static String getPostContent(String url, Map<String, String> params, boolean isPost) {
		try {
			HttpClient client = new HttpClient();
			PostMethod post = new PostMethod(url);
			post.setRequestHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
			if (params != null) {
				List<String> keys = new ArrayList<String>(params.keySet());
				for (String key : keys) {
					post.addParameter(key, String.valueOf(params.get(key)));
				}
			}
			int returnCode = client.executeMethod(post);
			if (returnCode == HttpStatus.SC_NOT_IMPLEMENTED) {
				return "";
			}
			String result = post.getResponseBodyAsString();
			return result;
		} catch (Exception ex) {
			getLogger().error(url + "->" + Util.exToString(ex));
		}
		return "";
	}

	public static Logger getLogger() {
		return slib_Logger.userAPI();
	}

	public static boolean isChargingCP(String cp) {
		String parentCP = "";//CfgServer.getParentCP(cp);

		if (parentCP != null && CPS.contains(parentCP.toLowerCase())) {
			return true;
		}

		if (cp != null && CPS.contains(cp.toLowerCase())) {
			return true;
		}
		
		return false;
	}
	
	public static void loadConfig(String strJson) {
		JSONObject json = JSONObject.fromObject(strJson);
		CPS.clear();
		USERNAME = json.getString("username");
		String pass = json.getString("password");
		String pin = json.getString("pin");
		PASSWORD = Util.getMD5(pass + pin);
		URL_GET_MOBILE = json.getString("get_mobile");
		URL_CHARGING_REGISTER = json.getString("register");
		URL_CHARGING_UNREGISTER = json.getString("unregister");
		URL_CHECK_REGISTER_INFOR = json.getString("register_infor");
		URL_UPDATE_CHARGING_MOBILE = json.getString("update_mobile");
		BONUS_KOIN = json.getInt("bonus_koin");
		MESSAGE = json.getString("message");
		TITLE = json.getString("title");
		JSONArray arrCP = json.getJSONArray("cp");
		for (int i = 0; i < arrCP.size(); i++) {
			CPS.add(arrCP.getString(i).toLowerCase());
		}
	}
}
