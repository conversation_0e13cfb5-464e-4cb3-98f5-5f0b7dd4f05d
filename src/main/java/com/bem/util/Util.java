/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.util;

import com.bem.config.CfgServer;
import com.bem.object.UserInfo;
import com.google.protobuf.AbstractMessage;
import com.k2tek.Constans;
import com.k2tek.common.slib_Logger;
import com.proto.GGProto;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFutureListener;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.validator.routines.EmailValidator;
import org.slf4j.Logger;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
public class Util {

    static SimpleDateFormat sdf = new SimpleDateFormat("HH:mm dd/MM/yyyy");
    static SimpleDateFormat chatSDF = new SimpleDateFormat("HH:mm");
    static SimpleDateFormat sdfH = new SimpleDateFormat("HH");

    static SimpleDateFormat sdfDay = new SimpleDateFormat("dd/MM/yyyy");

    static SimpleDateFormat sdfSql = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    final static long minute = 60;
    final static long hour = 60 * 60;
    final static long day = 60 * 60 * 24;
    final static long month = 60 * 60 * 24 * 30;

    public static void main(String[] args) {
        System.out.println(getHPass("abc123456"));
    }

    public static boolean isValidEmail(String value) {
        return EmailValidator.getInstance().isValid(value);
    }

    public static boolean isEasyPassword(String username, String password) {
        if (username.equals(password) || password.contains(username)) {
            return true;
        }

        try {
            if (password.length() < 10) {
                Long.parseLong(password);
                int tmp = -1;
                for (int i = 0; i < password.length(); i++) {
                    if (tmp > Integer.parseInt(password.charAt(i) + "")) {
                        return false;
                    }
                    tmp = Integer.parseInt(password.charAt(i) + "");
                }
                return true;
            }
        } catch (Exception ex) {
        }

        return false;
    }

    public static String lockChat(Date lockChat) {
        if (lockChat != null) {
            Date currDate = new Date();
            SimpleDateFormat sdFormat = new SimpleDateFormat("HH'h'mm dd/MM/yyyy");

            if (lockChat.after(currDate)) {
                return "Tk bị khóa chat đến " + sdFormat.format(lockChat);
            }
        }
        return null;
    }

    public static String formatCurrency(String value) {

        String result = "";
        try {
            long vl = Long.parseLong(value);
            int count = 0;
            for (int i = (value.length() - 1); i >= 0; i--) {
                count++;
                if (count == 3 || i == 0) {
                    result = value.substring(i, i + count) + ((result.equals("") || result.equals("-")) ? "" : ".") + result;
                    count = 0;
                }
            }
        } catch (Exception e) {
            result = value;
        }
        //
        return result;
    }

    public static int sumDig(int n) // Function for finding and returning sum of digits of a number
    {
        int a = 0;
        while (n > 0) {
            a = a + n % 10;
            n = n / 10;
        }
        return a;
    }

    public static boolean isValidImei(String imei) {
        imei = imei.trim();
        if (imei.length() != 15) {
            return false;
        }
        try {
            long n = Long.parseLong(imei); // 15 digits cannot be stored in 'int' data type

            String s = Long.toString(n); // Converting the number into String for finding length
            int l = s.length();

            int d = 0, sum = 0;
            for (int i = 15; i >= 1; i--) {
                d = (int) (n % 10);

                if (i % 2 == 0) {
                    d = 2 * d; // Doubling every alternate digit
                }

                sum = sum + sumDig(d); // Finding sum of the digits

                n = n / 10;
            }

//            debug("Output : Sum = " + sum);

            if (sum % 10 == 0) {
                return true;
            }
            return false;
        } catch (Exception ex) {
        }
        return false;
    }

    public static boolean verifyCheckSum(String checksum, String username, String imei, String captcha) {
        if (checksum == null || checksum.length() == 0) {
            return false;
        }

        if (!getMD5("pk15" + username + imei + captcha + "51kp").equals(checksum)) {
            return false;
        }

        return true;
    }

    public static boolean isFacebookAccount(String username) {
        try {
            Long.parseLong(username.substring(2));
            if (username.startsWith("fb")) {
                return true;
            }
        } catch (Exception ex) {

        }
        return false;
    }

    public static boolean isQuickAccount(String username) {
        try {
            Long.parseLong(username.substring(5));
            if (username.startsWith("khach")) {
                return true;
            }
        } catch (Exception ex) {

        }
        return false;
    }

    public static String getHPass(String pass) {
        long salt = System.currentTimeMillis() % 1000;
        return String.format("1,,%s,%s", salt, Util.getMD5(Util.getMD5(pass) + salt));
    }

    public static boolean isValidPassword(String password) {
        for (int i = 0; i < password.length(); i++) {
            char ch = password.charAt(i);
            if (ch < 33 || ch > 126) {
                return false;
            }
        }
        return true;
    }

    public static String validStringTopup(String input) {
        String output = "";
        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if (ch >= 33 && ch <= 126) {
                output += ch;
            }
        }
        return output;
    }

    public static boolean isValidUsername(String username) {
        String[] banned = {"admin"};
        for (String tmp : banned) {
            if (username.contains(tmp)) {
                return false;
            }
        }

        if (!username.matches("[@.a-zA-Z0-9]+")) {
            return false;
        }

        if (username.matches("fb[0-9]+")) {
            return false;
        }

        if (username.startsWith("boom")) {
            return false;
        }

        return true;
    }

    public static String formatDateTime(Date date) {
        return sdf.format(date);
    }

    public static String formatDateDAy(Date date) {
        return sdfDay.format(date);
    }

    public static String formatDateSql(Date date) {
        return sdfSql.format(date);
    }

    public static String chatFormatTime(Date date) {
        return chatSDF.format(date);
    }

    public static String HourFormatTime(Date date) {
        return sdfH.format(date);
    }

    public static String getBlogTime(Date date) {
        Calendar caNow = Calendar.getInstance();
        Calendar caBlog = Calendar.getInstance();
        caBlog.setTime(date);
        long time = (caNow.getTimeInMillis() - caBlog.getTimeInMillis()) / 1000;
        if (time < minute) {
            return "vài giây trước";
        } else if (time < hour) {
            return (time / 60) + " phút trước";
        } else if (time <= day) {
            return (time / 3600) + " giờ trước";
        }
        return sdf.format(date);
    }

    public static Calendar parseDateTime(String strDate) {
        Calendar ca = Calendar.getInstance();
        try {
            Date date = sdf.parse(strDate);
            ca.setTime(date);
            return ca;
        } catch (Exception ex) {
        }
        ca = Calendar.getInstance();
        ca.add(Calendar.HOUR_OF_DAY, -1);
        return ca;
    }

    public static Logger getLogger() {
        return slib_Logger.root();
    }

    public static void info(String username, String action, int money, int balancer, String message) {
        slib_Logger.money().info(username + "\t" + action + "\t" + money + "\t" + balancer + "\t" + message);
    }

    public static void sendProtoData(Channel channel, AbstractMessage data, int service, long curTime) {
        GGProto.ResponseData.Builder builder = GGProto.ResponseData.newBuilder();
        GGProto.Action.Builder action = GGProto.Action.newBuilder();
        action.setService(service);
        if (data != null) {
            action.setData(data.toByteString());
        }
        builder.addActions(action);
        sendRawData(channel, builder.build().toByteArray(), curTime);
    }

    public static void sendStrData(Channel channel, String data, long curTime) {
        if (CfgServer.debug) {
            try {
//                UserInfo user = getUser(channel);
//                if (service != IAction.TABLE_STATE) {
//                    if (user != null) {
//                        getLogger().info(curTime + " " + user.getUsername() + " <- output: " + service + " " + data + "=>" + (System.currentTimeMillis() - curTime));
//                    } else {
//                        getLogger().info(curTime + " none <- output: " + service + " " + data + "=>" + (System.currentTimeMillis() - curTime));
//                    }
//                }
            } catch (Exception ex) {
                getLogger().error(exToString(ex));
            }
        }
        sendRawData(channel, data != null ? data.getBytes() : new byte[0], curTime);
    }

    public static void sendRawData(Channel channel, byte[] data, long curTime) {
        if (channel != null && channel.isWritable()) {
            try {
                data = data == null ? new byte[0] : data;
                ByteBuf buffer = Unpooled.buffer();
                buffer.writeBytes(Constans.OUT_GAME_MAGIC.getBytes()); // K2
                buffer.writeInt(data.length);
                buffer.writeBytes(data);
                Integer protocol = ChUtil.getInteger(channel, Constans.KEY_PROTOCOL);
                if (protocol == null || protocol == Constans.PROTOCOL_TCP)
                    channel.writeAndFlush(buffer).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
                else
                    channel.writeAndFlush(new BinaryWebSocketFrame(buffer)).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
            } catch (Exception ex) {
                getLogger().error(Util.exToString(ex));
            }
        }
    }

    public static void sendGameData(Channel channel, byte[] data, long curTime) {
        if (channel != null && channel.isWritable()) {
            try {
                data = data == null ? new byte[0] : data;
                ByteBuf buffer = Unpooled.buffer();
                buffer.writeBytes(Constans.IN_GAME_MAGIC.getBytes()); // K3
                buffer.writeInt(data.length);
                buffer.writeBytes(data);
                Integer protocol = ChUtil.getInteger(channel, Constans.KEY_PROTOCOL);
                if (protocol == null || protocol == Constans.PROTOCOL_TCP)
                    channel.writeAndFlush(buffer).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
                else
                    channel.writeAndFlush(new BinaryWebSocketFrame(buffer)).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
            } catch (Exception ex) {
                ex.printStackTrace();
                getLogger().error(Util.exToString(ex));
            }
        }
    }

    public static String getStringJson(JSONObject json, String key) {
        try {
            String tmp = json.getString(key);
            if (tmp == null) {
                return "";
            }
            return tmp;
        } catch (Exception ex) {
        }
        return "";
    }

    public static int getIntJson(JSONObject json, String key) {
        try {
            return json.getInt(key);
        } catch (Exception ex) {
        }
        return -1;
    }

    public static boolean getBooleanJson(JSONObject json, String key) {
        try {
            return json.getBoolean(key);
        } catch (Exception ex) {
        }
        return false;
    }

    public static String getMD5(String source) {
        try {
            MessageDigest mdEnc = MessageDigest.getInstance("MD5"); // Encryption
            // algorithm
            mdEnc.update(source.getBytes(), 0, source.length());

            String md5 = new BigInteger(1, mdEnc.digest()).toString(16); // Encrypted

            while (md5.length() < 32) {
                md5 = "0" + md5;
            }

            return md5;
        } catch (Exception ex) {
        }
        return "";
    }

    public static String exToString(Exception ex) {
        StringWriter errors = new StringWriter();
        ex.printStackTrace(new PrintWriter(errors));
        return errors.toString();
    }

    public static String exToString(Throwable ex) {
        String tmp = "";
        for (StackTraceElement e : ex.getStackTrace()) {
            tmp += e.toString() + "\n";
        }
        return ex.toString() + ": " + tmp;
    }

    public static int getIntAttribute(Channel channel, String key) {
        try {
            return Integer.parseInt((String) ChUtil.get(channel, key));
        } catch (Exception ex) {
        }
        return -1;
    }

    public static String getStringAttribute(Channel channel, String key) {
        String value = (String) ChUtil.get(channel, key);
        if (value == null) {
            value = "";
        }
        return value;
    }

    public static long getLongAttribute(Channel channel, String key) {
        try {
            return Long.parseLong((String) ChUtil.get(channel, key));
        } catch (Exception ex) {
        }
        return -1;
    }

    public static boolean getBooleanAttribute(Channel channel, String key) {
        try {
            return (Boolean) ChUtil.get(channel, key);
        } catch (Exception ex) {
        }
        return false;
    }

    public static boolean getBooleanAttribute(Map<String, Object> map, String key) {
        try {
            return (Boolean) map.get(key);
        } catch (Exception ex) {
        }
        return false;
    }

    public static String printList(List<Integer> cards) {
        if (cards == null) {
            return "";
        }
        String output = "";
        for (int i = 0; i < cards.size(); i++) {
            output += cards.get(i) + " ";
        }
        return output;
    }

    public static String getString(String tmp) {
        if (tmp == null) {
            return "";
        }
        return tmp;
    }

    public static int getIntVersion(String version) {
        String[] v = version.split("\\.");
        int ret = 0;
        int count = 2;
        for (String str : v) {
            if (str.length() > 0) {
                int number = 0;
                try {
                    number = Integer.parseInt(str);
                } catch (Exception ex) {
                }
                ret += Math.pow(10, count * 2) * number;
                count--;
            }
        }
        return ret;
    }

    public static int getLevel(int experience, double rate) {
        return (int) Math.pow(experience, 1 / rate);
    }

    public static int getRate(int experience, double rate) {
        int level = (int) Math.pow(experience, 1 / rate);
        double preEx = Math.pow(level, rate);
        double afterEx = Math.pow((level + 1), rate);
        return (int) ((experience - preEx) * 100 / (afterEx - preEx));
    }

    public static int getPoint(List<Integer> userCards) {
        int total = 0;
        for (int j = 0; j < userCards.size(); j++) {
            total += userCards.get(j) / 4 + 1;
        }
        return total;
    }

    public static String getEncryptPassword(String password, int code) {
        int total = 0;
        for (int i = 0; i < password.length(); i++) {
            total += password.charAt(i);
        }
        return String.valueOf(total * code);
    }

    public static List<String> gameResume = Arrays.asList("solotienlenmn", "solotienlenmndc", "solophom", "solosam", "tienlenmn", "tienlenmndc", "phom", "sam", "bacaychuong", "bacay", "lieng", "chan", "tienlenmnnhatantat");
    public static List<String> gameResumeNew = Arrays.asList("bacaychuong", "bacay", "lieng", "chan");

    public static String getUsername(Channel channel) {
        try {
            return ((UserInfo) ChUtil.get(channel, Constans.KEY_USER)).getUsername();
        } catch (Exception ex) {
            // getLogger().error(Util.exToString(ex));
        }
        return "";
    }

    public static UserInfo getUser(Channel channel) {
        try {
            return (UserInfo) ChUtil.get(channel, Constans.KEY_USER);
        } catch (Exception ex) {
            // getLogger().error(Util.exToString(ex));
        }
        return null;
    }

    public static JSONArray getJsonArray(List<Integer> cards) {
        JSONArray arr = new JSONArray();
        if (cards != null) {
            for (Integer card : cards) {
                arr.add(card);
            }
        }
        return arr;
    }

    public static boolean isGoodString(String str) {
        if (str == null || str.length() == 0) {
            return false;
        }
        return true;
    }

    public static String decryptNew(String base64, int seedCode) {
        // trans number
        String pwd = "";
        try {
            String x = seedCode + "";
            int trans = Integer.parseInt(x.charAt(0) + "");

            String deTrans = base64;
            deTrans = transBase64String(base64, -trans);

            pwd = new String(Base64.decodeBase64(deTrans.getBytes()));
            pwd = transBase64String(pwd, -trans);
            return pwd;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return pwd;
    }

    public static String getUserIPAdress(Channel channel) {
        if (channel == null) {
            return "";
        }
        String ip = channel.remoteAddress().toString();
        if (ip.startsWith("/")) {
            ip = ip.substring(1);
        }
        if (ip.contains(":")) {
            ip = ip.substring(0, ip.indexOf(":"));
        }
        return ip;
    }

    public static String transBase64String(String input, int trans) {
        String ret = input;
        if (input.length() != 4) {
            int length = input.length() - 2;
            char[] charTrans = new char[length];
            //
            for (int i = 0; i < length; i++) {
                char ch = ret.charAt(i);
                int newPos = (i + trans) % length;
                if (newPos < 0)
                    newPos = newPos + length;
                charTrans[newPos] = ch;
            }
            //
            ret = new String(charTrans) + input.substring(length);
        }
        //
        return ret;
    }

    public static String formatStringLength(String value, int lengthRequired) {
        while (value.length() < lengthRequired) {
            value = "0" + value;
        }
        return value;
    }

    public static String getCurrencyFormat(double number) {
        DecimalFormatSymbols decimalFormatSymbols = new DecimalFormatSymbols();
        decimalFormatSymbols.setGroupingSeparator('.');
        DecimalFormat decimalFormat = new DecimalFormat("#,##0", decimalFormatSymbols);
        return decimalFormat.format(number);
    }

    public static <T> void joinList(List<T> list1, List<T> list2) {
        for (int i = 0; i < list2.size(); i++) {
            list1.add(list2.get(i));
        }
    }

    public static int[] listToArray(List<Integer> aInt) {
        int[] arr = new int[aInt.size()];
        for (int i = 0; i < aInt.size(); i++) {
            arr[i] = aInt.get(i);
        }
        return arr;
    }

    public static float[] listFloatToArray(List<Float> aInt) {
        float[] arr = new float[aInt.size()];
        for (int i = 0; i < aInt.size(); i++) {
            arr[i] = aInt.get(i);
        }
        return arr;
    }

}
