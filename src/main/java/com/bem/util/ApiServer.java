/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.util;

import com.bem.config.CfgCluster;
import com.bem.object.CPInfor;
import com.bem.object.UserInfo;
import com.k2tek.common.slib_Logger;
import grep.helper.Filer;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.PostMethod;
import org.slf4j.Logger;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"unused", "deprecation"})
public class ApiServer {

    public static final String CODE = "code";
    public static final String USER = "user";
    static Map<String, String> issuer = new HashMap<String, String>();
    static String URL_EXIST, URL_LOGIN, URL_LOGIN0P;
    static String URL_CHANGE_PASSWORD, URL_SUB_KOIN, URL_USER_INFOR, URL_TOP_RICH, URL_OVI_CHARGING;
    static String URL_LOGIN_FACEBOOK = "https://graph.facebook.com/me?access_token=%s";
    static String URL_LOGIN_FACEBOOK_NEW = "https://graph.facebook.com/v2.0/me/ids_for_business?access_token=%s";
    static String URL_LOGIN_APPOTA = "https://api.appota.com/user/info?access_token=%s&lang=LANG";
    static String URL_CAPTCHA_IMAGE = "http://captcha.beme.com.vn/test.php";
    static String URL_RECOVER_PASS = "http://local.api/login0p.php?email=%s";
    //
    static String URL_TOP_UP = "http://api.phang.mobi/the/clientreq_all.php?issuer=%s&cardCode=%s&userName=%s&serial=%s&cp=%s";
    //    static String URL_VERIFY_IAP = "http://api2.phang.mobi/iap/iap_verify.php?receipt=%s&username=%s&cp=%s&sandbox=%s";
    static String URL_VERIFY_IAP = "http://api.boomba.vn/iap/iap_verify.php?username=%s&cp=%s&os=%s&serverId=%s";
    static String URL_GIFT_CODE = "http://cj.grepgame.com/consume/%s/%s/%s"; // giftcode/userId/serverId

    public static String consumeGiftCode(long userId, String giftCode, int serverId) {
        String url = String.format(URL_GIFT_CODE, URLEncoder.encode(giftCode), userId, serverId);
        try {
            long l = System.currentTimeMillis();
            String ret = Filer.getDataFromUrl(url);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            if (ret != null && ret.length() > 0) {
                return ret;
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return "{\"code\":-1,\"message\":\"System error. Please try again later\"}";
    }


    public static String recoverPassword(String email) {
        try {
            long l = System.currentTimeMillis();
            String url = String.format(URL_RECOVER_PASS, URLEncoder.encode(email));
            String ret = getPostContent(url, null, true);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            if (ret != null && ret.length() > 0) {
                return ret;
            }
        } catch (Exception ex) {
            getLogger().error(URL_OVI_CHARGING + " -> " + Util.exToString(ex));
        }
        return "System error. Please try again later.";
    }

    public static String verifyIAP(String receipt, String username, String cp, String os, int serverId) {
        CPInfor cpInfor = null;// CfgServer.getCP(cp);
        if (cpInfor != null) {
            cp = cpInfor.getParentCP() + "_" + cp;
        }

        Map<String, String> mParam = new HashMap<String, String>();
        mParam.put("receipt", receipt);
        String url = String.format(URL_VERIFY_IAP, URLEncoder.encode(CfgCluster.getRealUsername(username)), URLEncoder.encode(cp), os, serverId + "");
        try {
            long l = System.currentTimeMillis();
            String ret = getPostContent(url, mParam, true);

            getLogger().info(url + " -> " + (System.currentTimeMillis() - l)); //  + "->" + ret + "->" + receipt
            if (ret != null && ret.length() > 0) {
                return ret;
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return "System error. Please try again later.";
    }

    public static JSONObject getCaptcha() {
        String url = URL_CAPTCHA_IMAGE;
        try {
            long l = System.currentTimeMillis();
            String result = getHttpContent(url);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.containsKey("url") && obj.containsKey("code")) {
                return obj;
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return null;
    }

    public static String appotaLogin(String token) {
        String url = String.format(URL_LOGIN_APPOTA, token);
        try {
            long l = System.currentTimeMillis();
            // String result = getPostContent(url, null, true);
            String result = getHttpContent(url);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.containsKey("data")) {
                if (obj.getJSONObject("data").containsKey("username")) {
                    return result;
                    // String remoteUsername = mapObject.getString("username");
                    // if (username.equalsIgnoreCase(remoteUsername)) {
                    // return result;
                    // }
                    // return "1Sai thông tin đăng nhập";
                }
                return "1Sai thông tin đăng nhập";
            } else {
                return "1Bạn chưa đăng nhập APPOTA";
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return "1Đăng nhập APPOTA thất bại. Hãy thử lại sau";
    }

    public static String vtcOnlineLogin(String token) {
        String url = "http://billing.graph.go.vn/me/?access_token=" + token;
        try {
            long l = System.currentTimeMillis();
            String result = getHttpContent(url);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.getInt("_code") == 1) {
                return result;
            } else {
                return "1Bạn chưa đăng nhập MobileId";
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return "1Đăng nhập MobileId thất bại. Hãy thử lại sau";
    }

    public static String facebookLoginNew(String fbId, String token) {
        String url = String.format(URL_LOGIN_FACEBOOK_NEW, token);
        try {
            long l = System.currentTimeMillis();
            String result = getHttpContent(url);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.containsKey("data")) {
                JSONArray arr = obj.getJSONArray("data");
                for (int i = 0; i < arr.size(); i++) {
                    JSONObject tmp = arr.getJSONObject(i);
                    if (tmp.getString("id").equals(fbId)) {
                        return result;
                    }
                }
                return "1Bạn chưa đăng nhập facebook";
            } else {
                return "1Bạn chưa đăng nhập facebook";
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return "1Đăng nhập Facebook thất bại. Hãy thử lại sau";
    }

    public static String facebookLogin(String fbId, String token) {
        String url = String.format(URL_LOGIN_FACEBOOK, token);
        try {
            long l = System.currentTimeMillis();
            // String result = getPostContent(url, null, true);
            String result = getHttpContent(url);
            // debug(result);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.containsKey("id")) {
                if (obj.getString("id").equals(fbId)) {
                    return result;
                } else {
                    return "1Sai thông tin đăng nhập";
                }
            } else {
                return "1Bạn chưa đăng nhập facebook";
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return "1Đăng nhập Facebook thất bại. Hãy thử lại sau";
    }

    public static String oviCharging(String username, String cp, String verify) {
        try {
            long l = System.currentTimeMillis();
            String url = String.format(URL_OVI_CHARGING, URLEncoder.encode(verify), URLEncoder.encode(username), cp);
            String ret = getPostContent(url, null, true);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            if (ret != null && ret.length() > 0) {
                return ret;
            }
        } catch (Exception ex) {
            getLogger().error(URL_OVI_CHARGING + " -> " + Util.exToString(ex));
        }
        return "Lỗi dịch vụ. Bạn hãy chờ vài phút rồi thử lại";
    }

    public static String topUp(String operator, String code, String username, String bonusUsername, String serial, String value, String cp) {
        CPInfor cpInfor = null;//CfgServer.getCP(cp);
        if (cpInfor != null) {
            cp = cpInfor.getParentCP() + "_" + cp;
        }
        String issuer = getIssuer(operator);
        if (issuer != null && issuer.length() > 0) {
            String url = "";
            code = code.replaceAll(" ", "");
            issuer = issuer.replaceAll(" ", "");
            serial = serial.replaceAll(" ", "");
            if (bonusUsername != null && bonusUsername.length() > 0) {
                url = String.format(URL_TOP_UP, URLEncoder.encode(issuer), URLEncoder.encode(code), URLEncoder.encode(bonusUsername + "adminadmin" + username), URLEncoder.encode(serial), cp);
            } else {
                url = String.format(URL_TOP_UP, URLEncoder.encode(issuer), URLEncoder.encode(code), URLEncoder.encode(username), URLEncoder.encode(serial), cp);
            }
            if (username.equals("tigeronline")) {
                getLogger().info(url + " -> ");
            }
            try {
                long l = System.currentTimeMillis();
                String ret = getPostContent(url, null, true);
                getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
                if (ret != null && ret.length() > 0) {
                    return ret;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                getLogger().error(url + " -> " + Util.exToString(ex));
            }
            return "Lỗi dịch vụ. Bạn hãy chờ vài phút rồi thử lại";
        } else {
            return "Loại thẻ " + operator + " chưa được hỗ trợ";
        }
    }

    public static List<UserInfo> getTopRichUser() {
        String url = URL_TOP_RICH;
        try {
            long l = System.currentTimeMillis();
            String result = getPostContent(url, null, true);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.getInt(CODE) == 0) {
                List<UserInfo> listUser = new ArrayList<UserInfo>();
                JSONArray arrUser = obj.getJSONArray("users");
                for (int i = 0; i < arrUser.size(); i++) {
                    JSONObject uson = arrUser.getJSONObject(i);
                    if (!uson.getString("username").equals("admin")) {
//                        UserInfor newUser = new UserInfor();
//                        newUser.setUserId(uson.getInt("id"));
//                        newUser.setUsername(uson.getString("username"));
//                        // newUser.setPassword(uson.getString("password"));
//                        newUser.setBalance(uson.getLong("koin"));
//                        newUser.setMobile(uson.getString("mobile"));
//                        listUser.add(newUser);
                    }
                }
                return listUser;
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return null;
    }

    public static String login(Channel channel, String ip, String username, String password, String code, String version, String osType) {
//        if (!username.matches("[a-zA-Z0-9]+")) {
//            return null;
//        }
        String url = "";
        String decryptPassword = "";
        // if (version.compareTo("2.2.6") > 0 || !osType.equalsIgnoreCase("j2me")) {
        decryptPassword = Util.decryptNew(password, Integer.parseInt(code));
        // if (CfgServer.loginMD5) {
        String md5 = decryptPassword;
        if (decryptPassword.length() < 32) {
            md5 = Util.getMD5(decryptPassword);
        }
        // }
        url = String.format(URL_LOGIN0P, URLEncoder.encode(username), URLEncoder.encode(md5), code);
        // } else {
        // url = String.format(URL_LOGIN, URLEncoder.encode(username), URLEncoder.encode(password), code);
        // }

        try {
            long l = System.currentTimeMillis();
            String result = getPostContent(url, null, true);
            JSONObject obj = JSONObject.fromObject(result);
            // getLogger().info(mapObject.toString());
            if (obj.getInt(CODE) == 0) {
                getLogger().info(ip + " " + channel.id().asShortText() + " " + url + " " + (System.currentTimeMillis() - l));
                return decryptPassword;
            } else {
                getLogger().info(ip + " " + channel.id().asShortText() + " " + url + " " + decryptPassword + " -> " + (System.currentTimeMillis() - l));
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + version + " -> " + password + " -> " + decryptPassword + " -> " + Util.exToString(ex));
        }
        return null;
    }

    public static int getVipTypeUser(String username) {
        String url = String.format(URL_USER_INFOR, URLEncoder.encode(username));
        try {
            long l = System.currentTimeMillis();
            String result = getPostContent(url, null, true);
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.getInt(CODE) == 0) {
                JSONObject uson = obj.getJSONObject(USER);
                if (uson.containsKey("vip_type")) {
                    int vipType = 0;
                    try {
                        vipType = uson.getInt("vip_type");
                    } catch (Exception ex) {
                    }
                    return vipType;
                }
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return 0;
    }

    public static UserInfo getUserInfor(Channel channel, String ip, String username, boolean getKoinAdded) {
        String url = String.format(URL_USER_INFOR, URLEncoder.encode(username));
        try {
            long l = System.currentTimeMillis();
            String result = getPostContent(url, null, true);
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.getInt(CODE) == 0) {
//                UserInfor newUser = new UserInfor();
//                JSONObject uson = mapObject.getJSONObject(USER);
//                newUser.setUserId(uson.getInt("id"));
//                newUser.setUsername(uson.getString("username"));
//                newUser.setMobile(uson.getString("mobile"));
//                newUser.setDbUser(new User());
//                newUser.getDbUser().setGender((byte) 1);
//
//                if (uson.containsKey("vip_type")) {
//                    int vipType = 0;
//                    try {
//                        vipType = uson.getInt("vip_type");
//                    } catch (Exception ex) {
//                    }
//                    newUser.setVipType(vipType);
//                }
//
//                if (mapObject.containsKey("next_percent")) {
//                    int vipPercent = 0;
//                    try {
//                        vipPercent = mapObject.getInt("next_percent");
//                    } catch (Exception ex) {
//                    }
//                    newUser.setVipPercent(vipPercent);
//                }
//                long balanceJedis = -1, balanceJedisLogout = -1;
//                long balance = uson.getLong("koin");
//                balanceJedisLogout = JCache.getInstance().getBalanceLogout(username);
//                if (balanceJedisLogout == -1) {
//                    balanceJedis = JCache.getInstance().getBalance(username);
//                } else {
//                    JCache.getInstance().removeValue(JedisService.KEY_BALANCE_LOGOUT + username);
//                }
//
////                getLogger().info(ip + " " + channel.getId() + " " + balanceJedisLogout + " " + balanceJedis + " " + balance + " " + url + " -> " + (System.currentTimeMillis() - l));
//                balance = balanceJedisLogout != -1 ? balanceJedisLogout : (balanceJedis != -1 ? balanceJedis : balance);
//                balance = balance < 0 ? 0 : balance;
////                newUser.setBalance(balance);
//                return newUser;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return null;
    }

    public static UserInfo getUserInfor(String username, boolean getKoinAdded) {
        String url = String.format(URL_USER_INFOR, URLEncoder.encode(username));
        try {
            long l = System.currentTimeMillis();
            String result = getPostContent(url, null, true);
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.getInt(CODE) == 0) {
//                UserInfor newUser = new UserInfor();
//                JSONObject uson = mapObject.getJSONObject(USER);
//                newUser.setUserId(uson.getInt("id"));
//                newUser.setUsername(uson.getString("username"));
//                newUser.setMobile(uson.getString("mobile"));
//                newUser.setDbUser(new User());
//                newUser.getDbUser().setGender((byte) 1);
//
//                if (uson.containsKey("vip_type")) {
//                    int vipType = 0;
//                    try {
//                        vipType = uson.getInt("vip_type");
//                    } catch (Exception ex) {
//                    }
//                    newUser.setVipType(vipType);
//                }
                // Kien add 2012/10/30
//                if (JCache.getInstance() != null) {
//                    long balanceJedis = -1;
//                    // int balance = ProcessUserKoin.getBalance(username);
//                    // if (balance == -1) {
//                    long balance = JCache.getInstance().getBalance(username);
//                    balanceJedis = balance;
//                    if (balance == -1) {
//                        balance = uson.getLong("koin");
//                    }
//                    // }
//
//                    getLogger().info(balanceJedis + " " + balance + " " + url + " -> " + (System.currentTimeMillis() - l));
//                    newUser.setBalance(balance);
//                } else {
//                    newUser.setBalance(uson.getLong("koin"));
//                }
//                return newUser;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return null;
    }

    public static boolean subUserBalance(String username, String password, String code, int koin) {
        String url = String.format(URL_SUB_KOIN, URLEncoder.encode(username), password, code, koin);
        try {
            long l = System.currentTimeMillis();
            String result = getPostContent(url, null, true);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            JSONObject obj = JSONObject.fromObject(result);
            return true;
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return false;
    }

    public static boolean checkExistUser(String username) {
        String url = String.format(URL_EXIST, URLEncoder.encode(username));
        try {
            String result = getPostContent(url, null, true);
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.getInt(CODE) == 1) {
                return true;
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return false;
    }

    public static boolean changePassword(String username, String oldPassword, String newPassword, String code) {
        String url = String.format(URL_CHANGE_PASSWORD, URLEncoder.encode(username), URLEncoder.encode(oldPassword), URLEncoder.encode(newPassword), code);
        try {
            long l = System.currentTimeMillis();
            String result = getPostContent(url, null, true);
            getLogger().info(url + " -> " + (System.currentTimeMillis() - l));
            JSONObject obj = JSONObject.fromObject(result);
            if (obj.getInt(CODE) == 0) {
                return true;
            }
        } catch (Exception ex) {
            getLogger().error(url + " -> " + Util.exToString(ex));
        }
        return false;
    }

    public static String getHttpContent(String address) {
        try {
            URL page = new URL(address);
            StringBuffer text = new StringBuffer();
            HttpURLConnection conn = (HttpURLConnection) page.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(10000);
            conn.connect();
            InputStreamReader in = new InputStreamReader((InputStream) conn.getContent());

            BufferedReader buff = new BufferedReader(in);
            String line = buff.readLine();
            while (line != null) {
                text.append(line);
                line = buff.readLine();
            }
            return text.toString();
        } catch (Exception ex) {
            getLogger().error(address + "->" + Util.exToString(ex));
        }
        return "";
    }

    public static String getPostContent(String url, Map<String, String> params, boolean isPost) {
        try {
            HttpClient client = new HttpClient();
            client.setTimeout(5000);
            PostMethod post = new PostMethod(url);
            post.setRequestHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
            if (params != null) {
                List<String> keys = new ArrayList<String>(params.keySet());
                for (String key : keys) {
                    post.addParameter(key, String.valueOf(params.get(key)));
                }
            }
            int returnCode = client.executeMethod(post);
            if (returnCode == HttpStatus.SC_NOT_IMPLEMENTED) {
                return "";
            }
            String result = post.getResponseBodyAsString();
            post.releaseConnection();
            return result;
        } catch (Exception ex) {
            getLogger().error(url + "->" + Util.exToString(ex));
        }
        return "";
    }

    public static int getUserBalance(Channel channel, String username) {
        // if (channel != null) {
        // AbstractTable tableCache = (AbstractTable) channel.getAttribute(Constans.KEY_TABLE);
        // Integer tableId = (Integer) channel.getAttribute(Constans.KEY_TABLEID);
        // if (tableCache != null || tableId != null) {
        // return -1;
        // }
        // }
        // UserInfor u = getUserInfor(username, false);
        // if (u == null) {
        // return -1;
        // } else {
        // return u.getBalance();
        // }
        return -1;
    }

    public static Logger getLogger() {
        return slib_Logger.userAPI();
    }

    public static void addIssuer(String strIssuer) {
        if (strIssuer != null && strIssuer.contains(":")) {
            int index = strIssuer.indexOf(":");
            issuer.put(strIssuer.substring(0, index), strIssuer.substring(index + 1, strIssuer.length()));
        }
    }

    public static String getIssuer(String key) {
        return issuer.get(key);
    }

    public static void loadConfig(String strJson) {
        JSONObject json = JSONObject.fromObject(strJson);
        URL_TOP_UP = json.getString("top_up");
        URL_VERIFY_IAP = json.getString("iap");
        URL_GIFT_CODE = json.getString("giftcode");
        JSONArray iss = json.getJSONArray("issuer");
        for (Object object : iss) {
            addIssuer((String) object);
        }
//        URL_EXIST = json.getString("exist");
//        URL_LOGIN = json.getString("login");
//        URL_LOGIN0P = json.getString("login0p");
//        URL_CHANGE_PASSWORD = json.getString("change_password");
//        URL_SUB_KOIN = json.getString("sub_koin");
//        URL_USER_INFOR = json.getString("user_infor");
//        URL_TOP_RICH = json.getString("top_rich");
//        URL_OVI_CHARGING = json.getString("ovi_charging");
//        URL_CAPTCHA_IMAGE = json.getString("captcha");
//        URL_VERIFY_IAP = json.getString("iap");
//        URL_RECOVER_PASS = json.getString("recover_password");
    }
}
