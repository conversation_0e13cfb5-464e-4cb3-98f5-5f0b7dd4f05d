package com.bem.util;

public class Level {

	public static String[] title = { "NOO<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ee", "<PERSON>tor", "Graduate", "Honor graduate", "Scholar", "Teacher", "Engineer", "Professor", "PHD", "1st degree Master", "2nd degree Master", "3rd degree Master", "4th degree Master", "5th degree Master", "6th degree Master", "7th degree Master", "8th degree Master", "9th degree Master", "10th degree Master", "Dominator" };

	// Level Chung: cac game choi nhanh nhu bacay, lieng se tinh exp = 1/3 cac game khac.
	// Trung binh exp cac game ra diem exp chung roi tu do tinh level theo cong thuc duoi

	public static void main(String[] args) {
	}

	public static int getRate(int exp) {
		int level = 1;
		if (exp >= 15000) {
			level = (exp - 15000) / 10000;
			if (level + 12 >= title.length) {
				return 100;
			} else {
				return (int) Math.floor((exp - 15000 - 10000 * level) * 100 / 10000);
			}
		} else if (exp >= 10000) {
			return (int) Math.floor((exp - 10000) * 100 / 5000);
		} else if (exp >= 7500) {
			return (int) Math.floor((exp - 7500) * 100 / 2500);
		} else if (exp >= 5000) {
			return (int) Math.floor((exp - 5000) * 100 / 2500);
		} else if (exp >= 3500) {
			return (int) Math.floor((exp - 3500) * 100 / 1500);
		} else if (exp >= 2750) {
			return (int) Math.floor((exp - 2750) * 100 / 750);
		} else if (exp >= 2000) {
			return (int) Math.floor((exp - 2000) * 100 / 750);
		} else if (exp >= 1500) {
			return (int) Math.floor((exp - 1500) * 100 / 500);
		} else if (exp >= 1000) {
			return (int) Math.floor((exp - 1000) * 100 / 500);
		} else if (exp >= 500) {
			return (int) Math.floor((exp - 500) * 100 / 500);
		} else if (exp >= 100) {
			return (int) Math.floor((exp - 100) * 100 / 400);
		} else if (exp >= 0) {
			return (int) Math.floor(exp * 100 / 100);
		}
		return level;
	}

	public static int getLevel(int exp) {
		int level = 1;
		if (exp >= 15000) {
			level = 12 + (exp - 15000) / 10000;
		} else if (exp >= 10000) {
			level = 11;
		} else if (exp >= 7500) {
			level = 10;
		} else if (exp >= 5000) {
			level = 9;
		} else if (exp >= 3500) {
			level = 8;
		} else if (exp >= 2750) {
			level = 7;
		} else if (exp >= 2000) {
			level = 6;
		} else if (exp >= 1500) {
			level = 5;
		} else if (exp >= 1000) {
			level = 4;
		} else if (exp >= 500) {
			level = 3;
		} else if (exp >= 100) {
			level = 2;
		} else if (exp >= 0) {
			level = 1;
		}
		return level;
	}

	public static int getLevel(long exp) {
		int level = 1;
		if (exp >= 15000) {
			level = (int) (12 + (exp - 15000) / 10000);
		} else if (exp >= 10000) {
			level = 11;
		} else if (exp >= 7500) {
			level = 10;
		} else if (exp >= 5000) {
			level = 9;
		} else if (exp >= 3500) {
			level = 8;
		} else if (exp >= 2750) {
			level = 7;
		} else if (exp >= 2000) {
			level = 6;
		} else if (exp >= 1500) {
			level = 5;
		} else if (exp >= 1000) {
			level = 4;
		} else if (exp >= 500) {
			level = 3;
		} else if (exp >= 100) {
			level = 2;
		} else if (exp >= 0) {
			level = 1;
		}
		return level;
	}
	
	public static int getGuildLevel(long exp) {
		int level = 1;
		if (exp >= 150000) {
			level = (int) (12 + (exp - 150000) / 100000);
		} else if (exp >= 100000) {
			level = 11;
		} else if (exp >= 75000) {
			level = 10;
		} else if (exp >= 50000) {
			level = 9;
		} else if (exp >= 35000) {
			level = 8;
		} else if (exp >= 27500) {
			level = 7;
		} else if (exp >= 20000) {
			level = 6;
		} else if (exp >= 15000) {
			level = 5;
		} else if (exp >= 10000) {
			level = 4;
		} else if (exp >= 5000) {
			level = 3;
		} else if (exp >= 1000) {
			level = 2;
		} else if (exp >= 0) {
			level = 1;
		}
		return level;
	}
	
	public static int getGuildRate(long exp) {
		int level = 1;
		if (exp >= 150000) {
			level = (int) (exp - 150000) / 100000;
			if (level + 12 >= title.length) {
				return 100;
			} else {
				return (int) Math.floor((exp - 150000 - 100000 * level) * 100 / 100000);
			}
		} else if (exp >= 100000) {
			return (int) Math.floor((exp - 100000) * 100 / 50000);
		} else if (exp >= 75000) {
			return (int) Math.floor((exp - 75000) * 100 / 25000);
		} else if (exp >= 50000) {
			return (int) Math.floor((exp - 50000) * 100 / 25000);
		} else if (exp >= 35000) {
			return (int) Math.floor((exp - 35000) * 100 / 15000);
		} else if (exp >= 27500) {
			return (int) Math.floor((exp - 27500) * 100 / 7500);
		} else if (exp >= 20000) {
			return (int) Math.floor((exp - 20000) * 100 / 7500);
		} else if (exp >= 15000) {
			return (int) Math.floor((exp - 15000) * 100 / 5000);
		} else if (exp >= 10000) {
			return (int) Math.floor((exp - 10000) * 100 / 5000);
		} else if (exp >= 5000) {
			return (int) Math.floor((exp - 5000) * 100 / 5000);
		} else if (exp >= 1000) {
			return (int) Math.floor((exp - 1000) * 100 / 4000);
		} else if (exp >= 0) {
			return (int) Math.floor(exp * 100 / 1000);
		}
		return level;
	}
}
