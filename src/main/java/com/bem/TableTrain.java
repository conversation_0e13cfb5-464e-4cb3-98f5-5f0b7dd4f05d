package com.bem;

import com.bem.boom.CacheBattle;
import com.bem.boom.TaskMonitor;
import com.bem.boom.effect.EffectDropBox;
import com.bem.boom.effect.EffectSuddenItem;
import com.bem.boom.map.MapResource;
import com.bem.boom.unit.Player;
import com.bem.matcher.TableMonitor;
import com.bem.matcher.TeamObject;
import com.bem.util.CommonProto;
import com.cache.MainCache;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.proto.GGProto.ProtoEndgame;
import com.proto.GGProto.ProtoPlayerResult;
import io.netty.channel.Channel;

import java.util.ArrayList;

/**
 * Nguoi choi dau tien vao ban, game start luon, nhung duoi client state la
 * WAITTING FOR OTHER PLAYER
 * <p/>
 * Tu do tro di cu co thang nao vao sau thi cho spawn o vi tri ngau nhien
 * <p/>
 * Khi tat ca cung thoat thi game stop
 * <p/>
 * Server moi lan tra ve chi can gui snapshot state cua tat ca client, client se
 * tu biet la co ai ra, ai vao
 */

public class TableTrain extends AbstractTable {
    public TableTrain() {
    }

    public TableTrain(CacheBattle cacheBattle) {
        super(cacheBattle);
        this.mapId = MapResource.getSoloMap();
        this.mode = TeamObject.TRAIN;
        delayStartGame = 5;
//        MainCache.getInstance().addBossCCU(numberPlayer, id, String.valueOf(this.hashCode()) + " " + mapId);
        initMap();
    }

    @Override
    protected boolean subJoinPlayer(Channel channel, byte[] srcRequest, boolean autoJoin) {
        return false;
    }

    @Override
    protected void subLeavePlayer(Player player, Channel channel, byte[] srcRequest) {
        player.leaveStatus = Constans.PLAYER_STATUS_LEAVE;
        if (channel == null) return;
//        TeamObject team = (TeamObject) ChUtil.get(channel, Constans.KEY_USER_TEAM);
//        if (team != null) {
//            for (int i = 0; i < team.aUser.size(); i++) {
//                if (team.aUser.get(i).getId() == player.user.id) {
//                    team.removeUser(team.aUser.get(i).getId());
//                }
//            }
//        }
//        ChUtil.remove(channel, Constans.KEY_USER_TEAM);

        ProtoEndgame.Builder protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(10);
        ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
        playerBuilder.setTeam(player.user.team);
        protoEndgame.addAResult(playerBuilder);
        sendPlayer(channel, IAction.LEAVE_TABLE_1, protoEndgame.build());
    }

    @Override
    protected void doSubUnSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
    }

    @Override
    protected synchronized int doSubSyncAction(Channel channel, int action, byte[] srcRequest, long curTime) {
        switch (action) {
        }
        return 0;
    }

    void endGame() {
        if (!isPlay) {
            return;
        }
        isPlay = false;
        protoEndgame = ProtoEndgame.newBuilder();
        protoEndgame.setType(mode);
        protoEndgame.setTeamWin(teamWin);
        protoEndgame.setTimeCreated(System.currentTimeMillis());

        botUsername = new ArrayList<String>();
        for (Player player : aPlayer) {
            resetPlayer(player);
            botUsername.add(player.user.username);
            ProtoPlayerResult.Builder playerBuilder = ProtoPlayerResult.newBuilder();
            playerBuilder.setUser(CommonProto.protoUser(player.user));
            playerBuilder.setTeam(player.user.team);
            playerBuilder.setStatus(player.leaveStatus);
            protoEndgame.addAResult(playerBuilder);
        }
        protoEndgame.setGameId(Integer.parseInt(id));
        updateHistory(protoEndgame);
        timeEndgame = server_time + 3;
        MainCache.getInstance().addRankCCU(-numberPlayer, id);
        TaskMonitor.getInstance().removeTable(id);
        TableMonitor.removeTable(cacheBattle.getKey());
    }

    public void checkEndGame() {
        int teamAlive = -1;
        for (Player player : aPlayer) {
            if (player.isAlive()) {
                if (teamAlive == -1) {
                    teamAlive = player.user.team;
                } else if (teamAlive != player.user.team) {
                    return;
                }
            }
        }
        teamWin = teamAlive;
        gameState = STATE_END_GAME;
    }

//    @Override
//    protected void initPlayer() {
//        debug("reset aplayer Team");
//        aPlayer = new ArrayList<Player>();
//        int count1 = 0, count2 = 0;
//        for (int i = 0; i < aTeam.size(); i++) {
//            debug("aTeam.get(i).aUser.size() = " + i + " " + aTeam.get(i).aUser.size());
//            if (aTeam.get(i).aUser.size() == 1) {
//                count1++;
//            } else {
//                count2++;
//            }
//        }
//        debug(count1 + " " + count2);
//        int posIndex = 0, teamIndex = 0, count = 0;
//        if (count1 == 2 && count2 == 0) {
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                for (UserInfo user : aUser) {
//                    createPlayer(user, teamIndex, posIndex);
//                    posIndex++;
//                }
//                posIndex++;
//                teamIndex++;
//            }
//        } else if (count1 == 4) {
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                if (aUser.size() == 1) {
//                    for (UserInfo user : aUser) {
//                        createPlayer(user, teamIndex, posIndex);
//                        posIndex++;
//                    }
//                    if (++count == 2) teamIndex++;
//                }
//            }
//        } else if (count1 == 2 && count2 == 1) {
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                teamIndex = aUser.size() == 1 ? 0 : 1;
//                for (UserInfo user : aUser) {
//                    createPlayer(user, teamIndex, posIndex);
//                    posIndex++;
//                }
//            }
//        } else if (count2 == 2) {
//            //  slib_Logger.root().warn(Xerver.mCounter.get(3) + " " + this + " " + Util.exToString(ex));
//            String debug = "";
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                debug += "+size=" + aTeam.get(i).aUser.size() + " - ";
//                for (UserInfo user : aUser) {
//                    debug += posIndex + " " + user.getDbUser().getUsername() + " ";
//                    posIndex++;
//                }
//                teamIndex++;
//            }
//            slib_Logger.root().warn(debug);
//            posIndex = 0;
//            teamIndex = 0;
//            for (int i = 0; i < aTeam.size(); i++) {
//                List<UserInfo> aUser = aTeam.get(i).aUser;
//                for (UserInfo user : aUser) {
//                    createPlayer(user, teamIndex, posIndex);
//                    posIndex++;
//                }
//                teamIndex++;
//            }
//        }
//    }

    void doAction() {

    }

    @Override
    void initMap() {
        super.initMap();
        aEffect.add(new EffectDropBox(this, map));
        aEffect.add(new EffectSuddenItem(this, map));
    }

    @Override
    public AbstractTable getNewInstance(CacheBattle cacheBattle) {
        return new TableTrain(cacheBattle);
    }
}
