package com.bem.config;

import com.bem.boom.Resources;
import com.bem.object.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.k2tek.Constans;
import com.proto.GGProto;
import grep.database.Database2;
import grep.helper.DateTime;
import net.sf.json.JSONObject;

import java.util.*;

public class CfgShop {
    public static final int SHOP_THANBI = 200;
    public static final int SHOP_BOSS = 201;
    public static final int SHOP_MEDAL = 202;
    public static final int SHOP_VIP = 203;
    public static final int SHOP_AVATAR = 204;

    public static final String[] SHOP_NAME = {"", "shop_thanbi", "shop_boss", "shop_medal", "shop_vip", "shop_avatar"};

    public static JSONObject json;
    public static DataConfig config;
    public static List<ShopSpecial> aShopSpecial;
    public static Map<String, ShopSpecial> mShopSpecial = new HashMap<>();

    public static ResPet getRandomEgg() {
        int ret = new Random().nextInt(100);
        for (int i = 0; i < config.egg.length; i++) {
            if (config.egg[i] >= ret) {
                return Resources.getRandomPet(i + 1);
            }
        }
        return null;
    }

    public static ResMaterial getRandomMaterial(int level) {
        int ret = new Random().nextInt(100);
        int[] rate = config.chestMaterial.get(level);
        for (int i = 0; i < rate.length; i++) {
            if (rate[i] >= ret) {
                return Resources.getRandomMaterial(i + 1);
            }
        }
        return null;
    }

    public static ResAvatar getRandomAvatar(int level) {
        int ret = new Random().nextInt(100);
        int[] rate = config.chestAvatar.get(level);
        for (int i = 0; i < rate.length; i++) {
            if (rate[i] >= ret) {
                return Resources.getRandomAvatar(i + 1);
            }
        }
        return null;
    }

    public static MyShop getMyShop(UserInfo user) {
        if (user.getUData().myShop == null) {
            user.getUData().myShop = new Gson().fromJson(user.getUData().getShopSpecial(), MyShop.class);
        }
        if (user.getUData().myShop == null) {
            user.getUData().myShop = new CfgShop().getMyShopInstance();
        }
        refreshShop(user);
        return user.getUData().myShop;
    }

    public static boolean refreshShop(UserInfo user) {
        MyShop shop = user.getUData().myShop;
        boolean hasUpdate = false;
        // shop than bi
        if (System.currentTimeMillis() - shop.startTime >= config.timeRefresh * 1000) {
            hasUpdate = true;
            shop.aItem.clear();
            shop.startTime = System.currentTimeMillis();
            // 2 bomb
            shop.aItem.addAll(ResBomb.get2Bomb(mShopSpecial, 1, Constans.PRICE_NONE));
            // 2 material
            shop.aItem.addAll(ResMaterial.get2Material(mShopSpecial, 2, Constans.PRICE_NONE));
            // 4 avatar
            shop.aItem.addAll(ResAvatar.get2Avatar(mShopSpecial, 7, Constans.PRICE_NONE));
//            // 2 avatar fragment
//            shop.aItem.addAll(ResAvatar.get2AvatarFragment(mShopSpecial, 3, Constans.PRICE_NONE));
            // 2 pet fragment
            shop.aItem.addAll(ResPet.get2PetFragment(mShopSpecial, 4, Constans.PRICE_NONE));
            // 2 pet food
            shop.aItem.addAll(ResPetFood.get2PetFood(mShopSpecial, 5, Constans.PRICE_NONE));
            // 2 accessory
            shop.aItem.addAll(ResAccessories.get2Accessories(mShopSpecial, 6, Constans.PRICE_NONE));
        }

        // shop trang phuc
        if (System.currentTimeMillis() - shop.startTimeAvatar >= config.timeRefresh * 1000) {
            hasUpdate = true;
            if (shop.aAvatar == null) shop.aAvatar = new ArrayList<>();
            else shop.aAvatar.clear();
            shop.startTimeAvatar = System.currentTimeMillis();
            // 2 bomb
//            shop.aAvatar.addAll(ResBomb.get2Bomb(mShopSpecial, 1, Constans.PRICE_NONE));
            // 4 avatar
            shop.aAvatar.addAll(ResAvatar.get10Avatar(mShopSpecial, 7, Constans.PRICE_NONE));
//            // 2 avatar fragment
//            shop.aItem.addAll(ResAvatar.get2AvatarFragment(mShopSpecial, 3, Constans.PRICE_NONE));
            // 2 accessory
//            shop.aAvatar.addAll(ResAccessories.get2Accessories(mShopSpecial, 6, Constans.PRICE_NONE));
        }

        // shop medal, boss, vip
        if (shop.strDate == null || !shop.strDate.equals(DateTime.getDateyyyyMMdd(new Date()))) {
            hasUpdate = true;
            shop.strDate = DateTime.getDateyyyyMMdd(new Date());

            shop.aBoss = new ArrayList<>();
//            shop.aBoss.addAll(ResAvatar.get2AvatarFragment(mShopSpecial, 3, Constans.PRICE_MEDAL_BOSS));
            shop.aBoss.addAll(ResPet.get2PetFragment(mShopSpecial, 4, Constans.PRICE_MEDAL_BOSS));
            shop.aBoss.addAll(ResPetFood.get2PetFood(mShopSpecial, 5, Constans.PRICE_MEDAL_BOSS));
            shop.aBoss.addAll(ResAccessories.get2Accessories(mShopSpecial, 6, Constans.PRICE_MEDAL_BOSS));

//            shop.aMedal = new ArrayList<>();
//            shop.aMedal.addAll(ResAvatar.get2AvatarFragment(mShopSpecial, 3, Constans.PRICE_MEDAL));
//            shop.aMedal.addAll(ResPet.get2PetFragment(mShopSpecial, 4, Constans.PRICE_MEDAL));
//            shop.aMedal.addAll(ResPetFood.get2PetFood(mShopSpecial, 5, Constans.PRICE_MEDAL));
//            shop.aMedal.addAll(ResAccessories.get2Accessories(mShopSpecial, 6, Constans.PRICE_MEDAL));

            shop.aMedal = new ArrayList<>();
            for (int i = 0; i < config.shopMedal.size(); i++) {
                shop.aMedal.add(new ArrayList<>(config.shopMedal.get(i)));
            }

            shop.aVip = new ArrayList<>();
            for (int i = 0; i < config.shopVip.size(); i++) {
                shop.aVip.add(new ArrayList<>(config.shopVip.get(i)));
            }
        }
        if (hasUpdate && shop.update(user.getId())) {
            user.getUData().myShop = shop;
            return true;
        }
        return false;
    }

    public static void load(String key, String value) {
        if (key.equals("res_shop_special")) {
            aShopSpecial = new Gson().fromJson(value, new TypeToken<ArrayList<ShopSpecial>>() {
            }.getType());
            for (ShopSpecial shopSpecial : aShopSpecial) {
                mShopSpecial.put(shopSpecial.key(), shopSpecial);
            }
        }
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, CfgShop.DataConfig.class);
        config.init();
    }

    public class ShopSpecial {
        public int Type, Rank, Gold, Gem, Boss, Medal;

        public String key() {
            return Type + "_" + Rank;
        }

        public int[] getPriceThanbi() {
            int index = new Random().nextInt(2) + 1;
            for (int i = 0; i < 2; i++) {
                if (index == Constans.PRICE_GOLD && Gold > 0) {
                    return new int[]{index, Gold};
                } else if (index == Constans.PRICE_GEM && Gem > 0) {
                    return new int[]{index, Gem};
                }
                if (++index == 3) {
                    index = 1;
                }
            }
            return new int[]{0, 0};
        }
    }

    public class DataConfig {
        public long timeRefresh;
        public int feeRefresh;
        public int[] egg;
        public List<int[]> chestAvatar, chestMaterial;
        public List<List<Integer>> shopMedal, shopBoss, shopVip;

        void init() {
            for (int i = 1; i < egg.length; i++) {
                egg[i] += egg[i - 1];
            }
            for (int[] values : chestAvatar) {
                for (int i = 1; i < values.length; i++) {
                    values[i] += values[i - 1];
                }
            }
            for (int[] values : chestMaterial) {
                for (int i = 1; i < values.length; i++) {
                    values[i] += values[i - 1];
                }
            }
        }
    }

    public class MyShop {
        public long startTime = 0, startTimeAvatar = 0;
        public List<List<Integer>> aItem = new ArrayList<List<Integer>>(); // id, imageId, priceType, price, number
        public String strDate = "";
        public List<List<Integer>> aMedal = new ArrayList<>();
        public List<List<Integer>> aBoss = new ArrayList<>();
        public List<List<Integer>> aVip = new ArrayList<>();
        public List<List<Integer>> aAvatar = new ArrayList<>();

        public String toString() {
            return new Gson().toJson(this);
        }

        public List<Integer> getItem(int shopType, int type, int imageId, int vip) {
            List<List<Integer>> tmp = new ArrayList<>();
            switch (shopType) {
                case SHOP_THANBI:
                    tmp = aItem;
                    break;
                case SHOP_BOSS:
                    tmp = aBoss;
                    break;
                case SHOP_MEDAL:
                    tmp = aMedal;
                    break;
                case SHOP_VIP:
                    tmp = aVip;
                    break;
                case SHOP_AVATAR:
                    tmp = aAvatar;
                    break;
            }
            for (int i = 0; i < tmp.size(); i++) {
                List<Integer> item = tmp.get(i);
                if (item.get(0) == type && item.get(1) == imageId) {
                    if (shopType == SHOP_VIP && vip < item.get(5)) return null;
                    return item;
                }
            }
            return null;
        }

        public GGProto.ProtoShopList toProto(int shopType) {
            GGProto.ProtoShopList.Builder builder = GGProto.ProtoShopList.newBuilder();
            builder.setTimeout(config.timeRefresh - (System.currentTimeMillis() - (shopType == SHOP_THANBI ? startTime : startTimeAvatar)) / 1000);
            builder.setFeeRefresh(CfgShop.config.feeRefresh);
            builder.setShopType(shopType);
            List<List<Integer>> items = shopType == SHOP_THANBI ? aItem : aAvatar;
            for (List<Integer> item : items) {
                GGProto.ProtoShop.Builder tmp = GGProto.ProtoShop.newBuilder();
                tmp.setType(item.get(0));
                tmp.setImageId(item.get(1));
                tmp.setPriceType(item.get(2));
                tmp.setPrice(item.get(3));
                tmp.setNumber(item.get(4));
                tmp.setSaleoff("");
                builder.addAShop(tmp);
            }
            return builder.build();
        }

        public GGProto.ProtoShopList toProto(int shopType, List<List<Integer>> aInts) {
            GGProto.ProtoShopList.Builder builder = GGProto.ProtoShopList.newBuilder();
            builder.setShopType(shopType);
            if (shopType == SHOP_MEDAL) builder.setTimeout(-1);
            else builder.setTimeout(getTimeOut());
            builder.setFeeRefresh(-1);
            for (int i = 0; i < aInts.size(); i++) {
                List<Integer> aInt = aInts.get(i);
                GGProto.ProtoShop.Builder tmp = GGProto.ProtoShop.newBuilder();
                tmp.setType(aInt.get(0));
                tmp.setImageId(aInt.get(1));
                tmp.setPriceType(aInt.get(2));
                tmp.setPrice(aInt.get(3));
                tmp.setNumber(aInt.get(4));
                tmp.setSaleoff("");
                builder.addAShop(tmp);
            }
            return builder.build();
        }

        public GGProto.ProtoShopList toVipProto() {
            GGProto.ProtoShopList.Builder builder = GGProto.ProtoShopList.newBuilder();
            builder.setShopType(SHOP_VIP);
            builder.setTimeout(getTimeOut());
            builder.setFeeRefresh(-1);
            for (int i = 0; i < aVip.size(); i++) {
                List<Integer> aInt = aVip.get(i);
                GGProto.ProtoShop.Builder tmp = GGProto.ProtoShop.newBuilder();
                tmp.setType(aInt.get(0));
                tmp.setImageId(aInt.get(1));
                tmp.setPriceType(aInt.get(2));
                tmp.setPrice(aInt.get(3));
                tmp.setNumber(aInt.get(4));
                tmp.setVipRequired(aInt.get(5));
                tmp.setSaleoff("");
                builder.addAShop(tmp);
            }
            return builder.build();
        }

        long getTimeOut() {
            Calendar ca = Calendar.getInstance();
            ca.add(Calendar.DATE, 1);
            ca.set(Calendar.HOUR_OF_DAY, 0);
            ca.set(Calendar.MINUTE, 0);
            ca.set(Calendar.SECOND, 0);
            return (ca.getTimeInMillis() - System.currentTimeMillis()) / 1000;
        }

        public boolean update(long userId) {
            return Database2.update("user_data", Arrays.asList("shop_special", toString()), Arrays.asList("user_id", String.valueOf(userId)));
        }
    }

    public MyShop getMyShopInstance() {
        return new MyShop();
    }

}