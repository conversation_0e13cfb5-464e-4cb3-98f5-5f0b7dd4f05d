package com.bem.config;

import com.bem.boom.Resources;
import com.bem.monitor.Bonus;
import com.bem.object.ResAccessories;
import com.bem.object.ResBomb;
import com.bem.object.ResPetFood;
import com.bem.object.StoneUpgradeUfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgGraftItem {


    public static List<DataConfig> data;


    public static void loadConfig(String strJson) {

        data = (ArrayList<DataConfig>) new Gson().fromJson(strJson,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());

    }

    public class DataConfig {
        public int numberUse, eventId, enable;
        public String name, desc;
        public int[][] graft;
        public List<Integer> id;

    }
}
