/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 
 * <AUTHOR>
 */
public class EventText {

	public static Date fromDate, toDate;
	public static int koin, numberAwardPerHour, numberPlayer = 4;
	public static List<String> text;
	public static String msg, eventTitle, eventDetail;
	static String disableHour;

	public static boolean isInEvent() {
		return true;
//		Date date = new Date();
//		if (fromDate != null && toDate != null && date.after(fromDate) && date.before(toDate)) {
//			return true;
//		}
//		return false;
	}

	public static boolean isDisableHour(int curHour) {
		if (disableHour.contains("," + curHour + ",")) {
			return true;
		}
		return false;
	}

	public static void loadConfig(String strJson) {
		JSONObject json = JSONObject.fromObject(strJson);
		koin = json.getInt("koin");
		numberAwardPerHour = json.getInt("numberAwardPerHour");
		disableHour = json.getString("disableHour");
		eventTitle = json.getString("eventTitle");
		eventDetail = json.getString("eventDetail");
		numberPlayer = json.getInt("numberPlayer");
		try {
			SimpleDateFormat formater = new SimpleDateFormat("dd/MM/yyyy");
			fromDate = formater.parse(json.getString("fromDate"));
			toDate = formater.parse(json.getString("toDate"));
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		text = new ArrayList<String>();
		JSONArray arr = json.getJSONArray("text");
		for (int i = 0; i < arr.size(); i++) {
			text.add(arr.getString(i));
		}
		msg = json.getString("message");

	}
}
