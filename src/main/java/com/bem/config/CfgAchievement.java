package com.bem.config;

import com.bem.boom.object.AchievementInfo;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.ShopItemEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.object.UserInfo;
import com.bem.util.Util;
import com.cache.JCache;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import grep.helper.GsonUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;

public class CfgAchievement {

    public final static int LV5 = 1, BATTLE50 = 2, FREINDS30 = 3, BATTLE100 = 4, TOP1CLANBOSSWORLD = 5, TOP1CLANBOSSWORLD20TIMES = 6, WINBATTLE50 = 7, WINBATTLE100 = 8, PETLV10 = 9, TOP3CUP = 10, TOP1CUP = 11, VIP5 = 12, VIP10 = 13, LIENTHANG100 = 14, TOP1LIENTHANG = 15, VIP20 = 16;
    //    public static List<AchievementInfo> aAch = new ArrayList<>();
//    public static Map<Integer, AchievementInfo> mAch = new HashMap<>();
    public static Map<Integer, List<AchievementInfo>> aAch = new HashMap<>();
    public static Map<Integer, Map<Integer, AchievementInfo>> mAch = new HashMap<>();
    public static String defaultConfig = "";
    public final static int first = 11;
    public final static int ACHIEVEMENT_ENERGY = 0, ACHIEVEMENT_LV = 1, WIN_MAP = 2, COLLECT_STAR = 3, ACHIEVEMENT_TEAM = 4, XEP_HANG = 5, CHARACTER = 5, EVENT_PET = 50, TOP_TROPHY = 101, TOP_STAR_MAP = 100, VIP = 102,
            NAP = 103, LIENTHANG = 104, CAPDO = 105, ROTAGEJACKPOT = 106, SAOVANG = 107,
            NAP_X2 = 108;
    public final static int ACHIEVEMENT_STATUS_HAS_TIME = 1, ACHIEVEMENT_STATUS_NOT_HAS_TIME = 2, ACHIEVEMENT_STATUS_DISAB = 0;
    public final static String KEYVIPDAILY = "CACHEVIPAWARĐAILY:";
    public final static String KEYAWARDROTAGEJACKPOTDAILY = "KEYAWARDROTAGEJACKPOTDAILY:";
    public final static String KEYNUMBERROTAGEJACKPOTDAILY = "KEYNUMBERROTAGEJACKPOTDAILY:";

    public static Map<String, String> mAvatarName = new HashMap<String, String>();
    public static Map<Integer, ShopItemEntity> mItem = new HashMap<Integer, ShopItemEntity>();
    public static List<Integer> newMission = new ArrayList<>();
    public static final int TYPE_TICHLUY = 1;

    public static String getEnergy(UserEntity user) {
        int hour = Integer.parseInt(Util.HourFormatTime(new Date()));
        if (hour == CfgAchievement.aAch.get(user.getServer()).get(0).getTime1()) {
            hour = 1;
        } else if (hour == CfgAchievement.aAch.get(user.getServer()).get(0).getTime2()) {
            hour = 2;
        } else if (hour == CfgAchievement.aAch.get(user.getServer()).get(0).getTime3()) {
            hour = 3;
        } else {
            hour = -1;
        }
        String rw = null;
        if (hour >= 1) {
            rw = JCache.getInstance().getValue(hour + "" + user.getUsername() + ":rewardEventEnergy:");
        } else {
            rw = "true";
        }
        return rw;
    }

    public static AchievementInfo getAchievement(int type, UserEntity user) {
        try {
            for (int i = 0; i < aAch.get(user.getServer()).size(); i++) {
                if (aAch.get(user.getServer()).get(i).getId() == type) {
                    return aAch.get(user.getServer()).get(i);
                }
            }
        } catch (Exception ex) {
        }
        return null;
    }

    public static AchievementInfo getAchievement(int type, int serverId) {

        for (int i = 0; i < aAch.get(serverId).size(); i++) {
            if (aAch.get(serverId).get(i).getId() == type) {
                return aAch.get(serverId).get(i);
            }
        }
        return null;
    }

//    public static boolean hasAchievement(UserInfo user) {
////        JSONArray tmp = JSONArray.fromObject(newUser.getAchievement());
////        for (int i = 0; i < tmp.size(); i++) {
////            if (tmp.getInt(i) < 0) {
////                return true;
////            }
////        }
//        return false;
//    }

    public static int getRecieveNumberAchievement(UserInfo user, AchievementInfo aInf, int index) {
        int number = 0;
        JSONArray aChieveRecieve = JSONArray.fromObject(user.getDbUser().getAchievementReceive());
        if (aInf.getId() > CfgAchievement.CHARACTER && aInf.getId() < 50) {
            try {
                number = user.getUEvent().getEvent(Constans.EVENT_CHARACTERPLUS + aInf.getId()).getEvent().getInfo();
            } catch (Exception ex) {
                number = 0;
            }
        } else if (aInf.getId() > CfgAchievement.EVENT_PET && aInf.getId() < 100) {
            try {
                number = user.getUEvent().getEvent(Constans.EVENT_PETPLUS + aInf.getId()).getEvent().getInfo();
            } catch (Exception ex) {
                number = 0;
            }
        } else if (!newMission.contains(aInf.getId())) {
            number = aChieveRecieve.getInt(index);
        } else {
            if (aInf.getId() == CfgAchievement.VIP) {
                number = user.getDbUser().checkHasAwardVip() + 1;
            }
            if (aInf.getId() == CfgAchievement.ROTAGEJACKPOT) {
                number = user.getDbUser().checkHasAwardRotage();
            } else if (aInf.getId() == CfgAchievement.NAP) {
                number = user.getDbUser().getGetEventNap();
            } else if (aInf.getId() == CfgAchievement.LIENTHANG) {
                number = user.getDbUser().getEventLienthang();
            } else if (aInf.getId() == CfgAchievement.CAPDO) {
                number = user.getDbUser().getTanglv();
            } else if (aInf.getId() == CfgAchievement.SAOVANG) {
                number = user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getInfo();
            }
        }
        return number;
    }

    public static long getCurentNumberAchievement(UserInfo user, AchievementInfo aInf, int index) {
        long curentNumber = 0;
        JSONArray aChieveStatus = JSONArray.fromObject(user.getDbUser().getAchievementStatus());
        if (aInf.getId() == CfgAchievement.WIN_MAP) {
            curentNumber = 0;
            for (int j = 0; j < user.getUData().getMap().getNormal().size(); j++) {
                if (user.getUData().getMap().getNormal().get(j) > 0) {
                    curentNumber++;
                }
            }
        } else if (aInf.getId() == CfgAchievement.COLLECT_STAR) {

            curentNumber = user.getUData().getMap().getStar();
        } else if (aInf.getId() == CfgAchievement.VIP) {
            curentNumber = user.getDbUser().getVip();
        } else if (aInf.getId() == CfgAchievement.ROTAGEJACKPOT) {
            curentNumber = user.getDbUser().checkHasNumberRotage();
        } else if (aInf.getId() == CfgAchievement.LIENTHANG) {
            curentNumber = user.getDbUser().getMaxwin();

        } else if (aInf.getId() == CfgAchievement.CAPDO) {
            try {
                curentNumber = user.getUEvent().getEvent(Constans.EVENT_TOP_CAPDO).getEvent().getValue();
            } catch (Exception ex) {
                curentNumber = 0;
            }

        } else if (aInf.getId() == CfgAchievement.SAOVANG) {
            try {
                curentNumber = user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getValue();
            } catch (Exception ex) {
                curentNumber = 0;
            }

        } else if (aInf.getId() == CfgAchievement.NAP) {
            curentNumber = user.getDbUser().getSumGemIntEvent();
            if (curentNumber < 0) {
                curentNumber = 0;
                try {
                    UserDAO uDao = new UserDAO();
                    SimpleDateFormat formater = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

                    curentNumber = uDao.getGemInEvent(user.getUsername(), formater.format(aInf.getMiliStart()), formater.format(aInf.getMiliEnd()), user.getDbUser().getServer());
                } catch (Exception ex) {
                }
                user.getDbUser().setSumGemIntEvent(curentNumber);
            }
        } else if (aInf.getId() > CfgAchievement.CHARACTER && aInf.getId() < 50) {
            try {
                curentNumber = user.getUEvent().getEvent(Constans.EVENT_CHARACTERPLUS + aInf.getId()).getEvent().getValue();
            } catch (Exception ex) {
                curentNumber = 0;
            }
        } else if (aInf.getId() > CfgAchievement.EVENT_PET && aInf.getId() < 100) {
            try {
                curentNumber = user.getUEvent().getEvent(Constans.EVENT_PETPLUS + aInf.getId()).getEvent().getValue();
            } catch (Exception ex) {
                curentNumber = 0;
            }
        } else {
            curentNumber = aChieveStatus.getInt(index);
        }
        return curentNumber;
    }

    public static boolean inEvent(int eventId, UserEntity user) {
        AchievementInfo ach = mAch.get(user.getServer()).get(eventId);
        return ach != null ? ach.inTime() : false;
    }

    public static boolean inEvent(int eventId, int serverId) {
        AchievementInfo ach = mAch.get(serverId).get(eventId);
        return ach != null ? ach.inTime() : false;
    }

    /**
     * @1 - thăng cấp - level
     * @2 - thống lĩnh - sở hữu tướng
     * @3 - chiến dịch 1 - bản đồ
     * @4 - chiến dịch 2 - trophy
     * @5 - chiến dịch 3 - thắng pvp
     * @6 - chiến dịch 4 - thủ thành công
     */
    /**
     * Loading config here
     */

//    public static void loadConfigNew(String value){
//        JSONArray arr = JSONArray.fromObject(value);
//
//        JSONArray def = new JSONArray();
//        for (int i = 0; i < arr.size() + 1; i++) {
//            def.add(0);
//        }
//        defaultConfig = def.toString();
//
////        aAch = new AchievementInfo[arr.size()];
//        aAch = new HashMap<>();
//        newMission = new ArrayList<>();
//        for (int i = 0; i < arr.size(); i++) {
//            JSONObject obj = arr.getJSONObject(i);
//            AchievementInfo ai = new AchievementInfo();
//            ai.setId(obj.getInt("id"));
//            ai.setName(obj.getString("name"));
//            ai.setDesc(obj.getString("desc"));
//            ai.setStatus(obj.getInt("status"));
//            ai.setImage(obj.getInt("image"));
//            JSONArray tmp = obj.getJSONArray("required");
//            int[] tmpInt = new int[tmp.size()];
//            for (int j = 0; j < tmp.size(); j++) {
//                tmpInt[j] = tmp.getInt(j);
//            }
//            ai.setARequired(tmpInt);
//
//            tmp = obj.getJSONArray("award");
//            JSONArray[] tmpArr = new JSONArray[tmp.size()];
//            for (int j = 0; j < tmp.size(); j++) {
//                tmpArr[j] = tmp.getJSONArray(j);
//
//            }
//            ai.setABonus(tmpArr);
//            ai.setDateBegin(obj.getString("dateBegin"));
//            ai.setDateEnd(obj.getString("dateEnd"));
//            try {
//                ai.setTime1(obj.getInt("time1"));
//                ai.setTime2(obj.getInt("time2"));
//                ai.setTime3(obj.getInt("time3"));
//            } catch (Exception ex) {
//
//            }
//            aAch.add(ai);
//            if (ai.getId() >= 100) {
//                newMission.add(ai.getId());
//            }
//        }
//        aAch.forEach(achievementInfo -> {
//            achievementInfo.init();
//            mAch.put(achievementInfo.getId(), achievementInfo);
//        });
//    }
    public static void setNewData(List<AchievementInfo> saAch) {
        if (newMission == null) newMission = new ArrayList<>();
        Map<Integer, AchievementInfo> smAch = new HashMap<>();
        for (AchievementInfo ai : saAch) {
            try {
                if (ai.getId() >= 100 && !newMission.contains(ai.getId())) {
                    newMission.add(ai.getId());
                }
                ai.init();
                smAch.put(ai.getId(), ai);
            } catch (Exception ex) {
                Logs.error(Util.exToString(ex));
            }
        }
        aAch.put(1, saAch);
        mAch.put(1, smAch);
        aAch.put(2, saAch);
        mAch.put(2, smAch);
    }

    public static void loadConfig(String value, String serverString) {
//        System.out.println();
        try {
            int serverId = Integer.parseInt(serverString);
            System.out.println("serverId----1111111111111111111111111111--------->" + serverId);
            JSONArray arr = JSONArray.fromObject(value);
            JSONArray def = new JSONArray();
            for (int i = 0; i < arr.size() + 1; i++) {
                def.add(0);
            }
            defaultConfig = def.toString();
            if (true) return;
            List<AchievementInfo> saAch = new ArrayList<>();
            Map<Integer, AchievementInfo> smAch = new HashMap<>();
            newMission = new ArrayList<>();
            for (int i = 0; i < arr.size(); i++) {
                JSONObject obj = arr.getJSONObject(i);
                AchievementInfo ai = new AchievementInfo();
                ai.setId(obj.getInt("id"));
                ai.setName(obj.getString("name"));
                ai.setDesc(obj.getString("desc"));
                ai.setStatus(obj.getInt("status"));
                ai.setImage(obj.getInt("image"));
                JSONArray tmp = obj.getJSONArray("required");
                int[] tmpInt = new int[tmp.size()];
                for (int j = 0; j < tmp.size(); j++) {
                    tmpInt[j] = tmp.getInt(j);
                }
                ai.setARequired(tmpInt);

                tmp = obj.getJSONArray("award");
                JSONArray[] tmpArr = new JSONArray[tmp.size()];
                for (int j = 0; j < tmp.size(); j++) {
                    tmpArr[j] = tmp.getJSONArray(j);

                }
                ai.setABonus(GsonUtil.strTo2ListInt(tmp.toString()));
                ai.setDateBegin(obj.getString("dateBegin"));
                ai.setDateEnd(obj.getString("dateEnd"));
                try {
                    ai.setTime1(obj.getInt("time1"));
                    ai.setTime2(obj.getInt("time2"));
                    ai.setTime3(obj.getInt("time3"));
                } catch (Exception ex) {

                }
                saAch.add(ai);
                if (ai.getId() >= 100) {
                    newMission.add(ai.getId());
                }
            }
            saAch.forEach(achievementInfo -> {
                achievementInfo.init();
                smAch.put(achievementInfo.getId(), achievementInfo);
            });
            aAch.put(serverId, saAch);
            mAch.put(serverId, smAch);
        } catch (Exception ex) {
            System.out.println("loi config event-------->");
        }
    }

//    public static void loadConfig(String value) {
////        System.out.println();
//        try {
////            System.out.println("serverId----1111111111111111111111111111--------->"+serverId);
//            JSONObject JsonObjectRoot = JSONObject.fromObject(value);
//            JSONArray arr = JsonObjectRoot.getJSONArray("content");
////            JSONArray arr = JSONArray.fromObject(value);
//            JSONArray def = new JSONArray();
//            for (int i = 0; i < arr.size() + 1; i++) {
//                def.add(0);
//            }
//            defaultConfig = def.toString();
//
////        aAch = new AchievementInfo[arr.size()];
////        aAch = new HashMap<>();
//            List<AchievementInfo> saAch = new ArrayList<>();
//            Map<Integer, AchievementInfo> smAch = new HashMap<>();
//            newMission = new ArrayList<>();
//            for (int i = 0; i < arr.size(); i++) {
//                JSONObject obj = arr.getJSONObject(i);
//                AchievementInfo ai = new AchievementInfo();
//                ai.setId(obj.getInt("id"));
//                ai.setName(obj.getString("name"));
//                ai.setDesc(obj.getString("desc"));
//                ai.setStatus(obj.getInt("status"));
//                ai.setImage(obj.getInt("image"));
//                JSONArray tmp = obj.getJSONArray("required");
//                int[] tmpInt = new int[tmp.size()];
//                for (int j = 0; j < tmp.size(); j++) {
//                    tmpInt[j] = tmp.getInt(j);
//                }
//                ai.setARequired(tmpInt);
//
//                tmp = obj.getJSONArray("award");
//                JSONArray[] tmpArr = new JSONArray[tmp.size()];
//                for (int j = 0; j < tmp.size(); j++) {
//                    tmpArr[j] = tmp.getJSONArray(j);
//
//                }
//                ai.setABonus(tmpArr);
//                ai.setDateBegin(obj.getString("dateBegin"));
//                ai.setDateEnd(obj.getString("dateEnd"));
//                try {
//                    ai.setTime1(obj.getInt("time1"));
//                    ai.setTime2(obj.getInt("time2"));
//                    ai.setTime3(obj.getInt("time3"));
//                } catch (Exception ex) {
//
//                }
//                saAch.add(ai);
//                if (ai.getId() >= 100) {
//                    newMission.add(ai.getId());
//                }
//            }
//            saAch.forEach(achievementInfo -> {
//                achievementInfo.init();
//                smAch.put(achievementInfo.getId(), achievementInfo);
//            });
//            System.out.println("saAch.size------>" + saAch.size());
//            System.out.println("serverid---------->" + JsonObjectRoot.getInt("serverId"));
//
//            aAch.put(JsonObjectRoot.getInt("serverId"), saAch);
//            mAch.put(JsonObjectRoot.getInt("serverId"), smAch);
////            aAch.put(serverId, saAch);
////            mAch.put(serverId, smAch);
//        } catch (Exception ex) {
//            System.out.println("loi config event-------->");
//        }
//    }

    public static String getDefaultString() {
        return defaultConfig;
    }


}
