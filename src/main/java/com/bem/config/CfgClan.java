package com.bem.config;

import com.bem.dao.mapping.ClanEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.object.UserInfo;
import com.cache.MCache;
import com.google.gson.Gson;
import grep.helper.DateTime;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.List;

public class CfgClan {
    public static JSONObject json;
    public static DataConfig config;

    public static boolean canReqJoin(UserInfo user, int clanId) {
        Long lastReq = MCache.getInstance().getLong("req" + user.getId() + "_" + clanId);
        if (lastReq == null) {
            lastReq = 0L;
        }
        if (System.currentTimeMillis() - lastReq > config.timeReqClan * 60000) {
            return true;
        }
        return false;
    }

    public static void cacheReq(UserInfo user, int clanId) {
        MCache.getInstance().set("req" + user.getId() + "_" + clanId, System.currentTimeMillis());
    }

    public static String canJoin(long userId) {
        if (CfgServer.isTest()) return "";
        Long lastReq = MCache.getInstance().getLong("clanLeave" + userId);
        if (lastReq == null) {
            lastReq = 0L;
        }
        if (System.currentTimeMillis() - lastReq > config.timeLeaveClan * 60000) {
            return "";
        }
        long time = config.timeLeaveClan * 60 - (System.currentTimeMillis() - lastReq) / 1000;
        return String.format("Bạn vừa thoát hội, hãy nghỉ ngơi %s trước khi tham gia hội khác", DateTime.formatTime(time));
    }

    public static void cacheLeave(long userId) {
        MCache.getInstance().set("clanLeave" + userId, System.currentTimeMillis());
    }

    public static int maxMember(int level) {
        return List.of(15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,
                40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 50, 50, 50, 50).get(level - 1);
    }

    public static int timeDiemdanh(UserEntity user) {
        int lastDiemdanh = JSONArray.fromObject(user.getDiemdanh()).getInt(1);
        int timePass = (int) (System.currentTimeMillis() / 1000) - lastDiemdanh;
        return config.diemdanh[2] > timePass ? config.diemdanh[2] - timePass : 0;
    }

    public static long[] calculateLevel(ClanEntity clan, int addExp) {
        long[] result = new long[2];
        result[0] = clan.getLevel();
        result[1] += clan.getExp() + addExp;
        while (result[1] > config.levels[(int) result[0]]) {
            result[1] -= config.levels[(int) result[0]];
            result[0]++;
            if (result[0] >= config.members.length) { // max level
                break;
            }
        }
        return result;
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public int feeCreate, feeChangeName, timeLeaveClan, timeReqClan, levelRequired;
        public int[] nameLength, sloganLength, members, diemdanh;
        public long[] levels;
    }

}