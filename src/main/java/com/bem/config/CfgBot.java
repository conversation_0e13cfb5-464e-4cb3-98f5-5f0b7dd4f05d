package com.bem.config;

import com.bem.boom.Resources;
import com.bem.boom.object.MyAvatar;
import com.bem.dao.mapping.UserAvatarEntity;
import com.bem.dao.mapping.UserBombEntity;
import com.bem.dao.mapping.UserHeroEntity;
import com.bem.dao.mapping.UserPetEntity;
import com.bem.object.*;
import com.bem.util.Util;
import com.google.gson.Gson;
import io.netty.channel.Channel;
import net.sf.json.JSONObject;

import java.util.List;
import java.util.Random;

public class CfgBot {
    public static JSONObject json;
    public static DataConfig config;
    public static int nameCounter = 0;

    public static UserInfo reloadBot(UserInfo botUser, UserInfo user) {
        botUser.getDbUser().setName(getName());
        MyAvatar botAvatar = botUser.getMAvatar();

        botUser.getDbUser().setTrophy(user.getDbUser().getTrophy());
        { // trophy
//            int newTrophy = user.getDbUser().getTrophy() + (new Random().nextInt(40) - 20);
//            if (newTrophy < 0) newTrophy = 20;
//            botUser.getDbUser().setTrophy(newTrophy);
//            botUser.getDbUser().getRank();
//            botUser.getDbUser().addAchievementStatus(CfgAchievement.XEP_HANG, 1, user.getDbUser(),user.getUData());
        }

        botAvatar.setAvatar(MyAvatar.USER_AVATAR, Resources.getRandomIcon().getId()); // icon
        int heroId = 0;
        { // avatar
            UserAvatarEntity uAvatar = user.getRes().mAvatar.get(user.getMAvatar().getClothes());
            int level = uAvatar == null ? 1 : getLevel(uAvatar.getLevel());
            ResAvatar rAvatar = Resources.getRandomAvatar();
            UserAvatarEntity bAvatar = botUser.getRes().mAvatar.get(rAvatar.getId());
            if (bAvatar == null) {
                bAvatar = new UserAvatarEntity(botUser.getId(), rAvatar.getId(), level);
                botUser.getRes().mAvatar.put(bAvatar.getAvatarId(), bAvatar);
            }
            botAvatar.setAvatar(MyAvatar.CLOTHES, rAvatar.getId());
            heroId = rAvatar.getCharacterId();
        }
        { // hero
            UserHeroEntity uHero = user.getRes().mHero.get(user.getMAvatar().getHero());
            int level = uHero == null ? 1 : getLevel(uHero.getLevel());
            UserHeroEntity botHero = botUser.getRes().mHero.get(heroId);
            if (botHero == null) {
                botHero = new UserHeroEntity(botUser.getId(), heroId);
                botUser.getRes().mHero.put(botHero.getHeroId(), botHero);
            }
            botHero.setLevel(level);
            botAvatar.setAvatar(MyAvatar.HERO, botHero.getHeroId());
        }

        {// pet
            UserPetEntity uPet = user.getRes().mPet.get(user.getMAvatar().getPet());
            if (uPet != null) {
                int level = getLevel(uPet.getLevel());
                int star = getStar(uPet.getStar());
                ResPet rPet = Resources.getRandomPet();
                UserPetEntity botPet = botUser.getRes().mPet.get(rPet.getId());
                if (botPet == null) {
                    botPet = new UserPetEntity(botUser.getId(), rPet.getId(), level, star, 0);
                    botUser.getRes().mPet.put(botPet.getPetId(), botPet);
                }
                botAvatar.setAvatar(MyAvatar.PET, botPet.getPetId());
            }
        }
        {// bomb
            UserBombEntity uBomb = user.getRes().mBomb.get(user.getMAvatar().getIdBomb());
            int level = uBomb == null ? 1 : getLevel(uBomb.getLevel());
            ResBomb rBomb = Resources.getRandomBomb();
            UserBombEntity botBomb = botUser.getRes().mBomb.get(rBomb.getId());
            if (botBomb == null) {
                botBomb = new UserBombEntity(botUser.getId(), rBomb.getId(), level);
                botUser.getRes().mBomb.put(botBomb.getBombId(), botBomb);
            }
            botAvatar.setAvatar(MyAvatar.BOMB_ID, botBomb.getBombId());
            botAvatar.setAvatar(MyAvatar.BOMB_IMAGE, botBomb.getBombId());
        }
        return botUser;
    }

    static int getLevel(int level) {
        if (config.level == 0) return level;
        int tmp = new Random().nextInt(config.level * 2) - config.level + level;
        return tmp < 1 ? 1 : tmp;
    }

    static int getStar(int star) {
        if (config.star == 0) return star;
        int tmp = new Random().nextInt(config.star * 2) - config.star + star;
        return tmp < 1 ? 1 : tmp;
    }

    public static synchronized String getName() {
        if (++nameCounter == config.names.size()) {
            nameCounter = 0;
        }
        return config.names.get(nameCounter);
    }


    public static boolean isBot(String udid) {
        return config.udid.equals(udid);
    }

    public static boolean isOkIp(Channel channel) {
        if (config.ips.isEmpty()) {
            return true;
        }
        return config.ips.contains(Util.getUserIPAdress(channel));
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, CfgBot.DataConfig.class);
    }

    public class DataConfig {
        public List<String> names;
        public List<String> ips;
        public String udid;
        public int level, star, timeWait, matchRank;
    }

}