/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import java.text.SimpleDateFormat;
import java.util.Date;

import net.sf.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class EventValentine {

    public static Date fromDate, toDate;
    public static String admin;
    public static int koinRate;

    public static boolean isAdmin(String username) {
        if (("," + admin + ",").contains("," + username + ",")) {
            Date date = new Date();
            if (date.after(fromDate) && date.before(toDate)) {
                return true;
            }
            return false;
        }
        return false;
    }
    public static void loadConfig(String strJson) {
		JSONObject json = JSONObject.fromObject(strJson);
		admin = json.getString("admin");
		koinRate = json.getInt("koin_rate");
		try {
			SimpleDateFormat formater = new SimpleDateFormat("dd/MM/yyyy");
			fromDate = formater.parse(json.getString("fromDate"));
			toDate = formater.parse(json.getString("toDate"));
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
}
