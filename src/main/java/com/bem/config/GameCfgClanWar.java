package com.bem.config;

import com.bem.boom.Resources;
import com.bem.monitor.Bonus;
import com.bem.object.EventStatus;
import com.bem.object.ResShopItem;
import com.google.gson.Gson;
import grep.helper.DateTime;
import lombok.Data;
import net.sf.json.JSONObject;

import java.io.Serializable;
import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgClanWar implements Serializable {
    public static JSONObject json;
    public static DataConfig config;

    public static java.util.Map<Integer, MapBonus> randomBonus() {
        java.util.Map<Integer, MapBonus> mMapBonus = new HashMap<>();
        getMapBonus(typeBonus, config.roomBonus, 1, mMapBonus);
        getMapBonus(typeEasyBonus, config.roomEasyBonus, 101, mMapBonus);
        return mMapBonus;
    }

    static void getMapBonus(List<Integer> typeBonus, java.util.Map<Integer, List<List<Integer>>> roomBonus, int startId, java.util.Map<Integer, MapBonus> mMapBonus) {
        List<MapBonus> aMapBonus = new ArrayList<>();
        Random ran = new Random();
        int[] numberMapLevel = {1, 1, 1, 1, 1, 1, 2, 2, 2, 3, 3};
        for (int i = 0; i < numberMapLevel.length; i++) {
            MapBonus bonus = new GameCfgClanWar().getMapBonusInstance();
            bonus.star = numberMapLevel[i];

            bonus.allClan = new ArrayList<>();
            for (int index = 0; index < 2; index++) {
                int type = typeBonus.get(ran.nextInt(typeBonus.size()));
                bonus.allClan.addAll(getBonus(type, roomBonus.get(type).get(ran.nextInt(2)), numberMapLevel[i]));
            }

            bonus.clan = new ArrayList<>();
            for (int index = 0; index < 2; index++) {
                int type = typeBonus.get(ran.nextInt(typeBonus.size()));
                bonus.clan.addAll(getBonus(type, roomBonus.get(type).get(ran.nextInt(3) + 2), numberMapLevel[i]));
            }

            aMapBonus.add(bonus);
        }
        Collections.shuffle(aMapBonus);
        for (int i = 0; i < aMapBonus.size(); i++) {
            mMapBonus.put(startId + i, aMapBonus.get(i));
        }
    }

    static List<Long> getBonus(int type, List<Integer> bonusInfo, int rate) {
        List<Long> aBonus = new ArrayList<>();
        Random ran = new Random();
        switch (type) {
            case Bonus.PET_FOOD:
                aBonus.addAll(Bonus.defaultLongBonusView(Bonus.PET_FOOD, Resources.getRandomFruit(bonusInfo.get(0)).getId(), rate * bonusInfo.get(1)));
                break;
            case Bonus.MATERIAL:
                aBonus.addAll(Bonus.defaultLongBonusView(Bonus.MATERIAL, Resources.getRandomMaterial(bonusInfo.get(0)).getId(), rate * bonusInfo.get(1)));
                break;
            case Bonus.AVATAR_FRAGMENT:
                aBonus.addAll(Bonus.defaultLongBonusView(Bonus.AVATAR_FRAGMENT, Resources.getRandomAvatar(bonusInfo.get(0)).getId(), rate * bonusInfo.get(1)));
                break;
            case Bonus.PET_FRAGMENT:
                aBonus.addAll(Bonus.defaultLongBonusView(Bonus.PET_FRAGMENT, Resources.getRandomPet(bonusInfo.get(0)).getId(), rate * bonusInfo.get(1)));
                break;
            case Bonus.GOLD:
                if (bonusInfo.get(1).equals(bonusInfo.get(0)))
                    aBonus.addAll(Bonus.defaultLongBonusView(Bonus.GOLD, rate * bonusInfo.get(0)));
                else
                    aBonus.addAll(Bonus.defaultLongBonusView(Bonus.GOLD, rate * (bonusInfo.get(0) + ran.nextInt(bonusInfo.get(1) - bonusInfo.get(0)))));
                break;
            case 3003:
                if (bonusInfo.get(1).equals(bonusInfo.get(0)))
                    aBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.GOLD_CHEST, rate * bonusInfo.get(0)));
                else
                    aBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.GOLD_CHEST, rate * (bonusInfo.get(0) + ran.nextInt(bonusInfo.get(1) - bonusInfo.get(0)))));
                break;
            case 3004:
                if (bonusInfo.get(1).equals(bonusInfo.get(0)))
                    aBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.GOLD_KEY, rate * bonusInfo.get(0)));
                else
                    aBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.GOLD_KEY, rate * (bonusInfo.get(0) + ran.nextInt(bonusInfo.get(1) - bonusInfo.get(0)))));
                break;
            case 3018:
                if (bonusInfo.get(1).equals(bonusInfo.get(0)))
                    aBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, rate * bonusInfo.get(0)));
                else
                    aBonus.addAll(Bonus.defaultLongBonusView(Bonus.ITEM, ResShopItem.REVIVE, rate * (bonusInfo.get(0) + ran.nextInt(bonusInfo.get(1) - bonusInfo.get(0)))));
                break;
        }
        return aBonus;
    }

    public static EventStatus getLastEndEvent() {
        Calendar caFirstDayOfWeek = DateTime.firstDayOfWeek();
        int curTime = getCurTime(Calendar.getInstance());
        for (int i = config.time.size() - 1; i >= 0; i--) {
            if (curTime >= config.time.get(i)) {
                int lastEventId = getEventId(newCalendar(caFirstDayOfWeek, config.time.get(i)), config.time.get(i));
                int nextEventId = i + 1 >= config.time.size()
                        ? getEventId(newCalendar(caFirstDayOfWeek, config.time.get(0) + (int) DateTime.WEEK_MIN), config.time.get(0))
                        : getEventId(newCalendar(caFirstDayOfWeek, config.time.get(i + 1)), config.time.get(i + 1));
                return new EventStatus(lastEventId, nextEventId, curTime, i + 1 >= config.time.size() ? config.time.get(0) : config.time.get(i + 1));
            }
        }
        int lastEventId = getEventId(newCalendar(caFirstDayOfWeek, config.time.get(config.time.size() - 1) - (int) DateTime.WEEK_MIN), config.time.get(config.time.size() - 1));
        int nextEventId = getEventId(newCalendar(caFirstDayOfWeek, config.time.get(0)), config.time.get(0));
        return new EventStatus(lastEventId, nextEventId, curTime, config.time.get(0));
    }

//    static int getEventId(Calendar caFirstDayOfWeek, int addMinute, int eventIndex) {
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTimeInMillis(caFirstDayOfWeek.getTimeInMillis());
//        calendar.add(Calendar.MINUTE, addMinute);
//        return Integer.parseInt(DateTime.getDateMMdd(calendar.getTime()) + eventIndex);
//    }

    public static long getTimeout(int eventIndex) {
        if (eventIndex == -1) return 0;
        Calendar caFirstDayOfWeek = DateTime.firstDayOfWeek();
        long endTime = caFirstDayOfWeek.getTimeInMillis() + DateTime.MIN_MILLI_SECOND * config.time.get(eventIndex) + config.duration * DateTime.MIN_MILLI_SECOND;
        long timeout = endTime - System.currentTimeMillis();
        return timeout < 0 ? 0 : timeout / 1000;
    }

    public static String getNextStrEvent() {
        int curTime = getCurTime(Calendar.getInstance());
        for (int i = 0; i < config.time.size(); i++) {
            if (curTime < config.time.get(i)) {
                return config.strTime[i];
            }
        }
        return config.strTime[0];
    }

    /**
     * @return int[]
     * - eventIndex
     * - eventId
     */
    public static EventStatus getEventStatus() {
        int curTime = getCurTime(Calendar.getInstance());
        for (int i = 0; i < config.time.size(); i++) {
            if (curTime >= config.time.get(i) && curTime < config.time.get(i) + config.duration) {
                EventStatus status = new EventStatus(getEventId(Calendar.getInstance(), config.time.get(i)), i);
                status.setNextTime(config.time.get(i));
                return status;
            }
        }
        EventStatus status = new EventStatus(-1, -1);
        status.setCurTime(curTime);
        for (int i = 0; i < config.time.size(); i++) {
            if (curTime < config.time.get(i)) {
                status.setNextTime(config.time.get(i));
                return status;
            }
        }
        status.setNextTime(config.time.get(0));
        return status;
    }

    static Calendar newCalendar(Calendar ca, int addMinute) {
        Calendar newCalendar = Calendar.getInstance();
        newCalendar.setTimeInMillis(ca.getTimeInMillis());
        newCalendar.add(Calendar.MINUTE, addMinute);
        return newCalendar;
    }

    static int getEventId(Calendar ca, int curTime) {
        return Integer.parseInt(DateTime.getDateMMdd(ca.getTime()) + curTime);
    }

    static int getCurTime(Calendar ca) {
        Calendar caFirstDayOfWeek = DateTime.firstDayOfWeek(ca);
        return (int) ((ca.getTimeInMillis() - caFirstDayOfWeek.getTimeInMillis()) / DateTime.MIN_MILLI_SECOND);
    }

    public static int getMapPlayer(int id) {
        List<Integer> maps = mMap.get(id > 100 ? id - 100 : id).mapPlayer;
        return maps.get(new Random().nextInt(maps.size()));
    }

    public static int getMapBoss(int id) {
        List<Integer> maps = mMap.get(id > 100 ? id - 100 : id).mapBoss;
        return maps.get(new Random().nextInt(maps.size()));
    }

    public static Map getMap(int id) {
        return mMap.get(id);
    }

    public static void loadConfig(String strJson) {
//        strJson = "{\"enable\":1,\"test\":0,\"requireLevel\":10,\"duration\":60,\"maxPoint\":10,\"bonusGold\":5000,\"time\":[900,2340,3780,5220,6660,8100],\"strTime\":[\"15h thứ Hai\",\"15h thứ Ba\",\"15h thứ Tư\",\"15h thứ Năm\",\"15h thứ Sáu\",\"15h thứ Bảy\"],\"feeRevive\":100,\"timeFree\":300,\"timeWaitLeave\":180,\"changeRoomMessage\":\"Chuyển lãnh địa sẽ bị mất chuỗi liên thắng và chờ 3 phút để tiếp tục đánh. Bạn có muốn tiếp tục không?\",\"roomBonus\":{\"9\":[[1,10],[2,10],[3,10],[4,5],[5,3]],\"8\":[[1,5],[2,5],[3,5],[4,5],[5,5]],\"3018\":[[5,5],[10,10],[15,15],[20,20],[30,30]],\"3003\":[[5,5],[10,10],[15,15],[20,20],[30,30]],\"11\":[[1,10],[2,10],[3,10],[4,7],[5,5]],\"10\":[[1,5],[2,5],[3,5],[4,4],[5,3]],\"1\":[[100000,100000],[150000,150000],[200000,200000],[250000,250000],[500000,500000]]},\"roomEasyBonus\":{\"9\":[[1,10],[2,10],[3,10],[4,4],[5,2]],\"8\":[[1,5],[2,5],[3,5],[4,4],[5,3]],\"3018\":[[5,5],[10,10],[15,15],[20,20],[25,25]],\"3003\":[[5,5],[10,10],[15,15],[20,20],[25,25]],\"11\":[[1,10],[2,10],[3,10],[4,6],[5,4]],\"10\":[[1,5],[2,5],[3,4],[4,3],[5,2]],\"1\":[[50000,50000],[75000,75000],[100000,100000],[125000,125000],[150000,150000]]},\"topPlayerBonus\":[[1,500000],[1,300000],[1,200000]],\"topEasyPlayerBonus\":[[1,400000],[1,200000],[1,100000]],\"topMapPlayerBonus\":[[1,500000],[1,300000],[1,200000],[1,100000],[1,50000]],\"topEasyMapPlayerBonus\":[[1,400000],[1,200000],[1,100000],[1,50000],[1,25000]],\"maps\":[{\"id\":1,\"name\":\"Tàn tích\",\"easyName\":\"Trụ Đá Cổ\",\"mapPlayer\":[2000,2001,2003,2005],\"mapBoss\":[3201]},{\"id\":2,\"name\":\"Đài phun nước\",\"easyName\":\"\",\"mapPlayer\":[2000,2001,2003,2005],\"mapBoss\":[3201]},{\"id\":3,\"name\":\"Nhà gỗ\",\"easyName\":\"Nhà Lá\",\"mapPlayer\":[2100,2106,2107],\"mapBoss\":[3202]},{\"id\":4,\"name\":\"Nhà băng\",\"easyName\":\"Ụ Băng\",\"mapPlayer\":[2100,2106,2107],\"mapBoss\":[3202]},{\"id\":5,\"name\":\"Dung nham\",\"easyName\":\"Núi Lửa\",\"mapPlayer\":[2400,2402,2404,2405,2406],\"mapBoss\":[3205]},{\"id\":6,\"name\":\"Nấm độc\",\"easyName\":\"Nấm Xanh\",\"mapPlayer\":[2400,2402,2404,2405,2406],\"mapBoss\":[3205]},{\"id\":7,\"name\":\"Đồi kem\",\"easyName\":\"Đầm Kem\",\"mapPlayer\":[2300,2301,2303,2305,2308,2309],\"mapBoss\":[3204]},{\"id\":8,\"name\":\"Bánh kẹo\",\"easyName\":\"Bánh Ngọt\",\"mapPlayer\":[2300,2301,2303,2305,2308,2309],\"mapBoss\":[3204]},{\"id\":9,\"name\":\"Nhân sư\",\"easyName\":\"Phế tích 2\",\"mapPlayer\":[2200,2202,2204,2205,2206,2207,2209],\"mapBoss\":[3203]},{\"id\":10,\"name\":\"Kim tự tháp\",\"easyName\":\"Phế tích 1\",\"mapPlayer\":[2200,2202,2204,2205,2206,2207,2209],\"mapBoss\":[3203]},{\"id\":11,\"name\":\"Thuyền chiến\",\"easyName\":\"Tàu Cổ\",\"mapPlayer\":[2500,2501,2502,2503,2504,2505],\"mapBoss\":[3206]}]}";
//        strJson = "{\"enable\":1,\"test\":0,\"requireLevel\":10,\"duration\":60,\"maxPoint\":10,\"bonusGold\":5000,\"time\":[900,2340,3780,5160,6660,8100],\"strTime\":[\"15h thứ Hai\",\"15h thứ Ba\",\"15h thứ Tư\",\"15h thứ Năm\",\"15h thứ Sáu\",\"15h thứ Bảy\"],\"feeRevive\":100,\"timeFree\":300,\"timeWaitLeave\":180,\"changeRoomMessage\":\"Chuyển lãnh địa sẽ bị mất chuỗi liên thắng và chờ 3 phút để tiếp tục đánh. Bạn có muốn tiếp tục không?\",\"roomBonus\":{\"9\":[[1,10],[2,10],[3,10],[4,5],[5,3]],\"8\":[[1,5],[2,5],[3,5],[4,5],[5,5]],\"3018\":[[5,5],[10,10],[15,15],[20,20],[30,30]],\"3003\":[[5,5],[10,10],[15,15],[20,20],[30,30]],\"11\":[[1,10],[2,10],[3,10],[4,7],[5,5]],\"10\":[[1,5],[2,5],[3,5],[4,4],[5,3]],\"1\":[[100000,100000],[150000,150000],[200000,200000],[250000,250000],[500000,500000]]},\"roomEasyBonus\":{\"9\":[[1,10],[2,10],[3,10],[4,4],[5,2]],\"8\":[[1,5],[2,5],[3,5],[4,4],[5,3]],\"3018\":[[5,5],[10,10],[15,15],[20,20],[25,25]],\"3003\":[[5,5],[10,10],[15,15],[20,20],[25,25]],\"11\":[[1,10],[2,10],[3,10],[4,6],[5,4]],\"10\":[[1,5],[2,5],[3,4],[4,3],[5,2]],\"1\":[[50000,50000],[75000,75000],[100000,100000],[125000,125000],[150000,150000]]},\"topPlayerBonus\":[[1,500000],[1,300000],[1,200000]],\"topEasyPlayerBonus\":[[1,400000],[1,200000],[1,100000]],\"topMapPlayerBonus\":[[1,500000],[1,300000],[1,200000],[1,100000],[1,50000]],\"topEasyMapPlayerBonus\":[[1,400000],[1,200000],[1,100000],[1,50000],[1,25000]],\"maps\":[{\"id\":1,\"name\":\"Tàn tích\",\"easyName\":\"Trụ Đá Cổ\",\"mapPlayer\":[2000,2001,2003,2005],\"mapBoss\":[3201]},{\"id\":2,\"name\":\"Đài phun nước\",\"easyName\":\"\",\"mapPlayer\":[2000,2001,2003,2005],\"mapBoss\":[3201]},{\"id\":3,\"name\":\"Nhà gỗ\",\"easyName\":\"Nhà Lá\",\"mapPlayer\":[2100,2106,2107],\"mapBoss\":[3202]},{\"id\":4,\"name\":\"Nhà băng\",\"easyName\":\"Ụ Băng\",\"mapPlayer\":[2100,2106,2107],\"mapBoss\":[3202]},{\"id\":5,\"name\":\"Dung nham\",\"easyName\":\"Núi Lửa\",\"mapPlayer\":[2400,2402,2404,2405,2406],\"mapBoss\":[3205]},{\"id\":6,\"name\":\"Nấm độc\",\"easyName\":\"Nấm Xanh\",\"mapPlayer\":[2400,2402,2404,2405,2406],\"mapBoss\":[3205]},{\"id\":7,\"name\":\"Đồi kem\",\"easyName\":\"Đầm Kem\",\"mapPlayer\":[2300,2301,2303,2305,2308,2309],\"mapBoss\":[3204]},{\"id\":8,\"name\":\"Bánh kẹo\",\"easyName\":\"Bánh Ngọt\",\"mapPlayer\":[2300,2301,2303,2305,2308,2309],\"mapBoss\":[3204]},{\"id\":9,\"name\":\"Nhân sư\",\"easyName\":\"Phế tích 2\",\"mapPlayer\":[2200,2202,2204,2205,2206,2207,2209],\"mapBoss\":[3203]},{\"id\":10,\"name\":\"Kim tự tháp\",\"easyName\":\"Phế tích 1\",\"mapPlayer\":[2200,2202,2204,2205,2206,2207,2209],\"mapBoss\":[3203]},{\"id\":11,\"name\":\"Thuyền chiến\",\"easyName\":\"Tàu Cổ\",\"mapPlayer\":[2500,2501,2502,2503,2504,2505],\"mapBoss\":[3206]}]}";

        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
        mMap.clear();
        typeBonus.clear();
        typeEasyBonus.clear();
        config.maps.forEach(map -> mMap.put(map.id, map));
        config.roomBonus.forEach((key, value) -> typeBonus.add(key));
        config.roomEasyBonus.forEach((key, value) -> typeEasyBonus.add(key));
    }

    public static java.util.Map<Integer, Map> mMap = new HashMap<>();
    public static List<Integer> typeBonus = new ArrayList<>();
    public static List<Integer> typeEasyBonus = new ArrayList<>();

    @Data
    public class DataConfig {
        String[] strTime;
        List<Integer> time;
        public List<List<Integer>> topPlayerBonus, topEasyPlayerBonus;
        public List<List<Integer>> topMapPlayerBonus, topEasyMapPlayerBonus;
        public java.util.Map<Integer, List<List<Integer>>> roomBonus, roomEasyBonus;
        public int feeRevive, enable, requireLevel, test, timeFree, timeWaitLeave, duration, maxPoint, bonusGold;
        public String changeRoomMessage;
        public List<Map> maps;
    }

    public class Map {
        public int id;
        public String name, easyName;
        public List<Integer> mapPlayer, mapBoss;
    }

    public class MapBonus implements Serializable {
        public int star;
        public List<Long> allClan, clan;
    }

    public MapBonus getMapBonusInstance() {
        return new MapBonus();
    }
}
