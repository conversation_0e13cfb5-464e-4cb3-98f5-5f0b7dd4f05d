package com.bem.config;

import com.bem.object.UserInfo;
import com.bem.object.UserInt;
import com.cache.JCache;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.helper.DateTime;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class CfgExchange {
    public static JSONArray json;
    public static List<DataConfig> config;
    public static final int EXCHANGE_GOLD = 1, EXCHANGE_ENERGY = 2, EXCHANGE_ATOMIC = 3, EXCHANGE_GAME_GOLD = 4, EXCHANGE_GAME_MATERIAL = 5,
            EXCHANGE_GAME_BOSS = 6, REFRESH_MAP_INSANE = 7;
    public static String[] keyCaches = {":countdayExchangeGold", ":countdayExchangeEnergy", ":countdayExchangeAtomic",
            ":countdayGameGold", ":countdayGameMaterial", ":countdayGameBoss", ":countdayMapInsane"};
    public static String[] keyLogs = {"exchange_gold", "exchange_energy", "exchange_atomic",
            "exchange_game_gold", "exchange_game_material", "exchange_game_boss", "refresh_map_insane"};

    public static int[] mappingUserInt = {UserInt.NUMBER_EXCHANGE_GOLD, UserInt.NUMBER_EXCHANGE_ENERGY, UserInt.NUMBER_EXCHANGE_ATOMIC,
            UserInt.NUMBER_EXCHANGE_GAME_GOLD, UserInt.NUMBER_EXCHANGE_GAME_MATERIAL, UserInt.NUMBER_EXCHANGE_GAME_BOSS, UserInt.NUMBER_RESET_MAP_MAO_HIEM};

    public static List<Integer> PRICE_PROGRESS = Arrays.asList(EXCHANGE_GOLD, EXCHANGE_ENERGY, EXCHANGE_GAME_GOLD, EXCHANGE_GAME_MATERIAL);

    public static int getMaxExchange(UserInfo user, int type) {
        return user.getUData().getUInt().getValue(mappingUserInt[type - 1]);
    }

    public static int getNumberRemain(UserInfo user, int type, String info) {
        return getMaxExchange(user, type) - getNumberExchange(user, type, info);
    }

    public static int getNumberExchange(UserInfo user, int type, String info) {
        Integer number = JCache.getInstance().getIntValue(getKey(user, type, info));
        if (number == null) number = 0;
        return number;
    }

    public static String getKey(UserInfo user, int type, String info) {
        return DateTime.getDateyyyyMMdd(new Date()) + user.getId() + keyCaches[type - 1] + info;
    }

    public static void loadConfig(String strJson) {
        json = JSONArray.fromObject(strJson);
        config = (ArrayList<DataConfig>) new Gson().fromJson(strJson,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());
    }

    public class DataConfig {
        public String nametype;
        public int type, number, numberExchange;
        public List<Integer> bonus;
    }

}