/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import io.netty.channel.Channel;
import net.sf.json.JSONObject;
import com.bem.object.UserInfo;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.k2tek.Xerver;
import com.k2tek.service.BaseServiceMessage;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgDailyBonus {

    private static SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");

    /**
     * Bonus Koin
     */
    public static int bonusMinLevel = 10;
    public static int bonusMaxKoin = -1;
    public static int bonusKoin = 1000;
    public static int facebookFirstLogin = 1000;
    public static int appotaFisrtLogin = 1000;
    public static int inviteBonus = 1000;
    public static int minNap = 50000;
    public static boolean bonusEnable = false;
    public static String bonusMessage = "Bạn vừa được tặng %s koin vào tài khoản từ chương trình tặng koin hàng ngày", bonusTitle = "Thông báo";
    public static String bonusInviteMessage = "Bạn nhận được %s koin từ chương trình mời bạn cùng chơi";
    public static String bonusInviteTitle = "Thêm bạn thêm vui";
    public static String inviteMessage1 = "", inviteMessage2 = "";

    public static Calendar curDate = Calendar.getInstance();
    public static Map<String, Integer> mBonusKoin = new HashMap<String, Integer>();

    public static void dailyBonusKoinMision(Channel channel, UserInfo user) {
        if (!sdf.format(curDate.getTime()).equals(sdf.format(Calendar.getInstance().getTime()))) {
            curDate = Calendar.getInstance();
            mBonusKoin.clear();
        }

        int value = 1;
        if (!mBonusKoin.containsKey(user.getUsername())) {
            mBonusKoin.put(user.getUsername(), new Integer(1));
        } else {
            value = mBonusKoin.get(user.getUsername());
            value++;
            mBonusKoin.put(user.getUsername(), new Integer(value));
        }

        if (value == 5) {
            mBonusKoin.put(user.getUsername(), new Integer(bonusKoin));
            Util.sendProtoData(channel, CommonProto.getCommonVectorProto(null, Arrays.asList("Admin", String.format(bonusMessage, String.valueOf(bonusKoin)))), BaseServiceMessage.SPEAKER_ROOM, System.currentTimeMillis());
        }
    }

    public static String dailyBonusKoin(Channel channel, UserInfo user, Date lastLogin, Date dateCreated, boolean isFacebook) {
        /*
        if (bonusEnable) {
            if (lastLogin == null) {
                lastLogin = new Date();
            }
            if (dateCreated == null) {
                dateCreated = new Date();
            }
            // if (!isFacebook && (newUser.getMobile() == null || newUser.getMobile().length() < 9 || newUser.getMobile().equals("000000000"))) {
            // // User chưa active
            // } else {
            if (!sdf.format(curDate.getTime()).equals(sdf.format(Calendar.getInstance().getTime()))) {
                curDate = Calendar.getInstance();
                mBonusKoin.clear();
            }
            if (!mBonusKoin.containsKey(newUser.getUsername())) {
                boolean hasBonus = false;
                // Điều kiện số koin hiện tại
                if ((!sdf.format(lastLogin).equals(sdf.format(new Date())))) {
                    if (newUser.getDailyBonus() == 5) {
                        hasBonus = new SystemDAO().hasNapKoin(newUser.getUsername());
                    } else if (newUser.getDailyBonus() > 1) {
                        hasBonus = true;
                    } else {
                        hasBonus = new SystemDAO().hasNapKoin(newUser.getUsername());
                    }
                    if (hasBonus) {
                        try {
                            mBonusKoin.put(newUser.getUsername(), new Integer(bonusKoin));
                            Xerver.koin.addKoin(newUser, bonusKoin, "DAILY_BONUS " + newUser.getUsername());
                            new SystemDAO().updateUserLoginTime(newUser.getUserId());
                        } catch (Exception ex) {
                        }
                        return String.format(newUser.getLang().get(Lang.mission_daily_bonus_content), String.valueOf(bonusKoin));
                    }
                }
            }
        }
        Xerver.moLoginTimes.addUserLogin(newUser.getUserId());
        */
        return null;
    }

    public static void loadConfig(String strJson) {
        JSONObject json = JSONObject.fromObject(strJson);

        // Bonus Koin
        bonusEnable = json.getJSONObject("bonus_koin").getInt("enable") == 1 ? true : false;
        bonusMinLevel = json.getJSONObject("bonus_koin").getInt("min_level");
        bonusMaxKoin = json.getJSONObject("bonus_koin").getInt("max_koin");
        bonusKoin = json.getJSONObject("bonus_koin").getInt("koin");
        facebookFirstLogin = json.getJSONObject("bonus_koin").getInt("facebook");
        appotaFisrtLogin = json.getJSONObject("bonus_koin").getInt("appota");
        inviteBonus = json.getJSONObject("bonus_koin").getInt("invite");
        minNap = json.getJSONObject("bonus_koin").getInt("min_nap");
        bonusMessage = json.getJSONObject("bonus_koin").getString("message");
        bonusTitle = json.getJSONObject("bonus_koin").getString("bonusTitle");
        bonusInviteTitle = json.getJSONObject("bonus_koin").getString("inviteTitle");
        bonusInviteMessage = json.getJSONObject("bonus_koin").getString("inviteMessage");
        inviteMessage1 = json.getJSONObject("bonus_koin").getString("inviteMessage1");
        inviteMessage2 = json.getJSONObject("bonus_koin").getString("inviteMessage2");

    }
}
