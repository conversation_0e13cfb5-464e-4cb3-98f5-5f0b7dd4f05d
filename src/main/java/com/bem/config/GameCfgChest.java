package com.bem.config;

import com.bem.boom.Resources;
import com.bem.monitor.Bonus;
import com.bem.object.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgChest {

    public static final int CHEST_TYPE_SILVER = 0;
    public static final int CHEST_TYPE_GOLD = 1;
    public static final int CHEST_TYPE_WOOD = 2;


//    static final int GOLD = 1;
//    static final int GEM = 2;
//    static final int BOM_MATERIAL = 3;
//    static final int AVATAR_PIECE = 4;
//    static final int PET_FRUIT = 5;
//    static final int BOMB = 6;
//    static final int PET_PIECE = 7;
//    static final int PET = 8;


    public static List<DataConfig> data;

    /**
     * @param numberLoop so lan mo
     * @param type       0 bac, 1 ngoc
     * @param firstChest
     * @return
     */
    public static JSONArray getGoldChest(int numberLoop, int type, boolean firstChest) {
        if (numberLoop != 1) {
            firstChest = false;
        }
        Random ran = new Random();
        JSONArray array = new JSONArray();
        int[][] rate = data.get(type).rate;
        for (int j = 0; j < numberLoop; j++) {
            if (j == 9) {
                rate = data.get(type).rate10;
            }
            int result = Bonus.MATERIAL;
            int[]arr = {};
            if (firstChest) {
                result = Bonus.PET;
                int[]arrfirst ={100,result,2,1,0};
                arr=arrfirst;
            } else {

                result = ran.nextInt(100);
//                result = (j + 1) * 7;
                int rand = new Random().nextInt(rate.length);
                for (int i = 0; i < rate.length; i++) {
//                    if (result <= rate[i][0]||i==rand) {
                        if (result <= rate[i][0]) {

                            result = rate[i][1];
                        arr = rate[i];
                        break;
                    }
                }
            }
//             result = ran.nextInt(8)+1;
            int index = 2;
            switch (result) {

                case Bonus.ITEM:// ngau nhien rank b--s ok
//                int number = ran.nextInt(data.item[1] - data.item[0]) + data.item[0];
//                    int lv = data.get(type).bommateriallv[ran.nextInt(data.get(type).bommateriallv.length)];
                    int id = arr[index];
                    int number =arr[index++];
//                    int number = data.get(type).bommaterialnumber[ran.nextInt(data.get(type).bommaterialnumber.length)];
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.ITEM,id, number)));

                    break;
                case Bonus.GOLD:// ngau nhien rank b--s ok
//                int number = ran.nextInt(data.item[1] - data.item[0]) + data.item[0];
//                    int lv = data.get(type).bommateriallv[ran.nextInt(data.get(type).bommateriallv.length)];
                    number =arr[index];
//                    int number = data.get(type).bommaterialnumber[ran.nextInt(data.get(type).bommaterialnumber.length)];
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s]", Bonus.GOLD, number)));

                    break;
                case Bonus.MATERIAL:// ngau nhien rank b--s ok
//                int number = ran.nextInt(data.item[1] - data.item[0]) + data.item[0];
//                    int lv = data.get(type).bommateriallv[ran.nextInt(data.get(type).bommateriallv.length)];
                    int lv =arr[index];
                     number =arr[++index];
//                    int number = data.get(type).bommaterialnumber[ran.nextInt(data.get(type).bommaterialnumber.length)];
                    ResMaterial material = Resources.getRandomMaterialLevel(lv);
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.MATERIAL, material.getId(), number)));

                    break;
//                case GOLD://ok
//                    array.addAll( JSONArray.fromObject(String.format("[%s,%s]",  CfgAchievement.GOLD, ran.nextInt(data.get(type).gold[1] - data.get(type).gold[0]) + data.get(type).gold[0])));
//                    break;
//                case GEM://ok
//                    array.addAll(  JSONArray.fromObject(String.format("[%s,%s]",  CfgAchievement.GEM, ran.nextInt(data.get(type).gem[1] - data.get(type).gem[0]) + data.get(type).gem[0])));
//                    break;
                case Bonus.ACCESSORIES:// ngau nhien rank b--s
//                    System.out.println("accessories---->");
                    ResAccessories accessory = new ResAccessories();
                    if(arr[index]>0) {
                        accessory = Resources.getAccessories(arr[index]);
                    }else{
                        accessory = Resources.getRandomAccessories(-arr[index],-arr[index]);
                    }
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.ACCESSORIES, accessory.getId(), 1)));
                    break;
                case Bonus.BOMB:// ngau nhien rank b--s ok
                    ResBomb bom = new ResBomb();
                    if(arr[index]>0) {
                        bom = Resources.getBomb(arr[index]);
                    }else{
                        bom = Resources.getRandomBom(-arr[index],-arr[index]);
                    }
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.BOMB, bom.getId(), 1)));
                    break;
                case Bonus.PET_FRAGMENT:// ngau nhien rank b--s ok
                    ResPet petF = new ResPet();
                    if(arr[index]>0) {
                        petF = Resources.getPet(arr[index]);
                    }else{
                        petF = Resources.getRandomPet(-arr[index],-arr[index]);
                    }
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.PET_FRAGMENT, petF.getId(), arr[++index])));
                    break;
                case Bonus.PET_FOOD:
                    ResPetFood food = new ResPetFood();
                    if(arr[index]>0) {
                        food = Resources.getPetFood(arr[index]);
                    }else{
                        food = Resources.getRandomFruit(-arr[index]);
                    }
//                    int rank = data.get(type).petfruit[ran.nextInt(data.get(type).petfruit.length)];
//                    ResPetFood fruit = Resources.getRandomFruit(rank);
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.PET_FOOD, food.getId(), arr[++index])));
                    break;
                case Bonus.AVATAR_FRAGMENT:
                    ResAvatar avatar = new ResAvatar();
                    if(arr[index]>0) {
                        avatar = Resources.getAvatar(arr[index]);
                    }else{
                        avatar = Resources.getRandomAvatar(-arr[index],-arr[index]);
                    }
//                ShopAvatarEntity avatar = null;
//                    rank = data.get(type).avatarpiece[ran.nextInt(data.get(type).avatarpiece.length)];
//                    ResAvatar avatar = Resources.getRandomAvatar(rank, rank);
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.AVATAR_FRAGMENT, avatar.getId(), arr[++index])));
                    break;
//                case CfgAchievement.PET_FRAGMENT:
////                    rank = ran.nextInt(5) + 1;
////                    ResPet pet = Resources.getRandomPet(rank);
////                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", CfgAchievement.PET_FRAGMENT, pet.getId(), 1)));
//                    break;
                case Bonus.PET:
                    ResPet pet = new ResPet();
                    if(arr[index]>0) {
                        pet = Resources.getPet(arr[index]);
                    }else{
                        pet = Resources.getRandomPet(-arr[index],-arr[index]);
                    }
                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s,%s]", Bonus.PET, pet.getId(), arr[++index], arr[++index])));
                    break;
            }
        }
//        if(arrGem>0){
//            array.addAll( JSONArray.fromObject(String.format("[%s,%s]",  CfgAchievement.GEM, arrGem)));
//        }
//        if(arrGold>0){
//            array.addAll( JSONArray.fromObject(String.format("[%s,%s]",  CfgAchievement.GOLD, arrGold)));
//        }
        return array;
    }

    public static void loadConfig(String strJson) {

         data= (ArrayList<DataConfig>) new Gson().fromJson(strJson,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());
        for (int i = 0; i < data.size(); i++) {
            for (int j = 1; j < data.get(i).rate.length; j++) {
                data.get(i).rate[j][0] += data.get(i).rate[j-1][0];
            }
            for (int j = 1; j < data.get(i).rate10.length; j++) {
                data.get(i).rate10[j][0] += data.get(i).rate10[j-1][0];
            }

        }
    }

    //    public class ListDataConfig{
//        List<DataConfig> lstData;
//    }
    public class DataConfig {
        public int fee, fee10, typefee, maxDay, timeFree;
        public int[][]rate, rate10;
        //, gold, gem;
//        , bommateriallv, bommaterialnumber, avatarpiece, petpiece, petfruit, bomb;
//        public long avatarTime;
    }
}
