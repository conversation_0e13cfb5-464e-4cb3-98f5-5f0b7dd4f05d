/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import com.google.gson.Gson;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgPayment {

    public static JSONObject json;
    public static DataConfig config;

    /**
     * 80 500 1200 2500 6500 14000
     * 1$ 4$ 7$ 12$ 20$ 35$
     * 25_000L, 96_000L, 175_000L, 300_000L, 500_000L, 925_000L
     */
    public static long getGemNapMoney(int index) {
        return List.of(25_000L, 96_000L, 175_000L, 300_000L, 500_000L, 925_000L).get(index);
    }

    /**
     * pack.diamond.1
     * pack.diamond.2
     * pack.diamond.3
     * pack.diamond.4
     * pack.diamond.5
     * pack.diamond.6
     * pack.month.1
     * pack.month.2
     *
     */
    public static long getIapGem(String productId) {
        String[] tmp = productId.split("\\.");
        String id = String.valueOf(Integer.parseInt(tmp[tmp.length - 1]) - 1);
        for (int i = 0; i < config.iapKey.size(); i++) {
            if (config.iapKey.get(i).contains(id)) {
                return config.iapTygia.get(i);
            }
        }
        return 50;
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public List<String> cardTygia;
        public List<String> iapKey;
        public List<Long> iapTygia;
        public List<String> zingxuKey;
        public List<Long> zingxuTygia;
        public List<String> sohaKey;
        public List<Long> sohaTygia;

        public int showTabCard, showTabIap, showViettel, showMobi, showVina, showGMobi;
    }
}
