package com.bem.config;

import com.google.gson.Gson;
import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class CfgKethon {
    public static JSONObject json;
    public static DataConfig config;
    public static final int CAUHON = 0, DONGY=1, TUCHOI =2 ;
    private static Map<Long,Long> dangkykethon = new HashMap<>();


    public static synchronized void makeDangkykethon(int type , long userKey , long userValue) {
        if(type ==CAUHON){
            dangkykethon.put(userKey,userValue);
        }else {
            dangkykethon.remove(userKey);
        }
    }


    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public int feeCreate, feeChangeName, timeLeaveClan, timeReqClan, levelRequired;
        public int[] nameLength, sloganLength, members, diemdanh;
        public long[] levels;
    }

}