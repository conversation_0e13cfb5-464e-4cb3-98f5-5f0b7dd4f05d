package com.bem.config;

import com.bem.object.EventStatus;
import com.google.gson.Gson;
import lombok.Data;
import net.sf.json.JSONObject;

import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgBoss {
    public static JSONObject json;
    public static DataConfig config;
    public static HashMap<Integer, Map> mMap = new HashMap<>();

    public static int[] getSupportItem() {
        int[] items = new int[config.numberItem];
        for (int i = 0; i < config.numberItem; i++) {
            items[i] = config.dropItem[new Random().nextInt(config.dropItem.length)];
        }
        return items;
    }

    public static long getTimeout(int eventIndex) {
        if (config.test == 0) {
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.HOUR_OF_DAY, config.time.get(eventIndex) + 1);
            ca.set(Calendar.MINUTE, 0);
            ca.set(Calendar.SECOND, 0);
            long timeout = ca.getTimeInMillis() - System.currentTimeMillis();
            if (timeout < 0) timeout = 0;
            return timeout / 1000;
        }
        Calendar calendar = Calendar.getInstance();
        int curMin = calendar.get(Calendar.MINUTE);
        int curSecond = calendar.get(Calendar.SECOND);
        return (5 - curMin % 5) * 60 + 60 - curSecond;
    }

    public  static void main(String[] args) {
        System.out.println(Calendar.getInstance().get(Calendar.HOUR_OF_DAY));
    }

    // Thời gian kết thúc sự kiện gần nhất
    public static int[] getLastEndEvent() {
        if (config.test == 0) {
            int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
            for (int i = config.time.size() - 1; i >= 0; i--) {
                if (hour >= config.time.get(i) + 1) {
                    return new int[]{i, (config.time.get(i) + 1) * 3600};
                }
            }
            return new int[]{0, (config.time.get(0) + 1) * 3600};
        }
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        if (minute % 10 < 5) return new int[]{-1, -1};
        return new int[]{0, hour * 3600 + ((minute / 10) * 10 + 5) * 60};
    }

    public static String getNextStrEvent() {
        if (config.test == 0) {
            int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
            for (int i = 0; i < config.time.size(); i++) {
                if (hour < config.time.get(i)) {
                    if (Calendar.getInstance().get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY && i == 1) {
                        return config.strTime[0];
                    }
                    return config.strTime[i];
                }
            }
            return config.strTime[0];
        }
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        if (minute % 10 < 5) return "";
        return String.format("%sh%s - %sh%s", hour, (minute / 10) * 10 + 10, hour, (minute / 10) * 10 + 15);
    }

    public static int getEventIndex(int index) {
        Calendar calendar = Calendar.getInstance();
        if (config.test == 0) {
            return calendar.get(Calendar.DAY_OF_YEAR) * 100 + config.time.get(index);
        }
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        return calendar.get(Calendar.DAY_OF_YEAR) * 10000 + hour * 100 + (minute / 10 + 1) % 3;
    }

    public static EventStatus getEventStatus() {
        if (config.test == 0) {
            Calendar calendar = Calendar.getInstance();
            int index = config.time.indexOf(calendar.get(Calendar.HOUR_OF_DAY));
            if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY && index == 1) index = -1;
            if (index == -1) return new EventStatus();
            return new EventStatus(calendar.get(Calendar.DAY_OF_YEAR) * 100 + config.time.get(index), index);
        }
        // test value
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int key = hour * 10000 + minute / 10;
        if (minute % 10 < 5) return new EventStatus((minute / 10 + 1) % 3, key * 10 + key % 3);
        return new EventStatus();
    }

    public static Map getRandomMap() {
        return config.maps.get(new Random().nextInt(config.maps.size()));
    }

    public static Map getMap() {
        return config.maps.get(Calendar.getInstance().get(Calendar.DAY_OF_WEEK) - 1);
    }

    public static Map getMap(int mapId) {
        return mMap.get(mapId);
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
        mMap.clear();
        for (Map map : config.maps) {
            mMap.put(map.id, map);
        }
        if (CfgServer.isTest()) {
            config.test = 1;
        }
    }

    @Data
    public class DataConfig {
        String[] strTime;
        List<Integer> time;
        public int[] dropItem;
        public List<List<Integer>> topBonus;
        public int feeRevive, enable, requireLevel, test, timeFree, numberItem;
        public List<Map> maps;
    }

    public class Map {
        public int id;
        public int[] monster, boss;

        public List<Integer> getMonsterId() {
            List<Integer> aMonster = new ArrayList<>();
            for (int i = 0; i < boss.length; i++) {
                aMonster.add(boss[i]);
            }
            return aMonster;
        }
    }
}
