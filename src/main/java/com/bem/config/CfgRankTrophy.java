package com.bem.config;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

public class CfgRankTrophy {
    public final static int CHUANHAN = 0, COTHENHAN = 1;
    public static List<DataConfig> data;


    public static void loadConfig(String value) {
            data = (ArrayList<DataConfig>) new Gson().fromJson(value,
                    new TypeToken<ArrayList<DataConfig>>() {
                    }.getType());
    }

    public class DataConfig {
        public int id;
        public int number, trophy;
        public String name;
    }

}