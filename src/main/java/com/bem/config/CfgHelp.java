package com.bem.config;

import net.sf.json.JSONObject;

public class CfgHelp {

	public static JSONObject mHelp;

	public static String getContent(String code) {
		try {
			String ret = mHelp.getString(code);
			if (ret == null || ret.length() == 0) {
				return "Chưa cập nhật nội dung";
			}
			return ret;
		} catch (Exception ex) {
		}
		return "Chưa cập nhật nội dung";
	}

	public static void loadConfig(String strJson) {
		JSONObject json = JSONObject.fromObject(strJson);
		mHelp = json.getJSONObject("help");
	}

}
