package com.bem.config;

import com.google.gson.Gson;
import com.k2tek.Config;
import com.k2tek.Xerver;
import net.sf.json.JSONObject;
import org.apache.commons.net.util.SubnetUtils;

import java.util.ArrayList;
import java.util.List;

public class CfgIp {
    public static JSONObject json;
    public static DataConfig config;
    public static List<SubnetUtils> maskVN = new ArrayList<SubnetUtils>();

//    public static void main(String[] rags) throws Exception {
//        Config.load(Xerver.DEFAULT_CONFIG_FILE);
//        Xerver.initGameConfig();
//            System.out.println(CfgIp.isIpVN("************"));
//        System.out.println(CfgIp.isIpVN("************"));
//    }

    public static boolean isIpVN(String ip) {
        for (SubnetUtils mask : maskVN) {
            if (mask.getInfo().isInRange(ip))
                return true;
        }
        return false;
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);

        maskVN.clear();
        config.vn.forEach(ip -> {
            try {
                SubnetUtils tmp = new SubnetUtils(ip);
                tmp.setInclusiveHostCount(true);
                maskVN.add(tmp);
            } catch (Exception ex) {
            }
        });
    }

    public class DataConfig {
        public List<String> vn;
    }

}