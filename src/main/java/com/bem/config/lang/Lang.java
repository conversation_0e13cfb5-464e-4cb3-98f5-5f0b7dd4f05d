package com.bem.config.lang;

import com.bem.config.CfgCommon;
import com.bem.dao.mapping.ConfigLanguage;
import com.bem.dao.mapping.ConfigLanguageClient;
import com.bem.util.Util;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import grep.helper.Filer;
import org.hibernate.Session;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class Lang implements Serializable {

    public static String LOCALE_VI = "vi", LOCALE_EN = "en";
    static int NUMBER_LANGUAGE = 2, VI = 0, EN = 1;
    static Map<String, Integer> keyIndex = new HashMap<String, Integer>();
    static Map<String, String> keyMap = new HashMap<String, String>();
    static String[][] aLang;
    static String[] errCode = {"Lỗi ngôn ngữ", "Language not found"};
    static Map<String, String> clientLanguage = new HashMap<String, String>();
    static Map<String, String> hackLanguage = new HashMap<String, String>();

    static {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            List<ConfigLanguage> tmp = session.createSQLQuery("select * from " + CfgCommon.mainDb + "config_language").addEntity(ConfigLanguage.class).list();
            aLang = new String[NUMBER_LANGUAGE][tmp.size()];
            for (int i = 0; i < tmp.size(); i++) {
                ConfigLanguage cLang = tmp.get(i);
                keyIndex.put(cLang.getK(), i);
                keyMap.put(LOCALE_VI + "_" + cLang.getK(), cLang.getVi());
                keyMap.put(LOCALE_EN + "_" + cLang.getK(), cLang.getEn());
                aLang[VI][i] = cLang.getVi();
                aLang[EN][i] = cLang.getEn();
            }

            List<ConfigLanguageClient> clientLang = session.createSQLQuery("select * from " + CfgCommon.mainDb + "config_language_client").addEntity(ConfigLanguageClient.class).list();
            for (ConfigLanguageClient configLanguageClient1 : clientLang) {
                clientLanguage.put(configLanguageClient1.getVi(), configLanguageClient1.getEn());
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
        try {
            var values = Filer.realAllFile("Localization.csv");
            for (String value : values) {
                String[] splits = value.split(",");
                if (splits.length > 4) {
                    hackLanguage.put(splits[3], splits[4]);
                }
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
    }

    public static String getClientLanguage(String k) {
        String value = clientLanguage.get(k);
        if (value == null) value = hackLanguage.get(k);
        return value == null ? k : value;
    }

    public static Lang instance(String locale) {
        locale = locale == null ? "en" : locale;
        locale = getValidLang(locale.toLowerCase());
        if (locale.equals("en")) {
            return LangEn.instance();
        }
        return LangVi.instance();
    }

    String locale = "";

    public String getLocale() {
        return locale;
    }

    public abstract String get(String key);

    public String get(String key, String languageCode) {
        return keyMap.get(languageCode + "_" + key);
    }

    public static String format(String input, String[] replace) {
        return String.format(input, replace);
    }

    static List<String> validLanguage = Arrays.asList("en", "vi");

    public static String getValidLang(String value) {
        if (validLanguage.contains(value)) {
            return value;
        }
        return validLanguage.get(0);
    }

    // Common error
    public static String err_system_down = "err_system_down";
    public static String err_unknown = "err_unknown";
    public static String err_maintaining = "err_maintaining";
    public static String err_param = "err_param";
    public static String err_not_enough_speaker = "err_not_enough_speaker";
    public static String err_quick_chat = "err_quick_chat";
    public static String err_bonus_not_exist = "err_bonus_not_exist";
    public static String err_search_length = "err_search_length";
    public static String err_bonus_receive_condition = "err_bonus_receive_condition";
    public static String err_bonus_already_received = "err_bonus_already_received";
    public static String err_max_level = "err_max_level";
    public static String err_not_enough = "err_not_enough";
    public static String err_not_enough_gold = "err_not_enough_gold";
    public static String err_not_enough_gem = "err_not_enough_gem";
    public static String err_not_enough_energy = "err_not_enough_energy";
    public static String err_not_enough_relationship = "err_not_enough_relationship";
    public static String err_not_enough_medal = "err_not_enough_medal";
    public static String err_not_enough_boss_medal = "err_not_enough_boss_medal";
    public static String err_max_chat = "err_max_chat";
    public static String err_max_purchase = "err_max_purchase";
    public static String err_has_free_attack = "err_has_free_attack";
    public static String err_only_exchange_in_event = "err_only_exchange_in_event";
    public static String err_vip_level = "err_vip_level";
    public static String err_item_not_exist = "err_item_not_exist";
    public static String err_out_of_stock = "err_out_of_stock";
    public static String err_required_hero = "err_required_hero";
    public static String err_already_own_hero = "err_already_own_hero";
    public static String err_already_own_ufo = "err_already_own_ufo";
    public static String err_map_id = "err_map_id";
    public static String err_user_not_same_server = "err_user_not_same_server";
    public static String err_clan_not_same_server = "err_clan_not_same_server";

    // Common
    public static String common_success = "common_success";
    public static String common_chat_length = "common_chat_length";
    public static String common_closed_event = "common_closed_event";
    public static String common_event_required_level = "common_event_required_level";

    // login
    public static String login_session_not_exist = "login_session_not_exist";
    public static String login_session_wrong = "login_session_wrong";
    public static String login_account_not_exist = "login_account_not_exist";

    // clan
    public static String clan_no_clan = "clan_no_clan";
    public static String clan_only_co_and_leader = "clan_only_co_and_leader";
    public static String clan_only_leader = "clan_only_leader";
    public static String clan_only_elder = "clan_only_elder";
    public static String clan_invite_expire = "clan_invite_expire";
    public static String clan_join_level_required = "clan_join_level_required";
    public static String clan_not_exist = "clan_not_exist";
    public static String clan_leave_first = "clan_leave_first";
    public static String clan_max_member = "clan_max_member";
    public static String clan_bonus_out_of_stock = "clan_bonus_out_of_stock";
    public static String clan_member_leave = "clan_member_leave";
    public static String clan_err_setting_desc = "clan_err_setting_desc";
    public static String clan_err_setting_trophy = "clan_err_setting_trophy";
    public static String clan_err_setting_name = "clan_err_setting_name";
    public static String clan_name_exist = "clan_name_exist";
    public static String clan_err_symbol = "clan_err_symbol";
    public static String clan_gift = "clan_gift";
    public static String clan_err_time_diemdanh = "clan_err_time_diemdanh";
    public static String clan_max_diemdanh = "clan_max_diemdanh";
    public static String clan_err_position = "clan_err_position";

    // user
    public static String user_not_online = "user_not_online";
    public static String user_not_exist = "user_not_exist";
    public static String user_invite_friend_only = "user_invite_friend_only";
    public static String user_report_no_reason = "user_report_no_reason";
    public static String user_report_quick = "user_report_quick";
    public static String user_name_length_invalid = "user_name_length_invalid";
    public static String user_name_exist = "user_name_exist";

    // giftcode
    public static String giftcode_invalid = "giftcode_invalid";
    public static String giftcode_success = "giftcode_success";

    // friend
    public static String err_make_friend = "err_make_friend";
    public static String mfriend_err_max_number = "mfriend_err_max_number";
    public static String mfriend_reject = "mfriend_reject";
    public static String mfriend_accept = "mfriend_accept";
    public static String no_friend_in_list = "no_friend_in_list";
    public static String not_marry = "not_marry";
    public static String invisible_proposal = "invisible_proposal";
    public static String not_enough_gem_to_propose = "not_enough_gem_to_propose";
    public static String be_married = "be_married";
    public static String be_married_person = "be_married_person";
    public static String you_has_been_married = "you_has_been_married";
    public static String you_has_been_married_this = "you_has_been_married_this";
    public static String request_propose_to_send = "request_propose_to_send";
    public static String bonus_to_marry = "bonus_to_marry";

    // inventory
    public static String pet_not_own = "pet_not_own";
    public static String pet_food_not_enough = "pet_food_not_enough";
    public static String pet_food_invalid = "pet_food_invalid";
    public static String avatar_not_own = "avatar_not_own";
    public static String bomb_not_own = "bomb_not_own";
    public static String symbol_not_own = "symbol_not_own";
    public static String hero_not_own = "hero_not_own";
    public static String accessory_not_own = "accessory_not_own";
    public static String ufo_not_own = "ufo_not_own";
    public static String ufo_stone_not_own = "ufo_stone_not_own";
    public static String material_not_own = "material_not_own";
    public static String accessory_user_level_required = "accessory_user_level_required";
    public static String avatar_fragment_not_enough = "avatar_fragment_not_enough";
    public static String not_sale = "not_sale";
    public static String not_sale_wearing_item = "not_sale_wearing_item";
    public static String chest_not_enough = "chest_not_enough";
    public static String goldkey_not_enough = "goldkey_not_enough";

    // Match
    public static String match_err_number_attack_per_day = "match_err_number_attack_per_day";
    public static String match_err_number_attack = "match_err_number_attack";
    public static String match_err_not_enough_atomic_bomb = "match_err_not_enough_atomic_bomb";
    public static String match_err_only_sweep_3_star = "match_err_only_sweep_3_star";
    public static String match_err_only_sweep_completed = "match_err_only_sweep_completed";
    public static String match_err_not_host_player = "match_err_not_host_player";

    // Require
    public static String required_to_chat = "required_to_chat";

    // Wish From System
    public static String marry_wish_from_system = "marry_wish_from_system";
    public static String jackpot1_wish_from_system = "jackpot1_wish_from_system";
    public static String jackpot2_wish_from_system = "jackpot2_wish_from_system";
    public static String upgrade_pet_wish_from_system = "upgrade_pet_wish_from_system";
    public static String upgrade_pet_star_wish_from_system = "upgrade_pet_star_wish_from_system";
    public static String upgrade_pet_level_wish_from_system = "upgrade_pet_level_wish_from_system";
    public static String upgrade_boom_level_wish_from_system = "upgrade_boom_level_wish_from_sytem";
    public static String arena_congratulation_from_system = "arena_congratulation_from_system";

    // Event
    public static String err_event_message = "err_event_message";
    public static String err_event_qualifier_round_message = "err_event_qualifier_round_message";
    public static String err_event_knockout_round_message = "err_event_knockout_round_message";
    public static String err_start_event = "err_start_event";
    public static String err_room_code = "err_room_code";
    public static String top_business_class_award = "top_business_class_award";
    public static String top_economy_class_award = "top_economy_class_award";
    public static String top_kingdom_award = "top_kingdom_award";
    public static String err_open_event = "err_open_event";
    public static String leave_room_fine = "leave_room_fine";
    public static String level_to_join_event = "level_to_join_event";
    public static String level_to_join_slot_machine = "level_to_join_slot_machine";
    public static String level_to_join_match = "level_to_join_rank_match";
    public static String closed_rank_match = "closed_rank_match";
    public static String not_correct_style = "not_correct_style";
    public static String level_to_unlock_map = "level_to_unlock_map";
    public static String not_exist_map = "not_exist_map";
    public static String not_enough_gem_to_skip = "not_enough_gem_to_skip";
    public static String no_turn_to_play = "no_turn_to_play";
    public static String time_to_play = "time_to_play";
    public static String time_to_play_after_change_kingdom = "time_to_play_after_change_kingdom";
    public static String wait_to_next_event = "wait_to_next_event";
    public static String clan_not_to_attack_lower = "clan_not_to_attack_lower";
    public static String not_to_attack_lower = "not_to_attack_lower";
    public static String format_user_chat = "format_user_chat";
    public static String format_user_leave = "format_user_leave";
    public static String format_battle_result1 = "format_battle_result1";
    public static String format_battle_result2 = "format_battle_result2";
    public static String format_battle_result3 = "format_battle_result3";
    public static String format_battle_result4 = "format_battle_result4";
    public static String nobody_get_award = "nobody_get_award";
    public static String level_to_play_slot_machine = "level_to_play_slot_machine";
    public static String not_enough_gem_to_spin = "not_enough_gem_to_spin";
    public static String not_correct_bet = "not_correct_bet";
    public static String leave_team_before_accept = "leave_team_before_accept";
    public static String not_exist_team = "not_exist_team";
    public static String team_is_playing = "team_is_playing";
    public static String only_solo = "only_solo";
    public static String team_full_members = "team_full_members";
    public static String is_not_room_master = "is_not_room_master";
    public static String no_turn_to_invite = "no_turn_to_invite";
    public static String has_invited_player = "has_invited_player";
    public static String is_playing_not_invite_player = "is_playing_not_invite_player";
    public static String permission_start_game = "permission_start_game";
    public static String all_members_not_ready = "all_members_not_ready";
    public static String is_playing_or_pending = "is_playing_or_pending";
    public static String is_not_allowed_map = "is_not_allowed_map";
    public static String not_correct_map = "not_correct_map";
    public static String check_not_enough_energy = "check_not_enough_energy";
    public static String check_not_enough_turn_attack = "check_not_enough_turn_attack";
    public static String dont_create_game = "dont_create_game";
    public static String minimum_player_to_train = "minimum_player_to_train";
    public static String game_is_creating = "game_is_creating";
    public static String is_room_master = "is_room_master";
    public static String dont_have_team = "dont_have_team";
    public static String not_enough_material = "not_enough_material";
    public static String over_upgrade_limit = "over_upgrade_limit";
}
