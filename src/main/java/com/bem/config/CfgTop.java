package com.bem.config;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class CfgTop {
    public static final int TYPE_USER = 1, TYPE_CLAN = 2, TYPE_EVENT = 3, TYPE_SIEUTHU = 4, TYPE_NHANVAT = 5;
    public static List<JSONObject> lstJobj = new ArrayList<>();

    public static void loadConfig(String value) {
        lstJobj.clear();
        JSONArray arr = JSONArray.fromObject(value);

        lstJobj.clear();
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            lstJobj.add(obj);
        }
    }
}
