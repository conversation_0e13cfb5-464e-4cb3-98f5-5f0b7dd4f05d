/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import com.google.gson.Gson;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgBonusPvp {
    public static JSONObject json;
    public static List<List<Integer>> bonus = new ArrayList<>();
    public static JSONArray lstbonus = new JSONArray();

    public static int percent;


    public static void loadConfig(String strJson) {
        bonus = new ArrayList<>();
        try {
            json = JSONObject.fromObject(strJson);
            percent = json.getInt("percent");
            json = JSONObject.fromObject(strJson);
//        bonus = new Gson().fromJson(strJson, DataConfig.class);
            lstbonus = json.getJSONArray("bonus");
            for (int i = 0; i < lstbonus.size(); i++) {
                int subPercent = lstbonus.getJSONObject(i).getInt("subPercent");
                for (int j = 0; j < subPercent; j++) {
                    JSONArray arr = lstbonus.getJSONObject(i).getJSONArray("subBonus");
                    bonus.add(lstbonus.getJSONObject(i).getJSONArray("subBonus"));
                }
            }
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

//    public class DataConfig {
//        public Map<String, String> debugAccount;
//    }

}
