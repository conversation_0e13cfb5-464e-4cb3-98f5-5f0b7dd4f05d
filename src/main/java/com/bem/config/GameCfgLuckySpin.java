package com.bem.config;

import com.bem.boom.Resources;
import com.bem.monitor.Bonus;
import com.bem.object.*;
import com.cache.JCache;
import com.google.gson.Gson;
import net.sf.json.JSONArray;

import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgLuckySpin {

    static final int MATERIAL = 1;
    static final int ACCESSORIES = 2;
    static final int AVATAR_FRAGMENT = 3;
    static final int BOMB = 4;
    static final int PET_FRAGMENT = 5;
    static final int PET_FOOD = 6;


    public static DataConfig data;
    public static Map<Integer, List<Integer>> award = new LinkedHashMap<>();
    public static List<List<Integer>> lstaward = new ArrayList<>();


//    public static JSONArray exchangeStar(int id) {
//        try {
//            int result = data.exchange_star_bonus.get(id);
//            switch (result) {
//                case CfgAchievement.PET:
//                    ResPet pet = Resources.getRandomPet(4, 5);
//                    return JSONArray.fromObject(String.format("[%s,%s,%s,%s]", result, pet.getId(), 1, 0));
//                case CfgAchievement.BOMB:
//                    ResBomb bom = Resources.getRandomBom(4, 5);
//                    return JSONArray.fromObject(String.format("[%s,%s,%s]", result, bom.getId(), 1));
//                case CfgAchievement.AVATAR:
//                    ResAvatar avatar = Resources.getRandomAvatar(4, 5);
//                    return JSONArray.fromObject(String.format("[%s,%s,%s]", result, avatar.getId(), 1));
//            }
//        } catch (Exception ex) {
//        }
//        return null;
//    }

//    public static JSONArray getLuckySpin(UserInfo user) {
//        Random ran = new Random();
//        int result = ran.nextInt(100);
//        for (int i = 0; i < data.rate.length; i++) {
//            if (result <= data.rate[i]) {
//                result = i + 1;
//                break;
//            }
//        }
////        if (result > 3) {
////            result = 1;
////        } else {
////            result = 6;
////        }
//        switch (result) {
//            case AVATAR:// ngau nhien rank b--s ok
////                int number = ran.nextInt(data.item[1] - data.item[0]) + data.item[0];
//                List<Integer> lstHero = new ArrayList<>();
//                for (int i = 0; i < user.getRes().getHeroes().size() ; i++) {
//                    lstHero.add(user.getRes().getHeroes().get(i).getHeroId());
//                }
//                ResAvatar avatar = Resources.getRandomAvatarByHero(3, 5,lstHero);
//                return JSONArray.fromObject(String.format("[%s,%s,%s,%s]", result, CfgAchievement.AVATAR, avatar.getId(), 1));
//            case GOLD://ok
//                return JSONArray.fromObject(String.format("[%s,%s,%s]", result, CfgAchievement.GOLD, ran.nextInt(data.gold[1] - data.gold[0]) + data.gold[0]));
//            case GEM://ok
//                return JSONArray.fromObject(String.format("[%s,%s,%s]", result, CfgAchievement.GEM, ran.nextInt(data.gem[1] - data.gem[0]) + data.gem[0]));
//            case BOMB:// ngau nhien rank b--s ok
//                ResBomb bom = Resources.getRandomBom(3, 5);
//                return JSONArray.fromObject(String.format("[%s,%s,%s,%s]", result, CfgAchievement.BOMB, bom.getId(), 1));
//            case INVENTORY:
//              JSONArray arr = JSONArray.fromObject(GameCfgChest.getGoldChest(1, 1, false));
//                arr.add(0,INVENTORY);
//                return arr;
//            case PET:
////                ShopAvatarEntity avatar = null;
//                ResPet pet = Resources.getRandomPet(3, 4);
//                return JSONArray.fromObject(String.format("[%s,%s,%s,%s,%s]", result, CfgAchievement.PET, pet.getId(), 1, 0));
//        }
//        return null;
//    }

    public static void initAwardSpin() {
        award = new HashMap<>();
        lstaward = new ArrayList<>();
        for (int i = 0; i < data.rate.length; i++) {

            int[] arr = data.rate[i];
            int index = 2;
            List<Integer> lst = new ArrayList<>();
            switch (arr[1]) {
                case Bonus.AVATAR_FRAGMENT:// ngau nhien rank b--s ok
                    ResAvatar avatar = new ResAvatar();
                    if (arr[index] > 0) {
                        avatar = Resources.getAvatar(arr[index]);
                    } else {
                        avatar = Resources.getRandomAvatar(-arr[index], -arr[index]);
                    }
                    lst.add(AVATAR_FRAGMENT);
                    lst.add(arr[1]);
                    lst.add(avatar.getId());
                    lst.add(arr[++index]);
                    award.put(arr[1], lst);
                    lstaward.add(lst);
                    break;
                case Bonus.BOMB:// ngau nhien rank b--s ok
//                    System.out.println("bomb");

                    ResBomb bom = new ResBomb();
                    if (arr[index] > 0) {
                        bom = Resources.getBomb(arr[index]);
                    } else if (arr[index] < 0) {
                        bom = Resources.getRandomBom(-arr[index], -arr[index]);
                    } else {
                        bom = Resources.getRandomBomb();

                    }
                    lst.add(BOMB);
                    lst.add(arr[1]);
                    lst.add(bom.getId());
                    lst.add(1);
                    award.put(arr[1], lst);
                    lstaward.add(lst);

//                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", CfgAchievement.BOMB, bom.getId(), 1)));
                    break;
                case Bonus.ACCESSORIES:// ngau nhien rank b--s
                    ResAccessories accessory = new ResAccessories();
                    if (arr[index] > 0) {
                        accessory = Resources.getAccessories(arr[index]);
                    } else {
                        accessory = Resources.getRandomAccessories(-arr[index], -arr[index]);
                    }
                    lst.add(ACCESSORIES);
                    lst.add(arr[1]);
                    lst.add(accessory.getId());
                    lst.add(1);
                    award.put(arr[1], lst);
                    lstaward.add(lst);

//                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", CfgAchievement.ACCESSORIES, accessory.getId(), 1)));
                    break;
                case Bonus.MATERIAL:// ngau nhien rank b--s ok
                    int lv = -arr[index];
                    int number = arr[++index];
//                    System.out.println("lv-->" + lv);
                    ResMaterial material = Resources.getRandomMaterialLevel(lv);
                    lst.add(MATERIAL);
                    lst.add(arr[1]);
                    lst.add(material.getId());
                    lst.add(number);
                    award.put(arr[1], lst);
                    lstaward.add(lst);

//                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", CfgAchievement.MATERIAL, material.getId(), number)));

                    break;
                case Bonus.PET_FRAGMENT:// ngau nhien rank b--s ok
                    ResPet petF = new ResPet();
                    if (arr[index] > 0) {
                        petF = Resources.getPet(arr[index]);
                    } else if (arr[index] < 0) {
                        petF = Resources.getRandomPet(-arr[index], -arr[index]);
                    } else {
                        petF = Resources.getRandomPet();
                    }
                    lst.add(PET_FRAGMENT);
                    lst.add(arr[1]);
                    lst.add(petF.getId());
                    lst.add(1);
                    award.put(arr[1], lst);
                    lstaward.add(lst);

//                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", CfgAchievement.PET_FRAGMENT, petF.getId(), 1)));
                    break;
                case Bonus.PET_FOOD:
                    ResPetFood food = new ResPetFood();
                    if (arr[index] > 0) {
                        food = Resources.getPetFood(arr[index]);
                    } else {
                        food = Resources.getRandomFruit(-arr[index]);
                    }
                    lst.add(PET_FOOD);
                    lst.add(arr[1]);
                    lst.add(food.getId());
                    lst.add(arr[++index]);
                    award.put(arr[1], lst);
                    lstaward.add(lst);

//                    int rank = data.get(type).petfruit[ran.nextInt(data.get(type).petfruit.length)];
//                    ResPetFood fruit = Resources.getRandomFruit(rank);
//                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", CfgAchievement.PET_FOOD, food.getId(), arr[++index])));
                    break;
            }
        }
    }

    public static int getTime() {
        int times = 1;
        String cache = JCache.getInstance().getValue(":GETAWARDLUCKYSPIN:");
        try {
            times = Integer.parseInt(cache);
        } catch (Exception ex) {
        }
        times = times * (data.timerefresh/60);
//        System.out.println("timerefresh--->"+data.timerefresh);
        Calendar ca = Calendar.getInstance();
        int h = ca.getTime().getHours();
        int m = ca.getTime().getMinutes();
        int s = ca.getTime().getSeconds();
        int rs = (times-h-1) * 60 * 60 + (60 - m) * 60 + (60 - s);
        return rs;
    }


    public static List<Integer> getAwardSpin(int key) {

        Calendar ca = Calendar.getInstance();
        int h = ca.getTime().getHours();
//        int m = ca.getTime().getMinutes();
//        int s = ca.getTime().getSeconds();
        int myTimes = (h / (data.timerefresh/60)) + 1;
        if (award == null || award.size() == 0) {
            initAwardSpin();
            JCache.getInstance().setValue(":GETAWARDLUCKYSPIN:", myTimes + "", data.timerefresh * 60);
        } else {
            String times = JCache.getInstance().getValue(":GETAWARDLUCKYSPIN:");
            int awardTimes = 0;
            try {
                awardTimes = Integer.parseInt(times);
            } catch (Exception ex) {
            }
            if (times == null || awardTimes == 0 || awardTimes != myTimes) {
                times = myTimes + "";
                initAwardSpin();
                JCache.getInstance().setValue(":GETAWARDLUCKYSPIN:", times, data.timerefresh * 60);
            }
        }
        return award.get(key);
    }

    public static JSONArray getLuckySpinnew(UserInfo user) {
        Random ran = new Random();
        int result = ran.nextInt(100);
        int[][] rate = data.rate;
        for (int i = 0; i < rate.length; i++) {
            if (result <= rate[i][0]) {
                result = rate[i][1];
                break;
            }
        }
        List<Integer> rs = getAwardSpin(result);
        return JSONArray.fromObject(rs.toString());
    }


    public static void loadConfig(String strJson) {
        data = new Gson().fromJson(strJson, DataConfig.class);
        for (int i = 1; i < data.rate.length; i++) {
            data.rate[i][0] += data.rate[i - 1][0];
        }
    }

    public class DataConfig {
        public int feeGem, bonusStar, timerefresh;
        public int[][] rate, exchange_star_new;
        public int[] gold, gem, item;
        public long avatarTime;
        public List<String> spin_data;
        public List<String> exchange_star;
        public List<Integer> exchange_star_fee;
        public List<Integer> exchange_star_bonus;
    }
}
