package com.bem.config;

import java.util.HashMap;
import java.util.Map;
import java.util.Vector;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class CfgMission {

    public static JSONObject json;
    public static JSONArray mission, moreapp, osDisable;
    public static Map<String, Integer> mGame = new HashMap<String, Integer>();
    public static int fbKoin = 5000;
    public static int fbMaxPerDay = 5;
    public static String androidStore;
    public static String iphoneStore;
    public static String windowphoneStore;
    public static String androidStore2;
    public static String iphoneStore2;
    public static String windowphoneStore2;
    public static Vector<Object> configStoreCp = new Vector<Object>();
    public static int koinfacebook = 5000;
    public static int sobanbe = 3;

    public static Integer getMoregameKoin(String gameId) {
        return mGame.get(gameId);
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);

        mission = json.getJSONArray("mission");
        moreapp = json.getJSONArray("app");
        osDisable = json.getJSONArray("osDisable");
        androidStore = json.getString("androidStore");
        iphoneStore = json.getString("iphoneStore");
        windowphoneStore = json.getString("windowphoneStore");
        androidStore2 = json.getString("androidStore2");
        iphoneStore2 = json.getString("iphoneStore2");
        windowphoneStore2 = json.getString("windowphoneStore2");
//		for (int i = 0; i < mission.size(); i++) {
//			JSONObject tmp = mission.getJSONObject(i);
//			if (tmp.getInt("id") == 2) {
//				fbKoin = tmp.getInt("koin");
//				fbMaxPerDay = tmp.getInt("maxPerDay");
//			}
//		}

        mGame.clear();
        for (int i = 0; i < moreapp.size(); i++) {
            JSONObject obj = moreapp.getJSONObject(i);
            mGame.put(obj.getString("android_url"), obj.getInt("koin"));
            mGame.put(obj.getString("iphone_package"), obj.getInt("koin"));
        }
        JSONArray arVipChat = json.getJSONArray("configStoreCp");
        for (Object object : arVipChat) {
            configStoreCp.add(object);
        }
        koinfacebook = json.getInt("koinfacebook");
        sobanbe = json.getInt("sobanbe");
    }
}