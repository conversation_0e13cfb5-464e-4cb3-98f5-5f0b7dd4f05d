package com.bem.config;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class CfgRegister {

    private static SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
    public static JSONObject json;
    private static Date curDate = new Date();
    public static int isBlock, accPerDay, timePerAccount, minPasswordLength;
    public static List<String> aCp = new ArrayList<String>();
    public static Map<String, Integer> mNumber = new HashMap<String, Integer>();
    public static Map<String, Long> mTimes = new HashMap<String, Long>();
    public static boolean iphoneChecksum = false, androidChecksum = false, checkValidMobile = true;
    static List<String> invalidUsername = new ArrayList<String>();
    static List<String> invalidScreenname = new ArrayList<String>();

    public static int isAllowRegisterAccount(String cp, String ip) {
        boolean block = false;
        switch (isBlock) {
            case 2:
                block = true;
                break;
            case 1:
                cp = cp.toLowerCase();
                if (aCp.contains(cp)) {
                    block = true;
                } else {
//                    String parentCP = CfgServer.getParentCP(cp);
//                    if (parentCP != null && aCp.contains(parentCP)) {
//                        block = true;
//                    }
                }
                break;
            case 0:
                block = false;
                break;
        }

        if (!block) {
            return 0;
        }

        Calendar ca = Calendar.getInstance();
        if (!sdf.format(curDate.getTime()).equals(sdf.format(ca.getTime()))) {
            mNumber.clear();
            mTimes.clear();
        }
        Integer number = mNumber.get(ip);
        if (number == null) {
            number = 0;
        }
        Long times = mTimes.get(ip);
        if (times == null) {
            times = ca.getTimeInMillis() - 60000 * 60 * 24;
        }

        if (number >= accPerDay) {
            return 1;
        }

        if (ca.getTimeInMillis() - times <= timePerAccount) {
            return 2;
        }

        mNumber.put(ip, ++number);
        mTimes.put(ip, ca.getTimeInMillis());

        return 0;
    }

    public static boolean isInvalidUsername(String value) {
        return false;
    }

    public static boolean isInvalidScreenname(String value) {
        return invalidScreenname.contains(value.toLowerCase());
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);

        iphoneChecksum = json.getInt("iphoneChecksum") == 1 ? true : false;
        androidChecksum = json.getInt("androidChecksum") == 1 ? true : false;
        checkValidMobile = json.getInt("checkValidMobile") == 1 ? true : false;

        isBlock = json.getInt("isBlock");
        accPerDay = json.getInt("per_day");
        timePerAccount = json.getInt("time_per_account");

        aCp.clear();
        JSONArray arr = json.getJSONArray("cp");
        for (int i = 0; i < arr.size(); i++) {
            aCp.add(arr.getString(i));
        }

        minPasswordLength = json.getInt("min_password_length");

        invalidScreenname.clear();
        invalidUsername.clear();
        arr = json.getJSONArray("invalid_username");
        for (int i = 0; i < arr.size(); i++) {
            invalidUsername.add(arr.getString(i));
        }
        arr = json.getJSONArray("invalid_screenname");
        for (int i = 0; i < arr.size(); i++) {
            invalidScreenname.add(arr.getString(i));
        }
    }
}
