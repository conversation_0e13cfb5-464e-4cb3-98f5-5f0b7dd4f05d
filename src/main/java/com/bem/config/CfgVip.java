package com.bem.config;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

public class CfgVip {
    public static JSONArray json;
    public static List<DataConfig> config;

    public static int getVip(long gemnap) {
//        int oldVip = user.getVip();
//        int newVip = user.getVip();
        int vip =0;
        for (int i = 1; i < CfgVip.config.size(); i++) {
            if (config.get(i).gem > gemnap) {
                vip = config.get(i - 1).vip;
                break;
            }
        }
        if (config.get(config.size() - 1).gem <= gemnap) {
            vip = config.get(config.size() - 1).vip;
        }
        return vip;
    }

    public static int maxFiend(int vip) {
        return config.get(vip).numMaxFriends;
    }

    public static void loadConfig(String strJson) {
        json = JSONArray.fromObject(strJson);
        config = (ArrayList<DataConfig>) new Gson().fromJson(strJson,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());
    }

    public class DataConfig {
        public int vip, gem, type, numberBuyAtomic, numBuyGold, numBuyEnergy, bonusEnergyMax, numMaxFriends, resetMapAdventure;
        public int numJoinMapGold, numJoinMapMaterial, numRevivalBoss;
        public List<Integer> gift;
    }

}