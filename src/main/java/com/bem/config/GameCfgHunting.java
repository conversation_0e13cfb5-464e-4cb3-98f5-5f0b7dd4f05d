package com.bem.config;

import com.google.gson.Gson;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Random;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgHunting {
    public static JSONObject json;
    public static DataConfig config;

    public static boolean inEvent() {
        if (config.test == 0) return Calendar.getInstance().get(Calendar.HOUR_OF_DAY) == config.getEventIndex();
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY), min = Calendar.getInstance().get(Calendar.MINUTE);
        return (hour * 60 + min) / config.test == config.getEventIndex();
    }

    public static int getRandomMap() {
        return config.maps.get(new Random().nextInt(config.maps.size())).id;
    }

    public static int[] getSupportItem(int level) {
        if (level > 1) {
            int[] items = new int[config.numberItem];
            for (int i = 0; i < config.numberItem; i++) {
                items[i] = config.dropItem[new Random().nextInt(config.dropItem.length)];
            }
            return items;
        }
        int[] items = new int[config.dropItem.length * 2];
        for (int i = 0; i < config.dropItem.length; i++) {
            items[i * 2] = config.dropItem[i];
            items[i * 2 + 1] = config.dropItem[i];
        }
        return items;
    }

    public static Map getMap() {
        return config.maps.get(Calendar.getInstance().get(Calendar.DATE) % config.maps.size());
    }

    public static int getHp(int index, int level) {
        return config.powers[index] * level;
    }

    public static int getAtk(int index, int level) {
        return config.powers[index] * level / 2;
    }

    public static int getGold(int index) {
        return config.powers[index] * 10;
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        String strTime;
        int time;
        public int coolDown, freeAtk, feeRevive, numberItem, enable, requireLevel, test, feeSkip;
        public List<Map> maps;
        public int[] dropItem, levels, powers;

        public int getEventIndex() {
            if (test == 0) return time;
            Calendar ca = Calendar.getInstance();
            int min = ca.get(Calendar.HOUR_OF_DAY * 60) + ca.get(Calendar.MINUTE);
            int index = min / test;
            return index % 2 == 0 ? index : index + 1;
        }

        public String getStrTime() {
            if (test == 0) return strTime;
            int eventIndex = getEventIndex(), min = eventIndex * test, nextMin = eventIndex * test + test;
            return String.format("%sh%s - %sh%s", min / 60, min % 60, nextMin / 60, nextMin % 60);
        }
    }

    public class Map {
        public int id, index;
        public int[] monster, boss;

        public List<Integer> getMonsterId(int level) {
            List<Integer> aMonster = new ArrayList<>();
            if (level % 5 == 0) aMonster.add(boss[0]);
            for (int i = 0; i < 5; i++) {
                aMonster.add(monster[new Random().nextInt(monster.length)]);
            }
            return aMonster;
        }

        public List<Integer> getMonsterId() {
            List<Integer> aMonster = new ArrayList<>();
            for (int i = 0; i < boss.length; i++) {
                aMonster.add(boss[i]);
            }
            for (int i = 0; i < monster.length; i++) {
                aMonster.add(monster[i]);
            }
            return aMonster;
        }
    }
}
