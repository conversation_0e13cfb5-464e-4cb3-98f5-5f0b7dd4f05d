package com.bem.config;

import com.bem.object.EventStatus;
import com.google.gson.Gson;
import com.handler.game.Arena;
import lombok.Data;
import net.sf.json.JSONObject;

import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgArena {
    public static JSONObject json;
    public static DataConfig config;


    public static long getTimeout(int eventIndex) {
        if (config.test == 0) {
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.HOUR_OF_DAY, config.time.get(eventIndex) + 1);
            ca.set(Calendar.MINUTE, 0);
            ca.set(Calendar.SECOND, 0);
            long timeout = ca.getTimeInMillis() - System.currentTimeMillis();
            if (timeout < 0) timeout = 0;
            return timeout / 1000;
        }
        Calendar calendar = Calendar.getInstance();
        int curMin = calendar.get(Calendar.MINUTE);
        int curSecond = calendar.get(Calendar.SECOND);
        return (5 - curMin % 5) * 60 + 60 - curSecond;
    }


    public static String getNextEvent() {
        long deltatime = 0;
        Calendar calendar = Calendar.getInstance();
        int curDay = calendar.get(Calendar.DAY_OF_WEEK);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);
        int timeInthusday = 11 * 60 * 60 + 55 * 60;
        int timeInday = (23 - hour) * 60 * 60 + (59 - minute) * 60 + 60 - second;

        if (curDay > config.day) {
            deltatime = (Calendar.SATURDAY - (curDay + 1) + config.day) * 24 * 60 * 60 + timeInday + timeInthusday;
        } else if (curDay < config.day) {
            deltatime = (config.day - (curDay + 1)) * 24 * 60 * 60 + timeInday + timeInthusday;
        } else {
            if(hour<12){
                deltatime = (11 -hour)*60 * 60 + (54 - minute) * 60 + 60 - second;
            }else{
                deltatime = (Calendar.SATURDAY - (curDay + 1) + config.day) * 24 * 60 * 60 + timeInday + timeInthusday;
            }
        }
        return String.format(deltatime+"");
    }

    public static int getEventIndex() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (config.test == 0) {
            return calendar.get(Calendar.DAY_OF_YEAR) * 10000 + hour *100 + config.time.get(0);
        }
//        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        return calendar.get(Calendar.DAY_OF_YEAR) * 10000 + hour * 100 + (minute / 10 + 1) % 3;
    }
//    public static int[] getLastEndEvent() {
//        if (config.test == 0) {
//            int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
//            for (int i = config.time.size() - 1; i >= 0; i--) {
//                if (hour >= config.time.get(i) + 1) {
//                    return new int[]{i, (config.time.get(i) + 1) * 3600};
//                }
//            }
//            return new int[]{0, (config.time.get(0) + 1) * 3600};
//        }
//        Calendar calendar = Calendar.getInstance();
//        int hour = calendar.get(Calendar.HOUR_OF_DAY);
//        int minute = calendar.get(Calendar.MINUTE);
//        if (minute % 10 < 5) return new int[]{-1, -1};
//        return new int[]{0, hour * 3600 + ((minute / 10) * 10 + 5) * 60};
//    }
    public static EventStatus getEventStatus() {
        Calendar calendar = Calendar.getInstance();
        int index = config.time.indexOf(calendar.get(Calendar.HOUR_OF_DAY));
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
//        if (calendar.get(Calendar.DAY_OF_WEEK) != Calendar.THURSDAY && index == 1) index = -1;
        if (calendar.get(Calendar.DAY_OF_WEEK) != config.day ) index = -1;
        if (index == -1) return new EventStatus();
        if(index>-1){
            index=0;
        }
        return new EventStatus(calendar.get(Calendar.DAY_OF_YEAR) * 10000 + hour *100 + config.time.get(index),index);
//        return new EventStatus(calendar.get(Calendar.DAY_OF_YEAR) * 100 + config.time.get(index), index);
    }


    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);

//        Arena.getInstance().resetRound();
//        System.out.println("aaaaa-->"+config.bonus.get(0).getWin().toString());
    }

    @Data
    public class DataConfig {
        public String[] strTime;
        public List<Integer> time;

        public int[] dropItem;
        public List<List<Integer>> topBonus;
        public int enable, requireLevel,requirePoint, test, timeLoop,timeBang,numberPlayerKnockout,totalTime,waittimeToKnockout,gamePlayNomal,gamePlayKnockout,day;
        public String startTime,desc;
        List<BonusArena> bonus;

    }
    @Data
    public class BonusArena {
        List<Long> win;
        List<Long> lose;


    }
}
