package com.bem.config;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

public class CfgOnlinegift {
    public static List<DataConfig> data;

    public static void loadConfig(String value) {
        data = (ArrayList<DataConfig>) new Gson().fromJson(value,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());
        if(data.size()>1) {
            data.get(0).time=data.get(0).time*60;
            for (int i = 1; i < data.size(); i++) {
                data.get(i).time = data.get(i).time*60+data.get(i-1).time;
            }
        }
    }

    public class DataConfig {
        public String award;
        public int time;
    }
}