/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import com.bem.boom.CacheBattle;
import com.bem.util.CommonProto;
import com.google.gson.Gson;
import com.k2tek.Config;
import com.k2tek.Constans;
import com.proto.GGProto;
import net.sf.json.JSONObject;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgMissionFB {
    public static int MISSION_STATUS_CLOSE = 0;
    public static int MISSION_STATUS_OPEN = 1;
    public static int MISSION_STATUS_WAIT_RECEIVE = 2;
    public static int MISSION_STATUS_RECEIVED = 3;

    public static JSONObject json;
    public static DataConfig config;

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
       public List<MissionFB> invite;
    }

    public class MissionFB {
        public String title;
        public int number;
        public List<Long> bonus;
    }

}
