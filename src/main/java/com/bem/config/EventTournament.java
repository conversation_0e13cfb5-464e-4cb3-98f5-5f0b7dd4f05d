package com.bem.config;

import java.util.ArrayList;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.bem.object.Menu;

@SuppressWarnings("deprecation")
public class EventTournament {

	public static String ROOM_ID = "";
	public static List<Menu> MENU_GAME;
	public static int feeMax, feeMin, feeNumberPlayer, feeJoin;

	public static Menu getMenu(String gameCode) {
		for (Menu m : MENU_GAME) {
			if (m.getGameCode().equals(gameCode)) {
				return m;
			}
		}
		return null;
	}

	public static boolean nearHourEvent(String gameCode) {
		Calendar ca = Calendar.getInstance();
		ca.add(Calendar.MINUTE, 5);
		for (int i = 0; i < MENU_GAME.size(); i++) {
			Menu menu = MENU_GAME.get(i);
			if (menu.getGameCode().equals(gameCode)) {
				if (("," + menu.getEventHour() + ",").contains("," + ca.get(Calendar.HOUR_OF_DAY) + ",")) {
					return true;
				}
			}
		}
		return false;
	}

	public static boolean inHourEvent(String gameCode) {
		for (int i = 0; i < MENU_GAME.size(); i++) {
			Menu menu = MENU_GAME.get(i);
			if (menu.getGameCode().equals(gameCode)) {
				Date date = new Date();
				if (("," + menu.getEventHour() + ",").contains("," + date.getHours() + ",")) {
					return true;
				}
			}
		}
		return false;
	}

	public static void loadConfig(String strJson) {
		JSONObject json = JSONObject.fromObject(strJson);

		ROOM_ID = json.getString("roomId");

		List<Menu> m = new ArrayList<Menu>();
		JSONArray arr = json.getJSONArray("menu");
		for (int i = 0; i < arr.size(); i++) {
			JSONObject obj = arr.getJSONObject(i);
			Menu menu = new Menu(obj.getInt("id"), obj.getInt("koin"), obj.getString("name"));
			menu.setAreaCode(obj.getString("areaCode"));
			menu.setGameCode(obj.getString("gameCode"));
			menu.setEventHour(obj.getString("hours"));
			m.add(menu);
		}

		MENU_GAME = m;

		JSONObject fee = json.getJSONObject("fee");
		feeMax = fee.getInt("max");
		feeMin = fee.getInt("min");
		feeJoin = fee.getInt("join");
		feeNumberPlayer = fee.getInt("numberPlayer");
	}

}
