/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import java.text.SimpleDateFormat;
import java.util.*;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 
 * <AUTHOR>
 */
@SuppressWarnings("deprecation")
public class EventZombie {

	public static Date fromDate, toDate;
	public static String hour;
	public static long ROUND_TIME = 1000 * 30;
	public static long TIME_BETWEEN_ROUND = 1000 * 10;
	public static int koin, minUser;
	public static List<String> gameArea = new ArrayList<String>();

	public static boolean isGameArea(String game) {
		for (int i = 0; i < gameArea.size(); i++) {
			if (game.contains(gameArea.get(i))) {
				return true;
			}
		}
		return false;
	}

	public static boolean inHourEvent() {
		Date date = new Date();
		if (("," + hour + ",").contains("," + date.getHours() + ",")) {
			return true;
		}
		return false;
	}

	public static boolean nearHourEvent() {
		Calendar ca = Calendar.getInstance();
		ca.add(Calendar.MINUTE, 2);
		if (("," + hour + ",").contains("," + ca.get(Calendar.HOUR_OF_DAY) + ",")) {
			return true;
		}
		return false;
	}

	public static boolean inDateEvent() {
		Date date = new Date();
		if (date.after(fromDate) && date.before(toDate)) {
			return true;
		}
		return false;
	}

	public static void loadConfig(String strJson) {
		JSONObject json = JSONObject.fromObject(strJson);
		SimpleDateFormat formater = new SimpleDateFormat("dd/MM/yyyy");
		try {
			EventZombie.fromDate = formater.parse(json.getString("fromDate"));
			EventZombie.toDate = formater.parse(json.getString("toDate"));
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		EventZombie.ROUND_TIME = json.getLong("round_time");
		EventZombie.koin = json.getInt("koin");
		EventZombie.hour = json.getString("hours");
		EventZombie.minUser = json.getInt("min_user");
		EventZombie.gameArea.clear();
		JSONArray arrArea = json.getJSONArray("area");
		for (Object object : arrArea) {
			EventZombie.gameArea.add((String) object);
		}
	}
}
