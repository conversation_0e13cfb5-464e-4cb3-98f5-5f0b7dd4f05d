package com.bem.config;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by vieth_000 on 11/14/2016.
 */
public class CfgDropItem {

    static List<DataConfig> aConfig;

    public static List<Integer> getSuddenDeathItem(int mode) {
        if (mode != 1 && mode != 2) mode = 1;
        DataConfig config = aConfig.get(mode - 1);
        List<Integer> basicItem = config.aItem.get(0);
        List<Integer> specItem = config.aItem.get(1);
        List<Integer> usedItem = config.aItem.get(3);
        Random ran = new Random();
        List<Integer> aItem = new ArrayList<Integer>();
        for (int i = 0; i < 4; i++) {
            aItem.add(basicItem.get(ran.nextInt(basicItem.size())));
        }
        for (int i = 0; i < 3; i++) {
            aItem.add(specItem.get(ran.nextInt(specItem.size())));
        }
        for (int i = 0; i < 2; i++) {
            aItem.add(usedItem.get(ran.nextInt(usedItem.size())));
        }
        return aItem;
    }

    public static int getItemId(int mode) {
        if (mode != 1 && mode != 2) mode = 1;
        DataConfig config = aConfig.get(mode - 1);
        Random r = new Random();
        int index = -1;
        int number = r.nextInt(100);
        for (int i = 0; i < config.percent.size(); i++) {
            if (number < config.percent.get(i)) {
                index = i;
                break;
            }
        }
        if (index == -1) return 0;
        return config.aItem.get(index).get(r.nextInt(config.aItem.get(index).size()));
    }

    public static void loadConfig(String strJson) {
        aConfig = new Gson().fromJson(strJson,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());
        for (DataConfig config : aConfig) {
            for (int i = 1; i < config.percent.size(); i++) {
                config.percent.set(config.mode, config.percent.get(i) + config.percent.get(i - 1));
            }
        }
    }

    public class DataConfig {
        int mode;
        List<List<Integer>> aItem = new ArrayList<List<Integer>>();
        List<Integer> percent = new ArrayList<Integer>();
    }
}
