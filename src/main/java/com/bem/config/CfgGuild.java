package com.bem.config;

import net.sf.json.JSONObject;

public class CfgGuild {
	public static int feeCreateGuild = 1000000;
	public static int maxMember = 200;
	public static int expWin = 2;
	public static int expLoose = 1;

	public static void loadConfig(String strJson) {
		JSONObject json = JSONObject.fromObject(strJson);
		feeCreateGuild = json.getInt("feeCreateGuild");
		maxMember = json.getInt("maxMember");
		expWin = json.getInt("expWin");
		expLoose = json.getInt("expLoose");
	}
}
