package com.bem.config;

import com.bem.object.TaskSchedule;
import com.bem.util.Util;
import com.k2tek.Xerver;
import com.k2tek.common.slib_Logger;
import org.slf4j.Logger;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

public class ImageConfig extends TaskSchedule {

    public ImageConfig(long jobInterval) {
        super(jobInterval);
        preLoadAvatar = new ArrayList<String>();
    }

    public static Map<String, byte[]> mImage = new HashMap<String, byte[]>();
    public static List<String> aImage = new ArrayList<String>();
    public static String ROOT_PATH = "../image";
    public final static int MAX_CACHE_IMAGE = 3000;
    public final static int MAX_IMAGE_SIZE = 280, MAX_THUMB_SIZE = 45;
    public final static int MAX_PIXEL = 60000;
    public static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    long SLEEP_TIME = 15 * 1000;
    List<String> preLoadAvatar;

    @Override
    protected void doJob() {
        try {
            while (preLoadAvatar.size() > 0) {
                String url = preLoadAvatar.get(0);
                if (!mImage.containsKey(url)) {
                    byte[] imgData = null;
                    if (url.startsWith("http")) {
                        String tmp = url;
                        if (tmp.contains("facebook")) {
                            tmp = tmp.substring(0, tmp.lastIndexOf("/")) + "/picture";
                        }
                        imgData = loadImageFromHttp(tmp);
                    } else {
                        imgData = loadImageFromDisk(url);
                    }
                    addCacheImage(url, imgData);
                }
                preLoadAvatar.remove(0);
            }
            Thread.yield();
            Thread.sleep(100);
        } catch (Exception ex) {
            try {
                if (preLoadAvatar != null) {
                    getLogger().info("preLoadAvatar.size()=" + preLoadAvatar.size());
                    if (preLoadAvatar.get(0) == null) {
                        preLoadAvatar.remove(0);
                    }
                } else {
                    preLoadAvatar = new ArrayList<String>();
                }
            } catch (Exception ex1) {
                getLogger().info("AvatarConfig Exception 1 >" + Util.exToString(ex1));
            }
            getLogger().info("AvatarConfig Exception 2 >" + Util.exToString(ex));
            sleepTime(3000);
        }
    }

    public void addPreloadImage(String image) {
        if (image != null && image.length() > 0) {
            //preLoadAvatar.add(image);
        }
    }

    public static synchronized void addCacheImage(String url, byte[] data) {
        if (data != null) {
            mImage.put(url, data);
            aImage.add(url);
            if (aImage.size() > MAX_CACHE_IMAGE) {
                mImage.remove(aImage.get(0));
                aImage.remove(0);
            }
        }
    }

    private void sleepTime(long time) {
        try {
            Thread.sleep(time);
        } catch (Exception ex) {
            getLogger().info("AvatarConfig " + Util.exToString(ex));
        }
    }

    public static float getScale(int _w, int _h) {
        int edge = _w > _h ? _w : _h;
        if (edge > MAX_IMAGE_SIZE) {
            return (float) MAX_IMAGE_SIZE / edge;
        }
        // int pixel = _w * _h;
        // if (pixel > MAX_PIXEL) {
        // return (float) MAX_PIXEL / pixel;
        // }
        return 0;
    }

    public static float getThumbScale(int _w, int _h) {
        int edge = _w > _h ? _w : _h;
        if (edge > MAX_THUMB_SIZE) {
            return (float) MAX_THUMB_SIZE / edge;
        }
        return 0;
    }

    static String getPreFolder(String username) {
        if (username.startsWith("khach") || username.startsWith("fb")) {
            return username.substring(username.length() - 1);
        }
        return username.substring(0, 1);
    }

    public static String uploadImage(byte[] data, String filename) {
        String preFolder = getPreFolder(filename);
        try {
            File f = new File(ROOT_PATH + "/upload/" + preFolder + "/");
            if (!f.exists()) {
                f.mkdirs();
            }
            ByteArrayInputStream bis = new ByteArrayInputStream(data);
            BufferedImage _bImg = ImageIO.read(bis);
            int type = _bImg.getType() == 0 ? BufferedImage.TYPE_INT_ARGB : _bImg.getType();
            int _w = _bImg.getWidth();
            int _h = _bImg.getHeight();

            // save the original
            ImageIO.write(_bImg, "jpg", new File(ROOT_PATH + "/upload/" + preFolder + "/" + filename + ".origin"));

            float scale = getScale(_w, _h);
            // debug(_w + " " + _h + " " + scale);
            if (scale > 0) {
                _bImg = resizeImage(_bImg, type, (int) (_w * scale), (int) (_h * scale));
            }

            // Save the scale max 280
            ImageIO.write(_bImg, "jpg", new File(ROOT_PATH + "/upload/" + preFolder + "/" + filename));
            _w = _bImg.getWidth();
            _h = _bImg.getHeight();
            // float thumbScale = getThumbScale(_w, _h);
            // // debug(_w + " " + _h + " " + thumbScale);
            // if (thumbScale > 0) {
            // _bImg = resizeImage(_bImg, type, (int) (_w * thumbScale), (int) (_h * thumbScale));
            // }

            _bImg = resizeImage(_bImg, type, 100, 100);

            // Save the thumb size
            ImageIO.write(_bImg, "jpg", new File(ROOT_PATH + "/upload/" + preFolder + "/" + filename + ".thumb"));

            // // _bImg.getWidth()
            // // _bImg.getHeight();
            // FileOutputStream fos = new FileOutputStream(ROOT_PATH + "/upload/" + filename);
            // fos.write(data);
            // fos.close();
            return "upload_" + preFolder + "_" + filename;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private static BufferedImage resizeImage(BufferedImage originalImage, int type, int width, int height) {
        BufferedImage resizedImage = new BufferedImage(width, height, type);
        Graphics2D g = resizedImage.createGraphics();
        g.drawImage(originalImage, 0, 0, width, height, null);
        g.dispose();

        return resizedImage;
    }

    private static BufferedImage resizeImageWithHint(BufferedImage originalImage, int type, int width, int height) {

        BufferedImage resizedImage = new BufferedImage(width, height, type);
        Graphics2D g = resizedImage.createGraphics();
        g.drawImage(originalImage, 0, 0, width, height, null);
        g.dispose();
        g.setComposite(AlphaComposite.Src);

        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        return resizedImage;
    }

    public static byte[] loadImage(String imageId) {
        byte[] imgData = mImage.get(imageId);
        if (imgData == null) {
            if (imageId.startsWith("http")) {
                Xerver.moImage.addPreloadImage(imageId);
            } else if (imageId.contains("_")) {
                imgData = loadImageFromDisk(imageId);
            }
            addCacheImage(imageId, imgData);
        }
        return imgData;
    }

    public static byte[] loadImageFromHttp(String imageUrl) {
        try {
            URL u = new URL(imageUrl);

            HttpURLConnection conn = (HttpURLConnection) u.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(10000);
            conn.connect();

            // Get the size of the file
            long length = conn.getContentLengthLong();

            InputStream is = conn.getInputStream();

            // You cannot create an array using a long type.
            // It needs to be an int type.
            // Before converting to an int type, check
            // to ensure that file is not larger than Integer.MAX_VALUE.

            // Create the byte array to hold the data
            byte[] bytes = new byte[(int) length];

            // Read in the bytes
            int offset = 0;
            int numRead = 0;
            while (offset < bytes.length && (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {
                offset += numRead;
            }

            // Ensure all the bytes have been read in
            if (offset < bytes.length) {
                throw new Exception("Could not completely read file " + imageUrl);
            }

            // Close the input stream and return bytes

            is.close();
            return bytes;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static byte[] loadImageFromDisk(String imageId) {
        String[] str = imageId.split("_");
        String filePath = "";
        for (int i = 0; i < str.length; i++) {
            filePath += "/" + str[i];
        }
        // filePath = filePath + ".png";
        try {
            File file = new File(ROOT_PATH + filePath);

            InputStream is = new FileInputStream(file);

            // Get the size of the file
            long length = file.length();

            // You cannot create an array using a long type.
            // It needs to be an int type.
            // Before converting to an int type, check
            // to ensure that file is not larger than Integer.MAX_VALUE.

            // Create the byte array to hold the data
            byte[] bytes = new byte[(int) length];

            // Read in the bytes
            int offset = 0;
            int numRead = 0;
            while (offset < bytes.length && (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {
                offset += numRead;
            }

            // Ensure all the bytes have been read in
            if (offset < bytes.length) {
                throw new Exception("Could not completely read file " + file.getName());
            }

            // Close the input stream and return bytes

            is.close();
            return bytes;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public Logger getLogger() {
        return slib_Logger.root();
    }

}
