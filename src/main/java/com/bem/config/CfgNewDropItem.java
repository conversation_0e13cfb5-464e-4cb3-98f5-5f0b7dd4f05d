package com.bem.config;

import com.bem.monitor.Bonus;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.k2tek.common.Logs;
import grep.helper.DateTime;
import net.sf.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by vieth_000 on 11/14/2016.
 */
public class CfgNewDropItem {

    public static JSONObject json;
    public static DataConfig config;

    public static Map<Integer, DropItem> mDropItem = new HashMap<>();

    public static List<Long> getDropItem(int mode) {
        List<Long> aLong = new ArrayList<>();
        for (EventItem event : config.events) {
            if (event.dateFrom.before(new Date()) && event.dateTo.after(new Date()) && event.modes.contains(mode)) {
                aLong.addAll(event.getDropItem());
            }
        }
        return aLong;
    }

    public static List<Integer> getSuddenDeathItem(int mode) {
        if (mode != 1 && mode != 2) mode = 1;
        DropItem config = mDropItem.get(mode);
        List<Integer> basicItem = config.aItem.get(0);
        List<Integer> specItem = config.aItem.get(1);
        List<Integer> usedItem = config.aItem.get(3);
        Random ran = new Random();
        List<Integer> aItem = new ArrayList<Integer>();
        for (int i = 0; i < 4; i++) {
            aItem.add(basicItem.get(ran.nextInt(basicItem.size())));
        }
        for (int i = 0; i < 3; i++) {
            aItem.add(specItem.get(ran.nextInt(specItem.size())));
        }
        for (int i = 0; i < 2; i++) {
            aItem.add(usedItem.get(ran.nextInt(usedItem.size())));
        }
        return aItem;
    }

    public static int getItemId(int mode) {
        if (mode != 1 && mode != 2) mode = 1;
        DropItem config = mDropItem.get(mode);
        Random r = new Random();
        int index = -1;
        int number = r.nextInt(100);
        for (int i = 0; i < config.percent.size(); i++) {
            if (number < config.percent.get(i)) {
                index = i;
                break;
            }
        }
        if (index == -1) return 0;
        return config.aItem.get(index).get(r.nextInt(config.aItem.get(index).size()));
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);

        mDropItem.clear();
        config.dropItems.forEach(value -> {
            value.init();
            mDropItem.put(value.mode, value);
        });

        config.events.forEach(event -> event.init());
    }

    public class DataConfig {
        public List<DropItem> dropItems;
        public List<EventItem> events;
    }

    public class EventItem {
        public List<Integer> modes, percents, items;
        public String from, to;
        public Date dateFrom, dateTo;

        public void init() {
            for (int i = 1; i < percents.size(); i++) {
                percents.set(i, percents.get(i) + percents.get(i - 1));
            }
            try {
                dateFrom = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(from);
                dateTo = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(to);
            } catch (Exception ex) {
                Logs.error(Util.exToString(ex));
            }
        }

        public List<Long> getDropItem() {
            int index = -1;
            int number = new Random().nextInt(100);
            for (int i = 0; i < percents.size(); i++) {
                if (number < percents.get(i)) {
                    index = i;
                    break;
                }
            }
            if (index > -1) return Bonus.defaultLongBonusView(Bonus.ITEM, items.get(index), 1);
            return new ArrayList<>();
        }
    }

    public class DropItem {
        int mode;
        List<List<Integer>> aItem = new ArrayList<List<Integer>>();
        List<Integer> percent = new ArrayList<Integer>();

        public void init() {
            for (int i = 1; i < percent.size(); i++) {
                percent.set(i, percent.get(i) + percent.get(i - 1));
            }
        }
    }
}
