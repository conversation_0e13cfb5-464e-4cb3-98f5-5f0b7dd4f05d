package com.bem.config;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by vieth_000 on 11/14/2016.
 */
public class CfgTienhoapet {

    public static DataConfig aConfig;


    public static void loadConfig(String strJson) {
        aConfig = new Gson().fromJson(strJson,
                new TypeToken<DataConfig>() {
                }.getType());
    }

    public class DataConfig {
        public int enable, maxUp;
        public List<Integer> starRequire = new ArrayList<Integer>();
        public List<Integer> megaRequire = new ArrayList<Integer>();
        public List<Integer> effectPerCent = new ArrayList<Integer>();
    }
}
