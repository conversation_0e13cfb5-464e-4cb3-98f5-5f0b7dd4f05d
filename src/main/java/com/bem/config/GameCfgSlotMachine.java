package com.bem.config;

import com.bem.boom.object.SystemSlide;
import com.bem.config.lang.Lang;
import com.bem.dao.SystemDAO;
import com.bem.dao.mapping.UserEntity;
import com.bem.monitor.Bonus;
import com.bem.dao.mapping.Jackpot;
import com.bem.object.UserInfo;
import com.bem.util.Actions;
import com.bem.util.Util;
import com.handler.AHandler;
import com.k2tek.common.slib_Logger;
import grep.database.HibernateUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgSlotMachine {
    public static JSONObject json;

    public static int HUYENTHOAI = 1, VANG = 2, CUP = 3, LOA = 4,
            BAR3 = 5, BAR2 = 6, BAR1 = 7,
            DUAHAU = 8, CATIM = 9, CHERRY = 10;
    static List<Integer> noCherry = Arrays.asList(HUYENTHOAI, VANG, CUP, LOA, BAR3, BAR2, BAR1, DUAHAU, CATIM);
    static List<Integer> noCherryBAR = Arrays.asList(HUYENTHOAI, VANG, CUP, LOA, DUAHAU, CATIM);
    static List<Integer> aBar = Arrays.asList(BAR1, BAR2, BAR3);
    public static double feeJackPot = 0.05f;
    public static double feePayout = 0.8f;
    public static int levelRequire = 10;
    public static boolean enable = true;

    public static List<Integer> aBet, aBonus, aRate;
    public static Jackpot jackpot;
    public static int minJackpot = 1000, minReceive = 6000;

    public static synchronized List<Long> get(AHandler handler, UserInfo uInfo, int betMoney) {
        UserEntity user = uInfo.getDbUser();
        Random r = new Random();
        int value = isGetJackpot(user.getVip());
        int index = 0;
//        System.out.println("aRate--->"+aRate.toString());
//        System.out.println("aBonus--->"+aBonus.toString());

//        if (value == -1) {
            index = aRate.get(r.nextInt(aRate.size()));
            value = aRate.size();
            index = index == 0 && jackpot.getPot() < jackpot.getMinReceive() ? index + 6 : index;
//        } else if (value > 0) {
//            index = aRate.get(r.nextInt(aRate.size()));
//            index = index == 0 ? index + 6 : index;
//        }
//        index = new Random().nextInt(14);
//        index =11;
        long bonus = 0;
        int bonusRate = aBonus.get(index);
//        System.out.println("jackpot.getPayout()------->"+jackpot.getPayout());
        JSONArray history = JSONArray.fromObject(jackpot.getHistory());
        if (index == 0) {

            bonus = jackpot.getPot();
            JSONObject tmp = new JSONObject();
            tmp.put("userId",user.getId());
            tmp.put("gem", bonus);
            tmp.put("name", user.getName());
            tmp.put("time", Util.formatDateSql(new Date()));

            boolean isAdd = false;
            for (int i = 0; i < history.size(); i++) {
                JSONObject obj = history.getJSONObject(i);
                if (bonus > obj.getInt("gem")) {
                    history.add(i, tmp);
                    isAdd = true;
                    break;
                }
            }
            if (!isAdd && history.size() < 10) {
                history.add(tmp);
            }
            if (history.size() > 20) {
                history.remove(history.size() - 1);
            }
        } else if (bonusRate > 0) {
            while (betMoney * bonusRate > jackpot.getPayout()) {
                index++;
                bonusRate = aBonus.get(index);
            }
            bonus = betMoney * bonusRate;
        }
        long userAddGem = bonus - betMoney;
        long newPot = index == 0 ? minJackpot : jackpot.getPot() + (int) (feeJackPot * betMoney);
        long addPayout = index == 0 ? 0 : (index < 13 ? -bonus : (int) (feePayout * betMoney));
        JSONArray arr = new JSONArray();
        arr.addAll(Arrays.asList(Bonus.GEM,userAddGem));
//        Bonus.receiveListItem(uInfo, arr, "JackPot|bet:"+betMoney+"|bonus:"+bonus+"|index:"+index+"|payout:"+jackpot.getPayout()+"|curJackPot:"+jackpot.getPot()+" |newPot-->"+newPot);
        boolean isUpdateOk = updateData(user.getId(), newPot, addPayout, history.toString(), index == 0,user.getServer());
        if (!isUpdateOk) {
            Actions.save(user, "err_update_jp", "err_update_jp: ", Actions.convertToLogString(Arrays.asList("desc", "JackPot|bet:"+betMoney+"|bonus:"+bonus+"|index:"+index+"|payout:"+jackpot.getPayout()+"|curJackPot:"+jackpot.getPot()+" |newPot-->"+newPot)));
            return null;
        }
        Bonus.receiveListItem(uInfo, arr, "JackPot|bet:"+betMoney+"|bonus:"+bonus+"|index:"+index+"|payout:"+jackpot.getPayout()+"|curJackPot:"+jackpot.getPot()+" |newPot-->"+newPot);

        if (index == 0) {
            jackpot.setLastJackpot(new Date());
            setEasyDate();
//            NotifyMonitor.addSlideMsg(CfgMsgTemplate.tplJackpot(user.getScreenname(), String.valueOf(bonus)));
        }
        jackpot.setHistory(history.toString());
        jackpot.setPot(newPot);
        jackpot.addPayout(addPayout);
//        user.addGem((int) userAddGem);
        List<Long> aResult = getResultByIndex(index);
        if (aResult.get(0) == aResult.get(1) && aResult.get(1) == aResult.get(2)) {
            if (bonus <= 0) {
//                slib_Logger.root().warn(user.getId() + " -> " + index + " " + aResult + " " + user.getGold() + " " + userAddGold);
            }
        }
        aResult.add(0, bonus);
        aResult.add(0, (long) user.getGem());
        aResult.add(0, jackpot.getPot());
        aResult.add(0, (long) value);
        try {
            if (index < 10 && index > 0) {
//                SystemSlide.addMessage("<color=\"#00b939\">" + user.getName() + "</color>" + " may mắn trúng thưởng slotMachine gấp " + "<color=\"#00b939\">" + bonusRate + " lần </color>" + " nhận thưởng lên đến" + "<color=\"#00b939\">" + bonus + " Kim cương </color>");
                SystemSlide.addMessage(String.format(uInfo.getLang().get(Lang.jackpot1_wish_from_system), user.getName(), bonusRate, bonus));
            } else if (index == 0) {
//                SystemSlide.addMessage("<color=\"#00b939\">" + user.getName() + "</color>" + " may mắn trúng thưởng JackPot slotMachine nhận thưởng lên đến" + "<color=\"#00b939\">" + bonus + " Kim cương </color>");
                SystemSlide.addMessage(String.format(uInfo.getLang().get(Lang.jackpot2_wish_from_system), user.getName(), bonus));
            }
        }catch(Exception ex){

        }
        return aResult;
    }

    static int isGetJackpot(int vip) {
        if (vip >= 5) {
            Random r = new Random();
            Date date = new Date();
            if (jackpot.getPot() >= jackpot.getMinReceive() && date.after(jackpot.getEasyDate())) {
                int size = aRate.size();
                size = size - (int) ((date.getTime() - jackpot.getEasyDate().getTime()) / 60000);
                size = size < 20 ? 20 : size;
                int index = r.nextInt(size);
                if (index == 0) {
                    return 0;
                }
                return size;
            }
        }
        return -1;
    }

    static boolean updateData(long userId, long addPot, long addPayout, String history, boolean winJackpot,int serverId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
//            System.out.println("addGem--->"+addGem);
//            SQLQuery query = session.createSQLQuery("update user set gem=gem+? where id=?");
//            query.setLong(0, addGem);
//            query.setLong(1, userId);
//            query.executeUpdate();

            SQLQuery query1 = null;
            if (winJackpot) {
                query1 = session.createSQLQuery("update boom.jackpot set pot=?, payout=payout+?, history=?, last_jackpot=? where server_id=?");
            } else {
                query1 = session.createSQLQuery("update boom.jackpot set pot=?, payout=payout+?, history=? where server_id=?");
            }
            query1.setLong(0, addPot);
            query1.setLong(1, addPayout);
            query1.setString(2, history);
            if (winJackpot) {
                query1.setTimestamp(3, new Date());
                query1.setInteger(4, serverId);
            } else {
                query1.setInteger(3, serverId);
            }
            query1.executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
//            ex.printStackTrace();
            slib_Logger.root().error(Util.exToString(ex));
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
        return false;
    }

    public static List<Long> getResultByIndex(int index) {
        Random r = new Random();
        List<Integer> aRet = new ArrayList<Integer>();
        switch (index) {
            case 0:
                aRet = Arrays.asList(HUYENTHOAI, HUYENTHOAI, HUYENTHOAI);
                break;
            case 1:
                aRet = Arrays.asList(VANG, VANG, VANG);
                break;
            case 2:
                aRet = Arrays.asList(CUP, CUP, CUP);
                break;
            case 3:
                aRet = Arrays.asList(LOA, LOA, LOA);
                break;
            case 4:
                aRet = Arrays.asList(BAR3, BAR3, BAR3);
                break;
            case 5:
                aRet = Arrays.asList(BAR2, BAR2, BAR2);
                break;
            case 6:
                aRet = Arrays.asList(BAR1, BAR1, BAR1);
                break;
            case 7:
                aRet = Arrays.asList(DUAHAU, DUAHAU, DUAHAU);
                break;
            case 8:
                aRet = Arrays.asList(CATIM, CATIM, CATIM);
                break;
            case 9:
                aRet = Arrays.asList(CHERRY, CHERRY, CHERRY);
                break;
            case 10:
                aRet = Arrays.asList(CHERRY, CHERRY, r.nextInt(9) + 1);
                Collections.shuffle(aRet);
                break;
            case 11:
                aRet = Arrays.asList(CHERRY, CHERRY, r.nextInt(9) + 1);
                Collections.shuffle(aRet);
                break;

//                aRet.add(r.nextInt(3) + 5);
//                aRet.add(r.nextInt(3) + 5);
//                for (int i = 5; i <= 7; i++) {
//                    if (i != aRet.get(0)) {
//                        aRet.add(i);
//                        break;
//                    }
//                }
//                Collections.shuffle(aRet);
//                break;

            case 12:
                aRet.add(CHERRY);
                aRet.add(noCherry.get(r.nextInt(noCherry.size())));
                aRet.add(noCherry.get(r.nextInt(noCherry.size())));
                Collections.shuffle(aRet);
                break;
            case 13:
                aRet.add(noCherry.get(r.nextInt(noCherry.size())));
                aRet.add(noCherry.get(r.nextInt(noCherry.size())));
                if (aRet.get(0) == aRet.get(1)) {
                    int ret = aRet.get(0);
                    while (ret == aRet.get(0)) {
                        ret = aBar.contains(aRet.get(0)) && aBar.contains(aRet.get(1)) ? noCherryBAR.get(r.nextInt(noCherryBAR.size())) : noCherry.get(r.nextInt(noCherry.size()));
                    }
                    aRet.add(ret);
                } else {
                    if (aBar.contains(aRet.get(0)) && aBar.contains(aRet.get(1))) {
                        aRet.add(noCherryBAR.get(r.nextInt(noCherryBAR.size())));
                    } else {
                        aRet.add(noCherry.get(r.nextInt(noCherry.size())));
                    }
                }
                Collections.shuffle(aRet);
                break;
        }
        List<Long> tmp = new ArrayList<Long>();
        for (int i = 0; i < aRet.size(); i++) {
            tmp.add((long) aRet.get(i));
        }
        return tmp;
    }

    public static void main(String[] rags) {
        Random r = new Random();
        int count = 0;
        while (true) {
            count++;
            List<Integer> aRet = new ArrayList<Integer>();
            aRet.add(noCherry.get(r.nextInt(noCherry.size())));
            aRet.add(noCherry.get(r.nextInt(noCherry.size())));
            if (aRet.get(0) == aRet.get(1)) {
                int ret = aRet.get(0);
                while (ret == aRet.get(0)) {
                    ret = aBar.contains(aRet.get(0)) && aBar.contains(aRet.get(1)) ? noCherryBAR.get(r.nextInt(noCherryBAR.size())) : noCherry.get(r.nextInt(noCherry.size()));
                }
                aRet.add(ret);
            } else {
                if (aBar.contains(aRet.get(0)) && aBar.contains(aRet.get(1))) {
                    aRet.add(noCherryBAR.get(r.nextInt(noCherryBAR.size())));
                } else {
                    aRet.add(noCherry.get(r.nextInt(noCherry.size())));
                }
            }
            System.out.println(count + " -> " + aRet);
            if (aRet.get(0) == aRet.get(1) && aRet.get(1) == aRet.get(2)) {
                break;
            }
        }
    }

    public static void loadConfig(String value) {
        JSONObject obj = JSONObject.fromObject(value);
//        System.out.println("load slot machine");
        minJackpot = obj.getInt("min_jackpot");
        minReceive = obj.getInt("min_receive");
        feeJackPot = obj.getDouble("jackpot");
        feePayout = obj.getDouble("payout");
        enable = obj.getBoolean("enable");
        levelRequire = obj.getInt("levelRequire");

        aBet = new ArrayList<Integer>();
        aBet.addAll(obj.getJSONArray("bet_list"));

        aBonus = new ArrayList<Integer>();
        aBonus.addAll(obj.getJSONArray("bonus"));

        aRate = new ArrayList<Integer>();
        JSONArray arr = obj.getJSONArray("rate");
        int index = 0;
        for (int i = 0; i < arr.size(); i++) {
            for (int j = 0; j < arr.getInt(i); j++) {
                aRate.add(index);
            }
            index++;
        }

        jackpot = new SystemDAO().getJackPot();
        setEasyDate();
    }

    public static void setEasyDate() {
        Calendar ca = Calendar.getInstance();
        ca.setTime(jackpot.getLastJackpot());
        ca.add(Calendar.DATE, jackpot.getMaxDay());
        jackpot.setEasyDate(ca.getTime());
    }



}
