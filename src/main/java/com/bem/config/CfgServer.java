/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import com.bem.config.lang.Lang;
import com.bem.util.CommonProto;
import com.google.gson.Gson;
import com.proto.GGProto;
import net.sf.json.JSONObject;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgServer {
    public static int SERVER_ID = 0;
    public static boolean debug = true;
    public static boolean cacheCCU = false;
    //    public static boolean test = false;
    public static JSONObject json;
    public static DataConfig config;
    public static Lang lang;

    public static int getServerIdForBattle() {
        if (SERVER_ID == 125) return 2;
        return isTest() || isSubmit() ? 1 : SERVER_ID;
    }

    public static boolean isMainServer() {
        return SERVER_ID >= 1 && SERVER_ID < 100;
    }

    public static boolean isTest() {
        return SERVER_ID >= 100 && SERVER_ID < 500;
    }

    public static boolean isSubmit() {
        return SERVER_ID == 0;
    }

    public static boolean isBattle() {
        return SERVER_ID >= 1000;
    }

    public static boolean isVNServer() {
        return config.mainLanguage.equals("vi");
    }

    public static GGProto.CommonVector getBattleServer(boolean ipVN, String battleKey) {
        //        if (CfgServer.isVNServer()) {
        //            if (CfgCluster.isRealServer()) {
        //                int serverId = 1;
        //                if (config.mBattle.containsKey(serverId))
        //                    return CommonProto.getCommonVectorProto(null, Arrays.asList(config.mBattle.get(serverId)[0], config.mBattle.get(serverId)[1], battleKey));
        //            }
        //            return CommonProto.getCommonVectorProto(null, Arrays.asList(config.serverBattle, Config.getStringServerPort(), battleKey));
        //        }
        return CommonProto.getCommonVectorProto(null, Arrays.asList(config.serverBattle, "6665", battleKey));
    }

    public static boolean isRankClosed() {
        if (isVNServer()) {
            if (Calendar.getInstance().get(Calendar.HOUR_OF_DAY) < 8 && Calendar.getInstance().get(Calendar.HOUR_OF_DAY) >= 1) { // 8h - 11h
                return true;
            }
        }
        return false;
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
        //        debug = true;
        lang = Lang.instance(config.mainLanguage);
    }

    public class DataConfig {
        Map<Integer, String[]> mBattle;
        Map<Integer, String[]> mBattleForeign;
        public String serverBattle, mainLanguage;
    }

    public static long getSlowSQLTime() {
        return 1000;
    }

}
