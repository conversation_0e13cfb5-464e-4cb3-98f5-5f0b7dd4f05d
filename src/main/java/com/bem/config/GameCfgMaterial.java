package com.bem.config;

import com.google.gson.Gson;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Random;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgMaterial {
    public static JSONObject json;
    public static DataConfig config;

    public static int getHp(int levelIndex, boolean isBoss) {
        if (isBoss) return config.hp[(levelIndex) * 2 + 1];
        return config.hp[(levelIndex) * 2];
    }

    public static int getAtk(int levelIndex) {
        return config.atk[levelIndex];
    }

    public static int getPoison(int levelIndex) {
        return config.poison[levelIndex];
    }

    public static List<Integer> getMaterials(int levelIndex) {
        return config.materials.get(levelIndex);
    }

    public static Map getMap() {
        return config.maps.get(Calendar.getInstance().get(Calendar.DATE) % config.maps.size());
//        if (++counter == config.maps.size()) counter = 0;
//        return config.maps.get(counter);
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public int feeRevive, enable, requireLevel, test, coolDown, freeAtk, feeSkip;
        public List<Map> maps;
        public int[] levels, poison, atk, hp;
        public List<List<Integer>> materials;
    }

    public class Map {
        public int id, monsterId, materialType;
    }
}
