package com.bem.config;

import com.bem.object.UserInfo;
import com.google.gson.Gson;
import grep.database.Database2;
import net.sf.json.JSONObject;

import java.util.Arrays;
import java.util.List;

public class CfgEnergy {
    public static JSONObject json;
    public static DataConfig config;

    public static void resetMyEnergy(UserInfo user) {
        user.getUData().myEnergy = new Gson().fromJson(user.getUData().getEnergy(), CfgEnergy.MyEnergy.class);
    }

    public static MyEnergy getMyEnergy(UserInfo user) {
        if (user.getUData().myEnergy == null) {
            if (user.getUData().getEnergy() == null) {
                user.getUData().myEnergy = new CfgEnergy().getMyEnergyInstance();
                user.getUData().myEnergy.maxEnergy = config.maxEnergy;
                user.getUData().myEnergy.energy = CfgCommon.config.newUser.energy;
                user.getUData().myEnergy.modified = System.currentTimeMillis() / 1000;
            } else {
                user.getUData().myEnergy = new Gson().fromJson(user.getUData().getEnergy(), CfgEnergy.MyEnergy.class);
            }
        }
        if (calculateEnergy(user.getUData().myEnergy)) {
            user.getUData().setEnergy(user.getUData().myEnergy.toString());
        }
        return user.getUData().myEnergy;
    }

    static boolean calculateEnergy(MyEnergy data) {
        long curTime = System.currentTimeMillis() / 1000;
        int addEnergy = 0;

        if (data.energy < data.maxEnergy) {
            addEnergy = (int) ((curTime - data.modified) / config.timeRefresh);
            int point = data.energy + addEnergy;
            point = point > data.maxEnergy ? data.maxEnergy : point;
            if (point >= data.maxEnergy) {
                data.modified = curTime;
            } else {
                data.modified = data.modified + addEnergy * config.timeRefresh;
            }
            addEnergy = point - data.energy;
            data.energy = point;
            if (addEnergy > 0) {
                return true;
            }
        }
        return false;
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, CfgEnergy.DataConfig.class);
    }

    public class DataConfig {
        public long timeRefresh;
        public int maxEnergy, upLevel;
    }

    public class MyEnergy {
        public int energy, maxEnergy;
        public long modified;

        public MyEnergy() {
            energy = config.maxEnergy;
            maxEnergy = config.maxEnergy;
            modified = System.currentTimeMillis() / 1000;
        }

        public void addEnergy(int value) {
            if (energy >= maxEnergy) {
                modified = System.currentTimeMillis() / 1000;
            }
            energy += value;
            if (energy >= maxEnergy) {
                modified = System.currentTimeMillis() / 1000;
            }
        }

        public long getCountdown() {
            long countdown = config.timeRefresh - (System.currentTimeMillis() / 1000 - modified);
            if (energy >= maxEnergy) {
                countdown = 0;
            }
            return countdown;
        }

        public boolean update(long userId) {
            return Database2.update("user_data", Arrays.asList("energy", toString()), Arrays.asList("user_id", String.valueOf(userId)));
        }

        public String toString() {
            return new Gson().toJson(this);
        }

        public List<Integer> info() {
            return Arrays.asList(energy, maxEnergy, (int) getCountdown());
        }
    }

    public CfgEnergy.MyEnergy getMyEnergyInstance() {
        return new CfgEnergy.MyEnergy();
    }
}