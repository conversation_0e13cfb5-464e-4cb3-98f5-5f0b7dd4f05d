package com.bem.config;

import com.bem.object.UserInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.Database2;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class CfgDiemdanh {
    public final static int CHUANHAN = 0, COTHENHAN = 1;
    //    static JSONArray bonusByDay;
    public static List<DataConfig> data;

//    public static JSONObject getBonus(int day) {
//        if (day < bonusByDay.size()) {
//            return bonusByDay.getJSONObject(day);
//        }
//        return null;
//    }

    public static void loadConfig(String value) {
        data = (ArrayList<DataConfig>) new Gson().fromJson(value,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());
//        JSONObject obj = JSONObject.fromObject(value);
//        bonusByDay = obj.getJSONArray("diemdanh");
    }

    public static MyAttendance getMyAttendance(UserInfo user) {
        if (user.getUData() == null) {
            return null;
        }
        try {
            if (user.getUData().myAttendance == null) {
                user.getUData().myAttendance = new Gson().fromJson(user.getUData().getAttendance(), MyAttendance.class);
            }
        }catch (Exception ex){
        }
        if (user.getUData().myAttendance == null) {
            user.getUData().myAttendance = innitAll(user);
        }
        if (!new SimpleDateFormat("dd").format(user.getUData().myAttendance.timeOnlineLastMinisecond).equalsIgnoreCase(new SimpleDateFormat("dd").format(new Date()))) {// reset online neu qua ngay moi
            user.getUData().myAttendance = innitOnline(user);
        }
        user.getUData().myAttendance.timeOnlineLastMinisecond = System.currentTimeMillis();
        user.getUData().myAttendance.update(user.getId());
        user.getUData().myAttendance.checkStatus(user);
        return user.getUData().myAttendance;
    }

    public class DataConfig {
        public int day, vipDouble;
        public String award;
    }

    public static MyAttendance innitAttend(UserInfo user) {
        MyAttendance at = user.getUData().getMyAttendance();
        if(at ==null) {
            at = innitAll(user);
            return at;

        }
        at.numberDateOnline = 0;
        at.numberReceive = 0;
        at.dateLastOnline = 0;
        at.status = CfgDiemdanh.CHUANHAN;
        user.getUData().myAttendance = at;
        user.getUData().myAttendance.update(user.getId());
        return at;
    }

    public static MyAttendance innitOnline(UserInfo user) {
        MyAttendance at = user.getUData().getMyAttendance();
        if(at ==null) {
            at = innitAll(user);
            return at;
        }
        at.statusOnline =CfgDiemdanh.CHUANHAN;
        at.numberonlineReceive=0;
        at.timeOnline =0;
        at.timeOnlineLastMinisecond =System.currentTimeMillis();
        return at;
    }

    public static MyAttendance innitAll(UserInfo user) {
        MyAttendance at = user.getUData().getMyAttendance();
        if(at ==null) {
            at = new CfgDiemdanh().getMyAtendanceInstance();
        }
        at.numberDateOnline = 0;
        at.numberReceive = 0;
        at.dateLastOnline = 0;
        at.status = CfgDiemdanh.CHUANHAN;
        at.statusOnline =CfgDiemdanh.CHUANHAN;
        at.numberonlineReceive=0;
        at.timeOnline =0;
        at.timeOnlineLastMinisecond =System.currentTimeMillis();
        user.getUData().myAttendance = at;
        user.getUData().myAttendance.update(user.getId());


        return at;
    }

    //    @Data
    public class MyAttendance {

        public int numberDateOnline, numberReceive, dateLastOnline, status ;// diem danh
        public int numberonlineReceive,statusOnline,timeOnline ;// online
        public long timeOnlineLastMinisecond;// online


        public boolean update(long userId) {
            return Database2.update("user_data", Arrays.asList("attendance", toString()), Arrays.asList("user_id", String.valueOf(userId)));
        }

        public String toString() {
            return new Gson().toJson(this);
        }
        public void checkStatusOnline(UserInfo user, boolean update) {
            //checkOnline
            boolean haveUpdate = update;
            if (!new SimpleDateFormat("dd").format(user.getUData().myAttendance.timeOnlineLastMinisecond).equalsIgnoreCase(new SimpleDateFormat("dd").format(new Date()))) {// reset online neu qua ngay moi
                user.getUData().myAttendance=innitOnline(user);
                update(user.getId());
                return;
            }else{

                if(user.getUData().myAttendance.numberonlineReceive >= CfgOnlinegift.data.size()) {
                    user.getUData().myAttendance.statusOnline =CfgDiemdanh.CHUANHAN;
                    user.getUData().myAttendance.timeOnline = -1;

                    return;
                }
            }
            if(user.getUData().myAttendance.statusOnline == CfgDiemdanh.COTHENHAN){
                return;
            }
            if (user.getUData().myAttendance.timeOnline < CfgOnlinegift.data.get(user.getUData().myAttendance.numberonlineReceive).time) {
                user.getUData().myAttendance.timeOnline += ((System.currentTimeMillis()-user.getUData().myAttendance.timeOnlineLastMinisecond)/1000);
                user.getUData().myAttendance.timeOnline = Math.min( user.getUData().myAttendance.timeOnline,CfgOnlinegift.data.get(user.getUData().myAttendance.numberonlineReceive).time);
                user.getUData().myAttendance.timeOnlineLastMinisecond=System.currentTimeMillis();
                if (user.getUData().myAttendance.timeOnline >= CfgOnlinegift.data.get(user.getUData().myAttendance.numberonlineReceive).time) {
                    haveUpdate = true;
                    user.getUData().myAttendance.statusOnline = CfgDiemdanh.COTHENHAN;
                    user.getUData().myAttendance.timeOnline = CfgOnlinegift.data.get(user.getUData().myAttendance.numberonlineReceive).time;
                }

            }else{
                user.getUData().myAttendance.timeOnline = CfgOnlinegift.data.get(user.getUData().myAttendance.numberonlineReceive).time;
                user.getUData().myAttendance.timeOnlineLastMinisecond=System.currentTimeMillis();
                if(user.getUData().myAttendance.statusOnline == CfgDiemdanh.CHUANHAN) {
                    user.getUData().myAttendance.statusOnline = CfgDiemdanh.COTHENHAN;
//                    user.getUData().myAttendance.timeOnlineLastMinisecond=System.currentTimeMillis();
                    haveUpdate= true;
                }
            }
            if(haveUpdate){
                update(user.getId());
            }
        }

        public void checkStatus(UserInfo user) {
            for (int i = 1; i <= 24; i++) {
                int day = Integer.parseInt(new SimpleDateFormat("dd").format(System.currentTimeMillis()+i * 60 *60*1000));
//                System.out.println("day-----------------"+i+"------------>"+day);
            }
            if (user.getUData().myAttendance.dateLastOnline != Integer.parseInt(new SimpleDateFormat("dd").format(System.currentTimeMillis())) && user.getUData().myAttendance.status != CfgDiemdanh.COTHENHAN) {
                if (user.getUData().myAttendance.numberReceive >= data.size()) {// reset neu da nhan het
                    user.getUData().myAttendance=innitAttend(user);
                }
                if (user.getUData().myAttendance.numberDateOnline < data.get(user.getUData().myAttendance.numberReceive).day) {
                    user.getUData().myAttendance.numberDateOnline += 1;
                    if (user.getUData().myAttendance.numberDateOnline >= data.get(user.getUData().myAttendance.numberReceive).day) {
                        user.getUData().myAttendance.numberDateOnline = data.get(user.getUData().myAttendance.numberReceive).day;
                        user.getUData().myAttendance.status = CfgDiemdanh.COTHENHAN;
                    }
                }else{
                    user.getUData().myAttendance.numberDateOnline = data.get(user.getUData().myAttendance.numberReceive).day;
                    user.getUData().myAttendance.status = CfgDiemdanh.COTHENHAN;
                }
                user.getUData().myAttendance.dateLastOnline = Integer.parseInt(new SimpleDateFormat("dd").format(System.currentTimeMillis()));
                update(user.getId());
            }

        }

        public void getAward(UserInfo user) {

        }

    }

    public MyAttendance getMyAtendanceInstance() {
        return new MyAttendance();
    }
}