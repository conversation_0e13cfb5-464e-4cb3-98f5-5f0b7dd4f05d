/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import com.bem.util.Util;
import com.google.gson.Gson;
import net.sf.json.JSONObject;

import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgAccount {
    public static JSONObject json;
    public static DataConfig config;

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public Map<String, String> debugAccount;
    }

}
