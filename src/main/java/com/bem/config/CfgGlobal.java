package com.bem.config;

import com.bem.object.Skill;
import com.google.gson.Gson;
import net.sf.json.JSONObject;

import java.util.Calendar;
import java.util.List;
import java.util.Map;

public class CfgGlobal {
    public static JSONObject json;
    public static DataConfig config;

    public static boolean inEvent() {
        return config.dayOfWeeks.contains(Calendar.getInstance().get(Calendar.DAY_OF_WEEK));
//        return DateTime.isDate("2018-05-03");
    }

    public static void main(String[] args) {
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, -1);
        System.out.println(ca.get(Calendar.DAY_OF_WEEK));
    }

    public static int getNumberItem(int itemId, int hasNumber) {
        Integer maxNumber = config.mLimitItem.get(itemId);
        if (maxNumber == null) return 0;
        return Math.min(hasNumber, maxNumber);
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public List<Integer> dayOfWeeks;
        public Map<Integer, Integer> mLimitItem;
        public Map<Integer, Skill> mBattleItem;
    }
}