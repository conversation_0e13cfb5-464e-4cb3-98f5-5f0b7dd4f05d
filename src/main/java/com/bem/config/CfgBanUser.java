package com.bem.config;

import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class CfgBanUser {

    public static List<String> imei = new ArrayList<String>();
    public static List<String> mobile = new ArrayList<String>();
    public static List<String> words = new ArrayList<String>();

    static String Keyword = "\\s\", \\.'\\*?/:;\\-_+=#$%&Z*!<>\\[{}\\]()“”~&^0123456789ABCDEFGHIJKLMNOPQRSTXYUWZVĂÂĐÊÔƠƯÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬĐÈẺẼÉẸÊỀỂỄẾỆÌỈĨÍỊÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢÙỦŨÚỤƯỪỬỮỨỰỲỶỸÝỴzxcvbnmasdfghjklqwertyuiopăâđêôơưàảãáạăằẳẵắặâầẩẫấậđèẻẽéẹêềểễếệìỉĩíịòỏõóọôồổỗốộơờởỡớợùủũúụưừửữứựỳỷỹýỵ";

    public static void main(String[] args) {
    }

    public static boolean isValidMessage(String message) {
        String msg = message.toLowerCase();
        String tmp = "";
        for (int i = 0; i < msg.length(); i++) {
            if (!Keyword.contains(String.valueOf(msg.charAt(i)))) {
                return false;
            }
        }

        if (msg.length() > 160) {
            return false;
        }

        tmp = "";
        for (int i = 0; i < msg.length(); i++) {
            char c = msg.charAt(i);
            if (c == 32) {
                tmp += c;
            } else if (c >= 48 && c <= 57) {
                tmp += c;
            } else if (c >= 97 && c <= 122) {
                tmp += c;
            }
        }

        while (tmp.contains("  ")) {
            tmp = tmp.replaceAll("  ", " ");
        }

        if (isContainMobile(tmp)) {
            return false;
        }

        tmp = tmp.replaceAll(" ", "");
        for (String word : words) {
            if (tmp.contains(word)) {
                return false;
            }
        }

        return true;
    }

    public static boolean isContainMobile(String tmp) {
        String[] obj = tmp.split(" ");
        for (int i = 0; i < obj.length; i++) {
            try {
                long number = Long.parseLong(obj[i]);
                if (obj[i].length() > 7) {
                    return true;
                }
            } catch (Exception ex) {
            }
        }
        return false;
    }

    public static boolean isBanMobile(String strMobile) {
        if (mobile.contains(strMobile.toLowerCase())) {
            return true;
        }
        return false;
    }

    public static boolean isBanImei(String strImei) {
        if (imei.contains(strImei.toLowerCase())) {
            return true;
        }
        return false;
    }

    public static void loadConfig(String strJson) {
        JSONObject json = JSONObject.fromObject(strJson);

        imei.clear();
        mobile.clear();
        words.clear();

        JSONArray arrImei = json.getJSONArray("imei");
        for (int i = 0; i < arrImei.size(); i++) {
            imei.add(arrImei.getString(i));
        }

        JSONArray arrMobile = json.getJSONArray("mobile");
        for (int i = 0; i < arrMobile.size(); i++) {
            mobile.add(arrMobile.getString(i));
        }

        JSONArray arrWord = json.getJSONArray("word");
        for (int i = 0; i < arrWord.size(); i++) {
            words.add(arrWord.getString(i));
        }
    }
}
