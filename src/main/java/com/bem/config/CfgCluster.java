package com.bem.config;

import com.google.gson.Gson;
import net.sf.json.JSONObject;

import java.util.List;

public class CfgCluster {
    public static JSONObject json;
    public static DataConfig config;
    public static String DB_MAIN = "dson_main.";
    public static int runningPort = 0;

    public static boolean isRealServer() {
        return runningPort == config.port;
    }

    public static int getServer(String username) {
        if (username.contains("_")) return new Integer(username.substring(0, username.indexOf("_")));
        return 1;
    }

    public static String getRealUsername(String username) {
        if (username.contains("_")) return username.substring(username.indexOf("_") + 1);
        return username;
    }

    public static String getKey(int serverId, int key) {
        if (serverId == 0) return String.valueOf(key);
        return String.format("%s:%s", serverId, key);
    }

    public static String getKey(int serverId, String key) {
        if (!isRealServer()) return String.format("%s:%s", serverId + 100, key);
        return String.format("%s:%s", serverId, key);
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();
    }

    public class DataConfig {
        public List<Integer> serverIds;
        public String strServerIds;
        public int port;

        public void init() {
            strServerIds = "";
            serverIds.forEach(serverId -> strServerIds += "," + serverId);
            strServerIds = String.format("(%s)", strServerIds.substring(1));
        }
    }
}