package com.bem.config;

import com.bem.dao.mapping.UserEventEntity;
import com.bem.object.UserInfo;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.k2tek.common.Logs;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import net.sf.json.JSONObject;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.*;

public class CfgEvent {
    public static JSONObject json;
    public static DataConfig config;

    public static Map<Integer, RepeatedEvent> mRepeatedEvent = new HashMap<>();
    public static Map<Integer, EventForLitmitUser> mEventForLimitUser = new HashMap<>();

    public static RepeatedEvent getEventRepeat(int eventId) {
        return mRepeatedEvent.get(eventId);
    }

    public static UserEventEntity dbloadEvent(long userId, int eventId, int eventIndex) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_event where user_id=" + userId + " and event_id=" + eventId
                    + " and event_index=" + eventIndex);
            List<UserEventEntity> aEvent = query.addEntity(UserEventEntity.class).list();
//            System.out.println(eventId);
            if (aEvent != null && aEvent.size() > 0 && aEvent.get(0) != null) {
                return aEvent.get(0);
            }
            return null;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
        return null;
    }

    public static boolean getEventForLimitUserInTime(int eventId, UserInfo user, int eventIndex) {
        if (1 == 1) {
            return false;
        }
        UserEventEntity userEvent = user.getUEvent().getEvent(eventId).getEvent();
        EventForLitmitUser event = mEventForLimitUser.get(eventId);
        if (event.endTimeInSeconds < (System.currentTimeMillis() / 1000) + (event.days * 24 * 60 * 60)) {
            return false;
        }
        if (event == null) {
            return false;
        }
        if (event.id != userEvent.getEventId()) {
            return false;
        }
        if (userEvent == null) {
            userEvent = dbloadEvent(user.getId(), eventId, eventIndex);
        }
        if (userEvent == null) {
            if (event.endTimeInSeconds < System.currentTimeMillis() / 1000 || event.startTimeInSeconds > System.currentTimeMillis() / 1000) {
                return false;
            } else {
                /// check dk thoa man con da insert roi tuc la thoa man
                if (user.getDbUser().getVip() < event.require.vipmin || user.getDbUser().getVip() > event.require.vipmax || user.getDbUser().getLevel() < event.require.levelmin || user.getDbUser().getLevel() > event.require.levelmax) {
                    return false;
                } else {
                    return true;
                }
            }
        }
        long time = userEvent.getDateCreated().getTime() / 1000;
        long expTime = time + (event.days * 24 * 60 * 60);
        if (System.currentTimeMillis() >= time && System.currentTimeMillis() <= expTime) {
            return true;
        }
        return false;
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
        //
        mRepeatedEvent.clear();
        for (RepeatedEvent repeatedEvent : config.repeatedEvent) {
            repeatedEvent.init();
            mRepeatedEvent.put(repeatedEvent.id, repeatedEvent);
        }
        mEventForLimitUser.clear();
        for (EventForLitmitUser eventForLitmitUser : config.eventForLitmitUser) {
            eventForLitmitUser.init();
            mEventForLimitUser.put(eventForLitmitUser.id, eventForLitmitUser);
        }
    }

    public class DataConfig {
        List<RepeatedEvent> repeatedEvent;
        public List<EventForLitmitUser> eventForLitmitUser;
        public JSONObject infor;
    }

    public static void main(String[] args) throws Exception {
        String cacheTime = "2024-11-02 00:00:00";
        Date date = DateTime.getSDFFullDate().parse(cacheTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);


        System.out.println(calendar.getTime());

        long time = System.currentTimeMillis() / 1000;
        int curIndex = 201705 + (int) (time - calendar.getTimeInMillis() / 1000) / (30 * 24 * 60 * 60);
        System.out.println(GsonUtil.toJson(new int[]{curIndex, curIndex - 1}));
    }

    public class RepeatedEvent {
        int id, startIndex, days;
        String startTime;
        long startTimeInSeconds;

        void init() {
            try {
                startTimeInSeconds = DateTime.getSDFFullDate().parse(startTime).getTime() / 1000;
            } catch (Exception ex) {
                Logs.error(Util.exToString(ex));
            }
        }

        public int[] eventIndex() {
            long time = System.currentTimeMillis() / 1000;
            int curIndex = startIndex + (int) (time - startTimeInSeconds) / (days * 24 * 60 * 60);
            return new int[]{curIndex, curIndex - 1};
        }

        public long nextSeason() {
            long time = System.currentTimeMillis() / 1000;
            int addIndex = (int) (time - startTimeInSeconds) / (days * 24 * 60 * 60);
            return startTimeInSeconds + (addIndex + 1) * days * 24 * 60 * 60 - time;
        }
    }

    public class Require {
        int levelmin, levelmax, vipmin, vipmax, timecreatedmin, timecreatedmax, curentgoldmin, curentgoldmax;
        String require;
    }

    public class EventForLitmitUser {
        public int enable, id, startIndex, days;
        String startTime;
        String endTime;
        Require require;


        long startTimeInSeconds, endTimeInSeconds;

        void init() {
            try {
                startTimeInSeconds =System.currentTimeMillis() / 1000;
                endTimeInSeconds = System.currentTimeMillis() / 1000;

            } catch (Exception ex) {
                Logs.error(Util.exToString(ex));
            }
        }

        public int[] eventIndex() {
            long time = System.currentTimeMillis() / 1000;
            int curIndex = startIndex + (int) (time - startTimeInSeconds) / (days * 24 * 60 * 60);
            return new int[]{curIndex, curIndex - 1};
        }

//        public long nextSeason() {
//            long time = System.currentTimeMillis() / 1000;
//            int addIndex = (int) (time - startTimeInSeconds) / (days * 24 * 60 * 60);
//            return startTimeInSeconds + (addIndex + 1) * days * 24 * 60 * 60 - time;
//        }
    }

}
