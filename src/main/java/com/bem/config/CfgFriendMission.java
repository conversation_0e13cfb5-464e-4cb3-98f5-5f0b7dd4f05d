package com.bem.config;

import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class CfgFriendMission {

	static List<String> disableCP;

	public static boolean isDisableCP(String cp) {
		if (cp == null || cp.length() == 0) {
			return true;
		}
		cp = cp.toUpperCase();
		return disableCP.contains(cp);
	}

	@SuppressWarnings("unchecked")
	public static void loadConfig(String strJson) {
		JSONObject json = JSONObject.fromObject(strJson);
		JSONArray arr = json.getJSONArray("disable");

		disableCP = new ArrayList<String>();
		disableCP.addAll(arr);

	}
}
