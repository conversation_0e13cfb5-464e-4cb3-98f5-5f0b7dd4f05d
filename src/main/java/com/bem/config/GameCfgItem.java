package com.bem.config;

import com.bem.boom.Resources;
import com.bem.monitor.Bonus;
import com.bem.object.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by <PERSON><PERSON> on 12/8/2014.
 */
public class GameCfgItem {

    public final static int TYPERANDOM = 0, TYPECHOSE = 1;
    public static List<DataConfig> data;


    public static void loadConfig(String strJson) {

        data = (ArrayList<DataConfig>) new Gson().fromJson(strJson,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());

        for (int i = 0; i < data.size(); i++) {
            for (int j = 1; j < data.get(i).rate.length; j++) {
                data.get(i).rate[j] += data.get(i).rate[j - 1];
            }
        }
        for (int i = 0; i < data.size(); i++) {
            for (int j = 1; j < data.get(i).specia.length; j++) {
                if(data.get(i).type==TYPERANDOM) {
                    data.get(i).specia[j][1] += data.get(i).specia[j - 1][1];
                }
            }
        }

    }

    public static JSONArray openItemBase(int[] item) {
        try {
            JSONArray array = new JSONArray();

            int index = 0;
            while (index<item.length) {
                int type = item[index++];
                int rankBegin = item[index++];
                int rankEnd = 0;
                if (rankBegin < 0) {
                    rankEnd = item[index++];
                }
                if (rankBegin < 0 && rankEnd >= 0) {
                    rankEnd = rankBegin;
                    index--;
                }
                switch (type) {

                    case Bonus.ITEM:
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.ITEM, rankBegin, item[index])));
                        }
                        break;
                    case Bonus.GOLD:
                        index =1;
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s]", Bonus.GOLD, rankBegin)));
                        }
                        break;
                    case Bonus.GEM:
                        index =1;
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s]", Bonus.GEM, rankBegin)));
                        }
                        break;
                    case Bonus.MATERIAL:
                        int number = item[index++];
                        if (rankBegin < 0 && rankEnd < 0) {
                            ResMaterial material = Resources.getRandomMaterialLevel(-rankBegin, -rankEnd);
                            rankBegin = material.getId();
                        }
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.MATERIAL, rankBegin, number)));
                        }
                        break;
                    case Bonus.ACCESSORIES:
                        ResAccessories accessory = new ResAccessories();
                        if (rankBegin < 0 && rankEnd < 0) {
                            accessory = Resources.getRandomAccessories(-rankBegin, -rankEnd);
                            rankBegin = accessory.getId();
                        }
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.ACCESSORIES, rankBegin, item[index++])));
                        }
                        break;
                    case Bonus.BOMB:
                        ResBomb bom = new ResBomb();
                        if (rankBegin < 0 && rankEnd < 0) {
                            bom = Resources.getRandomBom(-rankBegin, -rankEnd);
                            rankBegin = bom.getId();
                        }
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.BOMB, rankBegin, item[index++])));
                        }
                        break;
                    case Bonus.PET_FRAGMENT:
                        ResPet petF = new ResPet();
                        if (rankBegin < 0 && rankEnd < 0) {
                            petF = Resources.getRandomPet(-rankBegin, -rankEnd);
                            rankBegin = petF.getId();
                        }
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.PET_FRAGMENT, rankBegin, item[index++])));
                        }
                        break;
                    case Bonus.PET_FOOD:
                        ResPetFood food = new ResPetFood();
                        if (rankBegin < 0 && rankEnd < 0) {
                            food = Resources.getRandomFruit(-rankBegin, -rankEnd);
                            rankBegin = food.getId();
                        }
//
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.PET_FOOD, rankBegin, item[index++])));
                        }
                        break;
                    case Bonus.UFO_STONE:
                        StoneUpgradeUfo stone = new StoneUpgradeUfo();
                        if (rankBegin < 0 && rankEnd < 0) {
                            stone = Resources.getRandomUfoStone(-rankBegin, -rankEnd);
                            rankBegin = stone.id;
                        }
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.UFO_STONE, rankBegin, item[index++])));
                        }
                        break;
                    case Bonus.AVATAR_FRAGMENT:
                        ResAvatar avatar = new ResAvatar();
                        if (rankBegin < 0 && rankEnd < 0) {
                            avatar = Resources.getRandomAvatar(-rankBegin, -rankEnd);
                            rankBegin = avatar.getId();
                        }
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.AVATAR_FRAGMENT, rankBegin, item[index++])));
                        }
                        break;
                    case Bonus.PET:
                        ResPet pet = new ResPet();
                        if (rankBegin < 0 && rankEnd < 0) {
                            pet = Resources.getRandomPet(-rankBegin, -rankEnd);
                            rankBegin = pet.getId();
                        }
                        if (rankBegin > 0) {
                            array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s,%s]", Bonus.PET, rankBegin, item[index++], item[index++])));
                            index++;
                        }
                        break;
                }
                index++;
            }
            return array;
        } catch (Exception ex) {
        }
        return new JSONArray();
    }


    public static JSONArray openItemEvent(int id) {
        JSONArray array = new JSONArray();
        int ck = -1;
        for (int i = 0; i < data.size(); i++) {
            if (id == data.get(i).id && data.get(i).type == TYPERANDOM && data.get(i).resul < 0) {
                int ran = new Random().nextInt(100) + 1;
                for (int j = 0; j < data.get(i).specia.length; j++) {
                    if (ran <= data.get(i).specia[j][1]) {
                        int [] item = new int[data.get(i).specia[j].length-2];
                        for (int k = 2; k < data.get(i).specia[j].length; k++) {
                            item[k-2] =  data.get(i).specia[j][k];
                        }
                        array = openItemBase(item);
                        ck = 1;
                        break;
                    }
                }
                break;
            }
        }
        if (ck == -1) {
            return new JSONArray();
        }
        return array;
    }

    public static JSONArray openItemChose(int id, int type, int subId, List<Integer> number) {
        JSONArray array = new JSONArray();
        int ck = -1;
        for (int i = 0; i < data.size(); i++) {
            if (id == data.get(i).id && data.get(i).type == TYPECHOSE) {
                for (int j = 0; j < data.get(i).specia.length; j++) {
                    if (data.get(i).specia[j][0] == type && data.get(i).specia[j][1] == subId) {
                        ck = 1;
                        break;
                    }
                }
                break;
            }
        }
        if (ck == -1) {
            return new JSONArray();
        }
        array.add(type);
        array.add(subId);

        for (int i = 0; i < number.size(); i++) {
            array.add(number.get(i));
        }
//        switch (result) {
//            case Bonus.BOMB:// ngau nhien rank b--s ok
//                ResBomb bom = Resources.getBomb(subId);
//                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.BOMB, bom.getId(), 1)));
//                break;
//              case Bonus.AVATAR_FRAGMENT:
//                ResAvatar avatar = Resources.getAvatar(subId);
//                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.AVATAR_FRAGMENT, avatar.getId(), 10)));
//                break;
//        }
        return array;
    }

    public static JSONArray openItemRandom(int id) {
        JSONArray array = new JSONArray();
        int defaurank = 1;
        int[] rate = {};
        int result = 1;
        int ck = -1;
        int[][] special = null;
        for (int i = 0; i < data.size(); i++) {
            if (id == data.get(i).id && data.get(i).type == TYPERANDOM && data.get(i).resul > 0) {
                rate = data.get(i).rate;
                defaurank = data.get(i).defautRank;
                result = data.get(i).resul;
                ck = i;
                int ran = new Random().nextInt(100) + 1;
                for (int j = 0; j < rate.length; j++) {
                    if (rate[j] >= ran) {
                        defaurank = j;
                        break;
                    }
                }
                special = data.get(i).specia;
                if (defaurank < 0 || defaurank > 5) {
                    defaurank = 1;
                }
                break;
            }
        }
        if (ck == -1) {
            return new JSONArray();
        }
        int index = 2;
        switch (result) {

//            case Bonus.ITEM:// ngau nhien rank b--s ok
////                int number = ran.nextInt(data.item[1] - data.item[0]) + data.item[0];
////                    int lv = data.get(type).bommateriallv[ran.nextInt(data.get(type).bommateriallv.length)];
//                int id = arr[index];
//                int number = arr[index++];
////                    int number = data.get(type).bommaterialnumber[ran.nextInt(data.get(type).bommaterialnumber.length)];
//                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.ITEM, id, number)));
//
//                break;
//            case Bonus.GOLD:// ngau nhien rank b--s ok
////                int number = ran.nextInt(data.item[1] - data.item[0]) + data.item[0];
////                    int lv = data.get(type).bommateriallv[ran.nextInt(data.get(type).bommateriallv.length)];
//                number = arr[index];
////                    int number = data.get(type).bommaterialnumber[ran.nextInt(data.get(type).bommaterialnumber.length)];
//                array.addAll(JSONArray.fromObject(String.format("[%s,%s]", Bonus.GOLD, number)));
//
//                break;
//            case Bonus.MATERIAL:// ngau nhien rank b--s ok
////                int number = ran.nextInt(data.item[1] - data.item[0]) + data.item[0];
////                    int lv = data.get(type).bommateriallv[ran.nextInt(data.get(type).bommateriallv.length)];
//                int lv = arr[index];
//                number = arr[++index];
////                    int number = data.get(type).bommaterialnumber[ran.nextInt(data.get(type).bommaterialnumber.length)];
//                ResMaterial material = Resources.getRandomMaterialLevel(lv);
//                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.MATERIAL, material.getId(), number)));
//
//                break;
////                case GOLD://ok
////                    array.addAll( JSONArray.fromObject(String.format("[%s,%s]",  CfgAchievement.GOLD, ran.nextInt(data.get(type).gold[1] - data.get(type).gold[0]) + data.get(type).gold[0])));
////                    break;
////                case GEM://ok
////                    array.addAll(  JSONArray.fromObject(String.format("[%s,%s]",  CfgAchievement.GEM, ran.nextInt(data.get(type).gem[1] - data.get(type).gem[0]) + data.get(type).gem[0])));
////                    break;
            case Bonus.ACCESSORIES:// ngau nhien rank b--s
//                    System.out.println("accessories---->");
                ResAccessories accessory = new ResAccessories();
                if (defaurank > 0) {
                    accessory = Resources.getRandomAccessories(defaurank, defaurank);
                } else {
                    int ran = new Random().nextInt(100) + 1;
                    for (int i = 0; i < special.length; i++) {
                        if (ran <= special[i][1]) {
                            defaurank = special[i][0];
                            break;
                        }
                    }
                    accessory = Resources.getAccessories(defaurank);
                }
                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.ACCESSORIES, accessory.getId(), 1)));
                break;
            case Bonus.BOMB:// ngau nhien rank b--s ok
                ResBomb bom = new ResBomb();
                if (defaurank > 0) {
                    bom = Resources.getRandomBom(defaurank, defaurank);
                } else {
                    int ran = new Random().nextInt(100) + 1;
                    for (int i = 0; i < special.length; i++) {
                        if (ran <= special[i][1]) {
                            defaurank = special[i][0];
                            break;
                        }
                    }
                    bom = Resources.getBomb(defaurank);
                }
                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.BOMB, bom.getId(), 1)));
                break;
//            case Bonus.PET_FRAGMENT:// ngau nhien rank b--s ok
//                ResPet petF = new ResPet();
//                if (arr[index] > 0) {
//                    petF = Resources.getPet(arr[index]);
//                } else {
//                    petF = Resources.getRandomPet(-arr[index], -arr[index]);
//                }
//                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.PET_FRAGMENT, petF.getId(), arr[++index])));
//                break;
            case Bonus.PET_FOOD:
                ResPetFood food = new ResPetFood();
                if (defaurank > 0) {
                    food = Resources.getRandomFruit(defaurank);
                } else {
                    for (int i = 0; i < special.length; i++) {
                        for (int j = 0; j < special[i].length; j++) {
                            System.out.print(special[i][j] + "||");
                        }
                    }
                    int ran = new Random().nextInt(100) + 1;
                    for (int i = 0; i < special.length; i++) {
                        if (ran <= special[i][1]) {
                            defaurank = special[i][0];
                            break;
                        }
                    }
                    food = Resources.getPetFood(defaurank);
                }
//                    int rank = data.get(type).petfruit[ran.nextInt(data.get(type).petfruit.length)];
//                    ResPetFood fruit = Resources.getRandomFruit(rank);
                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.PET_FOOD, food.getId(), 1)));
                break;
            case Bonus.UFO_STONE:
                StoneUpgradeUfo stone = new StoneUpgradeUfo();
                if (defaurank > 0) {
                    stone = Resources.getRandomUfoStone(defaurank, defaurank);
                } else {
                    int ran = new Random().nextInt(100) + 1;
                    for (int i = 0; i < special.length; i++) {
                        if (ran <= special[i][1]) {
                            defaurank = special[i][0];
                            break;
                        }
                    }
                    stone = Resources.getStone(defaurank);
                }
//                    int rank = data.get(type).petfruit[ran.nextInt(data.get(type).petfruit.length)];
//                    ResPetFood fruit = Resources.getRandomFruit(rank);
                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.UFO_STONE, stone.id, 1)));
                break;
//            case Bonus.AVATAR_FRAGMENT:
//                ResAvatar avatar = new ResAvatar();
//                if (arr[index] > 0) {
//                    avatar = Resources.getAvatar(arr[index]);
//                } else {
//                    avatar = Resources.getRandomAvatar(-arr[index], -arr[index]);
//                }
////                ShopAvatarEntity avatar = null;
////                    rank = data.get(type).avatarpiece[ran.nextInt(data.get(type).avatarpiece.length)];
////                    ResAvatar avatar = Resources.getRandomAvatar(rank, rank);
//                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.AVATAR_FRAGMENT, avatar.getId(), arr[++index])));
//                break;
////                case CfgAchievement.PET_FRAGMENT:
//////                    rank = ran.nextInt(5) + 1;
//////                    ResPet pet = Resources.getRandomPet(rank);
//////                    array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", CfgAchievement.PET_FRAGMENT, pet.getId(), 1)));
////                    break;
//            case Bonus.PET:
//                ResPet pet = new ResPet();
//                if (arr[index] > 0) {
//                    pet = Resources.getPet(arr[index]);
//                } else {
//                    pet = Resources.getRandomPet(-arr[index], -arr[index]);
//                }
//                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s,%s]", Bonus.PET, pet.getId(), arr[++index], arr[++index])));
//                break;
        }
        return array;
    }


    //    }
    public class DataConfig {
        public int type, id, defautRank, resul;
        public int[] rate;
        public int[][] specia;
    }
}
