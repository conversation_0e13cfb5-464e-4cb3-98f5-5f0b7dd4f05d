package com.bem.config;

import com.google.gson.Gson;
import net.sf.json.JSONObject;

import java.util.Calendar;
import java.util.List;

public class CfgSolo {
    public static JSONObject json;
    public static DataConfig config;

    public static boolean inEvent() {
        return config.dayOfWeeks.contains(Calendar.getInstance().get(Calendar.DAY_OF_WEEK));
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public List<Integer> dayOfWeeks;
    }
}