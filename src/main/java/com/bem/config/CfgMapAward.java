package com.bem.config;

import com.bem.object.UserInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.Database2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CfgMapAward {
    public static final int award = 1,award3= 2, normal =1 , danger =2;
    public static List<DataConfig> data;


    public static void loadConfig(String value) {
        data = (ArrayList<DataConfig>) new Gson().fromJson(value,
                new TypeToken<ArrayList<DataConfig>>() {
                }.getType());
    }

    public static MyAward getMyAward(UserInfo user) {
        if (user.getUData() == null) {
            return null;
        }
        try {
            if (user.getUData().myAward == null) {
                user.getUData().myAward= new Gson().fromJson(user.getUData().getMapAward(), MyAward.class);
            }
        }catch (Exception ex){
        }
        if (user.getUData().myAward == null) {
            user.getUData().myAward = innitAll(user);
        }
        user.getUData().myAward.update(user.getId());
        return user.getUData().myAward;
    }

    public static MyAward validMapAward (UserInfo user) {
        MyAward at = user.getUData().getMyAward();
        if(at ==null) {
            at = new CfgMapAward().getMyAwardInstance();
            at.award = new ArrayList<>();
            at.awarddanger = new ArrayList<>();
        }
        if(at.award==null){
            at.award = new ArrayList<>();
        }
        if(  at.awarddanger == null){
            at.awarddanger = new ArrayList<>();
        }
        List<Integer> lstAward = new ArrayList<>();
        for (int i = at.award.size(); i < user.getUData().getMap().getNormal().size()/10 ; i++) {
            lstAward.add(0);
            lstAward.add(0);

        }

//        at.award = new ArrayList<>();
        at.award.addAll(lstAward);
        lstAward = new ArrayList<>();
        for (int i = at.awarddanger.size(); i < user.getUData().getMap().getInsane().size()/10 ; i++) {
            lstAward.add(0);
            lstAward.add(0);

        }
        at.awarddanger.addAll(lstAward);
        user.getUData().myAward = at;
        user.getUData().myAward.update(user.getId());


        return at;
    }

    public static MyAward innitAll(UserInfo user) {
        MyAward at = user.getUData().getMyAward();
        if(at ==null) {
            at = new CfgMapAward().getMyAwardInstance();
        }
        List<Integer> lstAward = new ArrayList<>();
        for (int i = 0; i < user.getUData().getMap().getNormal().size()/10 ; i++) {
            lstAward.add(0);
            lstAward.add(0);

        }
        at.award = new ArrayList<>();
        at.award.addAll(lstAward);
        lstAward = new ArrayList<>();
        for (int i = 0; i < user.getUData().getMap().getInsane().size()/10 ; i++) {
            lstAward.add(0);
            lstAward.add(0);

        }
        at.awarddanger = new ArrayList<>();
        at.awarddanger.addAll(lstAward);
        user.getUData().myAward = at;
        user.getUData().myAward.update(user.getId());


        return at;
    }
    public class DataConfig {
        public int map;
        public String award,award3,awardDanger,awardDanger3;
    }




    //    @Data
    public class MyAward {

        public List<Integer> award ;
        public List<Integer> awarddanger ;
        public boolean update(long userId) {
            return Database2.update("user_data", Arrays.asList("map_award", toString()), Arrays.asList("user_id", String.valueOf(userId)));
        }

        public String toString() {
            return new Gson().toJson(this);
        }


        public void getAward(UserInfo user) {

        }

    }

    public MyAward getMyAwardInstance() {
        return new MyAward();
    }
}