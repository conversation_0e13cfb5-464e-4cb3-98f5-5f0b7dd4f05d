package com.bem.config;

import com.bem.boom.object.MissionObject;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.ClanEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.topTrophyDaily;
import com.bem.monitor.Online;
import com.bem.object.event.TopTrophy;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.google.gson.Gson;
import com.k2tek.IAction;
import grep.database.Database2;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by <PERSON><PERSON> on 10/21/2014.
 */
public class CfgNewMission {
    public static int feeReject = 30;
    public static JSONArray aMission;
    public static JSONArray aAward;

    public static int checkPoint = 5;
    public static Map<Long, List<MissionObject>> mapMision = new HashMap<Long, List<MissionObject>>();
    private static SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy ");
    private static SimpleDateFormat sdfHH = new SimpleDateFormat("dd-MM-yyyy HH");
    private static SimpleDateFormat sdfY = new SimpleDateFormat("yyyy-MM-dd");

    private static SimpleDateFormat hM = new SimpleDateFormat("");
    public static List<topTrophyDaily> lstHm = new ArrayList<topTrophyDaily>();
    static List<UserEntity> lstUserTrophy = new ArrayList<UserEntity>();
    static List<UserEntity> lstUserTrophy100 = new ArrayList<UserEntity>();

    static List<UserEntity> lstUserLevel = new ArrayList<UserEntity>();
    static List<UserEntity> lstUserStar = new ArrayList<UserEntity>();
    static List<UserEntity> lstUserVip = new ArrayList<UserEntity>();
    static List<UserEntity> lstUserWin = new ArrayList<UserEntity>();
    static List<ClanEntity> lstClan = new ArrayList<ClanEntity>();

    public final static int aSet = 0, aGet = 1;

    public synchronized static List<ClanEntity> entryLstClanEntity(int type, List<ClanEntity> lst, int server) {
        int eventIndex = TopTrophy.getInstance().getEventIndex()[0];
        if (type == aGet) {
            String top = JCache.getInstance().getValue(":TOPCLAN:");
            if (top == null) {
                lstClan = Database2.getSelectQuery("SELECT clan.id, clan.name, clan.avatar, clan.slogan, clan.exp , clan.`level`, clan.join_rule, clan.join_trophy, clan.member ,clan.server_id, clan.chat , clan" +
                        ".date_created, sum(user_event.value) AS trophy from user,user_event,clan where clan.id > 0 and clan.server_id = "+server+" and user.id = user_event.user_id and user.clan = clan.id and user_event" +
                        ".event_index=" + eventIndex +
                        " GROUP by clan order by trophy desc limit 100", ClanEntity.class);
                top = new Gson().toJson(lstClan);
                JCache.getInstance().setValue(":TOPCLAN:", top, 30 * 60);
            } else if (lstClan == null || lstClan.size() == 0) {

                lstClan = Database2.getSelectQuery("SELECT clan.id, clan.name, clan.avatar, clan.slogan, clan.exp , clan.`level`, clan.join_rule, clan.join_trophy, clan.member ,clan.server_id, clan.chat , clan" +
                        ".date_created, sum(user_event.value) AS trophy from user,user_event,clan where clan.id > 0 and clan.server_id = "+server+" and user.id = user_event.user_id and user.clan = clan.id  and user_event.event_index="
                        + eventIndex + " GROUP by clan order by trophy desc limit 100", ClanEntity.class);
                top = new Gson().toJson(lstClan);
                JCache.getInstance().setValue(":TOPCLAN:", top, 30 * 60);
            }

            return lstClan;
        } else {
            lstClan = lst;
            return new ArrayList<>();
        }
    }

    public synchronized static List<UserEntity> entryLstUserTrophy(int type, List<UserEntity> lst, int server) {
        if (type == aGet) {
            String top = JCache.getInstance().getValue(":TOPTROPHY:"+server+":");
            if (top == null) {
                lstUserTrophy = Database2.getList("user", Arrays.asList("server",""+server), "order by trophy desc, username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserTrophy);
                JCache.getInstance().setValue(":TOPTROPHY:", top, 5 * 60);
                lstUserTrophy100.addAll(lstUserTrophy.subList(0, 100));
            } else if (lstUserTrophy == null || lstUserTrophy.size() == 0) {
                lstUserTrophy = Database2.getList("user", Arrays.asList("server",""+server), "order by trophy desc, username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserTrophy);
                JCache.getInstance().setValue(":TOPTROPHY:", top, 5 * 60);
                lstUserTrophy100.addAll(lstUserTrophy.subList(0, 100));

            }
            return lstUserTrophy100;
        } else {
            lstUserTrophy = lst;
            return new ArrayList<>();
        }
    }

    public synchronized static List<UserEntity> entryLstUserStar(int type, List<UserEntity> lst, int server) {
        if (type == aGet) {
            String top = JCache.getInstance().getValue(":TOPSTARMAP:"+server+":");
            if (top == null) {
                lstUserStar = Database2.getList("user", Arrays.asList("server",""+server), "order by star_map desc , username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserStar);
                JCache.getInstance().setValue(":TOPSTARMAP:", top, 5 * 60);
            } else if (lstUserStar == null || lstUserStar.size() == 0) {
                lstUserStar = Database2.getList("user", Arrays.asList("server",""+server), "order by star_map desc, exp desc , username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserStar);
                JCache.getInstance().setValue(":TOPSTARMAP:", top, 5 * 60);
            }
            return lstUserStar;
        } else {
            lstUserStar = lst;
            return new ArrayList<>();
        }
    }

    public synchronized static List<UserEntity> entryLstUserVip(int type, List<UserEntity> lst, int server) {
        if (type == aGet) {
            String top = JCache.getInstance().getValue(":TOPGEMNAP:"+server+":");
//            System.out.println("top--->"+top);
            if (top == null) {
                lstUserVip = Database2.getList("user", Arrays.asList("server",""+server), "order by gem_nap desc , username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserVip);
                JCache.getInstance().setValue(":TOPGEMNAP:", top, 5 * 60);
            } else if (lstUserVip == null || lstUserVip.size() == 0) {
                lstUserVip = Database2.getList("user", Arrays.asList("server",""+server), "order by gem_nap desc , username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserVip);
                JCache.getInstance().setValue(":TOPGEMNAP:", top, 5 * 60);
            }
            return lstUserVip;
        } else {
            lstUserVip = lst;
            return new ArrayList<>();
        }
    }

    public synchronized static List<UserEntity> entryLstUserWin(int type, List<UserEntity> lst, int server) {
        if (type == aGet) {
            String top = JCache.getInstance().getValue(":TOPWIN:"+server+":");
            if (top == null) {
                lstUserWin = Database2.getList("user", Arrays.asList("server",""+server), "order by win desc , username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserWin);
                JCache.getInstance().setValue(":TOPWIN:", top, 5 * 60);
            } else if (lstUserWin == null || lstUserWin.size() == 0) {
                lstUserWin = Database2.getList("user", Arrays.asList("server",""+server), "order by win desc, exp desc , username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserWin);
                JCache.getInstance().setValue(":TOPWIN:", top, 5 * 60);
            }
            return lstUserWin;
        } else {
            lstUserWin = lst;
            return new ArrayList<>();
        }
    }

    public synchronized static List<UserEntity> entryLstUserLevel(int type, List<UserEntity> lst, int server) {
        if (type == aGet) {
            String top = JCache.getInstance().getValue(":TOPLEVEL:"+server+":");
//            System.out.println("top--->"+top);
            if (top == null) {
                lstUserLevel = Database2.getList("user", Arrays.asList("server",""+server), "order by level desc, exp desc , username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserLevel);
                JCache.getInstance().setValue(":TOPLEVEL:", top, 5 * 60);
            } else if (lstUserLevel == null || lstUserLevel.size() == 0) {
                lstUserLevel = Database2.getList("user", Arrays.asList("server",""+server), "order by level desc, exp desc , username asc limit 1000", UserEntity.class);
                top = new Gson().toJson(lstUserLevel);
                JCache.getInstance().setValue(":TOPLEVEL:", top, 5 * 60);
            }
            return lstUserLevel;
        } else {
            lstUserLevel = lst;
            return new ArrayList<>();
        }
    }
    //    public static List<UserEntity> lstUserTrophy1000 = new ArrayList<UserEntity>();


    public static Calendar curDate = Calendar.getInstance();
    public static Calendar curDateTop = Calendar.getInstance();
    public static int top100trophy = 0;

    public static Map<Integer, JSONObject> mapRankTrophy = new HashMap<Integer, JSONObject>();
    public static JSONArray eventTowerBonus = JSONArray.fromObject("[]");

    // Episode, tro giup lien minh, thach dau, quoc chien, loi dai, cup thach dau, tan cong boss

    /**
     * Lay su kien
     *
     * @Theo level
     * @Theo thời gian của các sự kiện khác
     * - Kingdom: 11, 16, 21
     * - Boss: 13, 22
     * - Arena: 10, 20
     */
    public static MissionObject getMission(UserEntity user, int index) {
        int numberCompleted = 0;
        Random ran = new Random();
        JSONArray aMissionable = new JSONArray();
//        for (int i = 0; i < aMission.size(); i++) {
//            JSONObject mapObject = aMission.getJSONObject(i);
//            if (mapObject.getInt("status") >= 1) {
//                aMissionable.add(mapObject);
//            }
//
//        }
        if (aMission.getJSONObject(index).getInt("status") >= 1) {
            JSONObject obj = aMission.getJSONObject(index);
            if (obj.getInt("level") > user.getLevel()) {
                return null;
            }
            if (obj.getInt("id") == MissionObject.TOTALMISSION) {
                numberCompleted = 0;

                for (int i = 0; i < aMission.size(); i++) {
                    if (aMission.getJSONObject(i).getInt("level") <= user.getLevel() && aMission.getJSONObject(i).getInt("status") >= 1 && aMission.getJSONObject(i).getInt("id") != MissionObject.TOTALMISSION) {
                        numberCompleted++;
                    }
                }
            } else {
                numberCompleted = obj.getInt("min") + ran.nextInt(obj.getInt("range"));
            }
            return new MissionObject(obj.getInt("id"), obj.getString("desc"), numberCompleted, obj.getString("awardailyMission"));
        } else {
            return null;
        }
    }

    public static void addMission(UserEntity user, int misionType, int value) {
        List<MissionObject> lstMission = mapMision.get(user.getId());
        if (lstMission == null) {
            dailyListMission(user);
            lstMission = mapMision.get(user.getId());
        }
        if (lstMission != null) {
            for (int i = 0; i < lstMission.size(); i++) {
                if (lstMission.get(i).getMissionType() == misionType) {
                    lstMission.get(i).increaseValue(value);
                    if (lstMission.get(i).isComplete() && lstMission.get(i).getNotifyStatus() != 3) {
                        lstMission.get(i).isNotifyComplete();
                        Util.sendProtoData(Online.getChannel(user.getId()), CommonProto.getCommonLongVectorProto(Arrays.asList(1l), Arrays.asList("Bạn đã hoàn thành 1 nhiệm vụ")), IAction
                                .NOTIFY_MISSION, System
                                .currentTimeMillis());
                        List<Long> vLong = new ArrayList<>();
                        vLong.add((long) CfgNotify.MISSION);
                        vLong.add(1l);
                        Util.sendProtoData(Online.getChannel(user.getId()), CommonProto.getCommonLongVectorProto(vLong, null), IAction.LOGIN_NOTIFY, System.currentTimeMillis());
                    }
                    break;
                }
            }
        }
    }

    public static List<MissionObject> dailyListMission(UserEntity user) {
        if (!sdf.format(curDate.getTime()).equals(sdf.format(Calendar.getInstance().getTime()))) {
            curDate = Calendar.getInstance();
            mapMision.clear();
        }
        if (mapMision.get(user.getId()) == null) {
            List<MissionObject> lstMission = new ArrayList<MissionObject>();
            List<Integer> id = new ArrayList<Integer>();
            JSONArray arrBoss = new JSONArray();
            for (int i = 0; i < aMission.size(); i++) {
                JSONObject obj = aMission.getJSONObject(i);
                if (obj.getInt("status") == 2) {
                    arrBoss.add(obj);
                    continue;
                }
                if (obj.getInt("status") == 1) {
                    MissionObject ms = getMission(user, i);
                    if (ms != null) {
                        lstMission.add(ms);
                        id.add(ms.getMissionType());
                    }
                }
            }

            if (arrBoss != null && arrBoss.size() > 0) {
                int i = arrBoss.getJSONObject(new Random().nextInt(arrBoss.size())).getInt("id") - 1;
                MissionObject ms = getMission(user, i);
                if (ms != null) {
                    lstMission.add(ms);
                    id.add(ms.getMissionType());
                }
            }

            mapMision.put(user.getId(), lstMission);
            return lstMission;
        } else {
            List<MissionObject> lstMission = mapMision.get(user.getId());
            return lstMission;
        }
    }

    public static void dailyRankTrophy() {
        int minute = Calendar.getInstance().get(Calendar.MINUTE);
        if ((!sdfHH.format(curDateTop.getTime()).equals(sdfHH.format(Calendar.getInstance().getTime())) || lstHm == null || lstHm.size() == 0) && minute >= 5) {
            UserDAO uDao = new UserDAO();
            lstHm = uDao.getListTopTrophy(sdfY.format(Calendar.getInstance().getTime()));
            if (lstHm != null && lstHm.size() > 0) {
                curDateTop = Calendar.getInstance();
            }
        }
    }


    public static int TopMyRank(long id, List<UserEntity> lst) {

        for (int i = 0; i < lst.size(); i++) {
            if (id == lst.get(i).getId()) {
                return i + 1;
            }
        }
        return 1001;
    }


    public String convertDate(Date date) {
        return sdf.format(date);
    }

    public static void loadConfig(String value) {
        JSONObject obj = JSONObject.fromObject(value);

        feeReject = obj.getInt("feeReject");
        aMission = obj.getJSONArray("missionBoom");
        aAward = obj.getJSONArray("awardailyMission");
        JSONArray troPhy = obj.getJSONArray("rankTrophy");
        mapRankTrophy.clear();
        for (int i = 0; i < troPhy.size(); i++) {
            JSONObject objT = troPhy.getJSONObject(i);
            mapRankTrophy.put(objT.getInt("id"), objT);
        }
        eventTowerBonus = obj.getJSONArray("eventTowerBonus");
    }
}
