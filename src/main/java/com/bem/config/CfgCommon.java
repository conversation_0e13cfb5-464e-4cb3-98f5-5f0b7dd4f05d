/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.bem.config;

import com.google.gson.Gson;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgCommon {

    public static JSONObject json;
    public static DataConfig config;
//    public static String gameDb = "boom_s" + ((CfgServer.SERVER_ID == 123 || CfgServer.SERVER_ID == 300) ? 1 : CfgServer.SERVER_ID);

//    static {
//        switch (CfgServer.SERVER_ID) {
//            case 124:
//                gameDb = "boom_s1";
//                break;
//            case 125:
//                gameDb = "boom_s2";
//                break;
//        }
//    }

    public static String mainDb = "boom.";
    public static String tableConfig = "config", tableConfigLocal = "config_local";
    public static String tableMap = "map";

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public int maxMail, spamChat, limitRank, rankLevelRequired, zombie;
        public int petHorse, petLucifer, killIdle;
        public float petLuciferTimeAnimation, petUnicorn;
        public String zombieMsg;
        public NewUser newUser;
        public Battle battle;
        public Chat chat;
        public int[] rankTrophyGroup;
    }

    public class NewUser {
        public int trophy, userAvatar, gold, gem, energy;
    }

    public class Battle {
        public int waitReady;
        public int[] hackSpeed;
        public float glideDistance;
        public float[] baseSpeed;
    }

    public class Chat {
        public int[] length;
        public int spam;
    }
}
