package com.bem.config;


import com.bem.boom.BoomConfig;
import com.bem.boom.object.AchievementInfo;
import com.bem.dao.mapping.UserDataEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.UserFriendEntity;
import com.bem.dao.mapping.UserMessageEntity;
import com.bem.object.UserInfo;
import com.bem.util.Util;
import com.cache.JCache;
import com.handler.FriendHandler;
import grep.database.Database2;
import net.sf.json.JSONArray;

import java.util.*;

public class CfgNotify {
    public static final int DIEMDANH = 1, EVENT = 2, CHEST = 3, MAIL = 4, FREINDS = 5, NOTIFYENERGY = 6, MISSION = 7;

    public static void diemdanhNotify(UserInfo user) {
        List<Long> vLong = new ArrayList<Long>();
        if (user.getUData().getMyAttendance().status == CfgDiemdanh.COTHENHAN) {
            vLong.add((long) CfgNotify.DIEMDANH);
            vLong.add(1l);
        }
    }

    public static List<Long> mailNotify(long userId) {
        List<Long> vLong = new ArrayList<Long>();
        List<UserMessageEntity> aMsg = Database2.getList("user_message", Arrays.asList("user_id", String.valueOf(userId)), " order by receive desc,  date_created desc limit 0,10", UserMessageEntity.class);
        int cout = 0;
        for (int i = 0; i < aMsg.size(); i++) {
            if (aMsg.get(i).getReceive() == 0) {
                cout++;
            }
        }
        if (cout > 0) {
            vLong.add((long) CfgNotify.MAIL);
            vLong.add((long) cout);
        }
        return vLong;
    }

//    public static List<Long> MissionNotify(UserInfo user) {
//        List<Long> vLong = new ArrayList<Long>();
//        try {
//
//            List<MissionObject> lstMission = CfgNewMission.dailyListMission(user.getDbUser());
//
////        // add cho mission hoan thanh tat ca mission
//            int count = 0;
//            for (int i = 0; i < lstMission.size(); i++) {
//                if (lstMission.get(i).getNotifyStatus() > 0 && lstMission.get(i).getMissionType() != MissionObject.TOTALMISSION) {
//                    count++;
//                }
//            }
//            for (int i = 0; i < lstMission.size(); i++) {
//                if (lstMission.get(i).getMissionType() == MissionObject.TOTALMISSION&&lstMission.get(i).getNotifyStatus()<=0) {
//                    lstMission.get(i).increaseValue(count);
//                    break;
//                }
//            }
//            for (int i = 0; i < lstMission.size(); i++) {
//              if(lstMission.get(i).getNotifyStatus()>0){
//                  vLong.add((long) CfgNotify.MISSION);
//                  vLong.add(1l);
//                  break;
//              }
//            }
//
//            return vLong;
//        } catch (Exception ex) {
//
//        }
//        return vLong;
//
//    }


    public static List<Long> eventNotify(UserEntity user, UserDataEntity uData) {
        try {
            List<Long> vLong = new ArrayList<Long>();
            JSONArray aChieveStatus = JSONArray.fromObject(user.getAchievementStatus());
            JSONArray aChieveRecieve = JSONArray.fromObject(user.getAchievementReceive());
            for (int i = 0; i < CfgAchievement.aAch.get(user.getServer()).size(); i++) {
                if (CfgAchievement.aAch.get(user.getServer()).get(i).getStatus() <= 0 || (CfgAchievement.aAch.get(user.getServer()).get(i).getStatus() == 1 && !CfgAchievement.aAch.get(user.getServer()).get(i).inTime())) {
                    continue;
                }
                if (CfgAchievement.aAch.get(user.getServer()).get(i).getId() == CfgAchievement.ACHIEVEMENT_ENERGY) {
                    String rw = CfgAchievement.getEnergy(user);
                    if (rw == null) {
                        vLong.add((long) CfgNotify.EVENT);
                        vLong.add(1l);
//                        System.out.println("aaaaaaaaaa");
                        break;
                    }
                } else {
                    UserInfo uInfor = new UserInfo();
                    uInfor.setDbUser(user);
                    uInfor.setUData(uData);
                    long curentNumber = CfgAchievement.getCurentNumberAchievement(uInfor,CfgAchievement.aAch.get(user.getServer()).get(i),i);
//                    int curentNumber = aChieveStatus.getInt(i);
//                    if (CfgAchievement.aAch.get(i).getId() == CfgAchievement.WIN_MAP) {
//                        curentNumber = 0;
//                        for (int j = 0; j < uData.getMap().getNormal().size(); j++) {
//                            if (uData.getMap().getNormal().get(j) > 0) {
//                                curentNumber++;
//                            }
//                        }
//                    } else if (CfgAchievement.aAch.get(i).getId() == CfgAchievement.COLLECT_STAR) {
//                        curentNumber = uData.getMap().getStar() ;
////                        for (int j = 0; j < uData.getMap().getNormal().size(); j++) {
////                            curentNumber +=uData.getMap().getNormal().get(j);
////                        }
////                        for (int j = 0; j < uData.getMap().getInsane().size(); j++) {
////                            curentNumber +=uData.getMap().getInsane().get(j);
////                        }
//                    } else if(CfgAchievement.aAch.get(i).getId() == CfgAchievement.VIP){
//                        curentNumber = user.getVip();
//                    }else if (CfgAchievement.aAch.get(i).getId() == CfgAchievement.NAP){
//                        curentNumber = user.getGetEventNap();
//                    }else if (CfgAchievement.aAch.get(i).getId() == CfgAchievement.LIENTHANG) {
//                        curentNumber = user.getMaxwin();
//                    }
                    AchievementInfo aInf = CfgAchievement.aAch.get(user.getServer()).get(i);
                    if (aInf.getStatus() > 0) {
                        try {
//                            UserInfo uIn = new UserInfo();
//                            uIn.setDbUser(user);
//                            uIn.setUData(uData);
                            int recieve = CfgAchievement.getRecieveNumberAchievement(uInfor,CfgAchievement.aAch.get(user.getServer()).get(i),i);
                            if(CfgAchievement.aAch.get(user.getServer()).get(i).getId() != CfgAchievement.VIP) {
                                if (curentNumber >= aInf.getARequired()[recieve]) {
//                                    System.out.println(CfgAchievement.aAch.get(i).getName());
                                    vLong.add((long) CfgNotify.EVENT);
                                    vLong.add(1l);
//                                    System.out.println("bbbbbb");

                                    break;
                                }
                            }else {
                                if (curentNumber >= CfgAchievement.aAch.get(user.getServer()).get(i).getARequired()[user.checkHasAwardVip()+1]){
                                    vLong.add((long) CfgNotify.EVENT);
                                    vLong.add(1l);
//                                    System.out.println("CCCCCCCC");

                                    break;
                                }
                            }
                        } catch (Exception ex) {
                        }
                    }
                }
            }
            int hour = Integer.parseInt(Util.HourFormatTime(new Date()));
            AchievementInfo achi = CfgAchievement.aAch.get(user.getServer()).get(CfgAchievement.ACHIEVEMENT_ENERGY);
            if (hour != achi.getTime1() && hour != achi.getTime2() && hour != achi.getTime3()) {
                Calendar ca = Calendar.getInstance();
                int h = ca.getTime().getHours();
                int m = ca.getTime().getMinutes();
                int s = ca.getTime().getSeconds();
                long delTime = 0;
                if (hour < achi.getTime1()) {
                    delTime = (7 - h) * 60 * 60 + (60 - m) * 60 + (60 - s);

                } else if (hour < achi.getTime2()) {
                    delTime = (15 - h) * 60 * 60 + (60 - m) * 60 + (60 - s);

                } else if (hour < achi.getTime3()) {
                    delTime = (20 - h) * 60 * 60 + (60 - m) * 60 + (60 - s);

                }
                if (hour > achi.getTime3() && hour < 23) {
                    delTime = (23 - h + 8) * 60 * 60 + (60 - m) * 60 + (60 - s);
                }
                if (delTime > 0) {
                    vLong.add((long) CfgNotify.NOTIFYENERGY);
                    vLong.add(delTime);
                }
            }
            return vLong;
        } catch (Exception ex) {

        }
        return new ArrayList<Long>();

    }

    public static void chestNotify(UserInfo user) {

    }

    public static void freindsNotify(UserInfo user) {

    }

    public static List<Long> loginNotify(UserInfo user) {
        List<Long> vLong = new ArrayList<Long>();
        if (user.getUData().getMyAttendance().status == CfgDiemdanh.COTHENHAN) {
            vLong.add((long) CfgNotify.DIEMDANH);
            vLong.add(1l);
        }

        long timeGoldChest = user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GOLD_CHEST);
        long timeGemChest = user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GEM_CHEST);
        if (timeGoldChest > 0) {
            timeGoldChest = GameCfgChest.data.get(0).timeFree - ((System.currentTimeMillis() - timeGoldChest) / 1000);
        } else {
//            timeGoldChest = System.currentTimeMillis();
            timeGoldChest = 0;
        }
        if (timeGemChest > 0) {
            timeGemChest = GameCfgChest.data.get(1).timeFree - ((System.currentTimeMillis() - timeGemChest) / 1000);
        } else {
//            timeGemChest = System.currentTimeMillis();
            timeGemChest = 0;
        }

        int countRemain = GameCfgChest.data.get(0).maxDay;
        try {
            Calendar ca = Calendar.getInstance();
            countRemain = Integer.parseInt(JCache.getInstance().getValue(Util.formatDateDAy(ca.getTime()) + user.getUsername() + ":countdaygoldchest"));
        } catch (Exception ex) {
        }
        if (timeGemChest <= 0 || (timeGoldChest <= 0 && countRemain > 0)) {
            vLong.add((long) CfgNotify.CHEST);
            vLong.add(1l);
        }

        List<UserMessageEntity> aMsg = Database2.getList("user_message", Arrays.asList("user_id", String.valueOf(user.getId())), " order by receive desc,  date_created desc limit 0,10", UserMessageEntity.class);
        int cout = 0;
        for (int i = 0; i < aMsg.size(); i++) {
            if (aMsg.get(i).getReceive() == 0) {
                cout++;
            }
        }
        if (cout > 0) {
            vLong.add((long) CfgNotify.MAIL);
            vLong.add((long) cout);
        }
        List<UserFriendEntity> aUserEntity = new FriendHandler().dbListFriendRequest(user.getId());
        if (aUserEntity.size() > 0) {
            vLong.add((long) CfgNotify.FREINDS);
            vLong.add(1l);
        }

        vLong.addAll(eventNotify(user.getDbUser(),user.getUData()));
//        System.out.println(vLong);
        return vLong;
    }
}