package com.handler;

import com.bem.config.CfgBot;
import com.bem.config.CfgCommon;
import com.bem.monitor.Bonus;
import com.bem.monitor.BotMonitor;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.UserInfo;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MainCache;
import com.google.protobuf.AbstractMessage;
import com.k2tek.Constans;
import com.k2tek.Xerver;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.helper.DateTime;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.group.ChannelGroup;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 12/11/2014.
 */
public class MonitorHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        mGameHandler.put(INDEX, getInstance());
        mGameHandler.put(RELOAD_CONFIG, getInstance());
    }

    static MonitorHandler instance;

    public static MonitorHandler getInstance() {
        if (instance == null) {
            instance = new MonitorHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new MonitorHandler();
    }

    @Override
    public void doSubAction() {
        try {
            if (srcRequest == null || srcRequest.length == 0) return;
            String data = new String(srcRequest, "UTF-8");
            if (!data.contains("@")) return;
            service = Integer.parseInt(data.substring(0, data.indexOf("@")));
            data = data.substring(data.indexOf("@") + 1);
            switch (service) {
                case INDEX:
                    index();
                    break;
                case RELOAD_CONFIG:
                    reloadConfig();
                    break;
                case UPDATE_BATTLE_RESULT:
                    updateBattleResult(data);
                    break;
                case SEND_MSG_ALL:
                    sendMsgAll(data);
                    break;
                case SEND_MSG_USER:
                    sendMsgUser(data);
                    break;
                case SERVER_STATUS:
                    serverStatus();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void serverStatus() {
        sendStrMessage(channel, service, "1");
    }

    void sendMsgAll(String data) {
        JSONObject msg = JSONObject.fromObject(data);
        int type = msg.getInt("type");
        if (type == 1) {
            type = MSG_TOAST;
        } else if (type == 2) {
            type = MSG_POPUP;
        } else {
            type = MSG_SLIDE;
        }
        sendRawData(Online.authChannels, getResponseProto(CommonProto.getCommonVectorProto(null, Arrays.asList(msg.getString("msg"))), type));
        sendStrMessage(channel, service, "1");
    }

    void sendMsgUser(String data) {
    }


    void updateBattleResult(String data) {
        JSONArray arr = JSONArray.fromObject(data);
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            long userId = obj.getLong("id");
            boolean endgame = obj.getBoolean("endgame");
            JSONArray bonus = obj.getJSONArray("result");
            UserInfo user = UserMonitor.getUser(userId);
            if (user != null) {
                user.setEndgame(endgame);
                List<Long> bonusResult = Bonus.receiveListItem(user, bonus, "");
                if (!bonusResult.isEmpty()) {
                    user.getSyncBonus().add(bonusResult);
                }
            }
        }
    }

    void reloadConfig() {
        Xerver.initGameConfig();
        JSONObject obj = new JSONObject();
        obj.put("reloadConfig", true);
        sendStrMessage(channel, service, obj.toString());
    }

    void index() {
        JSONObject obj = new JSONObject();

        obj.put("glideDistance", CfgCommon.config.battle.glideDistance);

        JSONObject hackSpeed = new JSONObject();
        hackSpeed.put("enable", CfgCommon.config.battle.hackSpeed[0]);
        hackSpeed.put("time", CfgCommon.config.battle.hackSpeed[1]);
        hackSpeed.put("maxInput", CfgCommon.config.battle.hackSpeed[2]);
        obj.put("hackSpeed", hackSpeed);

        JSONObject bot = new JSONObject();
        bot.put("idleBot", BotMonitor.idleBot.size());
        bot.put("busyBot", BotMonitor.busyBot.size());
        bot.put("timeWait", CfgBot.config.timeWait);
        bot.put("limitRank", CfgCommon.config.limitRank);
        obj.put("bot", bot);

        JSONObject ccu = new JSONObject();
        Calendar ca = Calendar.getInstance();
        String hours = String.valueOf(ca.get(Calendar.HOUR_OF_DAY));
        String dateCreated = DateTime.getDateyyyyMMddCross(ca.getTime());
        String online = "0";// Database.getUniqueColumn(CfgCommon.mainDb + ".cache_ccu", Arrays.asList("date_created", dateCreated, "hours", hours), "online");
        ccu.put("online", online == null ? "not cache" : Integer.parseInt(online));
        ccu.put("rankCCU", MainCache.getInstance().rankCCU);
        ccu.put("bossCCU", MainCache.getInstance().bossCCU);
        obj.put("ccu", ccu);

        sendStrMessage(channel, service, obj.toString());
//        JSONObject obj = new JSONObject();
//        obj.put("task", TaskMonitor.monitorValue());
//        obj.put("searchQueue", TeamMatcher.monitorValue());
//        JSONArray arr = new JSONArray();
//        arr.add(Online.mChannel.size());
//        Object[] aObj = Online.mChannel.values().toArray();
//        for (Object o : aObj) {
//            Channel ch = (Channel) o;
//            UserInfo infor = (UserInfo) ChUtil.get(ch, Constans.KEY_USER);
//            if (infor != null) {
//                JSONObject tmp = new JSONObject();
//                tmp.put("user", infor.getUsername());
//                tmp.put("name", infor.getDbUser().getName());
//                TeamObject team = (TeamObject) ChUtil.get(ch, Constans.KEY_USER_TEAM);
//                if (team != null) {
//                    tmp.put("status", team.status == 0 ? "none" : (team.status == 1 ? "search" : "play"));
//                } else {
//                    tmp.put("status", "none");
//                }
//                arr.add(tmp);
//            }
//        }
//        obj.put("online", arr);
//        sendStrMessage(channel, service, obj.toString());
    }

    public byte[] getResponseProto(AbstractMessage data, int service) {
        GGProto.ResponseData.Builder builder = GGProto.ResponseData.newBuilder();
        GGProto.Action.Builder action = GGProto.Action.newBuilder();
        action.setService(service);
        if (data != null) {
            action.setData(data.toByteString());
        }
        builder.addActions(action);
        return builder.build().toByteArray();
    }

    public void sendRawData(ChannelGroup channels, byte[] data) {
        if (channels != null) {
            try {
                data = data == null ? new byte[0] : data;
                ByteBuf buffer = Unpooled.buffer();
                buffer.writeBytes(Constans.OUT_GAME_MAGIC.getBytes()); // K2
                buffer.writeInt(data.length);
                buffer.writeBytes(data);
                channels.writeAndFlush(buffer).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
            } catch (Exception ex) {
            }
        }
    }
}
