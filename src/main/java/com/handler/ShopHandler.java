package com.handler;

import com.bem.boom.Resources;
import com.bem.boom.map.MapResource;
import com.bem.boom.object.MissionObject;
import com.bem.boom.object.MyAvatar;
import com.bem.config.*;
import com.bem.config.lang.Lang;
import com.bem.dao.IapDAO;
import com.bem.dao.SystemDAO;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.*;
import com.bem.monitor.Bonus;
import com.bem.object.*;
import com.bem.util.Actions;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.gson.JsonObject;
import com.handler.game.BossGlobal;
import com.handler.game.GoldHunting;
import com.handler.game.MaterialHunting;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.GoogleAndroidAPI;
import grep.helper.GsonUtil;
import net.sf.json.JSONArray;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class ShopHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        List<Integer> services = Arrays.asList(STORAGE_LIST, EXCHANGE_GEM, EXCHANGE_GEM_STATUS, TOP_UP_STATUS, TOP_UP_CARD, TOP_UP_IAP, TOP_UP_HISTORY, BUY_ITEM, BUY_ITEM_OLD, SHOP_SPECIAL, SHOP_REFRESH, SALE_ITEM, USE_ITEM, LIST_CHOSE_ITEM, MONTHLY_CARD_STATUS, MONTHLY_CARD_RECEIVED, CHOOSE_BATTLE_ITEM);
        services.forEach(service -> mGameHandler.put(service, getInstance()));
    }

    static ShopHandler instance;

    public static ShopHandler getInstance() {
        if (instance == null) {
            instance = new ShopHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new ShopHandler();
    }

    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case BUY_ITEM:
                    buyItem();
                    break;
                case BUY_ITEM_OLD:
                    buyItemOld();
                    break;
                case SHOP_SPECIAL:
                    shopSpecial();
                    break;
                case SHOP_REFRESH:
                    shopRefreshNew();
                    break;
                case STORAGE_LIST:
                    storageList();
                    break;
                case USE_ITEM:
                    useItem();
                    break;
                case LIST_CHOSE_ITEM:
                    listChoseItem();
                    break;
                case EXCHANGE_GEM:
                    exchange();
                    break;
                case EXCHANGE_GEM_STATUS:
                    exchangeStatus();
                    break;
                case TOP_UP_STATUS:
                    topUpstatus();
                    break;
                case TOP_UP_CARD:
                    topUpCard();
                    break;
                case TOP_UP_IAP:
                    topUpIap();
                    break;
                case TOP_UP_HISTORY:
                    topUpHistory();
                    break;
                case SALE_ITEM:
                    saleItem();
                    break;
                case MONTHLY_CARD_STATUS:
                    monthlyCardStatus();
                    break;
                case MONTHLY_CARD_RECEIVED:
                    monthlyCardReceived();
                    break;
                case CHOOSE_BATTLE_ITEM:
                    chooseBattleItem();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void chooseBattleItem() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        System.out.println("aLong = " + aLong);
        UserInt uInt = user.getUData().getUInt();
        int battleItem1 = uInt.getValue(UserInt.BATTLE_ITEM_1), battleItem2 = uInt.getValue(UserInt.BATTLE_ITEM_2);
        uInt.setValue(UserInt.BATTLE_ITEM_1, 0);
        uInt.setValue(UserInt.BATTLE_ITEM_2, 0);
        if (aLong.size() > 2 && aLong.get(0) == aLong.get(1)) {
            addErrMessage("Không được mang hai vật phẩm giống nhau");
            return;
        }
        if (aLong.size() >= 1 && CfgGlobal.config.mBattleItem.containsKey(aLong.get(0).intValue())) {
            uInt.setValue(UserInt.BATTLE_ITEM_1, aLong.get(0).intValue());
        }
        if (aLong.size() >= 2 && CfgGlobal.config.mBattleItem.containsKey(aLong.get(1).intValue())) {
            uInt.setValue(UserInt.BATTLE_ITEM_2, aLong.get(1).intValue());
        }
        if (uInt.update(user.getId())) {
            System.out.println(aLong);
            addResponse(CommonProto.getCommonLongVectorProto(aLong, null));
        } else {
            uInt.setValue(UserInt.BATTLE_ITEM_1, battleItem1);
            uInt.setValue(UserInt.BATTLE_ITEM_2, battleItem2);
            addErrMessage();
        }
    }

    void monthlyCardStatus() {
        MonthlyCardEntity[] cards = null;// dbGetMonthlyCard(user.getUsername(), user.getDbUser().getServer());
        MonthlyCardReceivedEntity[] cardReceives = null;// dbGetMonthlyCardReceived(user.getId(), user.getDbUser().getServer());
        if (cards == null || cardReceives == null) {
            GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
            builder.addAVector(CommonProto.getCommonVectorProto(Arrays.asList(0, 100, 50000, 0, 1), null)); // đóng/mở, mua/chưa mua, nhận/đã nhận
            builder.addAVector(CommonProto.getCommonVectorProto(Arrays.asList(0, 200, 100000, 0, 1), null)); // đóng/mở, mua/chưa mua, nhận/đã nhận
            addResponse(builder.build());
            return;
        }
        boolean buyCard50 = !(cards[0] == null || cards[0].getToTime().before(new Date()));
        boolean buyCard100 = !(cards[1] == null || cards[1].getToTime().before(new Date()));

        System.out.println(cardReceives[0] + " " + cardReceives[1]);
        boolean received50 = cardReceives[0] != null;
        boolean received100 = cardReceives[1] != null;

        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        builder.addAVector(CommonProto.getCommonVectorProto(Arrays.asList(1, 100, 50000, buyCard50 ? 1 : 0, received50 ? 1 : 0), null)); // đóng/mở, mua/chưa mua, nhận/đã nhận
        builder.addAVector(CommonProto.getCommonVectorProto(Arrays.asList(1, 200, 100000, buyCard100 ? 1 : 0, received100 ? 1 : 0), null)); // đóng/mở, mua/chưa mua, nhận/đã nhận
        addResponse(builder.build());
    }

    void monthlyCardReceived() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int cardType = aLong.isEmpty() ? 0 : aLong.get(0).intValue();
        MonthlyCardEntity[] cards = dbGetMonthlyCard(user.getUsername(), user.getDbUser().getServer());
        MonthlyCardReceivedEntity[] cardReceives = dbGetMonthlyCardReceived(user.getId(), user.getDbUser().getServer());
        if (cards == null || cardReceives == null) {
            addErrMessage();
            return;
        }
        boolean buyCard = !(cards[cardType] == null || cards[cardType].getToTime().before(new Date()));
        if (!buyCard) {
            addErrMessage("Bạn chưa mua loại thẻ này");
            return;
        }
        if (cardReceives[cardType] != null) {
            addErrMessage("Bạn đã nhận kim cương hôm nay rồi");
            return;
        }
        MonthlyCardReceivedEntity cardReceivedEntity = new MonthlyCardReceivedEntity(user);
        cardReceivedEntity.setCardType(cards[cardType].getType());
        if (dbSaveMonthlyCard(cardReceivedEntity)) {
            addResponse(CommonProto.getCommonLongVectorProto(Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.GEM, cardType == 0 ? 100 : 200), "monthly_card"), null));
        }
    }

    void saleItem() {

        List<Long> intPut = CommonProto.parseCommonVector(srcRequest).getANumberList();
        if (intPut.size() % 3 != 0) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        if (1 == 1) {
            addErrMessage("Chức năng đang bảo trì!");
            return;
        }
        String decs = "";
        debug("input--->" + intPut.toString());
        List<Long> aLong = new ArrayList<Long>();
        long totalGold = 0;
        List<String> sql = new ArrayList<String>();
        for (int i = 0; i < intPut.size() - 2; i = i + 3) {
            int type = intPut.get(i).intValue();
            int id = intPut.get(i + 1).intValue();
            long number = intPut.get(i + 2);
            if (number < 0) {
                addErrMessage(getLang(Lang.err_param));
                return;
            }
            if (number == 0) {
                continue;
            }
            switch (type) {
                case Bonus.BOMB:// id rieng
                    UserBombEntity bomb = new UserBombEntity();

                    bomb = user.getRes().getMBomb().get(id);
                    //                    decs += "bomb; ";
                    //                    decs += "bomb_id: " + id + "; ";
                    //                    decs += "bomb_image: " + bomb.getBombId() + "; ";
                    //                    debug("id----->" + id);
                    //                    if (bomb == null) {
                    //                        debug("nullllllllllllllll");
                    //                    }
                    if (bomb == null || Resources.getBomb(bomb.getBombId()) == null) {
                        addErrMessage(getLang(Lang.bomb_not_own));
                        return;
                    }
                    if (user.getMAvatar().getIdBomb() == id) {
                        addErrMessage(getLang(Lang.not_sale_wearing_item));
                        return;
                    }
                    decs += "bomb; ";
                    decs += "bomb_id: " + id + "; ";
                    decs += "bomb_image: " + bomb.getBombId() + "; ";
                    sql.add(Database2.getDeleteQuery("user_bomb", Arrays.asList("id", String.valueOf(id))));
                    totalGold += Resources.getGoldSale(type, Resources.getBomb(bomb.getBombId()).getRank());
                    user.getRes().getMBomb().remove(id);
                    //                    user.getRes().getBombs().remove(bomb);
                    break;
                case Bonus.AVATAR_FRAGMENT://id res
                    if (user.getRes().getMAvatar().get(id) == null || user.getRes().getMAvatar().get(id).getFragment() < number || Resources.getAvatar((int) id) == null) {
                        addErrMessage(getLang(Lang.err_not_enough));
                        return;
                    }
                    debug("number---->" + number);
                    sql.add(Database2.getUpdateQuery("user_avatar", Arrays.asList("fragment", String.valueOf(user.getRes().getMAvatar().get(id).getFragment() - number)), Arrays.asList("user_id", String.valueOf(user.getId()), "avatar_id", String.valueOf(id))));
                    user.getRes().getMAvatar().get(id).setFragment(user.getRes().getMAvatar().get(id).getFragment() - (int) number);
                    //                    for (int j = 0; j <  user.getRes().getAvatars().size(); j++) {
                    //                        if(user.getRes().getAvatars().get(j).getAvatarId()==id){
                    //                            user.getRes().getAvatars().get(j).setFragment(user.getRes().getAvatars().get(j).getFragment()-(int)number);
                    //                            break;
                    //                        }
                    //                    }
                    decs += "avatar_fragment; ";
                    decs += "avatar_id: " + id + "; ";
                    decs += "numbersale: " + number + "; ";
                    decs += "numberremain: " + user.getRes().getMAvatar().get(id).getFragment() + "; ";

                    totalGold += Resources.getGoldSale(type, Resources.getAvatar((int) id).getRank()) * number;

                    break;
                case Bonus.PET_FRAGMENT://id res
                    if (user.getRes().getMPet().get(id) == null || user.getRes().getMPet().get(id).getFragment() < number || Resources.getPet((int) id) == null) {
                        addErrMessage(getLang(Lang.err_not_enough));
                        return;
                    }
                    //                    sql.add(Database.getUpdateQuery("user_pet", Arrays.asList("level", String.valueOf(1), "exp", String.valueOf(0), "fragment", String.valueOf(user.getRes().getMPet().get(id).getFragment() - number)), Arrays.asList("user_id", String.valueOf(user.getId()), "pet_id", String.valueOf(id))));
                    sql.add(Database2.getUpdateQuery("user_pet", Arrays.asList("fragment", String.valueOf(user.getRes().getMPet().get(id).getFragment() - number)), Arrays.asList("user_id", String.valueOf(user.getId()), "pet_id", String.valueOf(id))));
                    user.getRes().getMPet().get(id).setFragment(user.getRes().getMPet().get(id).getFragment() - (int) number);
                    //                    for (int j = 0; j <  user.getRes().getPets().size(); j++) {
                    //                        if(user.getRes().getPets().get(j).getPetId()==id){
                    //                            user.getRes().getPets().get(j).setFragment(user.getRes().getPets().get(j).getFragment()-(int)number);
                    //                            break;
                    //                        }
                    //                    }
                    decs += "pet_fragment; ";
                    decs += "pet_id: " + id + "; ";
                    decs += "numbersale: " + number + "; ";
                    decs += "numberremain: " + user.getRes().getMPet().get(id).getFragment() + "; ";
                    totalGold += Resources.getGoldSale(type, Resources.getPet((int) id).getRank()) * number;
                    break;
                case Bonus.PET_FOOD://id res
                    if (user.getRes().getMPetFood().get(id) == null || user.getRes().getMPetFood().get(id).getNumber() < number || Resources.getPetFood((int) id) == null) {
                        addErrMessage(getLang(Lang.err_not_enough));
                        return;
                    }
                    sql.add(Database2.getUpdateQuery("user_pet_food", Arrays.asList("number", String.valueOf(user.getRes().getMPetFood().get(id).getNumber() - number)), Arrays.asList("user_id", String.valueOf(user.getId()), "food_id", String.valueOf(id))));
                    user.getRes().getMPetFood().get(id).setNumber(user.getRes().getMPetFood().get(id).getNumber() - (int) number);
                    //                    for (int j = 0; j <  user.getRes().getPetFoods().size(); j++) {
                    //                        if(user.getRes().getPetFoods().get(j).getFoodId()==id){
                    //                            user.getRes().getPetFoods().get(j).setNumber(user.getRes().getPetFoods().get(j).getNumber()-(int)number);
                    //                            break;
                    //                        }
                    //                    }
                    decs += "pet_food; ";
                    decs += "pet_id: " + id + "; ";
                    decs += "numbersale: " + number + "; ";
                    decs += "numberremain: " + user.getRes().getMPetFood().get(id).getNumber() + "; ";
                    totalGold += Resources.getGoldSale(type, Resources.getPetFood((int) id).getRank()) * number;
                    break;
                case Bonus.MATERIAL://id res
                    if (user.getRes().getMMaterial().get(id) == null || user.getRes().getMMaterial().get(id).getNumber() < number || Resources.getMaterial((int) id) == null) {
                        addErrMessage(getLang(Lang.material_not_own));
                        return;
                    }
                    sql.add(Database2.getUpdateQuery("user_material", Arrays.asList("number", String.valueOf(user.getRes().getMMaterial().get(id).getNumber() - number)), Arrays.asList("user_id", String.valueOf(user.getId()), "material_id", String.valueOf(id))));
                    user.getRes().getMMaterial().get(id).setNumber(user.getRes().getMMaterial().get(id).getNumber() - (int) number);
                    decs += "material; ";
                    decs += "material_id: " + id + "; ";
                    decs += "numbersale: " + number + "; ";
                    decs += "numberremain: " + user.getRes().getMMaterial().get(id).getNumber() + "; ";
                    totalGold += Resources.getGoldSale(type, Resources.getMaterial((int) id).getRank()) * number;
                    break;
                case Bonus.ACCESSORIES:// id rieng
                    UserAccessoriesEntity acc = new UserAccessoriesEntity();
                    acc = user.getRes().getMAccessories().get(id);
                    //                    debug("id----->" + id);
                    //                    if (acc == null) {
                    //                        debug("nullllllllllllllll");
                    //                    }
                    if (acc == null || Resources.getAccessories(acc.getAccessoriesId()) == null) {
                        addErrMessage(getLang(Lang.accessory_not_own));
                        return;
                    }
                    if (user.getMAvatar().getListAccessories().contains(id)) {
                        addErrMessage(getLang(Lang.not_sale_wearing_item));
                        return;
                    }
                    sql.add(Database2.getDeleteQuery("user_accessories", Arrays.asList("id", String.valueOf(id))));
                    decs += "accessoried; ";
                    decs += "accessoried_id: " + id + "; ";
                    decs += "accessoried_image: " + acc.getAccessoriesId() + "; ";
                    totalGold += Resources.getGoldSale(type, Resources.getAccessories(acc.getAccessoriesId()).getRank());
                    user.getRes().getMAccessories().remove(id);
                    break;
            }

        }
        for (int i = 0; i < sql.size(); i++) {
            debug("sql-->" + sql.get(i));
        }
        if (totalGold > 0) {
            if (Database2.listQuery(sql)) {
                JSONArray bonus = new JSONArray();
                bonus.add(Bonus.GOLD);
                bonus.add(totalGold);
                aLong = Bonus.receiveListItem(user, bonus, "sale_item");
                addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
                Actions.save(user.getDbUser(), "sale_item", "sale_item: ", Actions.convertToLogString(Arrays.asList("desc", decs)));


            }
        } else {
            addErrMessage();
            return;
        }

    }

    public boolean existName(String name) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select count(*) from user where name=:name");
            query.setString("name", name);
            Integer count = Integer.parseInt(query.uniqueResult().toString());
            return count > 0;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return true;
    }

    void listChoseItem() {
        int itemId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        List<Long> vlong = new ArrayList<>();
        for (int i = 0; i < GameCfgItem.data.size(); i++) {
            if (itemId == GameCfgItem.data.get(i).id && GameCfgItem.data.get(i).type == GameCfgItem.TYPECHOSE) {
                for (int j = 0; j < GameCfgItem.data.get(i).specia.length; j++) {
                    //                    vlong.add((long) GameCfgItem.data.get(i).resul);
                    for (int k = 0; k < GameCfgItem.data.get(i).specia[j].length; k++) {
                        vlong.add((long) GameCfgItem.data.get(i).specia[j][k]);
                    }

                    //                    vlong.add(1l);
                }
                break;
            }
        }
        //        System.out.println("vlong -->"+vlong);
        addResponse(service, CommonProto.getCommonLongVectorProto(vlong, null));
    }

    void useItem() {
        //        System.out.println("user iteam--->");
        int itemId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        int number = 1;
        int item1 = -1;
        int item2 = -1;
        try {
            number = (int) CommonProto.parseCommonVector(srcRequest).getANumber(1);
        } catch (Exception ex) {
        }
        String newname = "";
        try {
            newname = CommonProto.parseCommonVector(srcRequest).getAString(0);
        } catch (Exception ex) {
        }
        try {
            item1 = (int) CommonProto.parseCommonVector(srcRequest).getANumber(2);

        } catch (Exception ex) {
        }
        try {
            item2 = (int) CommonProto.parseCommonVector(srcRequest).getANumber(3);
        } catch (Exception ex) {
        }
        if (number <= 0) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        List<Integer> numberRs = new ArrayList<>();
        if (itemId == ResShopItem.CHOSE_BOOM || itemId == ResShopItem.CHOSE_CLOTHING || itemId == ResShopItem.CHOSE_XBOX || itemId == ResShopItem.CHOSE_MARY_BOX) {

            int ck = 0;
            for (int i = 0; i < GameCfgItem.data.size(); i++) {
                if (itemId == GameCfgItem.data.get(i).id && GameCfgItem.data.get(i).type == GameCfgItem.TYPECHOSE) {
                    for (int j = 0; j < GameCfgItem.data.get(i).specia.length; j++) {
                        if (GameCfgItem.data.get(i).specia[j][0] == item1 && GameCfgItem.data.get(i).specia[j][1] == item2) {
                            //                            System.out.println("co nay");
                            for (int k = 2; k < GameCfgItem.data.get(i).specia[j].length; k++) {
                                numberRs.add(GameCfgItem.data.get(i).specia[j][k]);
                            }
                            ck = 1;
                            break;
                        }
                    }
                    break;
                }
            }
            if (ck == 0) {
                addErrMessage(getLang(Lang.err_param));
                return;
            }
        }

        if (itemId == ResShopItem.LUCKY_AMULET) {
            return;
        }
        if (itemId == ResShopItem.GOLD_KEY) {
            itemId = ResShopItem.GOLD_CHEST;
        }
        if (itemId == ResShopItem.CHANGE_NAME) {
            if (newname == null || newname.trim().equalsIgnoreCase("") || newname.trim().length() < 3 || newname.length() > 15) {
                addErrMessage(getLang(Lang.user_name_length_invalid));
                return;
            }
            if (existName(newname)) {
                addErrMessage(getLang(Lang.user_name_exist));
                return;
            }
        }
        if (itemId == ResShopItem.CHANGE_BOMB) {
            if (item1 < 0 || item2 < 0) {
                addErrMessage(getLang(Lang.err_param));
                return;
            }

            //            System.out.println(user.getRes().getMBomb().get(item1));
            //            System.out.println(user.getRes().getMBomb().get(item2));

            if (user.getRes().getMBomb().get(item1) == null || user.getRes().getMBomb().get(item2) == null) {
                addErrMessage(getLang(Lang.bomb_not_own));
                return;
            }
        }
        if (itemId == ResShopItem.GOLD_CHEST) {
            if (user.getRes().mItem.get(ResShopItem.GOLD_CHEST) == null || user.getRes().mItem.get(ResShopItem.GOLD_CHEST).getNumber() < number) {
                addErrMessage(getLang(Lang.chest_not_enough));
                return;
            }
            if (user.getRes().mItem.get(ResShopItem.GOLD_KEY) == null || user.getRes().mItem.get(ResShopItem.GOLD_KEY).getNumber() < number) {
                addErrMessage(getLang(Lang.goldkey_not_enough));
                return;
            }
            if (!user.getAction().addItem(Arrays.asList(itemId, ResShopItem.GOLD_KEY), -number)) {
                if (user.getRes().mItem.get(itemId).getNumber() <= 0) {
                    addErrMessage(getLang(Lang.chest_not_enough));
                } else {
                    addErrMessage(getLang(Lang.goldkey_not_enough));
                }
                return;
            }
        } else if (!user.getAction().addItem(Arrays.asList(itemId), -number)) {
            addErrMessage(getLang(Lang.err_not_enough));
            return;
        }
        List<Long> aLong = new ArrayList<Long>();
        List<String> aString = new ArrayList<String>();

        boolean isOk = false;
        switch (itemId) {
            case ResShopItem.CHANGE_NAME:
                if (Database2.update("user", Arrays.asList("name", newname), Arrays.asList("id", String.valueOf(user.getId())))) {
                    user.getDbUser().setName(newname);
                    aString.add(newname);
                }
                break;
            case ResShopItem.EGG:
                if (item1 <= 0) {
                    for (int i = 0; i < number; i++) {
                        aLong.addAll(Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.PET, CfgShop.getRandomEgg().getId()), "use_item"));
                    }
                } else {
                    if (!Resources.mPet.containsKey(item1)) {
                        addErrMessage(getLang(Lang.pet_not_own));
                        return;
                    }
                    for (int i = 0; i < number; i++) {
                        aLong.addAll(Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.PET_FRAGMENT, item1, 10), "use_item"));
                    }
                }
                //                ResPet pet = CfgShop.getRandomEgg();
                //                aLong = Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.PET, pet.getId()), "use_item");
                break;
            case ResShopItem.RANDOM_BOM:
            case ResShopItem.RANDOM_PETFOOD:
            case ResShopItem.RANDOM_ACCESSORY:
            case ResShopItem.RANDOM_STONE:
                for (int i = 0; i < number; i++) {
                    //                    System.out.println("itemId--->"+itemId);
                    aLong.addAll(Bonus.receiveListItem(user, GameCfgItem.openItemRandom(itemId), "use_item_random"));
                }
                break;
            case ResShopItem.CHOSE_BOOM:
            case ResShopItem.CHOSE_CLOTHING:
            case ResShopItem.CHOSE_XBOX:
            case ResShopItem.CHOSE_MARY_BOX:

                for (int i = 0; i < number; i++) {

                    aLong.addAll(Bonus.receiveListItem(user, GameCfgItem.openItemChose(itemId, item1, item2, numberRs), "use_item_chose"));
                }
                break;
            case ResShopItem.BANH_DAU_XANH:
            case ResShopItem.BANH_DEO:
            case ResShopItem.BANH_EGG:
            case ResShopItem.BANH_PIA:
            case ResShopItem.BANH_THAP_CAM:
            case ResShopItem.HOP_BANH_THUONG:
            case ResShopItem.HOP_BANH_THUONG_HANG:
                for (int i = 0; i < number; i++) {
                    aLong.addAll(Bonus.receiveListItem(user, GameCfgItem.openItemEvent(itemId), "use_item_event"));
                }
                break;
            case ResShopItem.CHEST_AVATAR_LVL1:
            case ResShopItem.CHEST_AVATAR_LVL2:
            case ResShopItem.CHEST_AVATAR_LVL3:

                //                Map<Integer,Integer> lstAvataF = new HashMap<>();
                for (int i = 0; i < number; i++) {
                    ResAvatar avatar = CfgShop.getRandomAvatar(itemId - ResShopItem.CHEST_AVATAR_LVL1);
                    //                    if(lstAvataF.containsKey(avatar.getId())){
                    //                        lstAvataF.put(avatar.getId(),lstAvataF.get(avatar.getId())+1);
                    //                    }else {
                    //                        lstAvataF.put(avatar.getId(),1);
                    //                    }
                    aLong.addAll(Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.AVATAR_FRAGMENT, avatar.getId(), 1), "use_item"));

                }
                //                for (Map.Entry<Integer, Integer> entry : lstAvataF.entrySet()) {
                //                    aLong.addAll(Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.AVATAR_FRAGMENT, entry.getKey(), entry.getValue()), "use_item"));
                //                }

                //                ResAvatar avatar = CfgShop.getRandomAvatar(itemId - ResShopItem.CHEST_AVATAR_LVL1);
                //                aLong = Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.AVATAR_FRAGMENT, avatar.getId(), 1), "use_item");
                break;
            case ResShopItem.CHEST_MATERIAL_LVL1:
            case ResShopItem.CHEST_MATERIAL_LVL2:
            case ResShopItem.CHEST_MATERIAL_LVL3:
                //                Map<Integer,Integer> lstMaterial = new HashMap<>();
                for (int i = 0; i < number; i++) {
                    ResMaterial material = CfgShop.getRandomMaterial(itemId - ResShopItem.CHEST_MATERIAL_LVL1);
                    //                    if(lstMaterial.containsKey(material.getId())){
                    //                        lstMaterial.put(material.getId(),lstMaterial.get(material.getId())+1);
                    //                    }else {
                    //                        lstMaterial.put(material.getId(),1);
                    //                    }
                    aLong.addAll(Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.MATERIAL, material.getId(), 1), "use_item"));

                }
                //                for (Map.Entry<Integer, Integer> entry : lstMaterial.entrySet()) {
                //                    aLong.addAll(Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.MATERIAL, entry.getKey(), entry.getValue()), "use_item"));
                //                }

                //                ResMaterial material = CfgShop.getRandomMaterial(itemId - ResShopItem.CHEST_MATERIAL_LVL1);
                //                aLong = Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.MATERIAL, material.getId(), 1), "use_item");
                break;
            case ResShopItem.ATOMIC_BAG:
                aLong = Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.ATOMIC, 100 * number), "use_item");
                break;
            case ResShopItem.CHANGE_BOMB:
                UserBombEntity uBom1 = user.getRes().getMBomb().get(item1);
                UserBombEntity uBom2 = user.getRes().getMBomb().get(item2);
                int level = uBom1.getLevel();
                UserDAO uDao = new UserDAO();
                if (uDao.changeBomb(uBom1.getId(), uBom2.getId(), 1, level)) {
                    aLong = Arrays.asList((long) Bonus.BOMB, (long) uBom1.getId(), (long) uBom1.getBombId(), 1l, (long) Bonus.BOMB, (long) uBom2.getId(), (long) uBom2.getBombId(), (long) level);
                    user.getRes().getMBomb().get(item1).setLevel(1);
                    user.getRes().getMBomb().get(item2).setLevel(level);
                } else {

                }

                break;
            case ResShopItem.ENERGY_FOR_PLAYER:
                aLong = Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.USER_ENERGY, 20 * number), "use_item");
                break;
            case ResShopItem.GOLD_CHEST:
                //                System.out.println("mo ruong");
                for (int i = 0; i < number; i++) {
                    aLong.addAll(Bonus.receiveListItem(user, GameCfgChest.getGoldChest(1, GameCfgChest.CHEST_TYPE_WOOD, false), "use_item"));
                }
                //                System.out.println("along------------------>" + aLong);
                break;
            case ResShopItem.EXP_X2_10:
            case ResShopItem.EXP_X2_100:
                int addValue = itemId == ResShopItem.EXP_X2_10 ? 10 : 100;
                user.getUData().getUInt().addValue(UserInt.HERO_EXP_X2, UserInt.TYPE_ADD, addValue * number);
                if (user.getUData().getUInt().update(user.getId())) {
                    isOk = true;
                } else {
                    user.getUData().getUInt().addValue(UserInt.HERO_EXP_X2, UserInt.TYPE_ADD, -addValue * number);
                }
                break;
        }
        if (itemId == ResShopItem.EXP_X2_10 || itemId == ResShopItem.EXP_X2_100) {
            if (!isOk) {
                addErrMessage();
                return;
            }
        } else if (aLong.isEmpty() && itemId != ResShopItem.CHANGE_NAME) {
            user.getAction().addItem(Arrays.asList(itemId), number);
            addErrMessage();
            return;
        }
        UserItemEntity uItem = user.getRes().getMItem().get(itemId);
        List<Long> retLong = new ArrayList<Long>();
        retLong.add((long) uItem.getNumber());
        retLong.add((long) user.getUData().getUInt().getValue(UserInt.HERO_EXP_X2));
        retLong.addAll(aLong);
        //        System.out.println("along--->"+aLong);
        addResponse(service, CommonProto.getCommonLongVectorProto(retLong, aString));
    }

    void topUpstatus() {
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        {//card
            GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
            tmp.addAllAString(CfgPayment.config.cardTygia);
            tmp.addANumber(-1l);
            tmp.addANumber(CfgPayment.config.showTabCard);
            tmp.addANumber(CfgPayment.config.showViettel);
            tmp.addANumber(CfgPayment.config.showMobi);
            tmp.addANumber(CfgPayment.config.showVina);
            tmp.addANumber(CfgPayment.config.showGMobi);
            builder.addAVector(tmp);
        }
        if (user.getCp().equalsIgnoreCase("zing")) {
            {//iap
                GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
                tmp.addANumber(CfgPayment.config.showTabIap);
                tmp.addAllAString(CfgPayment.config.zingxuKey);
                tmp.addAllANumber(CfgPayment.config.zingxuTygia);
                builder.addAVector(tmp);
            }
        } else if (user.getCp().equalsIgnoreCase("soha")) {
            {//iap
                GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
                tmp.addANumber(CfgPayment.config.showTabIap);
                tmp.addAllAString(CfgPayment.config.sohaKey);
                tmp.addAllANumber(CfgPayment.config.sohaTygia);
                builder.addAVector(tmp);
            }
        } else {
            GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
            tmp.addANumber(CfgPayment.config.showTabIap);
            tmp.addAllAString(CfgPayment.config.iapKey);
            tmp.addAllANumber(CfgPayment.config.iapTygia);
            builder.addAVector(tmp);
        }

        addResponse(service, builder.build());
    }

    void topUpCard() {
        //        user.getDbUser().addMoney(0, 10);
        //        addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getDbUser().getGem()), Arrays.asList("Bạn vừa nạp thành công 1 tỷ, số gem hiện tại là 1000000")));
    }

    /**
     * {
     * "Payload": "{\"json\":\"{\\\"orderId\\\":\\\"GPA.3370-4772-5589-89088\\\",\\\"packageName\\\":\\\"com.MetaBomb.BoomB\\\",\\\"productId\\\":\\\"pack.diamond.6\\\",\\\"purchaseTime\\\":1714801823237,\\\"purchaseState\\\":0,\\\"purchaseToken\\\":\\\"fddehidodjifonpgppogobfp.AO-J1Oz9OhAHS1iFFJtBGLML-PP996otZV_sWxTjzvY2060hhVyQoSVN6spGN3w80FMaWuoW97oxI6JBebKAHJDuptfSZ82PMw\\\",\\\"quantity\\\":1,\\\"acknowledged\\\":false}\",\"signature\":\"CtB7ghCKEZgg0Q6GKq8oA1lQVt5vgTHALyDmEVhqgQ1M7xKRG2lRfacLGXZ9iSd35KqHP3HQfXkaOWZ4czFmg0W8IIsscbWDHTsw0AEAAdUGUWQ6wCGyhoCBQNMDoVaZNoVQeAfnYyopn462uP60rnBTL+QaFmFovpi72yoArwt1/fKD2uyK9JYLAlTQZiw8k1OOFQ9yFDKo3EA8agwQ02XrENYSI7pZSCNPQK3l1ttWyXLrG1kt4ESewydm0W8o//qUhBdtf1wolkJcz1mj/vLNHv3T5XnfzSLd/qvABZYhJ83yMW+lwej8lOz3h+1///xnQAqH2MNpnL3BEGl2fQ==\",\"skuDetails\":[\"{\\\"productId\\\":\\\"pack.diamond.6\\\",\\\"type\\\":\\\"inapp\\\",\\\"title\\\":\\\"Diamond Pack 6 (com.MetaBomb.BoomB (unreviewed))\\\",\\\"name\\\":\\\"Diamond Pack 6\\\",\\\"iconUrl\\\":\\\"https:\\\\/\\\\/lh3.googleusercontent.com\\\\/btIgzqAzlb3OYTag94wYOyO-exTx4qkGZFfwopOydrJBWn4YbgdpNb2otjmBSRsgTA0\\\",\\\"description\\\":\\\"Diamond Pack 6\\\",\\\"price\\\":\\\"19,66\\u00a0US$\\\",\\\"price_amount_micros\\\":19656784,\\\"price_currency_code\\\":\\\"USD\\\",\\\"skuDetailsToken\\\":\\\"AEuhp4LXmQhyFlZQ53bWsqLjCBEr-S3e_A4-fSlw2dT2T0pT4IfF2V8kcP8GTPCYnTY=\\\"}\"]}",
     * "Store": "GooglePlay",
     * "TransactionID": "fddehidodjifonpgppogobfp.AO-J1Oz9OhAHS1iFFJtBGLML-PP996otZV_sWxTjzvY2060hhVyQoSVN6spGN3w80FMaWuoW97oxI6JBebKAHJDuptfSZ82PMw"
     * }
     * <p>
     * RECEIPT: {"Payload":"ThisIsFakeReceiptData","Store":"fake","TransactionID":"16e77abd-5947-423a-bb4b-9203cacf163e"}'
     */
    void topUpIap() {
        IapDAO dao = new IapDAO();
        UserDAO userDAO = new UserDAO();

        //        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        //        long gem = 0;
        //        try {
        //            int type = aLong.get(0).intValue();
        //            gem = CfgPayment.config.iapTygia.get(type);
        //        } catch (Exception ex) {
        //
        //        }
        List<String> lstKey = CommonProto.parseCommonVector(srcRequest).getAStringList();
        String receipt = lstKey.get(0);
        if (receipt == null || receipt.length() == 0) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        if (receipt.contains("ThisIsFakeReceiptData")) {
            long getGem = 500;
            // add gem
            Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.GEM, getGem), "iap");
            // sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList(curGem, addGem), Arrays.asList(msg)));
            addPopupMessage("IAP purchase success! Gem = %s, add gem = %s".formatted(user.getDbUser().getGem(), getGem));
            addResponse(UPDATE_MONEY, user.protoInfor());
            // save logNapKoin
            dao.saveObject(new LogNapKoin(user.getDbUser(), getGem, 0));
            if (userDAO.addGemNap(user.getId(), getGem)) {
                user.getDbUser().setGemNap(user.getDbUser().getGemNap() + getGem);
                try {
                    if (CfgAchievement.getAchievement(CfgAchievement.NAP, user.getDbUser()).inTime()) {
                        if (user.getDbUser().getSumGemIntEvent() < 0) {
                            user.getDbUser().setSumGemIntEvent(0);
                        }
                        user.getDbUser().setSumGemIntEvent(user.getDbUser().getSumGemIntEvent() + getGem);
                    }
                } catch (Exception ex) {
                }
                int oldVip = user.getDbUser().getVip();
                user.getUData().getInitUInt(user.getDbUser());
                int newVip = user.getDbUser().getVip();
                if (oldVip != newVip)
                    Database2.update("user", Arrays.asList("vip", String.valueOf(newVip)), Arrays.asList("id", String.valueOf(user.getId())));
                while (newVip > oldVip) {
                    new SystemDAO().sendMail(user.getId(), "Congratulations on becoming Vip " + newVip, CfgVip.config.get(newVip).gift.toString());
                    newVip--;
                }
                user.getDbUser().addSymBol(user);
                List<Long> aLong = new ArrayList<Long>();
                aLong.add(user.getDbUser().getGold());
                aLong.add(user.getDbUser().getGem());
                UserMoneys moneys = user.getUData().getUserMoneys();
                aLong.add(moneys.getValue(UserMoneys.MEDAL_BOSS));
                aLong.add(moneys.getValue(UserMoneys.MEDAL));
                aLong.add(moneys.getValue(UserMoneys.CLAN));
                Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(CfgNotify.loginNotify(user), null), IAction.LOGIN_NOTIFY, System.currentTimeMillis());
                Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(aLong, null), IAction.SYNC_MONEYS, System.currentTimeMillis());
                Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getDbUser().getVip(), user.getDbUser().getGemNap()), null), IAction.VIP_INFOR, System.currentTimeMillis());
            }
        }
        String os = "0";
        if (user.getOs().equalsIgnoreCase("android")) {
            os = "1";
        }
        JsonObject obj = GsonUtil.parseJsonObject(receipt);
        Logs.warn(obj.toString());
        String reqId = obj.get("TransactionID").getAsString();
        String reqData = GsonUtil.parseJsonObject(obj.get("Payload").getAsString()).get("json").getAsString();

        IapRequest iapRequest = dao.getRequest(reqId);
        if (iapRequest != null) {
            addErrMessage("This transaction has been confirmed.");
            return;
        }
        iapRequest = new IapRequest(user.getDbUser(), reqId, reqData);
        if (dao.saveObject(iapRequest)) {
            JsonObject objData = GsonUtil.parseJsonObject(reqData);
            String packageName = objData.get("packageName").getAsString();
            String productId = objData.get("productId").getAsString();
            String purchaseToken = objData.get("purchaseToken").getAsString();
            String orderId = objData.get("orderId").getAsString();

            //            {
            //                      "acknowledgementState": 1, // 0. Yet to be acknowledged 1. Acknowledged
            //                    "consumptionState": 1, // 0. Purchased 1. Canceled 2. Pending
            //                    "developerPayload": "",
            //                    "kind": "androidpublisher#productPurchase",
            //                    "orderId": "GPA.3370-4772-5589-89088",
            //                    "purchaseState": 0,
            //                    "purchaseTimeMillis": 1714801823237,
            //                    "purchaseType": 0, // 0 test
            //                    "regionCode": "US"
            //            }
            try {
                ProductPurchase purchase = GoogleAndroidAPI.productGet(packageName, productId, purchaseToken);
                if (purchase == null) addErrMessage();
                else {
                    String response = GsonUtil.toJson(purchase);
                    Logs.warn(user.getId() + " " + response);
                    boolean isTest = purchase.getPurchaseType() != null && purchase.getPurchaseType() == 0;
                    if (purchase.getPurchaseState() == 1) {
                        addPopupMessage("Your purchase is Canceled");
                        return;
                    }
                    if (purchase.getPurchaseState() == 2) {
                        addPopupMessage("Your purchase is Pending");
                        return;
                    }
//                    boolean isSuccess = isTest || purchase.getConsumptionState() == 0;
//                    if (!isSuccess) {
//                        addPopupMessage("Your purchase is fail");
//                        return;
//                    }
                    // save iap
                    Iap iap = new Iap(user.getDbUser(), orderId, productId, response, isTest);
                    if (dao.saveObject(iap)) {
                        // everything done + gem
                        long getGem = CfgPayment.getIapGem(productId);
                        if (user.getDbUser().getGemNap() == 0) getGem = getGem * 2;
                        try {
                            if (CfgAchievement.getAchievement(CfgAchievement.NAP_X2, user.getDbUser()).inTime()) {
                                getGem = getGem * 2;
                            }
                        } catch (Exception ex) {
                            Logs.error(Util.exToString(ex));
                        }
                        // add gem
                        Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.GEM, getGem), "iap");
                        Actions.save(user.getDbUser(), "iap", productId, "{}");
                        // sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList(curGem, addGem), Arrays.asList(msg)));
                        addPopupMessage("IAP purchase success! Gem = %s, add gem = %s".formatted(user.getDbUser().getGem(), getGem));
                        addResponse(UPDATE_MONEY, user.protoInfor());
                        // save logNapKoin
                        dao.saveObject(new LogNapKoin(user.getDbUser(), getGem, iap.getMoney()));
                        if (userDAO.addGemNap(user.getId(), getGem)) {
                            user.getDbUser().setGemNap(user.getDbUser().getGemNap() + getGem);
                            try {
                                if (CfgAchievement.getAchievement(CfgAchievement.NAP, user.getDbUser()).inTime()) {
                                    if (user.getDbUser().getSumGemIntEvent() < 0) {
                                        user.getDbUser().setSumGemIntEvent(0);
                                    }
                                    user.getDbUser().setSumGemIntEvent(user.getDbUser().getSumGemIntEvent() + getGem);
                                }
                            } catch (Exception ex) {
                                Logs.error(Util.exToString(ex));
                            }
                            int oldVip = user.getDbUser().getVip();
                            user.getUData().getInitUInt(user.getDbUser());
                            int newVip = user.getDbUser().getVip();
                            if (oldVip != newVip)
                                Database2.update("user", Arrays.asList("vip", String.valueOf(newVip)), Arrays.asList("id", String.valueOf(user.getId())));
                            while (newVip > oldVip) {
                                new SystemDAO().sendMail(user.getId(), "Congratulations on becoming Vip " + newVip, CfgVip.config.get(newVip).gift.toString());
                                newVip--;
                            }
                            user.getDbUser().addSymBol(user);
                            List<Long> aLong = new ArrayList<Long>();
                            aLong.add(user.getDbUser().getGold());
                            aLong.add(user.getDbUser().getGem());
                            UserMoneys moneys = user.getUData().getUserMoneys();
                            aLong.add(moneys.getValue(UserMoneys.MEDAL_BOSS));
                            aLong.add(moneys.getValue(UserMoneys.MEDAL));
                            aLong.add(moneys.getValue(UserMoneys.CLAN));
                            Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(CfgNotify.loginNotify(user), null), IAction.LOGIN_NOTIFY, System.currentTimeMillis());
                            Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(aLong, null), IAction.SYNC_MONEYS, System.currentTimeMillis());
                            Util.sendProtoData(channel, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getDbUser().getVip(), user.getDbUser().getGemNap()), null), IAction.VIP_INFOR, System.currentTimeMillis());
                        }
                    } else addErrMessage();
                }
            } catch (Exception ex) {
                Logs.error(GUtil.exToString(ex));
                addErrMessage();
            }
        } else addErrMessage();
        //        String ret = ApiServer.verifyIAP(receipt, user.getUsername(), user.getAuthUser().getCp(), os, user.getDbUser().getServer());
        //        try {
        //            JSONObject obj = JSONObject.fromObject(ret);
        //            long status = obj.getInt("status");
        //            long curGem = user.getDbUser().getGem();
        //            long addGem = 0l;
        //            //            if (status == 0) {
        //            //                curGem = obj.getLong("gemAfter");
        //
        //            //                addGem = obj.getLong("gem");
        //            //                user.getDbUser().setGem(   user.getDbUser().getGem()+addGem);
        //            //                user.getDbUser().addSymBol(user);
        //            //                user.getDbUser().addSymBol(user);
        //            //            }
        //            String msg = obj.getString("desc");
        //            //            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList(curGem, addGem), Arrays.asList(msg)));
        //            sendToastMessage(channel, msg);
        //        } catch (Exception ex) {
        //            addTopupMessage(getLang(Lang.err_system_down));
        //        }
        //        service = MSG_POPUP;
        //        response = CommonProto.getStringProto(ret);
    }

    void topUpHistory() {
        GGProto.ProtoListTopupHistory.Builder builder = GGProto.ProtoListTopupHistory.newBuilder();
        for (int i = 0; i < 5; i++) {
            GGProto.ProtoTopupHistory.Builder tmp = GGProto.ProtoTopupHistory.newBuilder();
            tmp.setAddGem(new Random().nextInt(100));
            tmp.setOldGem(new Random().nextInt(1000));
            tmp.setMoney("100000");
            tmp.setTimeCreated(DateTime.getFullDate(new Date()));
            tmp.setType(new Random().nextInt(2) == 1 ? "IAP" : "Card");
            builder.addHistory(tmp);
        }
        addResponse(service, builder.build());
    }

    void exchangeStatus() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        int type = (int) cmm.getANumber(0);
        String mapId = cmm.getANumberCount() == 2 ? String.valueOf(cmm.getANumber(1)) : "";
        //
        int numberExchange = CfgExchange.getNumberExchange(user, type, mapId);
        if (numberExchange < 0) numberExchange = 0;
        //
        List<Long> rs = new ArrayList<Long>();
        rs.add((long) type);
        if (CfgExchange.PRICE_PROGRESS.contains(type)) rs.add((long) CfgExchange.config.get(type - 1).number * (numberExchange + 1));
        else rs.add((long) CfgExchange.config.get(type - 1).number);

        rs.add((long) CfgExchange.config.get(type - 1).numberExchange);
        rs.add((long) CfgExchange.getNumberRemain(user, type, mapId));
        //        System.out.println("uint1---?"+user.getUData().getUInt());
        //        System.out.println("numberremain-->"+CfgExchange.getNumberRemain(user, type, mapId));
        addResponse(CommonProto.getCommonLongVectorProto(rs, null));
    }

    void exchange() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        int type = (int) cmm.getANumber(0);
        String info = cmm.getANumberCount() == 2 ? String.valueOf(cmm.getANumber(1)) : "";

        int maxExchange = CfgExchange.getMaxExchange(user, type);
        int numberExchange = CfgExchange.getNumberExchange(user, type, info);
        int numberRemain = maxExchange - numberExchange;
        if (numberRemain <= 0) {
            addErrMessage(getLang(Lang.err_max_purchase));
            return;
        }

        if (Arrays.asList(CfgExchange.EXCHANGE_GOLD, CfgExchange.EXCHANGE_ENERGY, CfgExchange.EXCHANGE_ATOMIC).contains(type)) {
            int gemRequired = CfgExchange.config.get(type - 1).number * (CfgExchange.PRICE_PROGRESS.contains(type) ? numberExchange + 1 : 1);
            if (user.getDbUser().getGem() < gemRequired) {
                addTopupMessage(getLang(Lang.err_not_enough_gem));
                return;
            }
            JSONArray arr = new JSONArray();
            arr.addAll(Arrays.asList(Bonus.GEM, -gemRequired));
            arr.addAll(CfgExchange.config.get(type - 1).bonus);
            List<Long> aLong = Bonus.receiveListItem(user, arr, CfgExchange.keyLogs[type - 1]);
            if (!aLong.isEmpty()) {
                List<Long> result = new ArrayList<>();
                result.add((long) type);
                result.add(user.getDbUser().getGem());
                result.add((long) CfgExchange.config.get(type - 1).number * (CfgExchange.PRICE_PROGRESS.contains(type) ? numberExchange + 2 : 1));
                result.add((long) (numberRemain - 1));
                result.addAll(aLong);
                JCache.getInstance().setValue(CfgExchange.getKey(user, type, ""), String.valueOf(maxExchange - numberRemain + 1));
                addResponse(CommonProto.getCommonLongVectorProto(result, null));
            } else {
                addErrMessage();
            }
        } else if (type == CfgExchange.REFRESH_MAP_INSANE) {
            int gemRequired = CfgExchange.config.get(type - 1).number;
            if (user.getDbUser().getGem() < gemRequired) {
                addTopupMessage(getLang(Lang.err_not_enough_gem));
                return;
            }
            int mapId = (int) cmm.getANumber(1);
            MapLevel mapLevel = user.getUData().getMap();

            int oldValue = mapLevel.getNumberAtk(mapId);
            MapEntity mapEntity = MapResource.getMap(mapId);
            if (oldValue < mapEntity.getTimesAtk()) {
                addErrMessage(getLang(Lang.err_has_free_attack));
                return;
            }

            mapLevel.setNumberAtk(mapId, 0);
            if (dbRefreshNumberAtk(user, gemRequired)) {
                user.getDbUser().addGem(-gemRequired);
                addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList((long) type, user.getDbUser().getGem(), (long) gemRequired, (long) numberRemain - 1), null));
                JCache.getInstance().setValue(CfgExchange.getKey(user, type, info), String.valueOf(maxExchange - numberRemain + 1));
            } else {
                mapLevel.setNumberAtk(mapId, oldValue);
                addErrMessage();
            }
        } else {
            if (type == CfgExchange.EXCHANGE_GAME_BOSS) {
                int eventIndex = GameCfgBoss.getEventStatus().getIndex();
                if (eventIndex < 0) {
                    addErrMessage(getLang(Lang.err_only_exchange_in_event));
                    return;
                }
            }
            int gemRequired = CfgExchange.config.get(type - 1).number * (CfgExchange.PRICE_PROGRESS.contains(type) ? numberExchange + 1 : 1);
            if (user.getDbUser().getGem() < gemRequired) {
                addTopupMessage(getLang(Lang.err_not_enough_gem));
                return;
            }
            JSONArray arr = new JSONArray();
            arr.addAll(Arrays.asList(Bonus.GEM, -gemRequired));
            List<Long> aLong = Bonus.receiveListItem(user, arr, CfgExchange.keyLogs[type - 1]);
            if (!aLong.isEmpty()) {
                List<Long> result = new ArrayList<>();
                result.add((long) type);
                result.add(user.getDbUser().getGem());
                result.add((long) CfgExchange.config.get(type - 1).number * (CfgExchange.PRICE_PROGRESS.contains(type) ? numberExchange + 2 : 1));
                result.add((long) (numberRemain - 1));
                result.addAll(aLong);

                int addValue = CfgExchange.config.get(type - 1).numberExchange;
                if (type == CfgExchange.EXCHANGE_GAME_BOSS) {
                    int numberAtk = BossGlobal.getInstance(user.getDbUser().getServer()).addNumberAtk(user, new long[]{addValue});
                    result.addAll(Arrays.asList((long) Bonus.GAME_NUMBER_ATK, (long) Constans.EVENT_BOSS_GLOBAL, (long) numberAtk, (long) addValue));
                } else if (type == CfgExchange.EXCHANGE_GAME_GOLD) {
                    int numberAtk = GoldHunting.getInstance().addNumberAtk(user, new long[]{addValue});
                    result.addAll(Arrays.asList((long) Bonus.GAME_NUMBER_ATK, (long) Constans.EVENT_GOLD_HUNTING, (long) numberAtk, (long) addValue));
                } else if (type == CfgExchange.EXCHANGE_GAME_MATERIAL) {
                    int numberAtk = MaterialHunting.getInstance().addNumberAtk(user, new long[]{addValue});
                    result.addAll(Arrays.asList((long) Bonus.GAME_NUMBER_ATK, (long) Constans.EVENT_MATERIAL, (long) numberAtk, (long) addValue));
                }
                JCache.getInstance().setValue(CfgExchange.getKey(user, type, ""), String.valueOf(maxExchange - numberRemain + 1));
                addResponse(CommonProto.getCommonLongVectorProto(result, null));
            } else {
                addErrMessage();
            }
        }
    }

    void shopSpecialBuy(int shopType, int typeItem, int itemId, int number) {
        CfgShop.MyShop myShop = CfgShop.getMyShop(user);
        if (myShop == null) {
            addErrMessage();
            return;
        }
        List<Integer> item = myShop.getItem(shopType, typeItem, itemId, user.getDbUser().getVip());
        if (item == null) {
            if (shopType == CfgShop.SHOP_VIP) {
                addErrMessage(getLang(Lang.err_vip_level));
                return;
            }
            addErrMessage(getLang(Lang.err_item_not_exist));
            return;
        }
        if (item.get(4) != -1 && item.get(4) < number) {
            addErrMessage(getLang(Lang.err_out_of_stock));
            return;
        }
        JSONArray arrBonus = user.getAction().checkMoney(this, item.get(2), -item.get(3) * number);
        if (arrBonus == null) {
            return;
        }
        if (typeItem == Bonus.AVATAR) {
            ResAvatar resAvatar = Resources.getAvatar(itemId);
            if (user.getRes().getMHero().get(resAvatar.getCharacterId()) == null) {
                addPopupMessage(String.format(getLang(Lang.err_required_hero), Resources.getHero(resAvatar.getCharacterId()).getName()));
                return;
            }
        }
        if (Arrays.asList(Bonus.ACCESSORIES, Bonus.BOMB, Bonus.AVATAR).contains(typeItem)) {
            for (int i = 0; i < number; i++) {
                arrBonus.addAll(Bonus.defaultLongBonusView(item.get(0), item.get(1), 1));
            }
        } else if (Arrays.asList(Bonus.AVATAR_FRAGMENT, Bonus.PET_FRAGMENT, Bonus.ITEM, Bonus.PET_FOOD, Bonus.MATERIAL, Bonus.UFO_STONE).contains(typeItem)) {
            arrBonus.addAll(Bonus.defaultLongBonusView(item.get(0), item.get(1), number));
        }
        arrBonus.add(Bonus.UPDATE_SHOP_SPECIAL);
        item.set(4, item.get(4) == -1 ? item.get(4) : item.get(4) - number);
        List<Long> tmp = Bonus.receiveListItem(user, arrBonus, CfgShop.SHOP_NAME[shopType - 199]);
        if (tmp.isEmpty()) {
            item.set(4, item.get(4) == -1 ? item.get(4) : item.get(4) + number);
            addErrMessage();
            return;
        }
        user.syncMoneys(this);
        addResponse(CommonProto.getCommonLongVectorProto(tmp, null));
    }

    void shopSpecialBuyOld(int shopType, int typeItem, int itemId) {
        CfgShop.MyShop myShop = CfgShop.getMyShop(user);
        if (myShop == null) {
            addErrMessage();
            return;
        }
        List<Integer> item = myShop.getItem(shopType, typeItem, itemId, user.getDbUser().getVip());
        if (item == null) {
            if (shopType == CfgShop.SHOP_VIP) {
                addErrMessage(getLang(Lang.err_vip_level));
                return;
            }
            addErrMessage(getLang(Lang.err_item_not_exist));
            return;
        }
        int number = item.get(4);
        if (number == 0) {
            addErrMessage(getLang(Lang.err_out_of_stock));
            return;
        }
        JSONArray arrBonus = user.getAction().checkMoney(this, item.get(2), -item.get(3));
        if (arrBonus == null) {
            return;
        }

        if (Arrays.asList(Bonus.ACCESSORIES, Bonus.BOMB).contains(typeItem)) {
            arrBonus.addAll(Bonus.defaultLongBonusView(item.get(0), item.get(1), 1));
        } else if (Arrays.asList(Bonus.AVATAR_FRAGMENT, Bonus.PET_FRAGMENT, Bonus.ITEM, Bonus.PET_FOOD, Bonus.MATERIAL).contains(typeItem)) {
            arrBonus.addAll(Bonus.defaultLongBonusView(item.get(0), item.get(1), item.get(4)));
        }
        arrBonus.add(Bonus.UPDATE_SHOP_SPECIAL);
        //        System.out.println(arrBonus.toString());
        item.set(4, 0);
        List<Long> tmp = Bonus.receiveListItem(user, arrBonus, CfgShop.SHOP_NAME[shopType - 199]);
        if (tmp.isEmpty()) {
            item.set(4, number);
            addErrMessage();
            return;
        }

        tmp.add(0, user.getDbUser().getGold());
        tmp.add(0, user.getDbUser().getGem());
        tmp.add(0, 0L);
        addResponse(CommonProto.getCommonLongVectorProto(tmp, null));
        user.syncMoneys(this);
    }

    void buyItem() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int typeOfShop = aLong.get(0).intValue();
        int typeItem = aLong.get(1).intValue();
        int itemId = aLong.get(2).intValue();
        int number = aLong.get(3).intValue();
        if (number <= 0) {
            return;
        }
        if (Arrays.asList(CfgShop.SHOP_THANBI, CfgShop.SHOP_VIP, CfgShop.SHOP_MEDAL, CfgShop.SHOP_BOSS, CfgShop.SHOP_AVATAR).contains(typeOfShop)) {
            shopSpecialBuy(typeOfShop, typeItem, itemId, number);
        } else if (typeItem == Bonus.HERO) {
            UserHeroEntity uHero = user.getRes().getMHero().get(itemId);
            if (uHero != null && uHero.getLevel() > 0) {
                addErrMessage(getLang(Lang.err_already_own_hero));
                return;
            }
            ResHero rHero = Resources.getHero(itemId);
            long useGem = 0, useGold = 0;
            if (rHero.getPrice_type() == Constans.PRICE_GOLD) {
                useGold = rHero.getPrice() * number;
            } else if (rHero.getPrice_type() == Constans.PRICE_GEM) {
                useGem = rHero.getPrice() * number;
            }
            JSONArray arr = user.getAction().addMoney(this, -useGold, -useGem);
            if (arr == null) {
                return;
            }
            arr.addAll(Arrays.asList(Bonus.HERO, itemId, Bonus.AVATAR, rHero.getAvatarId(), 1));
            List<Long> tmp = Bonus.receiveListItem(user, arr, "shop");
            if (tmp.isEmpty()) {
                addErrMessage();
                return;
            }
            aLong = new ArrayList<Long>();
            aLong.addAll(tmp);
            CfgNewMission.addMission(user.getDbUser(), MissionObject.BUYSHOPMISSION, 1);
            user.syncMoneys(this);
            addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
        } else if (typeItem == Bonus.UFO) {
            UserUfoEntity uUfo = user.getRes().getMUfo().get(itemId);
            if (uUfo != null) {
                addErrMessage(getLang(Lang.err_already_own_ufo));
                return;
            }
            ResUfo resUfo = Resources.getUfo(itemId);
            long useGem = 0, useGold = 0;
            if (resUfo.getPrice_type() == Constans.PRICE_GOLD) {
                useGold = resUfo.getPrice() * number;
            } else if (resUfo.getPrice_type() == Constans.PRICE_GEM) {
                useGem = resUfo.getPrice() * number;
            }
            JSONArray arr = user.getAction().addMoney(this, -useGold, -useGem);
            if (arr == null) {
                return;
            }
            arr.addAll(Arrays.asList(Bonus.UFO, itemId));
            List<Long> tmp = Bonus.receiveListItem(user, arr, "shop");
            if (tmp.isEmpty()) {
                addErrMessage();
                return;
            }
            aLong = new ArrayList<Long>();
            aLong.addAll(tmp);
            CfgNewMission.addMission(user.getDbUser(), MissionObject.BUYSHOPMISSION, 1);
            user.syncMoneys(this);
            addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
        } else {
            ResShopItem shop = Resources.mShop.get(itemId);
            if (shop == null || shop.getPricetype() == 0) {
                addErrMessage(getLang(Lang.err_out_of_stock));
                return;
            }
            long useGem = 0, useGold = 0;
            if (shop.getPricetype() == Constans.PRICE_GOLD) {
                useGold = shop.getPrice() * number;
            } else {
                useGem = shop.getPrice() * number;
            }
            JSONArray arr = user.getAction().addMoney(this, -useGold, -useGem);
            if (arr == null) {
                return;
            }
            arr.addAll(Arrays.asList(Bonus.ITEM, itemId, number));
            List<Long> tmp = Bonus.receiveListItem(user, arr, "shop");
            if (tmp.isEmpty()) {
                addErrMessage();
                return;
            }
            aLong = new ArrayList<Long>();
            aLong.addAll(tmp);
            CfgNewMission.addMission(user.getDbUser(), MissionObject.BUYSHOPMISSION, 1);
            user.syncMoneys(this);
            addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
        }
    }

    void buyItemOld() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int typeOfShop = aLong.get(0).intValue();
        int typeItem = aLong.get(1).intValue();
        int itemId = aLong.get(2).intValue();
        int number = aLong.get(3).intValue();
        if (number <= 0) {
            return;
        }
        if (Arrays.asList(CfgShop.SHOP_THANBI, CfgShop.SHOP_VIP, CfgShop.SHOP_MEDAL, CfgShop.SHOP_BOSS).contains(typeOfShop)) {
            shopSpecialBuyOld(typeOfShop, typeItem, itemId);
        } else if (typeItem == Bonus.HERO) {
            UserHeroEntity uHero = user.getRes().getMHero().get(itemId);
            if (uHero != null && uHero.getLevel() > 0) {
                addErrMessage(getLang(Lang.err_already_own_hero));
                return;
            }
            ResHero rHero = Resources.getHero(itemId);
            long useGem = 0, useGold = 0;
            if (rHero.getPrice_type() == Constans.PRICE_GOLD) {
                useGold = rHero.getPrice() * number;
            } else if (rHero.getPrice_type() == Constans.PRICE_GEM) {
                useGem = rHero.getPrice() * number;
            }
            JSONArray arr = user.getAction().addMoney(this, -useGold, -useGem);
            if (arr == null) {
                return;
            }
            arr.addAll(Arrays.asList(Bonus.HERO, itemId, Bonus.AVATAR, rHero.getAvatarId(), 1));
            List<Long> tmp = Bonus.receiveListItem(user, arr, "shop");
            if (tmp.isEmpty()) {
                addErrMessage();
                return;
            }
            aLong = new ArrayList<Long>();
            aLong.addAll(Arrays.asList(user.getDbUser().getGold(), user.getDbUser().getGem(), user.getDbUser().getMedal()));
            aLong.addAll(tmp);
            CfgNewMission.addMission(user.getDbUser(), MissionObject.BUYSHOPMISSION, 1);
            addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
        } else {
            ResShopItem shop = Resources.mShop.get(itemId);
            if (shop == null || shop.getPricetype() == 0) {
                addErrMessage(getLang(Lang.err_out_of_stock));
                return;
            }
            long useGem = 0, useGold = 0;
            if (shop.getPricetype() == Constans.PRICE_GOLD) {
                useGold = shop.getPrice() * number;
            } else {
                useGem = shop.getPrice() * number;
            }
            JSONArray arr = user.getAction().addMoney(this, -useGold, -useGem);
            if (arr == null) {
                return;
            }
            arr.addAll(Arrays.asList(Bonus.ITEM, itemId, number));
            List<Long> tmp = Bonus.receiveListItem(user, arr, "shop");
            if (tmp.isEmpty()) {
                addErrMessage();
                return;
            }
            aLong = new ArrayList<Long>();
            aLong.addAll(Arrays.asList(user.getDbUser().getGold(), user.getDbUser().getGem(), user.getDbUser().getMedal()));
            aLong.addAll(tmp);
            CfgNewMission.addMission(user.getDbUser(), MissionObject.BUYSHOPMISSION, 1);
            addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
        }
    }

    void storageList() {
        addResponse(service, protoStorage(user));
    }

    void shopSpecial() {
        CfgShop.MyShop myShop = CfgShop.getMyShop(user);
        if (myShop == null) {
            addErrMessage();
            return;
        }
        int shopIndex = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        switch (shopIndex) {
            case Bonus.ITEM:
                addResponse(protoShopItem());
                break;
            case Bonus.HERO:
                addResponse(protoShopHero());
                break;
            case CfgShop.SHOP_THANBI:
                addResponse(myShop.toProto(CfgShop.SHOP_THANBI));
                break;
            case CfgShop.SHOP_BOSS:
                addResponse(myShop.toProto(CfgShop.SHOP_BOSS, myShop.aBoss));
                break;
            case CfgShop.SHOP_MEDAL:
                addResponse(myShop.toProto(CfgShop.SHOP_MEDAL, myShop.aMedal));
                break;
            case CfgShop.SHOP_VIP:
                addResponse(myShop.toVipProto());
                break;
            case CfgShop.SHOP_AVATAR:
                addResponse(myShop.toProto(CfgShop.SHOP_AVATAR));
                break;
        }
    }

    void shopRefreshNew() {
        int type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        CfgShop.MyShop myShop = CfgShop.getMyShop(user);
        if (myShop == null) {
            addErrMessage();
            return;
        }
        if (user.getDbUser().getGem() < CfgShop.config.feeRefresh) {
            addErrMessage(getLang(Lang.err_not_enough_gem));
            return;
        }
        JSONArray arr = user.getAction().addMoney(this, Constans.PRICE_GEM, -CfgShop.config.feeRefresh);
        if (arr == null) {
            return;
        }
        List<Long> aLong = Bonus.receiveListItem(user, arr, "shop_refresh_" + type);
        if (aLong.isEmpty()) {
            addErrMessage();
            return;
        }
        if (type == CfgShop.SHOP_THANBI) myShop.startTime = 0;
        else myShop.startTimeAvatar = 0;
        if (CfgShop.refreshShop(user)) {
            addResponse(UPDATE_MONEY, user.protoInfor());
            addResponse(SHOP_SPECIAL, CfgShop.getMyShop(user).toProto(type));
        } else {
            addErrMessage();
        }
    }

    void shopRefresh() {
        CfgShop.MyShop myShop = CfgShop.getMyShop(user);
        if (myShop == null) {
            addErrMessage();
            return;
        }
        if (user.getDbUser().getGem() < CfgShop.config.feeRefresh) {
            addErrMessage(getLang(Lang.err_not_enough_gem));
            return;
        }
        JSONArray arr = user.getAction().addMoney(this, Constans.PRICE_GEM, -CfgShop.config.feeRefresh);
        if (arr == null) {
            return;
        }
        List<Long> aLong = Bonus.receiveListItem(user, arr, "shop_refresh");
        if (aLong.isEmpty()) {
            addErrMessage();
            return;
        }
        myShop.startTime = 0;
        if (CfgShop.refreshShop(user)) {
            addResponse(UPDATE_MONEY, user.protoInfor());
            addResponse(SHOP_SPECIAL, CfgShop.getMyShop(user).toProto(CfgShop.SHOP_THANBI));
        } else {
            addErrMessage();
        }
    }

    //<editor-fold desc="Database Access">
    void dbPlayerEnergy(UserInfo user, UserItemEntity uItem) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("update user_item");
            session.getTransaction().commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
    }

    boolean dbRefreshNumberAtk(UserInfo user, int gemCost) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("update user set gem=gem-" + gemCost + " where id=" + user.getId()).executeUpdate();
            SQLQuery query = session.createSQLQuery("update user_data set map_level=:mapLevel where user_id=" + user.getId());
            query.setString("mapLevel", user.getUData().getMap().toString(true));
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean dbSaveMonthlyCard(MonthlyCardReceivedEntity receivedEntity) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.save(receivedEntity);
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public MonthlyCardEntity[] dbGetMonthlyCard(String username, int serverId) {
        username = CfgCluster.getRealUsername(username);
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query50 = session.createSQLQuery("select * from " + CfgCommon.mainDb + "monthly_card where server_id=" + serverId + " and username='" + username + "' and type=50000 order by id desc limit 1").addEntity(MonthlyCardEntity.class);
            SQLQuery query100 = session.createSQLQuery("select * from " + CfgCommon.mainDb + "monthly_card where server_id=" + serverId + " and username='" + username + "' and type=100000 order by id desc limit 1").addEntity(MonthlyCardEntity.class);
            return new MonthlyCardEntity[]{(MonthlyCardEntity) query50.uniqueResult(), (MonthlyCardEntity) query100.uniqueResult()};
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public MonthlyCardReceivedEntity[] dbGetMonthlyCardReceived(long userId, int serverId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String strDate = DateTime.getDateyyyyMMddCross(new Date());
            SQLQuery query50 = session.createSQLQuery("select * from " + CfgCommon.mainDb + "monthly_card_received where server_id=" + serverId + " and user_id='" + userId + "' and card_type=50000 and date_created='" + strDate + "'").addEntity(MonthlyCardReceivedEntity.class);
            SQLQuery query100 = session.createSQLQuery("select * from " + CfgCommon.mainDb + "monthly_card_received where server_id=" + serverId + " and user_id='" + userId + "' and card_type=100000 and date_created='" + strDate + "'").addEntity(MonthlyCardReceivedEntity.class);
            return new MonthlyCardReceivedEntity[]{(MonthlyCardReceivedEntity) query50.uniqueResult(), (MonthlyCardReceivedEntity) query100.uniqueResult()};
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //</editor-fold>

    //<editor-fold desc="Proto">
    GGProto.CommonVector protoBuyAvatar(UserEntity dbUser, MyAvatar avatar) {
        List<Long> aLong = new ArrayList<Long>();
        aLong.add(dbUser.getGold());
        aLong.add(dbUser.getGem());
        List<Integer> a = avatar.toList();
        for (int i : a) {
            aLong.add((long) i);
        }
        return CommonProto.getCommonLongVectorProto(aLong, null);
    }

    GGProto.ProtoStorage protoStorage(UserInfo user) {
        GGProto.ProtoStorage.Builder builder = GGProto.ProtoStorage.newBuilder();
        builder.setBomb(protoBomb(user));
        builder.setHero(protoHero(user));
        builder.setMaterial(protoMaterial(user));
        builder.setItem(protoItem(user));
        builder.setAvatar(protoAvatar(user));
        builder.setPet(protoPet(user));
        builder.setPetFood(protoPetFood(user));
        builder.setAccessories(protoAccessories(user));
        builder.setStoneUfo(protoStone(user));

        return builder.build();
    }

    GGProto.CommonVector.Builder protoPetFood(UserInfo user) {
        List<UserPetFoodEntity> foods = user.getRes().getPetFoods();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserPetFoodEntity food : foods) {
            if (food.getNumber() > 0) {
                if (food.getFoodId() > Resources.aPetFood.size()) {
                    getLogger().warn("food " + food.getFoodId());
                    continue;
                }
                builder.addANumber(food.getFoodId());
                builder.addANumber(food.getNumber());
            }
        }
        return builder;
    }

    GGProto.CommonVector.Builder protoPet(UserInfo user) {
        List<UserPetEntity> pets = user.getRes().getPets();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserPetEntity pet : pets) {
            if (pet.getPetId() > Resources.aPet.size()) {
                getLogger().warn("pet " + pet.getPetId());
                continue;
            }
            builder.addANumber(pet.getPetId());
            builder.addANumber(pet.getExp());
            builder.addANumber(pet.getLevel());
            builder.addANumber(pet.getStar());
            builder.addANumber(pet.getFragment());
            if (user.getVersion().compareTo("1.4.2") > 0) {
                builder.addANumber(pet.getTienhoalv());
            }
        }
        return builder;
    }

    GGProto.CommonVector.Builder protoBomb(UserInfo user) {
        List<UserBombEntity> bombs = user.getRes().getBombs();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserBombEntity bomb : bombs) {
            if (bomb.getBombId() > Resources.aBomb.size()) {
                getLogger().warn("bomb " + bomb.getBombId());
                continue;
            }
            builder.addANumber(bomb.getId());
            builder.addANumber(bomb.getBombId());
            builder.addANumber(bomb.getLevel());
        }
        return builder;
    }

    GGProto.CommonVector.Builder protoStone(UserInfo user) {
        List<UserStoneUfoEntity> stones = user.getRes().getStoneUfos();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserStoneUfoEntity stone : stones) {
            if (stone.getStoneId() > Resources.ufoUpgrade.stones.size()) {
                continue;
            }
            builder.addANumber(stone.getStoneId());
            builder.addANumber(stone.getNumber());
        }
        return builder;
    }

    GGProto.CommonVector.Builder protoAccessories(UserInfo user) {
        List<UserAccessoriesEntity> accessories = user.getRes().getAccessories();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserAccessoriesEntity accessory : accessories) {
            if (accessory.getAccessoriesId() > Resources.aAccessories.size()) {
                getLogger().warn("access " + accessory.getAccessoriesId());
                continue;
            }
            builder.addANumber(accessory.getId());
            builder.addANumber(accessory.getAccessoriesId());
            builder.addANumber(accessory.getLevel());
        }
        return builder;
    }

    GGProto.CommonVector.Builder protoHero(UserInfo user) {
        List<UserHeroEntity> heroes = user.getRes().getHeroes();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserHeroEntity hero : heroes) {
            if (hero.getHeroId() > Resources.aHero.size()) {
                getLogger().warn("hero " + hero.getHeroId());
                continue;
            }
            builder.addANumber(hero.getHeroId());
            builder.addANumber(hero.getExp());
            builder.addANumber(hero.getLevel());
        }
        return builder;
    }

    GGProto.CommonVector.Builder protoMaterial(UserInfo user) {
        List<UserMaterialEntity> materials = user.getRes().getMaterials();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserMaterialEntity material : materials) {
            if (material.getNumber() > 0) {
                if (material.getMaterialId() > Resources.aMaterial.size()) {
                    getLogger().warn("material " + material.getMaterialId());
                    continue;
                }
                builder.addANumber(material.getMaterialId());
                builder.addANumber(material.getNumber());
            }
        }
        return builder;
    }

    GGProto.CommonVector.Builder protoItem(UserInfo user) {
        List<UserItemEntity> items = user.getRes().getItems();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserItemEntity item : items) {
            if (item.getNumber() > 0) {
                //                if (item.getItemId() > Resources.aItem.size()) {
                //                    getLogger().warn("item " + item.getItemId());
                //                    continue;
                //                }
                builder.addANumber(item.getItemId());
                builder.addANumber(item.getNumber());
            }
        }
        return builder;
    }

    GGProto.CommonVector.Builder protoAvatar(UserInfo user) {
        List<UserAvatarEntity> avatars = user.getRes().getAvatars();
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (UserAvatarEntity avatar : avatars) {
            if (avatar.getAvatarId() > Resources.aAvatar.size()) {
                getLogger().warn("avatar " + avatar.getAvatarId());
                continue;
            }
            builder.addANumber(avatar.getAvatarId());
            builder.addANumber(avatar.getLevel());
            builder.addANumber(avatar.getFragment());
        }
        return builder;
    }

    GGProto.ProtoShopList protoShopAvatar(int character, int type) {
        return null;
    }

    GGProto.ProtoShopList protoShopItem() {
        List<ResShopItem> aShop = Resources.aShop;
        GGProto.ProtoShopList.Builder builder = GGProto.ProtoShopList.newBuilder();
        builder.setShopType(Bonus.ITEM);
        builder.setTimeout(-1);
        builder.setFeeRefresh(-1);
        for (ResShopItem item : aShop) {
            if (item.getPricetype() > 0) {
                GGProto.ProtoShop.Builder tmp = GGProto.ProtoShop.newBuilder();
                tmp.setType(Bonus.ITEM);
                tmp.setPriceType(item.getPricetype());
                tmp.setPrice(item.getPrice());
                tmp.setSaleoff("");
                tmp.setImageId(item.getId());
                tmp.setNumber(-1);
                builder.addAShop(tmp);
            }
        }
        return builder.build();
    }

    GGProto.ProtoShopList protoShopHero() {
        List<ResHero> aHero = Resources.aHero;
        GGProto.ProtoShopList.Builder builder = GGProto.ProtoShopList.newBuilder();
        builder.setTimeout(-1);
        builder.setFeeRefresh(-1);
        builder.setShopType(Bonus.HERO);
        for (ResHero hero : aHero) {
            GGProto.ProtoShop.Builder tmp = GGProto.ProtoShop.newBuilder();
            tmp.setType(Bonus.HERO);
            tmp.setPriceType(hero.getPrice_type());
            tmp.setPrice(hero.getPrice());
            tmp.setSaleoff("");
            tmp.setImageId(hero.getId());
            tmp.setNumber(-1);
            builder.addAShop(tmp);
        }
        return builder.build();
    }

    //</editor-fold>
}
