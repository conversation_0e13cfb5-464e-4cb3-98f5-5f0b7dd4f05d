package com.handler;

import com.bem.config.GameCfgBoss;
import com.bem.config.GameCfgClanWar;
import com.handler.game.*;
import com.k2tek.Config;
import com.k2tek.Constans;
import com.proto.GGProto;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 12/11/2014.
 */
public class GameHandler extends AHandler {
    static Map<Integer, AAHandler> mSubGameHandler = new HashMap<Integer, AAHandler>();

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        GoldHunting.getInstance().initAction(mGameHandler, this.mSubGameHandler, getInstance());
        MaterialHunting.getInstance().initAction(mGameHandler, this.mSubGameHandler, getInstance());
        SlotMachine.getInstance().initAction(mGameHandler, this.mSubGameHandler, getInstance());

        for (int i = 0; i < Config.lstServerId.size(); i++) {
            BossGlobal.getInstance(Config.lstServerId.get(i)).initAction(mGameHandler, this.mSubGameHandler, getInstance());
            ClanBattle.getInstance(Config.lstServerId.get(i)).initAction(mGameHandler, this.mSubGameHandler, getInstance());
            Arena.getInstance(Config.lstServerId.get(i)).initAction(mGameHandler, this.mSubGameHandler, getInstance());
        }

        List<Integer> services = Arrays.asList(ACTIVITY_STATUS);
        services.forEach(service -> {
            mGameHandler.put(service, getInstance());
        });
    }

    static GameHandler instance;

    public static GameHandler getInstance() {
        if (instance == null) {
            instance = new GameHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new GameHandler();
    }

    @Override
    public void doSubAction() {
        AAHandler handler = mSubGameHandler.get(user.getDbUser().getServer() * 100000 + service);
        if (handler == null) handler = mSubGameHandler.get(service);
        if (handler != null) {
            handler.doAction(this, user, service, srcRequest);
        } else {
            switch (service) {
                case ACTIVITY_STATUS:
                    activityStatus();
                    break;
            }
        }
    }

    void activityStatus() {
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        { // Gold hunting
            GGProto.CommonVector.Builder cmm = GGProto.CommonVector.newBuilder();
            cmm.addANumber(Constans.EVENT_GOLD_HUNTING);
            cmm.addANumber(Constans.EVENT_GOLD_HUNTING);
            int numberAtk = GoldHunting.getInstance().getNumberAtk(user).getInt(0);
            cmm.addAString("Number Turn: " + (numberAtk < 0 ? 0 : numberAtk));
            builder.addAVector(cmm);
        }
        { // boss
            GGProto.CommonVector.Builder cmm = GGProto.CommonVector.newBuilder();
            cmm.addANumber(Constans.EVENT_BOSS_GLOBAL);
            int eventIndex = GameCfgBoss.getEventStatus().getIndex();
            cmm.addANumber(eventIndex < 0 ? 0 : 1);
            cmm.addANumber(eventIndex < 0 ? 0 : GameCfgBoss.getTimeout(eventIndex));
            cmm.addAString(eventIndex < 0 ? GameCfgBoss.getNextStrEvent() : "");
            builder.addAVector(cmm);
        }
        { // Material hunting
            GGProto.CommonVector.Builder cmm = GGProto.CommonVector.newBuilder();
            cmm.addANumber(Constans.EVENT_MATERIAL);
            cmm.addANumber(Constans.EVENT_MATERIAL);
            int numberAtk = MaterialHunting.getInstance().getNumberAtk(user).getInt(0);
            cmm.addAString("Number Turn: " + (numberAtk < 0 ? 0 : numberAtk));
            builder.addAVector(cmm);
        }
        {
            //            if (CfgServer.isVNServer()) {
                GGProto.CommonVector.Builder cmm = GGProto.CommonVector.newBuilder();
                cmm.addANumber(Constans.EVENT_CLAN_BATTLE);
                if (GameCfgClanWar.config.enable == 0) {
                    cmm.addANumber(0);
                    cmm.addANumber(0);
                    cmm.addAString("Closed");
                } else {
                    int eventIndex = GameCfgClanWar.getEventStatus().getIndex();
                    cmm.addANumber(eventIndex < 0 ? 0 : 1);
                    cmm.addANumber(eventIndex < 0 ? 0 : GameCfgClanWar.getTimeout(eventIndex));
                    cmm.addAString(eventIndex < 0 ? GameCfgClanWar.getNextStrEvent() : "");
                }
                builder.addAVector(cmm);
            //            }
        }
        {
//            GGProto.CommonVector.Builder cmm = GGProto.CommonVector.newBuilder();
//            cmm.addANumber(Constans.EVENT_ARENA);
//            if (GameCfgArena.config.enable == 0) {
//                cmm.addANumber(1);
//                cmm.addANumber(0);
//                cmm.addAString("Đang diễn ra sự kiện");
//            } else {
//                int eventIndex = GameCfgArena.getEventStatus().getIndex();
//                cmm.addANumber(eventIndex < 0 ? 0 : 1);
//                cmm.addANumber(eventIndex < 0 ? 0 : GameCfgArena.getTimeout(eventIndex));
//                cmm.addAString(eventIndex < 0 ? GameCfgArena.config.desc : "");
//            }
//
//            builder.addAVector(cmm);
        }
        addResponse(builder.build());
    }
}
