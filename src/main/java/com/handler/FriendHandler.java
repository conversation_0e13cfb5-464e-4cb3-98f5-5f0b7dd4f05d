package com.handler;

import com.bem.boom.BoomConfig;
import com.bem.boom.object.InviteBattle;
import com.bem.boom.object.SystemSlide;
import com.bem.config.CfgCommon;
import com.bem.config.CfgKethon;
import com.bem.config.CfgVip;
import com.bem.config.lang.Lang;
import com.bem.dao.SystemDAO;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.*;
import com.bem.monitor.Bonus;
import com.bem.monitor.ChatMonitor;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.ResShopItem;
import com.bem.object.UserInfo;
import com.bem.object.event.TopEventLoop;
import com.bem.util.Actions;
import com.bem.util.CommonProto;
import grep.database.HibernateUtil;
import com.bem.util.Util;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import com.bem.util.ChUtil;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class FriendHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        mGameHandler.put(MAIL_LIST, getInstance());
        mGameHandler.put(REQUEST_FRIEND, getInstance());
        mGameHandler.put(ANSWER_REQUEST_FRIEND, getInstance());
        mGameHandler.put(SEARCH_FRIEND, getInstance());
        mGameHandler.put(FRIEND_LIST, getInstance());
        mGameHandler.put(REMOVE_FRIEND, getInstance());
        mGameHandler.put(REMOVE_INVITE_BATTLE, getInstance());
        mGameHandler.put(MESSAGE_READ, getInstance());
        mGameHandler.put(MESSAGE_RECEIVE_BONUS, getInstance());
        mGameHandler.put(LOCK_ADD_FRIEND, getInstance());
        mGameHandler.put(FACEBOOK_FRIEND_LIST, getInstance());
        mGameHandler.put(GLOBAL_CHAT_HISTORY, getInstance());
        mGameHandler.put(GLOBAL_CHAT, getInstance());
        mGameHandler.put(LIST_REQUEST_ADDFRIEND, getInstance());
        mGameHandler.put(HOME_CHAT_LIST, getInstance());
        mGameHandler.put(TANG_QUA_BAN_BE, getInstance());
        mGameHandler.put(MARRY, getInstance());
        mGameHandler.put(DIVORCE, getInstance());
        mGameHandler.put(ACCEPT_MARY, getInstance());
    }

    static FriendHandler instance;

    public static FriendHandler getInstance() {
        if (instance == null) {
            instance = new FriendHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new FriendHandler();
    }

    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case MAIL_LIST:
                    mailList();
                    break;
                case REQUEST_FRIEND:
                    reqFriend();
                    break;
                case ANSWER_REQUEST_FRIEND:
                    answerReqFriend();
                    break;
                case SEARCH_FRIEND:
                    searchFriend();
                    break;
                case FRIEND_LIST:
                    friendList();
                    break;
                case REMOVE_FRIEND:
                    removeFriend();
                    break;
                case REMOVE_INVITE_BATTLE:
                    removeInviteBattle();
                    break;
                case MESSAGE_READ:
                    readMessage();
                    break;
                case MESSAGE_RECEIVE_BONUS:
                    receiveBonus();
                    break;
                case LOCK_ADD_FRIEND:
                    lockAddFriend();
                    break;
                case FACEBOOK_FRIEND_LIST:
                    fbFriendList();
                    break;
                case GLOBAL_CHAT_HISTORY:
                    globalChatHistory();
                    break;
                case GLOBAL_CHAT:
                    globalChat();
                    break;
                case LIST_REQUEST_ADDFRIEND:
                    addFriendsList();
                    break;
                case HOME_CHAT_LIST:
                    homeChatList();
                    break;
                case TANG_QUA_BAN_BE:
                    tangQuaBanBe();
                    break;
                case MARRY:
                    marry();
                    break;
                case DIVORCE:
                    divorce();
                    break;
                case ACCEPT_MARY:
                    acceptMarry();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void globalChatHistory() {
        long lastReq = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        addResponse(ChatMonitor.getChatHistory(lastReq));
    }

    void homeChatList() {
        addResponse(ChatMonitor.getHomeHistory());
    }

    void globalChat() {
        if (user.getDbUser().getLevel() < 10) {
            addErrMessage(getLang(Lang.required_to_chat));
            return;
        }

        String lockChat = Util.lockChat(user.getDbUser().getLockChat());
        if (lockChat != null) {
            addErrMessage(lockChat);
            return;
        }
        UserItemEntity uItem = user.getRes().mItem.get(ResShopItem.SPEAKER);
        if (uItem == null || uItem.getNumber() <= 0) {
            addShopMessage(getLang(Lang.err_not_enough_speaker));
            return;
        }
        if (System.currentTimeMillis() - user.getLastChat() > CfgCommon.config.chat.spam * 1000) {
            if (user.getAction().addItem(Arrays.asList(ResShopItem.SPEAKER), -1)) {
                GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
                String msg = cmm.getAString(0);
                if (user.getDbUser().getVip() < 3
                        && (msg.toLowerCase().contains("hackgame") || msg.toLowerCase().contains("hachgame") || msg.toLowerCase().contains("hackboom"))) {
                    long cheaterId = user.getId();
                    Channel kickChanel = Online.getChannel(cheaterId);
                    if (kickChanel != null) {
                        UserInfo kickUser = (UserInfo) ChUtil.get(kickChanel, Constans.KEY_USER);
                        if (kickUser != null) {
                            Calendar ca = Calendar.getInstance();
                            ca.add(Calendar.YEAR, 10);
                            kickUser.getDbUser().setLockChat(ca.getTime());
                            String session = kickUser.getSession();
                            long id = kickUser.getId();
                            Online.logoutChannel(kickChanel);
                            UserMonitor.mUser.remove(session);
                            UserMonitor.mUserId.remove(id);
                        }
                    }
                    Database2.update("user", Arrays.asList("lock_chat", "2020-01-01 00:00:00"), Arrays.asList("id", String.valueOf(cheaterId)));
                    Database2.insert(CfgCommon.mainDb + "auth_user_details", Arrays.asList("user_id", "block_until", "block_reason"), Arrays.asList(String.valueOf(cheaterId), "2020-01-01 00:00:00", msg));
                } else {
                    long lastReq = cmm.getANumber(0);
                    if (msg.length() < CfgCommon.config.chat.length[0] || msg.length() > CfgCommon.config.chat.length[1]) {
                        addErrMessage(String.format(getLang(Lang.common_chat_length), CfgCommon.config.chat.length[1]));
                        return;
                    }
                    if (!ChatMonitor.addChat(user, msg, Constans.CHANNEL_GLOBAL)) {
                        addErrMessage(getLang(Lang.err_param));
                        return;
                    }
                    user.setLastChat(System.currentTimeMillis());
                    addResponse(ChatMonitor.getChatHistory(lastReq));
                }
            } else {
                addErrMessage();
            }
        } else {
            addErrMessage(getLang(Lang.err_quick_chat));
        }
    }


    void lockAddFriend() {
        long setting = user.getDbUser().getSettingsIndex(BoomConfig.SETTING_BLOCK_ADD_FRIEND);
        long newSetting = setting == 0 ? 1 : 0;
        user.getDbUser().setSettingsIndex(BoomConfig.SETTING_BLOCK_ADD_FRIEND, newSetting);
        if (Database2.update("user", Arrays.asList("settings", user.getDbUser().getSettings()), Arrays.asList("id", String.valueOf(user.getId())))) {
            addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(newSetting), null));
        } else {
            user.getDbUser().setSettingsIndex(BoomConfig.SETTING_BLOCK_ADD_FRIEND, setting);
            addErrMessage();
        }
    }

    void readMessage() {
        int id = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        UserMessageEntity msg = (UserMessageEntity) Database2.getUnique("user_message", Arrays.asList("id", String.valueOf(id)), UserMessageEntity.class);
//        if (msg != null && !msg.isBonusMsg() && msg.getUserId() == user.getId()) {
        if (msg != null && msg.getUserId() == user.getId()) {
            Database2.update("user_message", Arrays.asList("receive", "1"), Arrays.asList("id", String.valueOf(id)));
            updateNotify(user.getId(), -1);
        }
    }

    void receiveBonus() {
        int id = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        UserMessageEntity msg = (UserMessageEntity) Database2.getUnique("user_message", Arrays.asList("id", String.valueOf(id)), UserMessageEntity.class);
        if (msg != null && msg.getUserId() == user.getId()) {
            if (msg.getReceive() == 1) {
                addErrMessage(getLang(Lang.err_bonus_not_exist));
            } else {
                if (Database2.update("user_message", Arrays.asList("receive", "1"), Arrays.asList("id", String.valueOf(id)))) {
                    List<Long> aLong = Bonus.receiveListItem(user, JSONArray.fromObject(msg.getBonus()), "message");
                    addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
                    updateNotify(user.getId(), -1);
                } else {
                    addErrMessage();
                }
            }
        }
    }

    void removeInviteBattle() {
        int teamId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        List<InviteBattle> aInvite = user.getAInvite();
        for (int i = 0; i < aInvite.size(); i++) {
            if (aInvite.get(i).getTeamId() == teamId) {
                aInvite.remove(i);
                break;
            }
        }
        addResponse(service, null);
    }

    void removeFriend() {
        long friendId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        try {
//            System.out.println("lyhon");
            divorce();
        } catch (Exception ex) {
//            System.out.println(ex);
        }
        if (dbDeleteFriend(user.getId(), friendId)) {
            addResponse(service, null);

        } else {
            addErrMessage();
        }
    }

    void fbFriendList() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        List<String> aStr = cmm == null ? new ArrayList<String>() : cmm.getAStringList();
        String strIds = "";
        for (String str : aStr) {
            strIds += ",'fb" + str + "'";
        }
        strIds = strIds.length() > 0 ? strIds.substring(1) : strIds;
        List<UserEntity> aUser = strIds.length() == 0 ? new ArrayList<UserEntity>() : dbListFbUser(strIds);
        if (aUser == null) {
            addErrMessage();
            return;
        }
        addResponse(service, protoListUserRequireAdd(aUser));
    }

    void divorce() {
        long userFId = CommonProto.parseCommonVector(srcRequest).getANumber(0);

        UserFriendEntity uF = new UserDAO().dbGetUserFriend(user.getId(), userFId);
        if (uF == null) {
            //addErrMessage("không có bằng hữu này");
            addErrMessage(getLang(Lang.no_friend_in_list));
            return;
        }
        if (uF.getWifeStatus() == 0) {
//            addErrMessage("Bạn chưa kết hôn với người này");
            addErrMessage(getLang(Lang.not_marry));
            return;
        }
        long user1 = 0;
        long user2 = 0;
        if (uF.getUser1() == userFId) {
            user1 = userFId;
            user2 = user.getId();
        } else {
            user2 = userFId;
            user1 = user.getId();
        }
        if (Database2.update("user_friend", Arrays.asList("wife_status", String.valueOf(0)), Arrays.asList("user1", String.valueOf(user1), "user2", String.valueOf(user2)))) {
//            addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(userFId, 1l, user.getDbUser().getGem()), null));
//            System.out.println("Arrays.asList(userFId, 1l,user.getDbUser().getGem())-->" + Arrays.asList(userFId, 0l));

            UserEntity ue = new UserDAO().getUserEntity(uF.getUser1());
//                UserDataEntity ud = new UserDAO().getUserData(uF.getUser1());
            UserDataEntity ud = (UserDataEntity) Database2.getUnique("user_data", Arrays.asList("user_id", String.valueOf(ue.getId())), UserDataEntity.class);

            if (ue != null && ud != null) {
                TopEventLoop ev = new TopEventLoop(ue.getId(), ud, ue, Constans.EVENT_TOP_CAP_DOI);
                if (Database2.update("user_event", Arrays.asList("value", String.valueOf(0)), Arrays.asList("user_id", String.valueOf(uF.getUser1()), "event_id", String.valueOf(ev.getEventId()), "event_index", String.valueOf(ev.getEventIndex()[0])))) {
//                    byte xByte = new byte(0);
                    uF.setWifeStatus((byte) (0));
//                    userCauhon.getUEvent().add(Constans.EVENT_TOP_CAP_DOI, new TopEventLoop(userFId, userCauhon.getUData(), userCauhon.getDbUser(), Constans.EVENT_TOP_CAP_DOI));
//                    Channel ch = Online.getChannel(uF.getUser1());
//                    UserInfo userCauhon = Util.getUser(ch);
//                    userCauhon.getUEvent().remove(Constans.EVENT_TOP_CAP_DOI);
                    Actions.save(user.getDbUser(), "LY_HON", "LY_HON", Actions.convertToLogString(Arrays.asList("user2", String.valueOf(userFId))));
                } else {
                    addErrMessage();
                    return;
                }
            } else {
                addErrMessage();
                return;

            }

            addResponse(DIVORCE, CommonProto.getCommonLongVectorProto(Arrays.asList(userFId, 0l, uF.getPoint()), null));
            Channel ch = Online.getChannel(userFId);
            UserInfo userCauhon = Util.getUser(ch);
            if (ch != null && userCauhon != null) {
                sendMessage(ch, DIVORCE, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), 0l, uF.getPoint()), null));
            }
        } else {
            addErrMessage();
            return;
        }

    }


    void acceptMarry() {
        long type = CommonProto.parseCommonVector(srcRequest).getANumber(0);// 0 tu choi , 1 dong y
        long userFId = CommonProto.parseCommonVector(srcRequest).getANumber(1);
        int point = (int) CommonProto.parseCommonVector(srcRequest).getANumber(2);
        Channel ch = Online.getChannel(userFId);
        if (type == 1) {
            if (ch == null) {
                CfgKethon.makeDangkykethon(CfgKethon.TUCHOI, userFId, 0);
//                addErrMessage("người cầu hôn không online");
                addErrMessage(getLang(Lang.invisible_proposal));
                return;
            }
            UserInfo userCauhon = Util.getUser(ch);
            if (userCauhon == null) {
                CfgKethon.makeDangkykethon(CfgKethon.TUCHOI, userFId, 0);
//                addErrMessage("người cầu hôn không online");
                addErrMessage(getLang(Lang.invisible_proposal));
                return;
            }
            if (userCauhon.getDbUser().getGem() < point) {
                CfgKethon.makeDangkykethon(CfgKethon.TUCHOI, userFId, 0);
//                addErrMessage("người cầu hôn không đủ tiền");
                addErrMessage(getLang(Lang.not_enough_gem_to_propose));
                return;
            }

            UserFriendEntity uF = new UserDAO().dbGetUserFriend(user.getId(), userFId);
            if (uF == null) {
                CfgKethon.makeDangkykethon(CfgKethon.TUCHOI, userFId, 0);
//                addErrMessage("không có bằng hữu này");
                addErrMessage(getLang(Lang.no_friend_in_list));
                return;
            }
            if (uF.getWifeStatus() == 1) {
                CfgKethon.makeDangkykethon(CfgKethon.TUCHOI, userFId, 0);
//                addErrMessage("Bạn đã kết hôn với người này");
                addErrMessage(getLang(Lang.be_married));
                return;
            }
            if (new UserDAO().dbHasFamily(userFId) != null) {
                CfgKethon.makeDangkykethon(CfgKethon.TUCHOI, userFId, 0);
//                addErrMessage("Người này đã có gia đình rồi chia bùn!");
                addErrMessage(getLang(Lang.be_married_person));
                return;
            }
            if (new UserDAO().dbHasFamily(user.getId()) != null) {
                CfgKethon.makeDangkykethon(CfgKethon.TUCHOI, userFId, 0);
//                addErrMessage("Bạn đã có gia đình rồi chia bùn!");
                addErrMessage(getLang(Lang.you_has_been_married));
                return;
            }

            JSONArray arr = userCauhon.getAction().addMoney(this, 2, -point);
            if (arr == null) {
                addErrMessage();
                return;
            }

            List<Long> tmp = Bonus.receiveListItem(userCauhon, arr, "mary");


            long user1 = 0;
            long user2 = 0;
            if (uF.getUser1() == userFId) {
                user1 = userFId;
                user2 = user.getId();
            } else {
                user2 = userFId;
                user1 = user.getId();
            }
            if (Database2.update("user_friend", Arrays.asList("wife_status", String.valueOf(1)), Arrays.asList("user1", String.valueOf(user1), "user2", String.valueOf(user2)))) {
                UserFriendEntity uf = new UserDAO().dbHasFamily(userFId);
                boolean ck = false;
//                if(uf==null){
//                    System.out.println("uf null----------------->");
//                }else{
//                    System.out.println("uf.getUser1()-->"+uf.getUser1());
//                    System.out.println("userFId---->"+userFId);
//                }

                if (uf != null && (uf.getUser1() == userFId || uf.getUser2() == userFId)) {
                    try {
//                        System.out.println("aaaaaaaaaaaaaaaaa");
//                        if(userCauhon.getUEvent())
                        if (uf.getUser1() == userFId) {
                            userCauhon.getUEvent().add(Constans.EVENT_TOP_CAP_DOI, new TopEventLoop(userFId, userCauhon.getUData(), userCauhon.getDbUser(), Constans.EVENT_TOP_CAP_DOI));
                        } else {
                            user.getUEvent().add(Constans.EVENT_TOP_CAP_DOI, new TopEventLoop(userFId, userCauhon.getUData(), userCauhon.getDbUser(), Constans.EVENT_TOP_CAP_DOI));

                        }
                        ck = true;
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
                if (ck) {
                    addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(userFId, 1l, user.getDbUser().getGem()), null));
                    sendMessage(ch, service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), 1l, userCauhon.getDbUser().getGem()), null));
                    if (point >= 10000) {
                        SystemDAO dao = new SystemDAO();
                        dao.sendMail(user.getId(), "Phần thưởng kết hôn!", "[3,37,1]");
                        dao.sendMail(userFId, "Phần thưởng kết hôn!", "[3,37,1]");
                        sendMessage(ch, IAction.MSG_POPUP, CommonProto.getCommonLongVectorProto(null, Arrays.asList("các bạn đã kết hôn thành công hãy vào hòm thư để khám phá quà từ chúng tôi nhé")));
//                        addPopupMessage("các bạn đã kết hôn thành công hãy vào hòm thư để khám phá quà từ chúng tôi nhé");
                        addPopupMessage(getLang(Lang.bonus_to_marry));

                    }
                    Actions.save(user.getDbUser(), "KET_HON", "KET_HON", Actions.convertToLogString(Arrays.asList("user2", String.valueOf(userCauhon.getDbUser().getId()))));
//                    SystemSlide.addMessage("Chúc Mừng <color=\"#00b939\">" + user.getDbUser().getName() + "</color>" + " và " + "<color=\"#00b939\">" + userCauhon.getDbUser().getName() + "</color>" + " đã trở thành vợ chồng ");
                    SystemSlide.addMessage(String.format(getLang(Lang.marry_wish_from_system), user.getDbUser().getName(), userCauhon.getDbUser().getName()));

                } else {
                    addErrMessage();
//                    System.out.println("loi 21");
                    return;
                }
            } else {
                addErrMessage();
//                System.out.println("loi 2");
                return;
            }
        } else {
            if (ch != null) {
                UserInfo userCauhon = Util.getUser(ch);
                if (userCauhon != null) {
                    sendMessage(ch, service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), 0l, userCauhon.getDbUser().getGem()), null));
                }
            }
        }
    }

    void marry() {
        long userFId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        int idGift = (int) CommonProto.parseCommonVector(srcRequest).getANumber(1);
        int[] arrPoint = {1000, 10000};
        String mess = CommonProto.parseCommonVector(srcRequest).getAString(0);
        int point = 1;
//        System.out.println("userFId-->" + userFId);
//        System.out.println("idGift-->" + idGift);
        try {
            point = arrPoint[idGift];
        } catch (Exception ex) {
            addErrMessage();
            return;
        }
        if (user.getDbUser().getGem() < point) {
//            addErrMessage("không có đủ kim cương!");
            addErrMessage(getLang(Lang.err_not_enough_gem));
            return;
        }
//        System.out.println("userFId-->" + userFId);
        UserFriendEntity uF = new UserDAO().dbGetUserFriend(user.getId(), userFId);
        if (uF == null) {
//            addErrMessage("không có bằng hữu này");
            addErrMessage(getLang(Lang.no_friend_in_list));
//            System.out.println("11111111111111111");
            return;
        }
        if (uF.getWifeStatus() == 1) {
//            addErrMessage("Bạn đã kết hôn với người này");
            addErrMessage(getLang(Lang.you_has_been_married_this));
//            System.out.println("222222222222222222");

            return;
        }
        if (new UserDAO().dbHasFamily(userFId) != null) {
//            addErrMessage("Người này đã có gia đình rồi chia bùn!");
            addErrMessage(getLang(Lang.be_married_person));
//            System.out.println("333333333333333333333333");

            return;
        }

        if (new UserDAO().dbHasFamily(user.getId()) != null) {
//            System.out.println("444444444444444444444444");

//            addErrMessage("Bạn đã có gia đình rồi chia bùn!");
            addErrMessage(getLang(Lang.you_has_been_married));
            return;
        }
        if (uF.getPoint() < 20000) {
//            addErrMessage("điểm thân mật chưa đủ");
            addErrMessage(getLang(Lang.err_not_enough_relationship));
//            System.out.println("555555555555555555555555");

            return;
        }

        Channel ch = Online.getChannel(userFId);
        if (ch == null) {
//            addErrMessage("người cầu hôn không online");
            addErrMessage(getLang(Lang.invisible_proposal));
            return;
        } else {
            CfgKethon.makeDangkykethon(CfgKethon.CAUHON, user.getId(), userFId);
            sendMessage(ch, service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), (long) point), Arrays.asList(mess)));
//            addErrMessage("Yêu cầu kết hôn đã được gửi");
            addErrMessage(getLang(Lang.request_propose_to_send));
//            System.out.println("111111111111111");
        }
//        long user1 = 0;
//        long user2 = 0;
//        if (uF.getUser1() == userFId) {
//            user1 = userFId;
//            user2 = user.getId();
//        } else {
//            user2 = userFId;
//            user1 = user.getId();
//        }
//        if (Database.update("user_friend", Arrays.asList("point_friend", String.valueOf(uF.getPoint() + point)), Arrays.asList("user1", String.valueOf(user1), "user2", String.valueOf(user2)))) {
//            addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(userFId, 1l, user.getDbUser().getGem()), null));
//            System.out.println("Arrays.asList(userFId, 1l,user.getDbUser().getGem())-->" + Arrays.asList(userFId, 1l, user.getDbUser().getGem()));
//        } else {
//            addErrMessage();
//            return;
//        }

    }

    void tangQuaBanBe() {
        int[] arrPoint = {50, 100, 500, 5, 10, 1};
        long userFId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        int idGift = (int) CommonProto.parseCommonVector(srcRequest).getANumber(1);
        int number = (int) CommonProto.parseCommonVector(srcRequest).getANumber(2);
//        System.out.println("userFId-->" + userFId);
//        System.out.println("idGift-->" + idGift);
//        System.out.println("number-->" + number);
        int point = 1;
        try {
            point = arrPoint[idGift - ResShopItem.IPHONE];
            point = point * number;
        } catch (Exception ex) {
            addErrMessage();
            return;
        }
        if (user.getRes().getMItem().get(idGift) == null || user.getRes().getMItem().get(idGift).getNumber() < number) {
//            addErrMessage("không có đủ vật phẩm");
            addErrMessage(getLang(Lang.err_not_enough));
            return;
        }

        UserFriendEntity uF = new UserDAO().dbGetUserFriend(user.getId(), userFId);
        if (uF == null) {
//            addErrMessage("không có bằng hữu này");
            addErrMessage(getLang(Lang.no_friend_in_list));
            return;
        }

        if (!user.getAction().addItem(Arrays.asList(idGift), -number)) {
            addErrMessage(getLang(Lang.err_not_enough));
            return;
        }
        long user1 = 0;
        long user2 = 0;
        if (uF.getUser1() == userFId) {
            user1 = userFId;
            user2 = user.getId();
        } else {
            user2 = userFId;
            user1 = user.getId();
        }
        if (uF.getWifeStatus() == 0) {// ban be thuong
            if (Database2.update("user_friend", Arrays.asList("point_friend", String.valueOf(uF.getPoint() + point)), Arrays.asList("user1", String.valueOf(user1), "user2", String.valueOf(user2)))) {
                addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(userFId, uF.getPoint() + point, (long) idGift, (long) user.getRes().getMItem().get(idGift).getNumber()), null));
//                System.out.println("userFId, uF.getPoint() + point, (long) idGift, (long) user.getRes().getMItem().get(idGift).getNumber()-->" + Arrays.asList(userFId, uF.getPoint() + point, (long) idGift, (long) user.getRes().getMItem().get(idGift).getNumber()));
                try {
                    Actions.save(user.getDbUser(), "TANG_QUA_BANBE", "TANG_QUA_BANBE", Actions.convertToLogString(Arrays.asList("idGift", String.valueOf(idGift),
                            "number", String.valueOf(number), "numberRemain", String.valueOf(user.getRes().getMItem().get(idGift).getNumber()))));
                } catch (Exception ex) {

                }
            } else {
//                System.out.println("aaaaaaaaaaaaaaaaa");
                addErrMessage();
                return;
            }
        } else {
            UserEntity ue = new UserDAO().getUserEntity(uF.getUser1());
//            UserDataEntity ud = new UserDAO().getUserData(uF.getUser1());
            UserDataEntity ud = (UserDataEntity) Database2.getUnique("user_data", Arrays.asList("user_id", String.valueOf(ue.getId())), UserDataEntity.class);
//            System.out.println("success--1--->");
            if (ue != null && ud != null) {
//                System.out.println("success--2--->");
                TopEventLoop ev = new TopEventLoop(ue.getId(), ud, ue, Constans.EVENT_TOP_CAP_DOI);
//                System.out.println("event id----------------->"+ev.getEventId());
//                System.out.println("event index----------------->"+ev.getEventIndex()[0]);
//                System.out.println("user_id--->"+uF.getUser1());

                if (Database2.update("user_event", Arrays.asList("value", String.valueOf(ev.getValue() + point)), Arrays.asList("user_id", String.valueOf(uF.getUser1()), "event_id", String.valueOf(ev.getEventId()), "event_index", String.valueOf(ev.getEventIndex()[0])))) {
//                    System.out.println("success--3--->");
                    addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(userFId, ev.getValue() + point, (long) idGift, (long) user.getRes().getMItem().get(idGift).getNumber()), null));
//                    System.out.println("userFId, uF.getPoint() + point, (long) idGift, (long) user.getRes().getMItem().get(idGift).getNumber()-->" + Arrays.asList(userFId, uF.getPoint() + point, (long) idGift, (long) user.getRes().getMItem().get(idGift).getNumber()));
                    Actions.save(user.getDbUser(), "TANG_QUA_CAPDOI", "TANG_QUA_CAPDOI", Actions.convertToLogString(Arrays.asList("idGift", String.valueOf(idGift),
                            "number", String.valueOf(number), "numberRemain", String.valueOf(user.getRes().getMItem().get(idGift).getNumber()))));
                    try {
                        user.getDbUser().setDiemcapdoi((int) (ev.getValue() + point));

                        Channel ch = Online.getChannel(userFId);
                        if (ch == null) {
                            UserInfo userFriend = Util.getUser(ch);
                            if (userFriend != null) {
                                userFriend.getDbUser().setDiemcapdoi((int) (ev.getValue() + point));
                            }
                        }
                    } catch (Exception ex) {

                    }
                } else {
//                    System.out.println("bbbbbbbbbbbbbbbbbbbbbbb");
                    addErrMessage();
                    return;
                }
            } else {
//                System.out.println("cccccccccccccccccccccc");
                addErrMessage();
                return;

            }
        }
    }

    void friendList() {
        int maxFreind = CfgVip.config.get(user.getDbUser().getVip()).numMaxFriends;
        List<UserFriendEntity> aUserEntity = new UserDAO().dbListUserFriend(user.getId(), maxFreind);
        if (aUserEntity == null) {
            addErrMessage();
            return;
        }
        Map<Long, UserFriendEntity> mFriends = new HashMap<>();
//        List<Long> points = new ArrayList<>();
        for (int i = 0; i < aUserEntity.size(); i++) {
            if (aUserEntity.get(i).getWifeStatus() == 1) {
                UserEntity ue = new UserDAO().getUserEntity(aUserEntity.get(i).getUser1());
                UserDataEntity ud = (UserDataEntity) Database2.getUnique("user_data", Arrays.asList("user_id", String.valueOf(ue.getId())), UserDataEntity.class);
                if (ue != null && ud != null) {
                    TopEventLoop ev = new TopEventLoop(ue.getId(), ud, ue, Constans.EVENT_TOP_CAP_DOI);
//                    points.add(ev.getValue());
                    aUserEntity.get(i).setPoint(ev.getValue());
//                    System.out.println("ev.value--->"+ev.getValue());
//                    System.out.println("point.ad--------->"+ev.getValue());
//                    System.out.println("i0-------->"+i);
                }
//                else {
//                    points.add(0l);
//                    System.out.println("00000000000001111111111111111");
//                }

            }
//            else {
//                System.out.println("ban be");
//                points.add(aUserEntity.get(i).getPoint());
//            }


            if (aUserEntity.get(i).getUser1() == user.getId()) {
                mFriends.put(aUserEntity.get(i).getUser2(), aUserEntity.get(i));
//                System.out.println("aUserEntity.get(i).getUser2()--->"+aUserEntity.get(i).getUser2());
//                System.out.println("aUserEntity.get(i).point-->"+aUserEntity.get(i).getPoint());

            } else if (aUserEntity.get(i).getUser2() == user.getId()) {
//                System.out.println("aUserEntity.get(i).getUser1()--->"+aUserEntity.get(i).getUser1());

                mFriends.put(aUserEntity.get(i).getUser1(), aUserEntity.get(i));
            }

        }
        String strIds = "";
        for (UserFriendEntity friend : aUserEntity) {
            strIds += "," + (friend.getUser1() == user.getId() ? friend.getUser2() : friend.getUser1());
        }
        strIds = strIds.length() > 0 ? strIds.substring(1) : strIds;
        List<UserEntity> aUser = strIds.length() == 0 ? new ArrayList<UserEntity>() : dbListUser(strIds);
        if (aUser == null) {
            addErrMessage();
            return;
        }
        addResponse(service, protoListUser(aUser, user.getId(), mFriends));
    }

    void searchFriend() {
        String name = "";
        Long userId = -1l;
        try {
            name = CommonProto.parseCommonVector(srcRequest).getAString(0);
        } catch (Exception ex) {
        }
        try {
            userId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        if (!name.trim().equalsIgnoreCase("") && name.trim().length() < 3) {
            addErrMessage(getLang(Lang.err_search_length));
            return;
        }
        if (!name.trim().equalsIgnoreCase("")) {
            UserEntity uEntity = (UserEntity) Database2.getUnique("user", Arrays.asList("name", name.trim(), "server", String.valueOf(user.getDbUser().getServer())), UserEntity.class);
            if (uEntity != null) {
                addResponse(service, CommonProto.protoUser(uEntity, null));
            } else {
                addErrMessage(getLang(Lang.user_not_exist));
            }
        } else if (userId > -1) {
            UserEntity uEntity = (UserEntity) Database2.getUnique("user", Arrays.asList("id", userId + "", "server", String.valueOf(user.getDbUser().getServer())), UserEntity.class);
            if (uEntity != null) {
                addResponse(service, CommonProto.protoUser(uEntity, null));
            } else {
                addErrMessage(getLang(Lang.user_not_exist));
            }
        } else {
            addErrMessage(getLang(Lang.user_not_exist));
        }
    }

    void reqFriend() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        int friendId = (int) cmm.getANumber(0);
        String name = cmm.getAString(0);
        //
        if (friendId == user.getId()) {
            addErrMessage(getLang(Lang.err_make_friend));
            return;
        }
        UserEntity friendUser = new UserDAO().getUserEntity(friendId);
        if (friendUser.getServer() != user.getDbUser().getServer()) {
            addErrMessage(getLang(Lang.err_user_not_same_server));
            return;
        }
        UserFriendEntity uFriend = dbUserFriend(user.getId(), friendId);
        if (uFriend == null) {
            addErrMessage();
            return;
        }
        //
        if (uFriend.getFstatus() == -1) {
            int numberFriend = new UserDAO().getNumberFriend(friendId);
            if (friendUser == null || numberFriend == -1) {
                addErrMessage();
                return;
            }
            int maxFriend = CfgVip.maxFiend(friendUser.getVip());
            if (numberFriend >= maxFriend) {
                addErrMessage(getLang(Lang.mfriend_err_max_number));
                return;
            }
            if (friendUser.getSettingsIndex(BoomConfig.SETTING_BLOCK_ADD_FRIEND) == 1) {
                addErrMessage(getLang(Lang.mfriend_reject));
            } else if (Database2.insert("user_friend", Arrays.asList("user1", "user2", "fstatus"),
                    Arrays.asList(String.valueOf(user.getId()), String.valueOf(friendId), "0")) != -1) {
                addResponse(service, null);
                Channel friendChannel = Online.getChannel(friendId);
                if (friendChannel != null) {
                    sendMessage(friendChannel, REQUEST_FRIEND_RECEIVE, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getDbUser().getId()), Arrays.asList(user.getDbUser().getName())));
                }
                updateNotify(friendUser.getId(), 1);
            } else {
                addErrMessage();
            }
        } else if (uFriend.getFstatus() == 0) {
            if (uFriend.getUser2() == user.getId()) { // dong y ket ban
                if (Database2.update("user_friend", Arrays.asList("fstatus", "1"), Arrays.asList("user1", String.valueOf(friendId), "user2", String.valueOf(user.getId())))) {
                    addErrMessage(String.format(getLang(Lang.mfriend_accept), name));
                } else {
                    addErrMessage();
                }
            } else {
                addResponse(service, null);
            }
        } else if (uFriend.getFstatus() == 1) {
            addErrMessage(String.format(getLang(Lang.mfriend_accept), name));
        }
    }

    void answerReqFriend() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        int friendId = (int) cmm.getANumber(0);
        int fstatus = (int) cmm.getANumber(1);
        String name = cmm.getAString(0);
        //
        UserFriendEntity uFriend = dbUserFriend(user.getId(), friendId);
        if (uFriend == null) {
            addErrMessage();
            return;
        }
        //
        if (fstatus == 0) {
            if (Database2.delete("user_friend", Arrays.asList("user1", String.valueOf(uFriend.getUser1()), "user2", String.valueOf(uFriend.getUser2())))) {
                addResponse(service, null);
                updateNotify(user.getId(), -1);
            } else {
                addErrMessage();
            }
        } else if (fstatus == 1) {
            int numberFriend = new UserDAO().getNumberFriend(user.getId());
            if (numberFriend == -1) {
                addErrMessage();
                return;
            }
            int maxFriend = CfgVip.maxFiend(user.getDbUser().getVip());
            if (numberFriend >= maxFriend) {
                addErrMessage(getLang(Lang.mfriend_err_max_number));
                return;
            }
            if (Database2.update("user_friend", Arrays.asList("fstatus", "1"),
                    Arrays.asList("user1", String.valueOf(uFriend.getUser1()), "user2", String.valueOf(uFriend.getUser2())))) {
                addResponse(service, null);
                Channel newChannel = Online.getChannel(friendId);
                sendMessage(newChannel, ANSWER_REQUEST_FRIEND1, CommonProto.protoUser(user.getDbUser(), user.getUData()));
                updateNotify(user.getId(), -1);
            } else {
                addErrMessage();
            }
        }
    }

    void addFriendsList() {
        List<UserFriendEntity> aUserEntity = dbListFriendRequest(user.getId());
        if (aUserEntity == null) {
            addErrMessage();
            return;
        }
        String strIds = "";
        for (UserFriendEntity friend : aUserEntity) {
            strIds += "," + (friend.getUser1() == user.getId() ? friend.getUser2() : friend.getUser1());
        }
        strIds = strIds.length() > 0 ? strIds.substring(1) : strIds;
        List<UserEntity> aUser = strIds.length() == 0 ? new ArrayList<UserEntity>() : dbListUser(strIds);
//        System.out.println(aUser.size());
        if (aUser == null) {
            addErrMessage();
            return;
        }
        addResponse(service, protoListUserRequireAdd(aUser));
    }

    void mailList() {
        List<UserMessageEntity> aMsg = Database2.getList("user_message", Arrays.asList("user_id", String.valueOf(user.getId())), " order by receive asc, date_created desc limit 0," + CfgCommon
                .config.maxMail, UserMessageEntity.class);
        aMsg = aMsg == null ? new ArrayList<UserMessageEntity>() : aMsg;
        addResponse(service, protoMailList(aMsg));
    }

    //<editor-fold desc="Database Access">
    boolean updateNotify(long userId, int value) {
        return Database2.updateNumber("user", Arrays.asList("notify", String.valueOf(value)), Arrays.asList("id", String.valueOf(userId)));
    }

    boolean dbDeleteFriend(long userId, long friendId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("delete from user_friend where (user1=:user1 and user2=:user2) or (user1=:user2 and user2=:user1)");
            query.setLong("user1", userId);
            query.setLong("user2", friendId);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return false;
    }

    List<UserEntity> dbSearchUser(String name, long myId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user where name like :name and id <> :id limit 0,20").addEntity(UserEntity.class);
            query.setString("name", "%" + name + "%");
            query.setLong("id", myId);

            return query.list();
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    List<UserEntity> dbListUser(String strIds) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user where id in(" + strIds + ")").addEntity(UserEntity.class);
            return query.list();
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    List<UserEntity> dbListFbUser(String strIds) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user where username in(" + strIds + ")").addEntity(UserEntity.class);
            return query.list();
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public List<UserFriendEntity> dbListFriendRequest(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_friend where user2=:id and fstatus=0 order by date_created desc limit 0,50").addEntity(UserFriendEntity.class);
            query.setLong("id", userId);
            return query.list();
        } catch (Exception he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    UserFriendEntity dbUserFriend(long userId, long friendId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from user_friend where (user1=:user1 and user2=:user2) or (user1=:user2 and user2=:user1) limit 0,1").addEntity(UserFriendEntity.class);
            query.setLong("user1", userId);
            query.setLong("user2", friendId);
            UserFriendEntity uFriend = (UserFriendEntity) query.uniqueResult();
            if (uFriend == null) {
                uFriend = new UserFriendEntity();
            }
            return uFriend;
        } catch (HibernateException he) {
            Logs.error(userId + " - " + friendId + " -> " + Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //<editor-fold>

    //<editor-fold desc="Proto">
    GGProto.ProtoMailList protoMailList(List<UserMessageEntity> aMsg) {
        long curTime = System.currentTimeMillis();
        GGProto.ProtoMailList.Builder builder = GGProto.ProtoMailList.newBuilder();
//        for (UserEntity user : aUser) {
//            GGProto.ProtoUser.Builder tmp = GGProto.ProtoUser.newBuilder();
//            tmp.setId(user.getId());
//            tmp.setName(user.getName());
//            tmp.setOnline(Online.isOnline(user.getId()) ? 1 : (user.getLogout() - curTime) / 1000);
//            builder.addAReqFriend(tmp);
//        }
//        for (InviteBattle invite : aInvite) {
//            GGProto.ProtoInviteBattle.Builder tmp = GGProto.ProtoInviteBattle.newBuilder();
//            tmp.setName(invite.getName());
//            tmp.setUserId(invite.getUserId());
//            tmp.setTeamId(invite.getTeamId());
//            builder.addAInviteBattle(tmp);
//        }
        for (UserMessageEntity msg : aMsg) {
            GGProto.ProtoMessage.Builder tmp = GGProto.ProtoMessage.newBuilder();
            tmp.setId(msg.getId());
            tmp.setTitle(msg.getTitle());
            tmp.setMessage(msg.getMessage());
            if (msg.getListBonus() != null && msg.getListBonus().size() > 0) {
                tmp.setMsgType(true);
                tmp.addAllBonus(msg.getListBonus());
            } else {
                tmp.setMsgType(false);
            }
            tmp.setReceive(msg.getReceive());
            tmp.setTime((curTime - msg.getDateCreated().getTime()) / 1000);
            builder.addAMsg(tmp);
        }
        return builder.build();
    }

    GGProto.ProtoListUser protoListUserRequireAdd(List<UserEntity> aUser) {
        long curTime = System.currentTimeMillis();
        GGProto.ProtoListUser.Builder builder = GGProto.ProtoListUser.newBuilder();
        for (UserEntity user : aUser) {
            GGProto.ProtoUser.Builder tmp = CommonProto.protoUser(user, null).toBuilder();
            tmp.setId(user.getId());
            tmp.setUsername(user.getUsername());
            tmp.setFacebookId(user.getFacebook());
            tmp.setName(user.getName());
            tmp.setOnline(Online.isOnline(user.getId()) ? 1 : (user.getLogout() - curTime) / 1000);
            tmp.setLvl(user.getLevel());
            tmp.setRank(0);
            tmp.setTrophy(0);

            builder.addAUser(tmp);
        }
        return builder.build();
    }

    GGProto.ProtoListUser protoListUser(List<UserEntity> aUser, long myUser, Map<Long, UserFriendEntity> lstFrends) {
        long curTime = System.currentTimeMillis();
        GGProto.ProtoListUser.Builder builder = GGProto.ProtoListUser.newBuilder();
//        for (UserEntity user : aUser) {
        for (int i = 0; i < aUser.size(); i++) {
            UserEntity user = aUser.get(i);
            GGProto.ProtoUser.Builder tmp = CommonProto.protoUser(user, null).toBuilder();
            tmp.setId(user.getId());
            tmp.setUsername(user.getUsername());
            tmp.setFacebookId(user.getFacebook());
            tmp.setName(user.getName());
            tmp.setOnline(Online.isOnline(user.getId()) ? 1 : (user.getLogout() - curTime) / 1000);
            tmp.setLvl(user.getLevel());
            UserFriendEntity uf = new UserDAO().dbHasFamily(user.getId());
            if (uf != null && uf.getWifeStatus() == 1 && (uf.getUser1() == myUser || uf.getUser2() == myUser)) {
                tmp.setRank(1);
            } else {
                tmp.setRank(0);

            }
            if (uf != null && uf.getWifeStatus() == 1) {
//                UserFriendEntity uotherUf = new UserDAO().dbHasFamily(user.getId());
//                if (uotherUf != null) {
//                System.out.println("asadas-->" + user.getName());
                if (user.getId() == uf.getUser1()) {
                    tmp.setGold(uf.getUser2());
//                    System.out.println("uf.getUser2()-->" + uf.getUser2());
                } else {
                    tmp.setGold(uf.getUser1());
//                    System.out.println("uf.getUser1()-->" + uf.getUser1());
                }
//                }
            } else {
//                System.out.println("0000000");
                tmp.setGold(0);
            }
            if (lstFrends.get(aUser.get(i).getId()) != null) {
//                System.out.println("ko null--------------->");
                tmp.setTrophy(lstFrends.get(aUser.get(i).getId()).getPoint());
            } else {
//                System.out.println("null-------------------->");

                tmp.setTrophy(0l);
            }
//            System.out.println("---------------->" + tmp.getGold());
//            System.out.println("auser-->" + user.getName());
//            System.out.println("uf.point--->" + tmp.getTrophy());
            builder.addAUser(tmp);
        }
        return builder.build();
    }
//    GGProto.ProtoListUser protoListUserFreinds(List<UserEntity> aUser) {
//        long curTime = System.currentTimeMillis();
//        GGProto.ProtoListUser.Builder builder = GGProto.ProtoListUser.newBuilder();
//        for (UserEntity user : aUser) {
//            GGProto.ProtoUser.Builder tmp = CommonProto.protoUser(user, null).toBuilder();
//            tmp.setId(user.getId());
//            tmp.setUsername(user.getUsername());
//            tmp.setFacebookId(user.getFacebook());
//            tmp.setName(user.getName());
//            tmp.setOnline(Online.isOnline(user.getId()) ? 1 : (user.getLogout() - curTime) / 1000);
//            tmp.setLvl(user.getLevel());
//            tmp.setGold();
//            builder.addAUser(tmp);
//        }
//        return builder.build();
//    }

    //</editor-fold>
}
