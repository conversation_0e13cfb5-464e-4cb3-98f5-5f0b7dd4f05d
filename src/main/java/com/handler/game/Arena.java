package com.handler.game;

import com.bem.boom.CacheBattle;
import com.bem.boom.object.CachePlayer;
import com.bem.boom.object.SystemSlide;
import com.bem.config.GameCfgArena;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.dao.mapping.LogEventEntity;
import com.bem.dao.mapping.UserMessageEntity;
import com.bem.matcher.TeamMatcher;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Online;
import com.bem.object.*;
import com.bem.object.event.ArenaInfo;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MCache;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.protobuf.AbstractMessage;
import com.handler.AHandler;
import com.handler.GameHandler;
import com.k2tek.Config;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.Xerver;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import io.netty.channel.Channel;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class Arena extends AAHandler implements ITimer {

    final static String name = "arena";
    final static String KEY_JOIN = name + ":";

    final static String KEY_INFO = name + ":info";
    final static String KEY_DAMAGE = name + ":damagearena:";
    int time = 5;
    int[] arrMinus = {GameCfgArena.config.timeBang, GameCfgArena.config.timeBang + GameCfgArena.config.waittimeToKnockout, GameCfgArena.config.timeBang + GameCfgArena.config.waittimeToKnockout + time, GameCfgArena.config.timeBang + GameCfgArena.config.waittimeToKnockout + (time * 2), GameCfgArena.config.timeBang + GameCfgArena.config.waittimeToKnockout + (time * 3)};


    public ArenaInfo info = new ArenaInfo();
    static Map<Long, UserInfo> mUserInfor = new HashMap<>();
    static Map<Long, UserInfo> mUserInforKnockout = new HashMap<>();
    static Map<Long, Channel> mUserChannel = new HashMap<>();

    int serverId;

    public Arena(int serverId) {
        this.serverId = serverId;
    }
    boolean sendBonus = false;
//    static Arena instance;

    public synchronized static Map<Long, UserInfo> getmUserInforKnockout() {
        return mUserInforKnockout;
    }

    public synchronized static void addMUserInforKnockout(UserInfo mUserInfor) {
        Arena.mUserInforKnockout.put(mUserInfor.getId(), mUserInfor);
    }

    public synchronized static Map<Long, UserInfo> getmUserInfor() {
        return mUserInfor;
    }

    public synchronized static void addMUserInfor(UserInfo mUserInfor) {
        Arena.mUserInfor.put(mUserInfor.getId(), mUserInfor);
    }

    public void resetRound() {
        info.setRound(-1);
    }

    public synchronized static int getRound(int serverId) {
        return getInstance(serverId).info.getRound();
    }


    public synchronized static Map<Long, Channel> getmUserChannel() {
        return mUserChannel;
    }

    public static void addMUserChannel(Channel channel, long userId) {
        Arena.mUserChannel.put(userId, channel);
    }

//    public static Arena getInstance() {
//        if (instance == null) {
//            instance = new Arena();
//        }
//        return instance;
//    }
static Map<Integer,Arena> instance;

    public static synchronized Arena getInstance(int serverId) {
        if(instance==null){
            instance = new HashMap<>();
        }
        if (instance.get(serverId) == null) {

            instance.put(serverId,new Arena(serverId));
        }
        return instance.get(serverId);
    }

    static {
        for (int i = 0; i < Config.lstServerId.size(); i++) {
            try {

//            MCache.getInstance().delete(KEY_INFO+":"+Config.lstServerId.get(i)+":");
                getInstance(Config.lstServerId.get(i)).info = (ArenaInfo) MCache.getInstance().get(KEY_INFO+":"+Config.lstServerId.get(i)+":");
                if (getInstance(Config.lstServerId.get(i)).info == null) {
                    try {
                        int eventIndex = GameCfgArena.getEventIndex();
                        int curYear = Calendar.getInstance().get(Calendar.YEAR);
                        List<UserMessageEntity> aMessage = new ArrayList<>();
                        String sInfor = getInstance(Config.lstServerId.get(i)).dbgetEventArena(Constans.EVENT_ARENA, eventIndex, curYear);
                        getInstance(Config.lstServerId.get(i)).info = new Gson().fromJson(sInfor, new TypeToken<ArenaInfo>() {
                        }.getType());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                getInstance(Config.lstServerId.get(i)).getLogger().error(Util.exToString(ex));
            }

            if (getInstance(Config.lstServerId.get(i)).info == null) {
                getInstance(Config.lstServerId.get(i)).info = new ArenaInfo();
            }
        }
    }

    //region init class
    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler, Map<Integer, AAHandler> mSubGameHandler, GameHandler parent) {
        List<Integer> services = Arrays.asList(ARENA_OUT_BATTLE, ARENA_LIST_USER, ARENA_LIST_USER_KNOCKOUT, ARENA_BATLE, BOSS_TOP_BONUS, ARENA_JOIN, ARENA_OUT, ARENA_ATTENTION_BATTLE, ARENA_ROOMINFOR, ARENA_ATTENTION_BATTLE);

        services.forEach(service -> {
            mGameHandler.put(service, parent);
            for (int i = 0; i < Config.lstServerId.size(); i++) {
                mSubGameHandler.put(service, getInstance(Config.lstServerId.get(i)));
            }
//            mSubGameHandler.put(service, getInstance());
        });
    }

    @Override
    public void doAction(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (GameCfgArena.config.enable != 1) {
//            handler.addErrMessage("Sự kiện chưa mở");
            return;
        }
//        if (GameCfgArena.config.requireLevel > user.getDbUser().getLevel()) {
//            handler.addErrMessage(String.format("Đạt cấp %s để tham gia sự kiện", GameCfgArena.config.requireLevel));
//            return;
//        }
        EventStatus status = GameCfgArena.getEventStatus(); // index, index from year
        synchronized (info) {
            if (info.status.getId() != status.getId()) {
                if (status.getId() >= 0) { // vao su kien
                    Calendar calendar = Calendar.getInstance();
                    int hour = calendar.get(Calendar.HOUR_OF_DAY);
                    int minute = calendar.get(Calendar.MINUTE);
                    if (info.getType() != ArenaInfo.TYPEGROUPTIME && GameCfgArena.config.time.contains(hour) && minute < (GameCfgArena.config.timeBang)) {// vong bang
                        initHistory();
                        info.setType(ArenaInfo.TYPEGROUPTIME);
                    }
                    info.topPlayer = new ArrayList<>();
                    sendBonus = false;
                }
                info.status = status;
                cacheEventInfo();
            } else {

            }
        }
        try {
            switch (service) {
                case ARENA_JOIN:

                    joinArena(handler, user, service, srcRequest);
                    break;
                case ARENA_OUT:
                    outArena(handler, user, service, srcRequest);
                    break;
                case ARENA_BATLE:
                    battle(handler, user, service, srcRequest);
                    break;
                case ARENA_OUT_BATTLE:
                    cancelGame(handler, user, service, srcRequest);
                    break;
                case ARENA_LIST_USER_KNOCKOUT:
                    listUserKnouckout(handler, user, service, srcRequest);
                    break;
                case ARENA_LIST_USER:
                    listUserBang(handler, user, service, srcRequest);
                    break;
                case ARENA_ROOMINFOR:
                    roomInfor(user.getId());
                    break;
            }
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
            handler.addErrMessage();
        }
    }


    public void chat(UserInfo user, String text) {
        try {
            long userId = user.getId();
            if (info.getRound() < 3 && info.getRound() + 1 >= -1) {
                int index = -1;
                for (int i = 0; i < info.lstHistoryUserKnockout.get(info.getRound() + 1).size(); i++) {
                    if (info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId() == userId) {
                        index = i;
                        break;
                    }
                }
                if (index > -1) {
                    Channel chOther = null;
                    for (int i = 0; i < info.lstHistoryUserKnockout.get(info.getRound() + 1).size(); i++) {
                        if (info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId() != userId && (i / 2) == (index / 2)) {
                            chOther = getmUserChannel().get(info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId());
                            break;
                        }
                    }
                    if (chOther != null) {
                        Util.sendProtoData(chOther, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getId(), (long) user.getDbUser().getMAvatar().getUserAvatar()), Arrays.asList(user.getDbUser().getName(), text)), IAction.TEAM_CHAT, System.currentTimeMillis());
                    }
                }
            }
        } catch (Exception ex) {

        }
    }

    public int gettimeOut() {
        Calendar calendar = Calendar.getInstance();
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);
        int rs = 0;
        for (int i = 0; i < arrMinus.length; i++) {
            if (minute < arrMinus[i]) {
                rs = 60 - second + (arrMinus[i] - 1 - minute) * 60;
                break;
            }
        }
        return rs;
    }

    public int getNumberUserRemain(List<UserArena> lst) {
        try {
            int rs = 0;
            for (int i = 0; i < lst.size(); i++) {
                if (lst.get(i).getStatus() >= ArenaInfo.STATUSWIN) {
                    rs++;
                }
            }
            return rs;
        } catch (Exception ex) {

        }
        return 0;
    }

    public void initHistory() {
        info.lstHistoryUserKnockout = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            info.lstHistoryUserKnockout.add(new ArrayList<>());
        }
        for (int j = 0; j < 16; j++) {
            info.lstHistoryUserKnockout.get(0).add(j, getNoneUserArena());
        }
        for (int j = 0; j < 8; j++) {
            info.lstHistoryUserKnockout.get(1).add(j, getNoneUserArena());
        }
        for (int j = 0; j < 4; j++) {
            info.lstHistoryUserKnockout.get(2).add(j, getNoneUserArena());
        }
        for (int j = 0; j < 2; j++) {
            info.lstHistoryUserKnockout.get(3).add(j, getNoneUserArena());
        }
        info.lstHistoryUserKnockout.get(4).add(0, getNoneUserArena());
    }

    @Override
    public void checkStatus() {
//        if (sendBonus || info.status.getId() != -1) return;
//        System.out.println("t-->"+info.getType());
        if (info.getType() > ArenaInfo.TYPENOTTIME) {
            Calendar calendar = Calendar.getInstance();
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);
//            System.out.println(info.status);
            synchronized (info) {
//                if (info.getType() != ArenaInfo.TYPEGROUPTIME && GameCfgArena.config.time.contains(hour) && minute < (GameCfgArena.config.timeBang)) {// vong bang
//                    initHistory();
//                    info.setType(ArenaInfo.TYPEGROUPTIME);
//                } else

                if (info.getType() != ArenaInfo.TYPEWAITTIME && GameCfgArena.config.time.contains(hour) && minute == (GameCfgArena.config.timeBang)) {// cho chuyen knockout

                    info.setType(ArenaInfo.TYPEWAITTIME);
//                    info.lstUserKnockout = new ArrayList<>();
                    for (int i = 0; i < Math.min(info.topPlayer.size(), 16); i++) {
                        info.topPlayer.get(i).setRoundKnockout(info.getRound());
//                        info.lstUserKnockout.add(info.topPlayer.get(i));

                        UserArena u = new UserArena(info.topPlayer.get(i));
                        u.setPoint(1);
//                        u.setStatus(ArenaInfo.STATUSREADY);
                        info.lstHistoryUserKnockout.get(0).set(i, u);
                        try {
                            MCache.getInstance().delete(ArenaInfo.ARENADELAY + info.topPlayer.get(i).getUserId());
                        } catch (Exception ex) {

                        }
                        try {
                            addMUserChannel(Online.getChannel(info.topPlayer.get(i).getUserId()), info.topPlayer.get(i).getUserId());
                            addMUserInforKnockout(getmUserInfor().get(info.topPlayer.get(i).getUserId()));
                        } catch (Exception ex) {
                        }
                    }
                    cacheEventInfo();
                    TeamMatcher.removeQueueAll();
//                    for (int i = 0; i < info.lstUserKnockout.size(); i++) {
//                        Channel ch = getmUserChannel().get(info.lstUserKnockout.get(i).getUserId());
//                        if (ch != null) {
//                            Util.sendProtoData(ch, protoStatus(info.lstUserKnockout.get(i).getUserId()), IAction.ARENA_JOIN, System.currentTimeMillis());
//                            roomInfor(info.lstUserKnockout.get(i).getUserId());
//                        }
//                    }

                    for (int i = 0; i < info.lstHistoryUserKnockout.get(0).size(); i++) {
                        if (info.lstHistoryUserKnockout.get(0).get(i).getUserId() > 0) {
                            Channel ch = getmUserChannel().get(info.lstHistoryUserKnockout.get(0).get(i).getUserId());
                            if (ch != null) {
                                Util.sendProtoData(ch, protoStatus(info.lstHistoryUserKnockout.get(0).get(i).getUserId()), IAction.ARENA_JOIN, System.currentTimeMillis());
                                roomInfor(info.lstHistoryUserKnockout.get(0).get(i).getUserId());
                            }
                        }
                    }

                } else if ((info.getType() != ArenaInfo.TYPEKNOCKOUTTIME && info.getType() != ArenaInfo.TYPENOTTIME) && GameCfgArena.config.time.contains(hour) && minute == (GameCfgArena.config.timeBang + GameCfgArena.config.waittimeToKnockout)) {// knockout
                    info.setType(ArenaInfo.TYPEKNOCKOUTTIME);
                    cacheEventInfo();
                }

                if (minute >= arrMinus[arrMinus.length - 1] + 4 && info.getType() > ArenaInfo.TYPENOTTIME) {
                    getLogger().warn("chan bi ket thuc ARENA--NEW #-->" + minute);
                    setFinalHisTory();
                    return;

                }
//                if (info.getRound()>=0&&(info.getType() == ArenaInfo.TYPEKNOCKOUTTIME || info.getType() == ArenaInfo.TYPEWAITTIME || (info.getType() == ArenaInfo.TYPEGROUPTIME && minute >= GameCfgArena.config.timeBang)) && getNumberUserRemain(info.lstHistoryUserKnockout.get(info.getRound())) <= 1) {// co nguoi chien thang hoac khong du nguoi tham gi
//                    setFinalHisTory();
//                    getLogger().warn("ARENA_TEST KET THUC NEW 5");
//                }

                /*chon team nockout*/
                if (info.getType() == ArenaInfo.TYPEKNOCKOUTTIME) {
                    if (minute >= arrMinus[1]) {
                        for (int i = 1; i < arrMinus.length; i++) {
                            if (arrMinus[i] == minute && (i - 1) != info.getRound()) {
                                info.setRound(i - 1);
                                if (getNumberUserRemain(info.lstHistoryUserKnockout.get(info.getRound())) <= 1) {
                                    clearUser(info.getRound());
                                    setFinalHisTory();
                                    getLogger().warn("ARENA_TEST KET THUC NEW 4");
                                    return;

                                } else {
                                    getLogger().warn("ARENA_TEST_ROUND: bat dau tran dau " + "|" + info.getRound());
                                    choseTeam();
                                }
                                break;
                            }
                        }
                    }
                }


                if (info.getRound() >= -1 && info.getRound() < 4) {
                    int round = info.getRound();
                    if (round < 0) {
                        round = 0;
                    }
                     /* Gui thong bao nguoi choi*/
                    for (int i = 1; i < arrMinus.length; i++) {
                        if (arrMinus[i] == minute + 1 && second >= 50) {
                            for (int j = 0; j < info.lstHistoryUserKnockout.get(round).size(); j++) {
                                if (info.lstHistoryUserKnockout.get(round).get(j).getUserId() > 0 && info.lstHistoryUserKnockout.get(round).get(j).getStatus() == ArenaInfo.STATUSWIN) {
                                    GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
                                    builder.addAString("chuẩn bị đánh knockout");
                                    Util.sendProtoData(Online.getChannel(info.lstHistoryUserKnockout.get(round).get(j).getUserId()), builder.build(), IAction.ARENA_ATTENTION_BATTLE, System.currentTimeMillis());
                                }
                            }
                        }
                    }
                }


            }
        }
    }

    protected String dbgetEventArena(int eventId, int eventIndex, int eventYear) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            String sql = "select data from log_event where event_id = " + eventId + " and event_index = " + eventIndex + " and event_year = " + eventYear+ " and server_id = "+serverId;
            Object ret = session.createSQLQuery(sql).uniqueResult();
            if (ret == null) {
                return null;
            } else {
                return (String) ret;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex),serverId);
        } finally {
            closeSession(session);
        }
        return null;
    }

    protected boolean dbSaveEventResult(int eventId, int eventIndex, int eventYear, String data, List<UserMessageEntity> aMessage) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            LogEventEntity logEvent = new LogEventEntity(serverId);
            logEvent.setEventId(eventId);
            logEvent.setEventIndex(eventIndex);
            logEvent.setEventYear(eventYear);
            logEvent.setData(data);
            session.beginTransaction();
            session.save(logEvent);
//            for (UserMessageEntity messageEntity : aMessage) {
//                session.save(messageEntity);
//            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    void cancelGame(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (getInstance(serverId).info.getRound() >= 0) {
            getLogger().warn("ARENA_CANCEL_GAME -knockout-->" + user.getUsername() + "||" + user.getDbUser().getName());
            return;
        }
        getLogger().warn("ARENA_CANCEL_GAME -bang-->" + user.getUsername() + "||" + user.getDbUser().getName());
        TeamObject team = user.getTeam();
        TeamMatcher.removeQueue(team);
        team.sendMessage(service, null);
        team.status = TeamObject.STATUS_NONE;

    }

    void battle(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (info.status.getId() == -1) {
//            handler.addErrMessage("Sự kiện chưa diễn ra");
            handler.addErrMessage(user.getLang().get(Lang.err_open_event));
            return;
        }
        String cache = (String) MCache.getInstance().get(ArenaInfo.ARENADELAY + user.getId());
        if (cache != null) {
            try {
//                handler.addErrMessage("Bạn bị phạt do rời bàn hãy chờ ít phút");
                handler.addErrMessage(user.getLang().get(Lang.leave_room_fine));
                return;
            } catch (Exception e) {

            }
        }
        if (info.getType() == ArenaInfo.TYPEGROUPTIME) {
            TeamObject team = new TeamObject(TeamObject.ARENA, user);
            TeamMatcher.addQueue(team);
            handler.addResponse(IAction.ARENA_BATLE, null);
        } else if (info.getType() == ArenaInfo.TYPEWAITTIME) {
//            handler.addErrMessage("Bạn hãy chờ vài phút để chuẩn bị cho vòng knockout");
            handler.addErrMessage(user.getLang().get(Lang.err_event_knockout_round_message));
            return;
        } else if (info.getType() == ArenaInfo.TYPEKNOCKOUTTIME) {
            TeamObject team = new TeamObject(TeamObject.ARENA, user);
        }

    }
    //endregion

    public boolean containTopUser(UserInfo user) {
        if (user.getHasArena() == -1) {
            synchronized (info) {
                for (int i = 0; i < info.topPlayer.size(); i++) {
                    if (info.topPlayer.get(i).getUserId() == user.getId()) {
                        user.setHasArena(info.getType());
                        return true;
                    }
                }
            }
        } else {
            return true;
        }
        return false;
    }


//    public void setHistoryKnockOut(UserArena u, int round, int win) {
//        if (round >= 0) {
////            int index = -1;
//            for (int i = 0; i < info.lstHistoryUserKnockout.get(round).size(); i++) {
//                if (info.lstHistoryUserKnockout.get(round).get(i).getUserId() == u.getUserId()) {
//                    info.lstHistoryUserKnockout.get(round).get(i).setPoint(win);
//                    if (win > 0) {
//                        getLogger().warn("ARENA_TEST: add history win" + "|" + info.lstHistoryUserKnockout.get(round).get(i).getUsername() + " ||| " + info.lstHistoryUserKnockout.get(round).get(i).getName() + " ||| vong: " + (round + 1));
//                        info.lstHistoryUserKnockout.get(round + 1).set(i / 2, new UserArena(info.lstHistoryUserKnockout.get(round).get(i)));
//                    }
//                    break;
//                }
//            }
//
//        }
//    }

    public void setFinalHisTory() {
        if (sendBonus) {
            return;
        }
        UserArena u = new UserArena();
//        for (int i = 0; i < info.lstUserKnockout.size(); i++) {
//            if (info.lstUserKnockout.get(i).getStatus() >= ArenaInfo.STATUSWIN) {
//                u = new UserArena(info.lstUserKnockout.get(i));
//                u.setPoint(1);
//                break;
//            }
//        }

        if (info.getRound() < 4) {

            int index = -1;

            for (int i = 0; i < info.lstHistoryUserKnockout.size(); i++) {
                int ck = 0;
                for (int j = 0; j < info.lstHistoryUserKnockout.get(i).size(); j++) {
                    if (info.lstHistoryUserKnockout.get(i).get(j).getUserId() > 0) {
                        ck = 1;
                        break;
                    }
                }
                if (ck == 0) {
                    index = i;
                    break;
                }
            }
            getLogger().warn("index History---> " + index);
            if (index >= 1) {
                int t = 0;
                for (int i = 0; i < info.lstHistoryUserKnockout.get(index - 1).size(); i++) {
//                    getLogger().warn("ket thuc ARENA--i -->"+i);
//                    getLogger().warn("ket thuc ARENA-- index - 1-->"+(index - 1));

                    if (info.lstHistoryUserKnockout.get(index - 1).get(i).getUserId() > 0 && info.lstHistoryUserKnockout.get(index - 1).get(i).getStatus() >= ArenaInfo.STATUSWIN) {
                        getLogger().warn("ARENA final win " + info.lstHistoryUserKnockout.get(index - 1).get(i).getName());
                        u = new UserArena(info.lstHistoryUserKnockout.get(index - 1).get(i));
                        u.setPoint(1);
                        t = t + 1;
                    }
                }
                if (t > 1) {
                    getLogger().warn("ARENA Khong co nguoi chien thang " + t);
                    u = new UserArena();
                    u.setName("");

                }
            }
            if (index > -1) {
                getLogger().warn("ARENA set nguoi chien thang tu " + index);
                for (int i = index; i <= 4; i++) {
                    info.lstHistoryUserKnockout.get(i).set(0, u);
                }
            }
        }
        if (u.getName() != null && !u.getName().trim().equalsIgnoreCase("")) {
            SystemSlide.addMessage("<color=\"#00b939\">" + u.getName() + "</color>" + " Đã doạt cúp vô địch ");
        }

        info.setRound(ArenaInfo.CHUNGKET + 1);
        info.setType(ArenaInfo.TYPENOTTIME);

        int eventIndex = GameCfgArena.getEventIndex();
        int curYear = Calendar.getInstance().get(Calendar.YEAR);
        List<UserMessageEntity> aMessage = new ArrayList<>();
        if (dbSaveEventResult(Constans.EVENT_ARENA, eventIndex, curYear, new Gson().toJson(info), aMessage)) {
            sendBonus = true;
        }
        clearUser(ArenaInfo.CHUNGKET + 1);
        getLogger().warn("ket thuc ARENA---->");
    }


    public void clearUser(int round) {
        if (round >= 1 && round <= 4) {
            for (int i = 0; i < info.lstHistoryUserKnockout.get(round - 1).size(); i++) {
                if (info.lstHistoryUserKnockout.get(round - 1).get(i).getUserId() > 0) {
                    info.lstHistoryUserKnockout.get(round - 1).get(i).setPoint(0);
                }
            }

            for (int i = 0; i < info.lstHistoryUserKnockout.get(round - 1).size(); i++) {
                for (int j = 0; j < info.lstHistoryUserKnockout.get(round).size(); j++) {
                    if (info.lstHistoryUserKnockout.get(round - 1).get(i).getUserId() > 0 &&
                            info.lstHistoryUserKnockout.get(round - 1).get(i).getUserId() == info.lstHistoryUserKnockout.get(round).get(j).getUserId()) {
                        info.lstHistoryUserKnockout.get(round - 1).get(i).setPoint(1);
                    }
                }
            }
        }
    }


    public void choseTeam() {
//        int[] round = {2, 4, 8, 16};
        synchronized (info) {
//            int number = round[info.getRound()];
            int number = 2;
            clearUser(info.getRound());
            List<UserArena> lst = info.lstHistoryUserKnockout.get(info.getRound());
            if (getNumberUserRemain(lst) >= 1 && lst.size() >= 2) {
                for (int i = 0; i < lst.size(); i = i + number) {
                    List<UserInfo> lstUser = new ArrayList<>();
                    for (int j = i; j < Math.min(i + number, lst.size()); j++) {
                        if (lst.get(j).getUserId() > 0) {
                            if (lst.get(j).getStatus() == ArenaInfo.STATUSREADY && getmUserInforKnockout().get(lst.get(j).getUserId()) != null) {
                                lstUser.add(getmUserInforKnockout().get(lst.get(j).getUserId()));
                            } else {
//                            if (info.lstUserKnockout.get(j).getStatus() == ArenaInfo.STATUSWIN) {
                                if (lst.get(j).getStatus() != ArenaInfo.STATUSREADY) {

                                    getLogger().warn("ARENA_TEST: Khong trong phong cho not ready" + "|" + lst.get(j).getUsername() + " ||| " + lst.get(j).getName() + " ||| " + info.getRound());
                                    getLogger().warn("ARENA_TEST STATUS-->" + lst.get(j).getStatus());
                                }
                                if (getmUserInforKnockout().get(lst.get(j).getUserId()) == null) {
                                    getLogger().warn("ARENA_TEST: Khong trong phong cho null infor" + "|" + lst.get(j).getUsername() + " ||| " + lst.get(j).getName() + " ||| " + info.getRound());

                                }
//                            }
//                            info.lstUserKnockout.get(j).setStatus(ArenaInfo.STATUSLOSE);
//                            setHistoryKnockOut(info.lstUserKnockout.get(j), info.getRound(), ArenaInfo.STATUSLOSE);
                                updateKnockoutPlayerWin(lst.get(j).getUserId(), ArenaInfo.STATUSLOSE, info.getRound());
                            }
                        }
                    }
                    if (lstUser.size() == 2) {
                        battleNextRound(lstUser, i);
                        for (int j = 0; j < lstUser.size(); j++) {
                            getLogger().warn("ARENA_TEST: Bat dau battle" + "|" + lstUser.get(j).getUsername() + " ||| " + lstUser.get(j).getDbUser().getName() + " ||| vong: " + info.getRound());
                            for (int k = 0; k < lst.size(); k++) {
                                if (lst.get(k).getUserId() == lstUser.get(j).getId()) {
//                                            info.lstUserKnockout.get(k).setStatus(ArenaInfo.STATUSWIN);
//                                    updateKnockoutPlayerWin( lstUser.get(j).getId(),  1);
                                    lst.get(k).setStatus(ArenaInfo.STATUSPLAY);
                                    roomInfor(lstUser.get(j).getId());
                                    break;
                                }
                            }
                        }
                    } else {
                        if (lstUser.size() > 0) {
                            getLogger().warn("ARENA_TEST: Nhom dau thua hoac thieu nguoi choi" + "|lstUser.size()" + lstUser.size() + "||roud: " + info.getRound());
                            for (int j = 0; j < lstUser.size(); j++) {
                                if (j == 0) {
                                    getLogger().warn("ARENA_TEST: Nguoi choi vao thang phong trong" + "|" + lstUser.get(j).getUsername() + " ||| " + lstUser.get(j).getDbUser().getName() + " ||| vong: " + info.getRound());
                                    for (int k = 0; k < lst.size(); k++) {
                                        if (lst.get(k).getUserId() == lstUser.get(j).getId()) {
//                                            info.lstUserKnockout.get(k).setStatus(ArenaInfo.STATUSWIN);
                                            updateKnockoutPlayerWin(lstUser.get(j).getId(), ArenaInfo.STATUSWIN, info.getRound());
//                                            updateKnockoutPlayerWin(lstUser.get(j).getId(), ArenaInfo.STATUSLOSE, info.getRound());
                                            updateKnockoutPlayerWin(lstUser.get(j).getId(), ArenaInfo.STATUSREADY, info.getRound() + 1);
//                                            info.lstUserKnockout.get(k).setStatus(ArenaInfo.STATUSREADY);
//                                            setHistoryKnockOut(info.lstUserKnockout.get(k), info.getRound(), ArenaInfo.STATUSWIN);
                                            sendToUser(lstUser.get(j).getId(), CommonProto.getErrorMsg("Bạn được vào vòng trong do người chơi bỏ cuộc"), IAction.MSG_TOAST);
                                            roomInfor(lstUser.get(j).getId());
                                            sendToUser(lstUser.get(j).getId(), protoStatus(lstUser.get(j).getId()), IAction.ARENA_JOIN);
                                            break;
                                        }
                                    }
                                } else {
                                    getLogger().warn("ARENA_TEST: Nguoi choi bi loai ra ngoai" + "|" + lst.get(j).getUsername() + " ||| " + lst.get(j).getName() + " ||| vong: " + info.getRound());
                                    for (int k = 0; k < lst.size(); k++) {
                                        if (lst.get(k).getUserId() == lstUser.get(j).getId()) {
//                                            info.lstUserKnockout.get(k).setStatus(ArenaInfo.STATUSLOSE);
                                            roomInfor(lstUser.get(j).getId());
//                                            setHistoryKnockOut(info.lstUserKnockout.get(k), info.getRound(), ArenaInfo.STATUSLOSE);
                                            updateKnockoutPlayerWin(lst.get(j).getUserId(), ArenaInfo.STATUSLOSE, info.getRound());
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                getLogger().warn("ARENA_TEST END NEW 1 chose team: round:" + info.getRound() + "||getNumberUserRemain(lst)->" + getNumberUserRemain(lst) + "||lst.size-->" + lst.size());
                setFinalHisTory();
                getLogger().warn("ARENA_TEST END NEW 1 chose team: round:" + info.getRound() + "||getNumberUserRemain(lst)->" + getNumberUserRemain(lst) + "||lst.size-->" + lst.size());

            }
        }
    }

    public void battleNextRound(List<UserInfo> lstUser, int i) {
//            System.out.println("battle next -------------->");
        TeamObject team1 = new TeamObject(TeamObject.ARENA, lstUser.get(0));
        try {
            getLogger().warn("ARENA_ CREATE TEAM-indexStar:" + i + "-user1->" + lstUser.get(0).getUsername() + "|" + lstUser.get(0).getDbUser().getName() + "|teamId-->" + team1.id + "|userTeamId-->" + lstUser.get(0).getTeam().id);
        } catch (Exception ex) {
            getLogger().warn("ARENA_EXCEPTION--->" + ex.toString());
        }
        TeamObject team2 = new TeamObject(TeamObject.ARENA, lstUser.get(1));
        try {
            getLogger().warn("ARENA_ CREATE TEAM-indexStar:" + i + "-user2->" + lstUser.get(1).getUsername() + "|" + lstUser.get(1).getDbUser().getName() + "|teamId-->" + team2.id + "|userTeamId-->" + lstUser.get(1).getTeam().id);
        } catch (Exception ex) {
            getLogger().warn("ARENA_EXCEPTION--->" + ex.toString());
        }
        createTableOld(Arrays.asList(team1, team2), 1);
    }

    BattleDataEntity prepareBattle(List<CachePlayer> aCachePlayer) {
        Session session = null;
        try {
//            gameType = TeamObject.ARENA;
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(TeamObject.ARENA);
            session.save(battle);

            for (CachePlayer cachePlayer : aCachePlayer) {
                session.save(new BattleUserEntity(battle.getId(), cachePlayer.getUserId(), TeamObject.ARENA));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }


    //region prepare battle
    String getKey(List<CachePlayer> aCachePlayer) {
        String key = String.valueOf(serverId) + "_" + TeamObject.ARENA + "_KNOUCKOUT_" + new Random().nextInt(100);
        for (int i = 0; i < aCachePlayer.size(); i++) {
            key += "_" + aCachePlayer.get(i).getUserId();
        }
        return key;
    }

    void createTableOld(List<TeamObject> tmp, int numberPlayer) {
        List<CachePlayer> aCachePlayer = new ArrayList<>();
        boolean cancel = false;
        List<List<Long>> lstUser = new ArrayList<>();
        for (int i = tmp.size() - 1; i >= 0; i--) {
            List<CachePlayer> aCache = tmp.get(i).getCachePlayer(0);
            getLogger().warn("ARENA aUser SIZE CACHE--" + i + "--aUser.size-->" + tmp.get(i).aUser.size());
            List<Long> lst = new ArrayList<>();
            for (int j = 0; j < tmp.get(i).aUser.size(); j++) {
                lst.add(tmp.get(i).aUser.get(j).getDbUser().getId());
            }
            lstUser.add(lst);
            if (aCache.size() < numberPlayer) {
                cancel = true;
            }
            aCachePlayer.addAll(aCache);
        }
        if (cancel) return;
        List<Long> aPlayerId = new ArrayList<>();
        for (int i = 0; i < aCachePlayer.size(); i++) {
            if (i < aCachePlayer.size() / 2) aCachePlayer.get(i).setTeam(0);
            else aCachePlayer.get(i).setTeam(1);
            aPlayerId.add(aCachePlayer.get(i).getUserId());
        }
        BattleDataEntity battle = prepareBattle(aCachePlayer);
        if (battle != null) {
            CacheBattle cacheBattle = new CacheBattle(aCachePlayer, 0, getKey(aCachePlayer), TeamObject.ARENA);
            cacheBattle.setBattleId(battle.getId());
            MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
            cacheBattle.setLevelIndex(info.getRound());
            getLogger().warn("ARENA TEAM SIZE ------>" + tmp.size());

            for (int i = tmp.size() - 1; i >= 0; i--) {
//                System.out.println("send tran danh ");
                tmp.get(i).status = TeamObject.STATUS_PLAY;
//                getLogger().warn("ARENA TEAM SIZE --" + i + "--aUser.size-->" + tmp.get(i).aUser.size());
                tmp.get(i).sendBattleInfoArena(cacheBattle.getKey());

//                List<Long> ids = new ArrayList<>();
//                if (tmp.get(i).aUser == null || tmp.get(i).aUser.size() == 0) {
//                    try {
//                        ids = lstUser.get(i);
//                    }catch (Exception ex){
//                    }
//                }else{
//                    for (int j = tmp.get(i).aUser.size() - 1; j >= 0; j--) {
//                        ids.add(tmp.get(i).aUser.get(j).getDbUser().getId());
//                    }
//                }
//                getLogger().warn("ARENA TEAM SIZE --id-new-->" + i + "--aUser.size-->" + ids.size());

//                for (int j = ids.size() - 1; j >= 0; j--) {
////                    for (int j = tmp.get(i).aUser.size() - 1; j >= 0; j--) {
////                    if (tmp.get(i).aUser.get(j) != null) {
////                        UserInfo user = UserMonitor.getUser(tmp.get(i).aUser.get(j).getId());
//                        UserInfo user = UserMonitor.getUser(ids.get(j));
//                        if (user != null && user.getTeam() != null && user.getTeam().id == tmp.get(i).id) {
//                            getLogger().warn("ARENA_SEND_BATTLE_INFOR--index:" + i + "->" + user.getId() + "|" + user.getUsername() + "|" + user.getDbUser().getName());
//                        } else if (user != null) {
//                            if (user.getTeam() == null) {
//                                getLogger().warn("ARENA_SEND_BATTLE_INFOR ERROR Ko co team--->" + user.getId() + "|" + user.getUsername() + "|" + user.getDbUser().getName());
//                            } else {
//                                getLogger().warn("ARENA_SEND_BATTLE_INFOR ERROR team ko khop--->" + user.getId() + "|" + user.getUsername() + "|" + user.getDbUser().getName());
//                            }
//                        } else {
//                            getLogger().warn("ARENA_SEND_BATTLE_INFOR ERROR Khong co trong userMonitor--->" + user.getId() + "|" + user.getUsername() + "|" + user.getDbUser().getName());
//                        }
////                    } else {
////                        getLogger().warn("ARENA_SEND_BATTLE_INFOR ERROR Khong co trong team--->");
////                    }
//                }

//                tmp.get(i).setLastMatchPlayer(aPlayerId);
//                aTeam.remove(tmp.get(i));
            }
        }
    }

    public boolean containKnockoutUser(long userId) {
//        if (user.getHasArena() != info.type) {
        int round = info.getRound();
        if (round < 0) {
            round = 0;
        }
        if (round > 4) {
            round = 4;
        }
        synchronized (info) {
            for (int i = 0; i < info.lstHistoryUserKnockout.get(round).size(); i++) {
                if (info.lstHistoryUserKnockout.get(round).get(i).getUserId() == userId && (info.lstHistoryUserKnockout.get(round).get(i).getStatus() == ArenaInfo.STATUSWIN || info.lstHistoryUserKnockout.get(round).get(i).getStatus() == ArenaInfo.STATUSREADY)) {
//                    user.setHasArena(info.getType());
                    return true;
                }
            }
//            }
        }
        return false;
    }


    public int typeKnockoutUser(long userId) {
//        if (user.getHasArena() != info.type) {
        try {
            synchronized (info) {
                for (int i = 0; i < info.lstHistoryUserKnockout.get(info.getRound()).size(); i++) {
                    if (info.lstHistoryUserKnockout.get(info.getRound()).get(i).getUserId() == userId) {
//                    user.setHasArena(info.getType());
                        return info.lstHistoryUserKnockout.get(info.getRound()).get(i).getStatus();
                    }
                }
//            }
            }
        } catch (Exception ex) {

        }
        return -1;
    }


    void listUserBang(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        List<GGProto.ProtoUser> lstU = new ArrayList<>();

        for (int i = 0; i < info.topPlayer.size(); i++) {
            if (info.topPlayer.get(i).getUserId() == user.getId()) {
                lstU.add(info.changeToProtoUser(info.topPlayer.get(i), getmUserInfor().get(info.topPlayer.get(i).getUserId())));
                break;
            }
        }
        for (int i = 0; i < info.topPlayer.size(); i++) {
            if (i <= 20) {
                lstU.add(info.changeToProtoUser(info.topPlayer.get(i), getmUserInfor().get(info.topPlayer.get(i).getUserId())));
            } else {
                break;
            }
        }
        handler.addResponse(IAction.ARENA_LIST_USER, CommonProto.ListProtoUser(lstU));
    }

    public UserArena getNoneUserArena() {
        UserArena u = new UserArena();
        u.setUsername("");
        u.setName("");
        u.setUserId(0);
        u.setPoint(1);
        u.setAvatar(Arrays.asList(0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));
        u.setRank(0);
        return u;
    }

    public void sendToUser(long userId, AbstractMessage data, int service) {
        Channel ch = getmUserChannel().get(userId);
        if (ch != null) {
            Util.sendProtoData(ch, data, service, System.currentTimeMillis());
        }
    }

    void roomInfor(long userId) {
        Channel ch = getmUserChannel().get(userId);
        if (info.getRound() < 4 && info.getRound() >= -1) {
            List<GGProto.ProtoUser> lstU = new ArrayList<>();
            int index = -1;
            for (int i = 0; i < info.lstHistoryUserKnockout.get(info.getRound() + 1).size(); i++) {
                if (info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId() == userId) {
                    index = i;
                    break;
                }
            }
            if (index > -1) {
                Channel chOther = null;
                for (int i = 0; i < info.lstHistoryUserKnockout.get(info.getRound() + 1).size(); i++) {
                    if (info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId() != userId && (i / 2) == (index / 2) && info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId() > 0) {
                        lstU.add(info.changeToProtoUser(info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i), getmUserInfor().get(info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId())));
                        chOther = getmUserChannel().get(info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId());
                        break;
                    }
                }
                Util.sendProtoData(ch, CommonProto.ListProtoUser(lstU), IAction.ARENA_ROOMINFOR, System.currentTimeMillis());
                if (chOther != null) {
                    lstU = new ArrayList<>();
                    lstU.add(info.changeToProtoUser(info.lstHistoryUserKnockout.get(info.getRound() + 1).get(index), getmUserInfor().get(info.lstHistoryUserKnockout.get(info.getRound() + 1).get(index).getUserId())));
                    Util.sendProtoData(chOther, CommonProto.ListProtoUser(lstU), IAction.ARENA_ROOMINFOR, System.currentTimeMillis());
                }
            }
        }
    }

    void listUserKnouckout(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        List<GGProto.ProtoUser> lstU = new ArrayList<>();
        int index = 0;
        for (int i = 0; i < info.lstHistoryUserKnockout.size(); i++) {
            try {
                for (int j = 0; j < info.lstHistoryUserKnockout.get(i).size(); j++) {
                    if (info.lstHistoryUserKnockout.get(i).get(j).getUserId() > 0) {
                        index = i;
                        break;
                    }
                }
            } catch (Exception ex) {
            }
        }
        for (int i = 0; i <= Math.max(index, info.getRound()); i++) {
//        System.out.println("info.lstHistoryUserKnockout.size()---->" + info.lstHistoryUserKnockout.size());
//        for (int i = 0; i < info.lstHistoryUserKnockout.size(); i++) {

            try {
                for (int j = 0; j < info.lstHistoryUserKnockout.get(i).size(); j++) {
                    lstU.add(info.changeToProtoUser(info.lstHistoryUserKnockout.get(i).get(j), getmUserInforKnockout().get(info.lstHistoryUserKnockout.get(i).get(j).getUserId())));
//                    System.out.println("info.lstHistoryUserKnockout.get(" + i + ").get(" + j + ")--->" + info.lstHistoryUserKnockout.get(i).get(j).getUsername() + "||" + info.lstHistoryUserKnockout.get(i).get(j).getName());
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
//        System.out.println("lisU.s--->" + lstU.size());
        handler.addResponse(IAction.ARENA_LIST_USER_KNOCKOUT, CommonProto.ListProtoUser(lstU));
    }


    void joinArena(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
//        info.type =ArenaInfo.TYPEKNOCKOUTTIME;
        if (info.getType() == ArenaInfo.TYPEGROUPTIME) {
            if (!containTopUser(user)) {
                addMUserInfor(user);
                updateTopPlayer(handler, user, 0);
            } else {
                updateTopPlayer(handler, user, 0);
            }
            handler.addResponse(protoStatus(user.getId()));
            listUserBang(handler, user, service, srcRequest);
        } else if (info.getType() == ArenaInfo.TYPEKNOCKOUTTIME || info.getType() == ArenaInfo.TYPEWAITTIME) {
            if (containKnockoutUser(user.getId())) {
                updateKnockoutPlayerWin(user.getId(), ArenaInfo.STATUSREADY, info.getRound() + 1);
                addMUserChannel(handler.getChannel(), user.getId());
                roomInfor(user.getId());
                getLogger().warn("ARENA_TEST_JOIN" + "|" + user.getUsername() + " ||| " + user.getDbUser().getName() + " ||| " + info.getRound());
            }
            handler.addResponse(protoStatus(user.getId()));
            listUserBang(handler, user, service, srcRequest);
        } else {
            handler.addResponse(protoStatus(user.getId()));
            listUserBang(handler, user, service, srcRequest);
//            handler.addErrMessage("Sự kiện đã kết thúc hãy quay lại vào lần sau");
            handler.addErrMessage(user.getLang().get(Lang.err_event_message));
        }

    }

    public void outArena(UserInfo user) {
        if (info.getType() == ArenaInfo.TYPEGROUPTIME) {
//            if (containTopUser(user)) {
////                updateKnockoutPlayerWin(user.getId(), ArenaInfo.STATUSWIN, info.getRound());
//            }
            GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
            builder.addANumber(user.getId());
        } else if (info.getType() == ArenaInfo.TYPEKNOCKOUTTIME || info.getType() == ArenaInfo.TYPEWAITTIME) {
            if (containKnockoutUser(user.getId())) {
                //updateKnockoutPlayerWin(user.getId(), ArenaInfo.STATUSWIN, info.getRound()+1);
                for (int i = 0; i < info.lstHistoryUserKnockout.get(info.getRound() + 1).size(); i++) {
                    if (info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId() == user.getId()) {
//                setHistoryKnockOut(info.lstHistoryUserKnockout.get(round).get(i), info.getRound(), status);

                        info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).setStatus(ArenaInfo.STATUSWIN);
                    }
                }
            }
            GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
            builder.addANumber(user.getId());
        }
    }

    void outArena(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (info.getType() == ArenaInfo.TYPEGROUPTIME) {
//            if (containTopUser(user)) {
//                updateKnockoutPlayerWin(user.getId(), ArenaInfo.STATUSWIN, info.getRound());
//            }
            GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
            builder.addANumber(user.getId());
            handler.addResponse(builder.build());
        } else if (info.getType() == ArenaInfo.TYPEKNOCKOUTTIME || info.getType() == ArenaInfo.TYPEWAITTIME) {
            if (containKnockoutUser(user.getId())) {
//                updateKnockoutPlayerWin(user.getId(), ArenaInfo.STATUSWIN, info.getRound()+1);
                for (int i = 0; i < info.lstHistoryUserKnockout.get(info.getRound() + 1).size(); i++) {
                    if (info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).getUserId() == user.getId()) {
//                setHistoryKnockOut(info.lstHistoryUserKnockout.get(round).get(i), info.getRound(), status);

                        info.lstHistoryUserKnockout.get(info.getRound() + 1).get(i).setStatus(ArenaInfo.STATUSWIN);
                    }
                }
                getLogger().warn("ARENA_TEST_OUT" + "|" + user.getUsername() + " ||| " + user.getDbUser().getName() + " ||| " + info.getRound());
            }
            GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
            builder.addANumber(user.getId());
            handler.addResponse(builder.build());
        } else {
            GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
            builder.addANumber(user.getId());
            handler.addResponse(builder.build());
        }
    }

    public synchronized void updateTopPlayer(AHandler handler, UserInfo user, long totalPoint) {
        if (info.getType() == ArenaInfo.TYPEGROUPTIME) {
            totalPoint += getPoint(user.getId());
            cachePoint(user.getId(), totalPoint);
            boolean add = false;
            for (int i = 0; i < info.topPlayer.size(); i++) {
                if (info.topPlayer.get(i).getUserId() == user.getId()) {
                    info.topPlayer.get(i).setStatus(ArenaInfo.STATUSREADY);
                    if (info.topPlayer.get(i).getPoint() >= GameCfgArena.config.requirePoint) {
                        return;
                    }
                    if (info.topPlayer.get(i).getPoint() >= totalPoint) {
                        return;
                    } else {
                        info.topPlayer.remove(i);
                    }
                    break;
                }
            }
            for (int i = 0; i < info.topPlayer.size(); i++) {
                if (info.topPlayer.get(i).getPoint() < totalPoint) {
                    info.topPlayer.add(i, info.protoArenaUser(user, totalPoint, ArenaInfo.STATUSREADY, 0));
                    add = true;
                    break;
                }
            }
            if (!add) {// add vao cuoi neu nho hon tat ca
                info.topPlayer.add(info.protoArenaUser(user, totalPoint, ArenaInfo.STATUSREADY, 0));
            }
            cacheEventInfo();
        } else if (info.getType() == ArenaInfo.TYPEKNOCKOUTTIME) {
            if (typeKnockoutUser(user.getId()) == ArenaInfo.STATUSPLAY) {
                updateKnockoutPlayerWin(user.getId(), totalPoint == 0 ? ArenaInfo.STATUSLOSE : ArenaInfo.STATUSWIN, info.getRound());
                getLogger().warn("ARENA_TEST :nguoi choi " + user.getDbUser().getName() + (totalPoint > 0 ? "win " : "lose ") + "|" + info.getRound());
            }
        } else if (info.getType() == ArenaInfo.TYPEWAITTIME) {
//            handler.addErrMessage("Đã kết thúc vòng bảng. Bạn hãy chờ vài phút để chuẩn bị cho vòng knockout");
            handler.addErrMessage(user.getLang().get(Lang.err_event_qualifier_round_message));
            return;
        }
    }


//    public synchronized void updateKnockoutPlayerStatus(long userId, int status) {
//        for (int i = 0; i < info.lstUserKnockout.size(); i++) {
//            if (info.lstUserKnockout.get(i).getUserId() == userId) {
//                info.lstUserKnockout.get(i).setStatus(status);
//                break;
//            }
//        }
//        cacheEventInfo();
//    }


//    public synchronized void updateKnockoutPlayerWin(long userId, int win) {
//        for (int i = 0; i < info.lstUserKnockout.size(); i++) {
//            if (info.lstUserKnockout.get(i).getUserId() == userId) {
////                info.lstUserKnockout.get(i).setStatus(win);
//                if (win >= 1) {
//                    updateKnockoutPlayerStatus(userId, ArenaInfo.STATUSWIN);
//                    setHistoryKnockOut(info.lstUserKnockout.get(i), info.getRound(), ArenaInfo.STATUSWIN);
////                    if (info.getRound() == 3) {
//////                        getLogger().warn("chan bi ket thuc ARENA--3-->");
//////                        setFinalHisTory();
////                    }
//                } else {
//                    updateKnockoutPlayerStatus(userId, ArenaInfo.STATUSLOSE);
//                    setHistoryKnockOut(info.lstUserKnockout.get(i), info.getRound(), ArenaInfo.STATUSLOSE);
//                }
//                break;
//            }
//        }
//
//        cacheEventInfo();
//    }

    public synchronized void updateKnockoutPlayerWin(long userId, int status, int round) {
        getLogger().warn("Update history win user-->" + userId + "| status :" + status + "| vong :" + round);
        for (int i = 0; i < info.lstHistoryUserKnockout.get(round).size(); i++) {
            if (info.lstHistoryUserKnockout.get(round).get(i).getUserId() == userId) {
//                setHistoryKnockOut(info.lstHistoryUserKnockout.get(round).get(i), info.getRound(), status);

                info.lstHistoryUserKnockout.get(round).get(i).setStatus(status);
                info.lstHistoryUserKnockout.get(round).get(i).setPoint(status > ArenaInfo.STATUSPLAY ? 1 : 0);
                if (status == ArenaInfo.STATUSWIN) {
                    if (round <= 3) {
                        if (info.lstHistoryUserKnockout.get(round + 1).get(i / 2).getUserId() <= 0) {
                            info.lstHistoryUserKnockout.get(round + 1).set(i / 2, new UserArena(info.lstHistoryUserKnockout.get(round).get(i)));
                        }
                        if (round == 3) {

                            setFinalHisTory();
                            getLogger().warn("ARENA_TEST_ DONE NEW 2");
                        }

                    }
                }
                break;
            }
        }

        cacheEventInfo();
    }


    public int addNumberAtk(UserInfo user, long[] aValue) {
        return 0;
    }


    String keyJoin(long userId) {
        return DateTime.getDateyyyyMMdd(new Date()) + info.status.getId() + KEY_JOIN + userId;
    }

    void cacheEventInfo() {
        try {
            MCache.getInstance().set(KEY_INFO+":"+serverId+":", info);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex),serverId);
        }
    }

    //region Cache
    void cachePoint(long userId, long point) {
        MCache.getInstance().set(KEY_DAMAGE + "TEST" + userId, point, MCache.EXPIRE_1H * 2);
    }

    Long getPoint(long userId) {
        Long point = MCache.getInstance().getLong(KEY_DAMAGE + "TEST" + userId);
        return point == null ? 0 : point;
    }

    GGProto.CommonVector protoStatus(long userId) {
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        int[] arrConver = {0, 1, 3, 2, 4};
        int stagegame = 0;
        if (getInstance(serverId).info.getRound() >= 0 && getInstance(serverId).info.getRound() < 4) {
            stagegame = arrConver[getInstance(serverId).info.getRound() + 1];
        } else {
            stagegame = info.getRound() + 1;
        }
        int statusplayer = 0;
        if (info.getType() == ArenaInfo.TYPEGROUPTIME) {
            statusplayer = 1;
        } else if (info.getType() >= ArenaInfo.TYPEWAITTIME) {
            if (containKnockoutUser(userId)) {
                statusplayer = ArenaInfo.TYPEWAITTIME + (info.getRound() >= 0 ? info.getRound() : 0);
            }
        }
        String cache = (String) MCache.getInstance().get(ArenaInfo.ARENADELAY + userId);
        long timeCache = 0;
        if (cache != null) {
            try {
                timeCache = 3 * 60 - ((System.currentTimeMillis() / 1000) - Integer.parseInt(cache));
                if (timeCache < 0) {
                    timeCache = 0;
                }
            } catch (Exception ex) {

            }
        }
        if (info.getType() >= 0) {
            if (statusplayer > 0) {
                int ck = 0;
                for (int i = info.lstHistoryUserKnockout.size() - 1; i >= 0; i--) {
                    for (int j = 0; j < info.lstHistoryUserKnockout.get(i).size(); j++) {
                        if (info.lstHistoryUserKnockout.get(i).get(j).getUserId() == userId) {
                            try {
                                stagegame = arrConver[i + 1];
                            } catch (Exception ex) {
                                stagegame = i + 1;
                            }
                            ck = 1;
                            break;
                        }
                    }
                    if (ck == 1) {
                        break;
                    }
                }
            }
        } else {
            stagegame = -1;
        }
        if (statusplayer > 1) {
            statusplayer = 1;
        }
        builder.addANumber(stagegame);

        builder.addANumber(statusplayer);
        builder.addANumber(gettimeOut());//fake time remain
        builder.addANumber(timeCache);//fake time contdown
//        System.out.println("builder join--->sdasdasdasdasd--->" + builder.build().getANumberList());
        return builder.build();
    }

    @Override
    public void doExpireTurn(int turnId) {
        try {
            Xerver.mCounter.get(3).addQueue(new TurnInfor(this, 0, 3));
        } catch (Exception ex) {
            getLogger().warn(Xerver.mCounter.get(3) + " " + this + " " + Util.exToString(ex));
        }
    }

    //region Database Access
    protected LogEventEntity dbGetLogEventArena(int eventIndex, int serverId) {
        return dbGetLogEvent(Constans.EVENT_ARENA, eventIndex,serverId);
    }

    protected boolean dbSaveEventResultArena(int eventId, int eventIndex, int eventYear, String data, List<UserMessageEntity> aMessage) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            LogEventEntity logEvent = new LogEventEntity(serverId);
            logEvent.setEventId(eventId);
            logEvent.setEventIndex(eventIndex);
            logEvent.setEventYear(eventYear);
            logEvent.setData(data);
            session.beginTransaction();
            session.save(logEvent);
            for (UserMessageEntity messageEntity : aMessage) {
                session.save(messageEntity);
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

}
