package com.handler.game;

import com.bem.boom.CacheBattle;
import com.bem.boom.object.CachePlayer;
import com.bem.config.CfgServer;
import com.bem.config.GameCfgClanWar;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.dao.mapping.ClanEntity;
import com.bem.matcher.TeamObject;
import com.bem.monitor.ClanMonitor;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.ChatObject;
import com.bem.object.ITimer;
import com.bem.object.TurnInfor;
import com.bem.object.UserInfo;
import com.bem.object.clanbattle.ClanPoint;
import com.bem.object.clanbattle.ClanSummary;
import com.bem.object.clanbattle.PlayerPoint;
import com.bem.object.clanbattle.PlayerSummary;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MCache;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.handler.AHandler;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.Xerver;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.HibernateUtil;
import io.netty.channel.Channel;
import org.hibernate.Session;

import java.io.Serializable;
import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class ClanRoomBattle extends IAction implements ITimer, Serializable {

    final static String name = "clan_battle";
    String KEY_ROOM = name + ":room:";
    String KEY_CHAT = name + ":chat";
    String KEY_LEAVE = name + ":leave";
    String KEY_TOP_CLAN = name + ":clan:";
    String KEY_TOP_PLAYER = name + ":player:";

    int serverId;

    public synchronized void doAction(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        try {
            switch (service) {
                case CBATTLE_JOIN_ROOM:
                    joinRoom(handler, user, service, srcRequest);
                    break;
                case CBATTLE_TOP_PLAYER:
                    topPlayer(handler, user, service, srcRequest);
                    break;
                case CBATTLE_TOP_CLAN:
                    topClan(handler, user, service, srcRequest);
                    break;
                case CBATTLE_CHAT_LIST:
                    battleChatList(handler, CommonProto.parseCommonVector(srcRequest).getANumber(1));
                    break;
                case CBATTLE_CHAT:
                    battleChat(handler, user, service, srcRequest);
                    break;
                case CBATTLE_CHEAR_HISTORY:
                case CBATTLE_FIND_MATCH:
                    findMatch(handler, user, service, srcRequest);
                    break;
                case CBATTLE_CANCEL_FIND_MATCH:
                    cancelFindMatch(handler, user, service, srcRequest);
                    break;
                case CBATTLE_MATCH_UP:
                    matchUp(handler, user, service, srcRequest);
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex), user.getDbUser().getServer());
            if (handler != null) handler.addErrMessage();
        }
    }

    //region service handler
    void matchUp(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        int index = 0;
        while (index < aSearch.size() - 1) {
            PlayerPoint point = aSearch.get(index);
            TeamObject team = getTeamObject(point, TeamObject.GAME_CLAN_BATTLE);
            if (team == null) {
                aSearch.remove(index);
                continue;
            }
            for (int i = index + 1; i < aSearch.size(); i++) {
                PlayerPoint opp = aSearch.get(i);
                if (opp.clanId != point.clanId && !justMatch(point.id, opp.id)) {
                    TeamObject oppTeam = getTeamObject(opp, TeamObject.GAME_CLAN_BATTLE);
                    if (oppTeam == null) {
                        aSearch.remove(i);
                        continue;
                    } else {
                        List<CachePlayer> aCachePlayer = new ArrayList<>();
                        aCachePlayer.addAll(team.getCachePlayer(0));
                        aCachePlayer.addAll(oppTeam.getCachePlayer(1));
                        BattleDataEntity battle = prepareBattle(aCachePlayer, TeamObject.GAME_CLAN_BATTLE);
                        if (battle != null) {
                            CacheBattle cacheBattle = new CacheBattle(aCachePlayer, 0, getKey(aCachePlayer), TeamObject.GAME_CLAN_BATTLE);
                            cacheBattle.setBattleId(battle.getId());
                            cacheBattle.setMapId(GameCfgClanWar.getMapPlayer(roomId > 100 ? roomId - 100 : roomId));

                            MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
                            team.sendBattleInfo(cacheBattle.getKey());
                            oppTeam.sendBattleInfo(cacheBattle.getKey());
                            aSearch.remove(i);
                            aSearch.remove(index);
                            addOnlinePlayer(-2);
                            continue;
                        }
                    }
                }
            }
            index++;
        }
        while (!aSearch.isEmpty()) {
            if (System.currentTimeMillis() - aSearch.get(0).lastSearch >= 15000) {
                TeamObject team = getTeamObject(aSearch.get(0), TeamObject.GAME_CLAN_BATTLE_MONSTER);
                if (team == null) {
                    aSearch.remove(0);
                    continue;
                }
                List<CachePlayer> aCachePlayer = new ArrayList<>();
                aCachePlayer.addAll(team.getCachePlayer(0));
                BattleDataEntity battle = prepareBattle(aCachePlayer, TeamObject.GAME_CLAN_BATTLE_MONSTER);
                if (battle == null) {
                    handler.addErrMessage();
                    return;
                }
                CacheBattle cacheBattle = team.getCacheValue(battle.getId());
                cacheBattle.setBattleId(battle.getId());
                cacheBattle.setMapId(GameCfgClanWar.getMapBoss(roomId));

                MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
                team.sendBattleInfo(cacheBattle.getKey());
                aSearch.remove(0);
                addOnlinePlayer(-1);
            } else break;
        }
    }

    void cancelFindMatch(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        PlayerPoint player = mPlayer.get(user.getId());
        if (player != null) {
            aSearch.remove(player);
            handler.addResponse(null);
        }
    }

    void findMatch(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        long timeLeave = timeWaitSearch(user.getId());
        if (timeLeave > 0)
//            handler.addErrMessage(String.format("Bạn vừa chuyển lãnh địa, hãy chờ %s giây trước khi tìm trận", timeLeave));
            handler.addErrMessage(String.format(user.getLang().get(Lang.time_to_play_after_change_kingdom), timeLeave));
        else {
            PlayerPoint player = mPlayer.get(user.getId());
            if (player != null) {
                if (!aSearch.contains(player)) {
                    aSearch.add(player);
                }
                player.lastSearch = System.currentTimeMillis();
                handler.addResponse(null);
            }
        }
    }

    void joinRoom(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (MCache.getInstance().get("joinclan:" + user.getId()) != null) {
//            handler.addErrMessage("Bạn mới tham gia bang hội, hãy chờ sự kiện lần sau");
            handler.addErrMessage(user.getLang().get(Lang.wait_to_next_event));
            return;
        }
        if (roomId > 100 && ClanBattle.getInstance(user.getDbUser().getServer()).restrictedClanIds.contains(user.getDbUser().getClan())) {
//            handler.addErrMessage("Bang của bạn không được tấn công lãnh địa sơ cấp");
            handler.addErrMessage(user.getLang().get(Lang.clan_not_to_attack_lower));
            return;
        }
        if (roomId > 100 && ClanBattle.getInstance(user.getDbUser().getServer()).restrictedPlayerIds.contains(user.getId())) {
//            handler.addErrMessage("Bạn không được tấn công lãnh địa sơ cấp");
            handler.addErrMessage(user.getLang().get(Lang.not_to_attack_lower));
            return;
        }
//        if (roomId > 100 && CfgServer.SERVER_ID == 2) {
//            handler.addErrMessage("Bạn không được tấn công lãnh địa sơ cấp");
//            return;
//        }
        if (!mPlayer.containsKey(user.getId())) {
            mPlayer.put(user.getId(), ClanBattle.getInstance(user.getDbUser().getServer()).newPlayerPoint(user.getId(), user.getDbUser().getName(), user.getDbUser().getClan(), ClanMonitor.getClan(user.getDbUser().getClan()).getName()));
            aPlayer.add(mPlayer.get(user.getId()));
            ClanBattle.getInstance(user.getDbUser().getServer()).mClanSummary.get(user.getDbUser().getClan()).playerJoinNewRoom(user.getId(), user.getDbUser().getName());
        }
        if (!mClan.containsKey(user.getDbUser().getClan())) {
            ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
            if (clan != null) {
                mClan.put(clan.getId(), new ClanPoint(clan.getId(), clan.getName()));
                aClan.add(mClan.get(clan.getId()));
            }
        }
        //
        Integer oldRoomId = (Integer) user.getMCache().get(KEY_ROOM + user.getId());
        if (oldRoomId != null && oldRoomId != roomId) {
            ClanBattle.getInstance(user.getDbUser().getServer()).mRoom.get(oldRoomId).leaveRoom(user);
        }
        user.getMCache().set(KEY_ROOM + user.getId(), roomId, MCache.EXPIRE_2H);
        addOnlinePlayer(1);
        //
        handler.addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList(GameCfgClanWar.getTimeout(ClanBattle.getInstance(user.getDbUser().getServer()).info.status.getIndex()), timeWaitSearch(user.getId())), null));
    }

    void battleChat(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        long lastReq = cmm.getANumber(1);
        String msg = cmm.getAString(0);
//        addChat(user.getId(), String.format(FORMAT_USER_CHAT, user.getDbUser().getName(), ClanMonitor.getClan(user.getDbUser().getClan()).getName(), msg), Constans.CLAN_CHAT_MSG);
        addChat(user.getId(), String.format(user.getLang().get(Lang.format_user_chat), user.getDbUser().getName(), ClanMonitor.getClan(user.getDbUser().getClan()).getName(), msg), Constans.CLAN_CHAT_MSG);
        battleChatList(handler, lastReq);
    }

    void battleChatList(AHandler handler, long lastReq) {
        int maxReturn = 10;
        GGProto.ProtoListChat.Builder builder = GGProto.ProtoListChat.newBuilder();
        for (int i = aChat.size() - 1; i >= 0; i--) {
            if (aChat.get(i).getTime() > lastReq) {
                builder.addAChat(aChat.get(i).toProto());
                if (--maxReturn <= 0) break;
            }
        }
        handler.addResponse(IAction.CBATTLE_CHAT_LIST, builder.build());
    }

    void topPlayer(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        PlayerPoint point = mPlayer.get(user.getId());
        if (point == null) {
            ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
            point = ClanBattle.getInstance(user.getDbUser().getServer()).newPlayerPoint(user.getId(), user.getDbUser().getName(), clan.getId(), clan.getName());
        }
        int rank = -1;
        for (int i = 0; i < topPlayer.size(); i++) {
            if (topPlayer.get(i).id == user.getId()) rank = i;
        }
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        builder.addAVector(0, CommonProto.getCommonLongVectorProto(Arrays.asList((long) point.point, point.id, (long) point.winStraight, (long) numberPlayer),
                Arrays.asList(rank == -1 ? "20+" : String.valueOf(rank + 1), point.name)));
        for (int i = 0; i < topPlayer.size(); i++) builder.addAVector(topPlayer.get(i).toProto(i + 1));
        handler.addResponse(service, builder.build());
    }

    void topClan(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        ClanPoint point = mClan.get(user.getDbUser().getClan());
        if (point == null) {
            ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
            point = new ClanPoint(clan.getId(), clan.getName());
        }
        int rank = -1;
        for (int i = 0; i < topClan.size(); i++) if (topClan.get(i).id == user.getDbUser().getClan()) rank = i;
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        builder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList((long) point.point, (long) point.id), Arrays.asList(rank == -1 ? "20+" : String.valueOf(rank + 1), point.name)));
        for (int i = 0; i < topClan.size(); i++) builder.addAVector(topClan.get(i).toProto(i + 1));
        handler.addResponse(service, builder.build());
    }
    //endregion

    //region logic
    public String getTopClan() {
        if (topClan == null || topClan.isEmpty()) return "";
        return topClan.get(0).name;
    }

    public List<Long> getTopPlayerId() {
        List<Long> aIds = new ArrayList<>();
        for (int i = 0; i < GameCfgClanWar.config.topPlayerBonus.size(); i++) {
            if (topPlayer.size() > i && topPlayer.get(i).point > 0) {
                aIds.add(topPlayer.get(i).id);
            }
        }
        return aIds;
    }

    public Integer getTopClanId() {
        if (topClan.size() > 0 && topClan.get(0).point > 0) {
            return topClan.get(0).id;
        }
        return 0;
    }

    void clearOldEvent() {
        numberPlayer = 0;
        aChat = new ArrayList<>();
        aClan = new ArrayList<>();
        aPlayer = new ArrayList<>();
        mClan = new HashMap<>();
        mPlayer = new HashMap<>();
        aSearch = new ArrayList<>();
        topClan = new ArrayList<>();
        topPlayer = new ArrayList<>();
        mCounter.clear();
        cacheData();
    }

    long timeWaitSearch(long userId) {
        Long timeLeave = MCache.getInstance().getLong(KEY_LEAVE + userId);
        if (timeLeave != null)
            return GameCfgClanWar.config.timeWaitLeave - (System.currentTimeMillis() - timeLeave) / 1000;
        return 0;
    }

    synchronized void leaveRoom(UserInfo user) {
        PlayerPoint pPoint = mPlayer.get(user.getId());
        if (pPoint != null) {
//            pPoint.point -= pPoint.bonusPoint;
//            ClanPoint cPoint = mClan.get(user.getDbUser().getClan());
//            if (cPoint != null) {
//                cPoint.point -= pPoint.bonusPoint;
//                addChat(user.getId(), String.format(FORMAT_USER_LEAVE, pPoint.name, pPoint.clanName, pPoint.bonusPoint));
//            addChat(user.getId(), String.format(user.getLang().get(Lang.format_user_leave), pPoint.name, pPoint.clanName, pPoint.bonusPoint));
//            }
            pPoint.winStraight = 0;
            user.getMCache().set(KEY_LEAVE + user.getId(), System.currentTimeMillis(), GameCfgClanWar.config.timeWaitLeave);
//            pPoint.bonusPoint = 0;
            addOnlinePlayer(-1);
        }
    }

    synchronized void addChat(long userId, String msg, int msgType) {
        aChat.add(new ChatObject(userId, msg, msgType));
        while (aChat.size() > 200) {
            aChat.remove(0);
        }
    }

    public void addPoint(UserInfo user, long[] aValue) {
        if (aValue[0] == 1) {
            PlayerPoint pPoint = mPlayer.get(user.getId());
            if (pPoint != null) {
                pPoint.winStraight += 1;
                int bonusPoint = pPoint.winStraight - 1;
                int winPoint = 1;
                pPoint.bonusPoint += bonusPoint;
                int totalPoint = bonusPoint + winPoint;
                if (totalPoint > GameCfgClanWar.config.maxPoint) totalPoint = GameCfgClanWar.config.maxPoint;
                pPoint.point += totalPoint;
                ClanPoint cPoint = mClan.get(user.getDbUser().getClan());
                if (cPoint != null) cPoint.point += totalPoint;
                if (aValue[1] > 0) {
                    PlayerPoint oppPoint = mPlayer.get(aValue[1]);
                    if (oppPoint != null) {
                        if (pPoint.winStraight <= 1)
//                            addChat(user.getId(), String.format(FORMAT_BATTLE_RESULT1, pPoint.name, pPoint.clanName, oppPoint.name, oppPoint.clanName, totalPoint), Constans.CLAN_CHAT_BATTLE_RESULT);
                            addChat(user.getId(), String.format(user.getLang().get(Lang.format_battle_result1), pPoint.name, pPoint.clanName, oppPoint.name, oppPoint.clanName, totalPoint), Constans.CLAN_CHAT_BATTLE_RESULT);
                        else
//                            addChat(user.getId(), String.format(FORMAT_BATTLE_RESULT2, pPoint.name, pPoint.clanName, oppPoint.name, oppPoint.clanName, pPoint.winStraight, totalPoint), Constans.CLAN_CHAT_BATTLE_RESULT);
                            addChat(user.getId(), String.format(user.getLang().get(Lang.format_battle_result2), pPoint.name, pPoint.clanName, oppPoint.name, oppPoint.clanName, pPoint.winStraight, totalPoint), Constans.CLAN_CHAT_BATTLE_RESULT);
                    }
                } else {
                    if (pPoint.winStraight <= 1)
//                        addChat(user.getId(), String.format(FORMAT_BATTLE_RESULT3, pPoint.name, pPoint.clanName, totalPoint), Constans.CLAN_CHAT_BATTLE_RESULT);
                        addChat(user.getId(), String.format(user.getLang().get(Lang.format_battle_result3), pPoint.name, pPoint.clanName, totalPoint), Constans.CLAN_CHAT_BATTLE_RESULT);
                    else
//                        addChat(user.getId(), String.format(FORMAT_BATTLE_RESULT4, pPoint.name, pPoint.clanName, pPoint.winStraight, totalPoint), Constans.CLAN_CHAT_BATTLE_RESULT);
                        addChat(user.getId(), String.format(user.getLang().get(Lang.format_battle_result4), pPoint.name, pPoint.clanName, pPoint.winStraight, totalPoint), Constans.CLAN_CHAT_BATTLE_RESULT);
                }

                // recheck
                if (!ClanBattle.getInstance(user.getDbUser().getServer()).mClanSummary.containsKey(user.getDbUser().getClan()))
                    ClanBattle.getInstance(user.getDbUser().getServer()).mClanSummary.put(user.getDbUser().getClan(), new ClanSummary());
                if (!ClanBattle.getInstance(user.getDbUser().getServer()).mMapPlayer.containsKey(user.getId()))
                    ClanBattle.getInstance(user.getDbUser().getServer()).mMapPlayer.put(user.getId(), new PlayerSummary(user.getId(), user.getDbUser().getName(), cPoint.name));
                if (!ClanBattle.getInstance(user.getDbUser().getServer()).mEasyMapPlayer.containsKey(user.getId()))
                    ClanBattle.getInstance(user.getDbUser().getServer()).mEasyMapPlayer.put(user.getId(), new PlayerSummary(user.getId(), user.getDbUser().getName(), cPoint.name));

                ClanBattle.getInstance(user.getDbUser().getServer()).mClanSummary.get(user.getDbUser().getClan()).addPoint(user.getId(), totalPoint);
                if (roomId < 100)
                    ClanBattle.getInstance(user.getDbUser().getServer()).mMapPlayer.get(user.getId()).addPoint(totalPoint);
                else
                    ClanBattle.getInstance(user.getDbUser().getServer()).mEasyMapPlayer.get(user.getId()).addPoint(totalPoint);
            }
        } else {
            PlayerPoint pPoint = mPlayer.get(user.getId());
            if (pPoint != null) {
                pPoint.winStraight = 0;
            }
        }
    }

    boolean justMatch(long id1, long id2) {
        List<Long> aInt1 = mCounter.get(id1);
        List<Long> aInt2 = mCounter.get(id2);
        if (aInt1 != null && aInt1.contains(id2)) return true;
        if (aInt2 != null && aInt2.contains(id1)) return true;
        if (aInt1 == null) {
            aInt1 = new ArrayList<>();
            mCounter.put(id1, aInt1);
        }
        if (aInt2 == null) {
            aInt2 = new ArrayList<>();
            mCounter.put(id2, aInt2);
        }
        aInt1.add(id2);
        aInt2.add(id1);
        while (aInt1.size() >= 3) aInt1.remove(0);
        while (aInt2.size() >= 3) aInt2.remove(0);
        return false;
    }

    String getKey(List<CachePlayer> aCachePlayer) {
        String key = String.valueOf(CfgServer.SERVER_ID) + "_" + TeamObject.GAME_CLAN_BATTLE + "_" + new Random().nextInt(100);
        for (int i = 0; i < aCachePlayer.size(); i++) {
            key += "_" + aCachePlayer.get(i).getUserId();
        }
        return key;
    }

    TeamObject getTeamObject(PlayerPoint point, int gameType) {
        Channel channel = Online.getChannel(point.id);
        if (channel == null) return null;
        UserInfo user = UserMonitor.getUser(point.id);
        if (user == null) return null;
        return new TeamObject(gameType, user);
    }

    synchronized void addOnlinePlayer(int value) {
        numberPlayer += value;
        if (numberPlayer > aPlayer.size()) numberPlayer = aPlayer.size();
        if (numberPlayer < 0) numberPlayer = 0;
    }

    void cacheData() {
        {
            List<ClanPoint> aPoint = new ArrayList<>(mClan.values());
            Collections.sort(aPoint, (p1, p2) -> new Integer(p2.point).compareTo(new Integer(p1.point)));
            aPoint = aPoint.size() > 20 ? aPoint.subList(0, 20) : aPoint;
            topClan = new Gson().fromJson(new Gson().toJson(aPoint), new TypeToken<ArrayList<ClanPoint>>() {
            }.getType());
        }
        {
            List<PlayerPoint> aPoint = new ArrayList<>(mPlayer.values());
            Collections.sort(aPoint, (p1, p2) -> new Integer(p2.point).compareTo(new Integer(p1.point)));
            aPoint = aPoint.size() > 20 ? aPoint.subList(0, 20) : aPoint;
            topPlayer = new Gson().fromJson(new Gson().toJson(aPoint), new TypeToken<ArrayList<PlayerPoint>>() {
            }.getType());
        }
        try {
            MCache.getInstance().set(KEY_TOP_PLAYER + roomId, topPlayer, MCache.EXPIRE_WEEK);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
        try {
            MCache.getInstance().set(KEY_TOP_CLAN + roomId, topClan, MCache.EXPIRE_WEEK);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
        try {
            MCache.getInstance().set(KEY_CHAT + roomId, aChat, MCache.EXPIRE_2H);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
    }
    //endregion

    //region proto
    //endregion

    //region Database Access
    BattleDataEntity prepareBattle(List<CachePlayer> aPlayer, int gameType) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(gameType);
            session.save(battle);

            for (int i = 0; i < aPlayer.size(); i++) {
                session.save(new BattleUserEntity(battle.getId(), aPlayer.get(i).getUserId(), gameType));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    protected void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }
    //endregion

    //region init class
    public ClanRoomBattle(int serverId, int roomId, GameCfgClanWar.Map map) {
        KEY_ROOM = serverId + KEY_ROOM;
        KEY_CHAT = serverId + KEY_CHAT;
        KEY_LEAVE = serverId + KEY_LEAVE;
        KEY_TOP_CLAN = serverId + KEY_TOP_CLAN;
        KEY_TOP_PLAYER = serverId + KEY_TOP_PLAYER;

        this.roomId = roomId;
        this.roomName = roomId < 100 ? map.name : map.easyName;
        try {
            aChat = (List<ChatObject>) MCache.getInstance().get(KEY_CHAT + roomId);
            topClan = (List<ClanPoint>) MCache.getInstance().get(KEY_TOP_CLAN + roomId);
            topPlayer = (List<PlayerPoint>) MCache.getInstance().get(KEY_TOP_PLAYER + roomId);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
        if (aChat == null) aChat = new ArrayList<>();
        if (topClan == null) topClan = new ArrayList<>();
        if (topPlayer == null) topPlayer = new ArrayList<>();
        timer();
    }

    Map<Long, List<Long>> mCounter = new HashMap<>();
    int roomId, numberPlayer;
    String roomName;
    List<ChatObject> aChat = new ArrayList<>();
    List<ClanPoint> aClan = new ArrayList<>();
    List<PlayerPoint> aPlayer = new ArrayList<>();
    Map<Integer, ClanPoint> mClan = new HashMap<>();
    Map<Long, PlayerPoint> mPlayer = new HashMap<>();
    List<PlayerPoint> aSearch = new ArrayList<>();
    List<ClanPoint> topClan = new ArrayList<>();
    List<PlayerPoint> topPlayer = new ArrayList<>();

    static final String FORMAT_USER_CHAT = "<color='#5FCBFE'>%s{</color><color='#FFE400'>%s</color><color='#5FCBFE'>}:</color> %s";
    static final String FORMAT_USER_LEAVE = "<color='#5FCBFE'>%s{</color><color='#FFE400'>%s</color><color='#5FCBFE'>}</color> vừa chuyển phòng, bang hội giảm <color='#4898be'>%s</color> điểm";
    static final String FORMAT_BATTLE_RESULT1 = "<color='#AAAAAA'><color='#4898be'>%s{</color><color='#b9a604'>%s</color><color='#4898be'>}</color> vừa đánh bại " +
            "<color='#4898be'>%s{</color><color='#b9a604'>%s</color><color='#4898be'>}</color>, nhận được <color='#4898be'>%s</color> điểm</color>";
    static final String FORMAT_BATTLE_RESULT2 = "<color='#AAAAAA'><color='#4898be'>%s{</color><color='#b9a604'>%s</color><color='#4898be'>}</color> vừa đánh bại " +
            "<color='#4898be'>%s{</color><color='#b9a604'>%s</color><color='#4898be'>}</color>, có <color='#4898be'>%s</color> liên thắng, nhận được <color='#4898be'>%s</color> điểm</color>";
    static final String FORMAT_BATTLE_RESULT3 = "<color='#AAAAAA'><color='#4898be'>%s{</color><color='#b9a604'>%s</color><color='#4898be'>}</color> vừa đánh bại <color='#b9a604'>Quái Vật</color>, " +
            "nhận được <color='#4898be'>%s</color> điểm</color>";
    static final String FORMAT_BATTLE_RESULT4 = "<color='#AAAAAA'><color='#4898be'>%s{</color><color='#b9a604'>%s</color><color='#4898be'>}</color> vừa đánh bại <color='#b9a604'>Quái Vật</color>, " +
            "có <color='#4898be'>%s</color> liên thắng, nhận được <color='#4898be'>%s</color> điểm</color>";
    //endregion

    @Override
    public void doExpireTurn(int turnId) {
        doAction(null, null, IAction.CBATTLE_MATCH_UP, null);
        timer();
    }

    void timer() {
        try {
            Xerver.mCounter.get(3).addQueue(new TurnInfor(this, 0, 3));
        } catch (Exception ex) {
            Logs.error(Xerver.mCounter.get(3) + " " + Util.exToString(ex));
        }
    }
}
