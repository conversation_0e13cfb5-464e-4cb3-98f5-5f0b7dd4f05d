package com.handler.game;

import com.bem.boom.CacheBattle;
import com.bem.config.GameCfgBoss;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.dao.mapping.LogEventEntity;
import com.bem.dao.mapping.UserMessageEntity;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.EventStatus;
import com.bem.object.GameInfo;
import com.bem.object.UserInfo;
import com.bem.util.Actions;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MCache;
import com.google.gson.Gson;
import com.handler.AHandler;
import com.handler.GameHandler;
import com.k2tek.Config;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/11/2014.
 */
public class BossGlobal extends AAHandler {

    final static String name = "boss_global";
    String KEY_JOIN = name + ":";
    String KEY_INFO = name + ":info";
    String KEY_DAMAGE = name + ":damage:";

    GameInfo info = new GameInfo();
    boolean sendBonus = false;
    int serverId;

    public BossGlobal(int serverId) {
        this.serverId = serverId;
        KEY_JOIN = serverId + KEY_JOIN;
        KEY_INFO = serverId + KEY_INFO;
        KEY_DAMAGE = serverId + KEY_DAMAGE;
    }

    static {
        for (int i = 0; i < Config.lstServerId.size(); i++) {
            BossGlobal bossGlobal = getInstance(Config.lstServerId.get(i));

            try {
                bossGlobal.info = (GameInfo) MCache.getInstance().get(bossGlobal.KEY_INFO);
            } catch (Exception ex) {
                ex.printStackTrace();
                bossGlobal.getLogger().error(Util.exToString(ex));
            }
            if (bossGlobal.info == null)
                bossGlobal.info = new GameInfo();
        }
    }

    @Override
    public void doAction(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (GameCfgBoss.config.enable != 1) {
//            handler.addErrMessage("Sự kiện chưa mở");
            handler.addErrMessage(user.getLang().get(Lang.err_open_event));
            return;
        }
        if (GameCfgBoss.config.requireLevel > user.getDbUser().getLevel()) {
//            handler.addErrMessage(String.format("Đạt cấp %s để tham gia sự kiện", GameCfgBoss.config.requireLevel));
            handler.addErrMessage(String.format(user.getLang().get(Lang.level_to_join_event), GameCfgBoss.config.requireLevel));
            return;
        }
        EventStatus status = GameCfgBoss.getEventStatus(); // index, index from year
        synchronized (info) {
            if (info.status.getId() != status.getId()) {
                if (status.getId() >= 0) { // vao su kien
                    info.topPlayer.clear();
                    info.mapId = GameCfgBoss.getMap().id;
                    cacheEventInfo();
                    sendBonus = false;
                }
                info.status = status;
                cacheEventInfo();
            }
        }
        try {
            switch (service) {
                case BOSS_STATUS:
                    status(status.getIndex(), handler, user, service, srcRequest);
                    break;
                case BOSS_JOIN:
                    join(handler, user, service, srcRequest);
                    break;
                case BOSS_TOP_BONUS:
                    topBonus(handler, user, service, srcRequest);
                    break;
            }
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
            handler.addErrMessage();
        }
    }

    //region service handler
    void topBonus(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        for (List<Integer> topBonus : GameCfgBoss.config.topBonus) {
            builder.addAVector(CommonProto.getCommonVectorProto(topBonus, null));
        }
        handler.addResponse(builder.build());
    }

    void status(int eventIndex, AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        handler.addResponse(protoStatus(eventIndex, getNumberAtk(user)));
//        if (user.getUsername().equals("1_mashi1") || user.getUsername().equals("2_mashi1")){
//            List<UserMessageEntity> aMessage = new ArrayList<>();
//            List<List<Integer>> topBonus = GameCfgBoss.config.topBonus;
//            for (int j = 0; j < info.topPlayer.size(); j++) {
//                UserMessageEntity uMessage = new UserMessageEntity();
//                uMessage.setTitle(String.format("Phần thưởng top %s sự kiện đánh boss", String.valueOf(j + 1)));
//                uMessage.setDateCreated(new Date());
//                uMessage.setUserId(info.topPlayer.get(j).getId());
//                uMessage.setBonus(new Gson().toJson(topBonus.get(j)));
//                aMessage.add(uMessage);
//                if (j == 9) {
//                    break;
//                }
//            }
//            if (info.topPlayer != null && info.topPlayer.size() > 0)
//                Database.updateNumber("user", Arrays.asList("number_boss_top", "1"), Arrays.asList("id", String.valueOf(info.topPlayer.get(0).getId())));
//
//            if (dbSaveEventResult(Constans.EVENT_BOSS_GLOBAL, 13712, 2018, new Gson().toJson(info), aMessage)) {
//                getLogger().warn(serverId + " -> update db ok");
//                sendBonus = true;
//            } else {
//                getLogger().warn(serverId + " -> update db false");
//            }
//        }
    }

    void join(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (info.status.getId() == -1) {
//            handler.addErrMessage("Sự kiện chưa diễn ra");
            handler.addErrMessage(user.getLang().get(Lang.err_open_event));
            return;
        }
        int numberAtk = getNumberAtk(user);
        if (numberAtk <= 0) {
//            handler.addErrMessage("Bạn đã hết lượt đánh ngày hôm nay, hãy mua thêm lượt đánh");
            handler.addErrMessage(user.getLang().get(Lang.no_turn_to_play));
            return;
        }
        TeamObject team = new TeamObject(TeamObject.GAME_BOSS, user);
        BattleDataEntity battle = prepareBattle(team.aUser);
        if (battle == null) {
            handler.addErrMessage();
            return;
        }
        CacheBattle cacheBattle = team.getCacheValue(battle.getId());
        cacheBattle.setMapId(GameCfgBoss.getMap().id);
        MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
        team.sendBattleInfo(cacheBattle.getKey());
        Actions.save(user.getDbUser(), "boss", "attack", Actions.convertToLogString(Arrays.asList("attack", String.valueOf(numberAtk))));
    }
    //endregion

    //region logic
    @Override
    public void checkStatus() {
        if (sendBonus || info.status.getId() != -1) return;

        int[] lastEndEvent = GameCfgBoss.getLastEndEvent();
        Calendar ca = Calendar.getInstance();
        if (ca.get(Calendar.HOUR_OF_DAY) * 3600 + ca.get(Calendar.MINUTE) * 60 > lastEndEvent[1] + 2 * 60) { // 2 phút sau kết thúc sự kiện
            int curYear = Calendar.getInstance().get(Calendar.YEAR);
            int eventIndex = GameCfgBoss.getEventIndex(lastEndEvent[0]);
            getLogger().warn(serverId + " -> endEvent = " + curYear + " " + eventIndex);
            LogEventEntity logEvent = dbGetLogEvent(eventIndex, serverId);
            getLogger().warn(serverId + " -> logEvent.getId() = " + logEvent.getId());
            if (logEvent.getId() > 0) {
                sendBonus = true;
            } else if (logEvent.getId() == Constans.DB_STATUS_NOT_FOUND) { // send bonus here
                getLogger().warn(serverId + " -> topPlayer = " + info.topPlayer.size());
                List<UserMessageEntity> aMessage = new ArrayList<>();
                List<List<Integer>> topBonus = GameCfgBoss.config.topBonus;
                for (int j = 0; j < info.topPlayer.size(); j++) {
                    UserMessageEntity uMessage = new UserMessageEntity();
                    uMessage.setTitle(String.format("Phần thưởng top %s sự kiện đánh boss", String.valueOf(j + 1)));
                    uMessage.setDateCreated(new Date());
                    uMessage.setUserId(info.topPlayer.get(j).getId());
                    uMessage.setBonus(new Gson().toJson(topBonus.get(j)));
                    aMessage.add(uMessage);
                    if (j == 9) {
                        break;
                    }
                }
                if (info.topPlayer != null && info.topPlayer.size() > 0)
                    Database2.updateNumber("user", Arrays.asList("number_boss_top", "1"), Arrays.asList("id", String.valueOf(info.topPlayer.get(0).getId())));

                if (dbSaveEventResult(Constans.EVENT_BOSS_GLOBAL, eventIndex, curYear, new Gson().toJson(info), aMessage)) {
                    getLogger().warn(serverId + " -> update db ok");
                    sendBonus = true;
                } else {
                    getLogger().warn(serverId + " -> update db false");
                }
            }
        }
    }

    public synchronized void updateTopPlayer(UserInfo user, long totalDamage) {
        totalDamage += getDamage(user.getId());
        cacheDamage(user.getId(), totalDamage);
        for (int i = 0; i < info.topPlayer.size(); i++) {
            if (info.topPlayer.get(i).getId() == user.getId()) {
                if (info.topPlayer.get(i).getGold() >= totalDamage) {
                    return;
                } else {
                    info.topPlayer.remove(i);
                }
                break;
            }
        }
        boolean forceAdd = info.topPlayer.size() < 10;
        boolean add = false;
        if (forceAdd) {
            for (int i = 0; i < info.topPlayer.size(); i++) {
                if (info.topPlayer.get(i).getGold() < totalDamage) {
                    info.topPlayer.add(i, protoUser(user, totalDamage));
                    add = true;
                    break;
                }
            }
            if (!add) {
                add = true;
                info.topPlayer.add(protoUser(user, totalDamage));
            }
        } else {
            for (int i = 0; i < info.topPlayer.size(); i++) {
                if (info.topPlayer.get(i).getGold() < totalDamage) {
                    info.topPlayer.add(i, protoUser(user, totalDamage));
                    add = true;
                    break;
                }
            }
        }
        while (info.topPlayer.size() > 10) {
            info.topPlayer.remove(info.topPlayer.size() - 1);
        }
        if (add) {
            cacheEventInfo();
        }
    }

    public int addNumberAtk(UserInfo user, long[] aValue) {
        int value = (int) aValue[0];
        int numberAtk = getNumberAtk(user);
        user.getJCache().set(keyJoin(user.getId()), String.valueOf(numberAtk + value), MCache.EXPIRE_1D);
        return numberAtk + value;
    }

    public int getNumberAtk(UserInfo user) {
        if (info.status.getId() < 0) return 0;
        Integer numberAtk = user.getJCache().getInt(keyJoin(user.getId()));
        return numberAtk == null ? 1 : numberAtk;
    }

    String keyJoin(long userId) {
        return DateTime.getDateyyyyMMdd(new Date()) + info.status.getId() + KEY_JOIN + userId;
    }

    void cacheEventInfo() {
        try {
            MCache.getInstance().set(KEY_INFO, info);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
    }
    //endregion

    //region Cache
    void cacheDamage(long userId, long damage) {
        MCache.getInstance().set(KEY_DAMAGE + userId, damage, MCache.EXPIRE_1H * 2);
    }

    Long getDamage(long userId) {
        Long damage = MCache.getInstance().getLong(KEY_DAMAGE + userId);
        return damage == null ? 0 : damage;
    }
    //endregion

    //region proto
    GGProto.ProtoUser protoUser(UserInfo user, long totalDamage) {
        GGProto.ProtoUser.Builder builder = GGProto.ProtoUser.newBuilder();
        builder.setId(user.getId());
        builder.setName(user.getDbUser().getName());
        builder.setUsername(user.getUsername());
        builder.addAllAvatar(user.getMAvatar().toList());
        builder.setLvl(user.getDbUser().getLevel());
        builder.setGold(totalDamage);
        return builder.build();
    }

    GGProto.ProtoGameStatus protoStatus(int eventIndex, int numberAtk) {
        GGProto.ProtoGameStatus.Builder builder = GGProto.ProtoGameStatus.newBuilder();
        builder.setStatus(eventIndex >= 0 ? STATUS_OPEN : STATUS_CLOSE);
        builder.setNumberAtk(numberAtk < 0 ? 0 : numberAtk);
        builder.setFee(GameCfgBoss.config.feeRevive);
//        System.out.println("a");
        GGProto.ListCommonVector.Builder infoBuilder = GGProto.ListCommonVector.newBuilder();
        if (eventIndex < 0) {
            builder.setStrTime("Đang đóng");
            List<Integer> aLong = new ArrayList<>();
            aLong.add(-1);
            aLong.add(info.bossId);
            infoBuilder.addAVector(CommonProto.getCommonVectorProto(aLong, null));
        } else {
            GameCfgBoss.Map map = GameCfgBoss.getMap(info.mapId);
            List<Integer> aLong = new ArrayList<>();
            aLong.add((int) GameCfgBoss.getTimeout(eventIndex));
            aLong.addAll(map.getMonsterId());
            infoBuilder.addAVector(CommonProto.getCommonVectorProto(aLong, null));
        }
        builder.addAllAUser(info.topPlayer);
        builder.setInfo(infoBuilder);
        return builder.build();
    }
    //endregion

    //region Database Access
    protected LogEventEntity dbGetLogEvent(int eventIndex, int serverId) {
        return dbGetLogEvent(Constans.EVENT_BOSS_GLOBAL, eventIndex, serverId);
    }

    protected boolean dbSaveEventResult(int eventId, int eventIndex, int eventYear, String data, List<UserMessageEntity> aMessage) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            LogEventEntity logEvent = new LogEventEntity(serverId);
            logEvent.setEventId(eventId);
            logEvent.setEventIndex(eventIndex);
            logEvent.setEventYear(eventYear);
            logEvent.setData(data);
            session.beginTransaction();
            session.save(logEvent);
            for (UserMessageEntity messageEntity : aMessage) {
                session.save(messageEntity);
            }
            getLogger().warn("save event object = " + eventId + " " + eventIndex + " " + eventYear + " " + data);
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
//            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    BattleDataEntity prepareBattle(List<UserInfo> aUser) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(TeamObject.GAME_BOSS);
            session.save(battle);

            for (int i = 0; i < aUser.size(); i++) {
                session.save(new BattleUserEntity(battle.getId(), aUser.get(i).getId(), Bonus.defaultBonusView(Bonus.GAME_NUMBER_ATK, Constans.EVENT_BOSS_GLOBAL, -1).toString(), TeamObject
                        .GAME_BOSS));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

    //region init class
    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler, Map<Integer, AAHandler> mSubGameHandler, GameHandler parent) {
        List<Integer> services = Arrays.asList(BOSS_JOIN, BOSS_STATUS, BOSS_TOP_BONUS);

        services.forEach(service -> {
            mGameHandler.put(service, parent);
            for (Integer serverId : Config.lstServerId) {
                mSubGameHandler.put(serverId * 100000 + service, getInstance(serverId));
            }
//            mSubGameHandler.put(service, getInstance());
        });
    }

    //    static BossGlobal instance;
    //
    //    public static BossGlobal getInstance() {
    //        if (instance == null) {
    //            instance = new BossGlobal();
    //        }
    //        return instance;
    //    }
    static Map<Integer, BossGlobal> instance;

    public static synchronized BossGlobal getInstance(int serverId) {
        if (instance == null) {
            instance = new HashMap<>();
        }
        if (instance.get(serverId) == null) {

            instance.put(serverId, new BossGlobal(serverId));
        }
        return instance.get(serverId);
    }

    //endregion
}
