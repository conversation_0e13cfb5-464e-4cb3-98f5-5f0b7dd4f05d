package com.handler.game;

import com.bem.boom.CacheBattle;
import com.bem.config.GameCfgHunting;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.UserInfo;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MCache;
import com.handler.AHandler;
import com.handler.GameHandler;
import com.k2tek.Constans;
import com.proto.GGProto;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import net.sf.json.JSONArray;
import org.hibernate.Session;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 12/11/2014.
 */
public class GoldHunting extends AAHandler {

    final static String name = "gold_hunt";
    final static String KEY_JOIN = name + ":";

//    List<GGProto.ProtoUser> topHunting = new ArrayList<GGProto.ProtoUser>();
//
//    static {
//        try {
//            getInstance().topHunting = (List<GGProto.ProtoUser>) MCache.getInstance().get(KEY_TOP_HUNTING);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            getInstance().getLogger().error(Util.exToString(ex));
//        }
//    }

    @Override
    public void doAction(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (GameCfgHunting.config.enable != 1) {
//            handler.addErrMessage("Sự kiện chưa mở");
            handler.addErrMessage(user.getLang().get(Lang.err_open_event));
            return;
        }
        if (GameCfgHunting.config.requireLevel > user.getDbUser().getLevel()) {
//            handler.addErrMessage(String.format("Đạt cấp %s để tham gia sự kiện", GameCfgHunting.config.requireLevel));
            handler.addErrMessage(String.format(user.getLang().get(Lang.level_to_join_event), GameCfgHunting.config.requireLevel));
            return;
        }
//        boolean inEvent = GameCfgHunting.inEvent();
//        if (!inEvent && service != HUNT_STATUS) {
//            handler.addResponse(HUNT_END, CommonProto.getCommonLongVectorProto(null, Arrays.asList("Đã kết thúc sự kiện")));
//            return;
//        }
        try {
            switch (service) {
                case HUNT_STATUS:
                    status(true, handler, user, service, srcRequest);
                    break;
                case HUNT_JOIN:
                    join(handler, user, service, srcRequest);
                    break;
                case HUNT_SKIP_WAITING:
                    skipWaiting(handler, user, service, srcRequest);
                    break;
            }
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
            handler.addErrMessage();
        }
    }

    @Override
    public void checkStatus() {

    }

    void skipWaiting(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        JSONArray arrAtk = getNumberAtk(user);
        int coolDown = GameCfgHunting.config.coolDown - (int) (System.currentTimeMillis() / 1000 - arrAtk.getInt(1));
        if (coolDown <= 0) {
            handler.addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList(user.getDbUser().getGem()), null));
            return;
        }
        if (user.getDbUser().getGem() < GameCfgHunting.config.feeSkip) {
//            handler.addShopMessage("Không đủ kim cướng để đánh nhanh");
            handler.addShopMessage(user.getLang().get(Lang.not_enough_gem_to_skip));
            return;
        }
        List<Long> aLong = Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.GEM, -GameCfgHunting.config.feeSkip), "game_gold_skip");
        if (aLong.isEmpty()) {
            handler.addErrMessage();
            return;
        }
        arrAtk.set(1, 0);
        cacheAtk(user, arrAtk.toString());
        handler.addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList(user.getDbUser().getGem()), null));
    }

    //region service handler
    void status(boolean inEvent, AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        handler.addResponse(protoStatus(getNumberAtk(user)));
    }

    void join(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        int levelIndex = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0) - 1;
        if (levelIndex < 0 || levelIndex > 4) {
//            handler.addErrMessage("Dữ liệu không hợp lệ");
            handler.addErrMessage(user.getLang().get(Lang.err_param));
            return;
        }
        JSONArray arrAtk = getNumberAtk(user);
        if (arrAtk.getInt(0) <= 0) {
//            handler.addErrMessage("Bạn đã hết lượt đánh ngày hôm nay, hãy mua thêm lượt đánh hoặc chờ lượt miễn phí");
            handler.addErrMessage(user.getLang().get(Lang.no_turn_to_play));
            return;
        }
        int coolDown = GameCfgHunting.config.coolDown - (int) (System.currentTimeMillis() / 1000 - arrAtk.getInt(1));
        if (coolDown > 0) {
//            handler.addErrMessage(String.format("Hãy chờ %s trước khi bắt đầu đánh", DateTime.formatTime(coolDown)));
            handler.addErrMessage(String.format(user.getLang().get(Lang.time_to_play), DateTime.formatTime(coolDown)));
            return;
        }
        TeamObject team = new TeamObject(TeamObject.GAME_HUNTING, user);
        BattleDataEntity battle = prepareBattle(team.aUser);
        if (battle == null) {
            handler.addErrMessage();
            return;
        }
        CacheBattle cacheBattle = team.getCacheValue(battle.getId());
        cacheBattle.setLevelIndex(levelIndex);
        MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
        team.sendBattleInfo(cacheBattle.getKey());

//        arrAtk.set(0, arrAtk.getInt(0) - 1);
//        arrAtk.set(1, System.currentTimeMillis() / 1000);
//        cacheAtk(user, arrAtk.toString());
    }
    //endregion

    //region logic
    public int addNumberAtk(UserInfo user, long[] aValue) {
        int value = (int) aValue[0];
        JSONArray arrAtk = getNumberAtk(user);
        arrAtk.set(0, arrAtk.getInt(0) + value);
        if (value < 0) {
            arrAtk.set(1, System.currentTimeMillis() / 1000);
        }
        cacheAtk(user, arrAtk.toString());
        return arrAtk.getInt(0);
    }

    public JSONArray getNumberAtk(UserInfo user) {
        String cacheAtk = user.getJCache().get(keyJoin(user.getId()));
        if (cacheAtk == null) {
            cacheAtk = "[" + GameCfgHunting.config.freeAtk + ",0]";
        }
        return JSONArray.fromObject(cacheAtk);
    }

    String keyJoin(long userId) {
        return DateTime.getDateyyyyMMdd(new Date()) + KEY_JOIN + userId;
    }

    void cacheAtk(UserInfo user, String value) {
        user.getJCache().set(keyJoin(user.getId()), value, MCache.EXPIRE_1D);
    }
    //endregion

    //region proto
    GGProto.ProtoGameStatus protoStatus(JSONArray arrAtk) {
        GGProto.ProtoGameStatus.Builder builder = GGProto.ProtoGameStatus.newBuilder();
        builder.setStatus(STATUS_OPEN);
        builder.setNumberAtk(arrAtk.getInt(0));
        int coolDown = GameCfgHunting.config.coolDown - (int) (System.currentTimeMillis() / 1000 - arrAtk.getInt(1));
        builder.setCownDown(coolDown < 0 ? 0 : coolDown);
        builder.setFee(GameCfgHunting.config.feeSkip);

        GGProto.ListCommonVector.Builder infoBuilder = GGProto.ListCommonVector.newBuilder();
        {
            GameCfgHunting.Map map = GameCfgHunting.getMap();
            infoBuilder.addAVector(CommonProto.getCommonVectorProto(map.getMonsterId(), null));
        }
        {
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(1l, 15000l, 1l), null));
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(2l, 75000l, 10l), null));
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(3l, 150000l, 20l), null));
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(4l, 300000l, 30l), null));
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(5l, 750000l, 40l), null));
        }
        builder.setInfo(infoBuilder);
        return builder.build();
    }
    //endregion

    //region Database Access
    BattleDataEntity prepareBattle(List<UserInfo> aUser) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(TeamObject.GAME_HUNTING);
            session.save(battle);

            for (int i = 0; i < aUser.size(); i++) {
                session.save(new BattleUserEntity(battle.getId(), aUser.get(i).getId(), Bonus.defaultBonusView(Bonus.GAME_NUMBER_ATK, Constans.EVENT_GOLD_HUNTING, -1).toString(), TeamObject.GAME_HUNTING));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

    //region init class
    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler, Map<Integer, AAHandler> mSubGameHandler, GameHandler parent) {
        List<Integer> services = Arrays.asList(HUNT_JOIN, HUNT_STATUS, HUNT_SKIP_WAITING);

        services.forEach(service -> {
            mGameHandler.put(service, parent);
            mSubGameHandler.put(service, getInstance());
        });
    }

    static GoldHunting instance;

    public static GoldHunting getInstance() {
        if (instance == null) {
            instance = new GoldHunting();
        }
        return instance;
    }
    //endregion
}
