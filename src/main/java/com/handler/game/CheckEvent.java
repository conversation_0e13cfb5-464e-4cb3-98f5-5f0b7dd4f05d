package com.handler.game;

import com.bem.config.CfgGlobal;
import com.bem.config.CfgSolo;
import com.bem.matcher.*;
import com.bem.object.UserInfo;
import com.handler.AHandler;
import com.handler.GameHandler;

import java.util.Map;

public class CheckEvent extends AAHandler {

    int timeCounter = 0;

    @Override
    public void checkStatus() {
        if (timeCounter % 300 == 0) {
            checkSoloDay();
        }
        timeCounter += 3;
        if (timeCounter >= 300) {
            timeCounter = 0;

        }
    }

    void checkSoloDay() {
        if (CfgGlobal.inEvent()) {
            if (!(TeamMatcher.mQueue.get(TeamObject.RANK) instanceof GlobalQueue)) {
                TeamMatcher.mQueue.put(TeamObject.RANK, TeamMatcher.availableQueue.get(TeamObject.GLOBAL));
            }
        } else if (CfgSolo.inEvent()) {
            if (!(TeamMatcher.mQueue.get(TeamObject.RANK) instanceof SoloQueue)) {
                TeamMatcher.mQueue.put(TeamObject.RANK, TeamMatcher.availableQueue.get(TeamObject.SOLO));
            }
        } else {
            if (!(TeamMatcher.mQueue.get(TeamObject.RANK) instanceof zombieQueue)) {
                TeamMatcher.mQueue.put(TeamObject.RANK, TeamMatcher.availableQueue.get(TeamObject.RANK));
            }
        }

    }


    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler, Map<Integer, AAHandler> mSubGameHandler, GameHandler parent) {

    }

    @Override
    public void doAction(AHandler handler, UserInfo user, int service, byte[] srcRequest) {

    }

    @Override
    public int addNumberAtk(UserInfo user, long[] aValue) {
        return 0;
    }

    static CheckEvent instance;

    public static synchronized CheckEvent getInstance() {
        if (instance == null) {
            instance = new CheckEvent();
        }
        return instance;
    }
}
