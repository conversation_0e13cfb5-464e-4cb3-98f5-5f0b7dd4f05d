package com.handler.game;

import com.bem.config.CfgServer;
import com.bem.dao.mapping.LogEventEntity;
import com.bem.object.UserInfo;
import com.bem.util.Util;
import com.handler.AHandler;
import com.handler.GameHandler;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.slib_Logger;
import grep.database.HibernateUtil;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.slf4j.Logger;

import java.util.Calendar;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 12/5/2014.
 */
public abstract class AAHandler extends IAction {

    protected static int STATUS_CLOSE = 0;
    protected static int STATUS_OPEN = 1;

    public abstract void initAction(Map<Integer, AHandler> mGameHandler, Map<Integer, AAHandler> mSub<PERSON><PERSON><PERSON><PERSON><PERSON>, GameHandler parent);

    public abstract void doAction(<PERSON><PERSON><PERSON><PERSON> handler, UserInfo user, int service, byte[] srcRequest);

    protected Logger getLogger() {
        return slib_Logger.root();
    }

    protected void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }

    static void debug(String msg) {
        if (CfgServer.debug)
            slib_Logger.root().debug(msg);
    }

    public abstract void checkStatus();

    public abstract int addNumberAtk(UserInfo user, long[] aValue);

    protected LogEventEntity dbGetLogEvent(int eventId, int eventIndex,int severId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from log_event where server_id=:serverId and event_id=:eventId and event_index=:eventIndex and event_year=:eventYear");
            query.setInteger("serverId", severId);
            query.setInteger("eventId", eventId);
            query.setInteger("eventIndex", eventIndex);
            query.setInteger("eventYear", Calendar.getInstance().get(Calendar.YEAR));
            LogEventEntity event = (LogEventEntity) query.addEntity(LogEventEntity.class).uniqueResult();
            return event == null ? new LogEventEntity(Constans.DB_STATUS_NOT_FOUND,severId) : event;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return new LogEventEntity(Constans.DB_STATUS_EXCEPTION,severId);
    }
}
