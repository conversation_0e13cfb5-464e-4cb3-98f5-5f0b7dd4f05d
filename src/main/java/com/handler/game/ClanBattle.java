package com.handler.game;

import com.bem.config.CfgNewMission;
import com.bem.config.GameCfgClanWar;
import com.bem.config.GameCfgHunting;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.*;
import com.bem.monitor.ClanMonitor;
import com.bem.object.EventStatus;
import com.bem.object.GameInfo;
import com.bem.object.UserInfo;
import com.bem.object.clanbattle.ClanSummary;
import com.bem.object.clanbattle.LogBattleBonus;
import com.bem.object.clanbattle.PlayerPoint;
import com.bem.object.clanbattle.PlayerSummary;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MCache;
import com.cache.MainCache;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.handler.AHandler;
import com.handler.GameHandler;
import com.k2tek.Config;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import grep.helper.StringHelper;
import org.hibernate.Session;

import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class ClanBattle extends AAHandler {

    final static String name = "clan_battle";
    String KEY_INFO = name + ":info";
    String KEY_ROOM = name + ":room:";
    String KEY_BONUS = name + ":bonus";
    String KEY_TOP_MAP_PLAYER = name + ":top_map_player";
    String KEY_TOP_EASY_MAP_PLAYER = name + ":top_easy_map_player";
    String KEY_CLAN_SUMMARY = name + ":KEY_CLAN_SUMMARY";
    final static int MODE_EASY = 2, MODE_HARD = 1;

    GameInfo info = new GameInfo();
    boolean sendBonus = false, clearOldEvent = false;
    int serverId;

    public ClanBattle(int serverId) {
        logDebug("create ClanBattle");
        this.serverId = serverId;
        KEY_INFO = serverId + KEY_INFO;
        KEY_ROOM = serverId + KEY_ROOM;
        KEY_BONUS = serverId + KEY_BONUS;
        KEY_TOP_MAP_PLAYER = serverId + KEY_TOP_MAP_PLAYER;
        KEY_TOP_EASY_MAP_PLAYER = serverId + KEY_TOP_EASY_MAP_PLAYER;
        KEY_CLAN_SUMMARY = serverId + KEY_CLAN_SUMMARY;
    }

    @Override
    public void doAction(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (GameCfgClanWar.config.enable != 1 || serverId == 3) {
            handler.addErrMessage(handler.getLang(Lang.common_closed_event));
            return;
        }
        if (GameCfgHunting.config.requireLevel > user.getDbUser().getLevel()) {
            handler.addErrMessage(String.format(handler.getLang(Lang.common_event_required_level), GameCfgHunting.config.requireLevel));
            return;
        }
        if (user.getDbUser().getClan() == 0) {
            handler.addErrMessage(handler.getLang(Lang.clan_no_clan));
            return;
        }
        try {
            switch (service) {
                case CBATTLE_MAP_STATUS:
                    mapStatus(handler, user, info.status, srcRequest);
                    break;
                case CBATTLE_ROOM_PREVIEW:
                    roomPreview(handler, user, service, srcRequest);
                    break;
                case CBATTLE_STATISTIC:
                    statistic(handler, user, service, srcRequest);
                    break;
                case CBATTLE_TOP_MAP_PLAYER:
                    topMapPlayer(handler, user, info.status, srcRequest);
                    break;
                default:
                    if (info.status.getId() == -1)
//                        handler.addErrMessage(String.format("Sự kiện bắt đầu vào %s", GameCfgClanWar.getNextStrEvent()));
                        handler.addErrMessage(String.format(user.getLang().get(Lang.err_start_event), GameCfgClanWar.getNextStrEvent()));
                    else {
                        int roomId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
                        ClanRoomBattle room = mRoom.get(roomId);
//                        if (room == null) handler.addErrMessage("Mã phòng không hợp lệ");
                        if (room == null) handler.addErrMessage(user.getLang().get(Lang.err_room_code));
                        else room.doAction(handler, user, service, srcRequest);
                    }
                    break;
            }
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
            handler.addErrMessage();
        }
    }

    @Override
    public void checkStatus() {
        timeFromEndEvent++;
        EventStatus status = GameCfgClanWar.getEventStatus(); // index, index from year - su kien dang dien ra
        EventStatus curStatus = GameCfgClanWar.getLastEndEvent(); // status dung khi khong trong su kien
        logDebug("checkStatus status=%s curStatus=%s".formatted(status, curStatus));
        if (status.getId() == -1) {
            if (status.getCurTime() < status.getNextTime() && status.getNextTime() - status.getCurTime() <= 12 * DateTime.HOUR_MIN) { // truoc su kien 12 hour
                if (!clearOldEvent) {
                    logDebug("!clearOldEvent");
                    mBonus = null;
                    mRoom.forEach((key, value) -> value.clearOldEvent());
                    mClanSummary = new HashMap<>();
                    mMapPlayer = new HashMap<>();
                    mEasyMapPlayer = new HashMap<>();
                    clearOldEvent = true;
                    cacheData();
                }
                if (mBonus == null) {
                    LogEventEntity logEvent = dbGetLogEvent(curStatus.getNextEventId());
                    logDebug("mBonus null id=" + logEvent.getId());
                    if (logEvent.getId() == Constans.DB_STATUS_NOT_FOUND) {
                        mBonus = GameCfgClanWar.randomBonus();
                        LogEventEntity logNewEvent = new LogEventEntity(serverId);
                        logNewEvent.setData("");
                        logNewEvent.setInfo(new Gson().toJson(mBonus));
                        logNewEvent.setEventId(Constans.EVENT_CLAN_BATTLE);
                        logNewEvent.setEventIndex(curStatus.getNextEventId());
                        if (!dbLogEvent(logNewEvent)) mBonus = null;
                    } else if (logEvent.getId() > 0) {
                        mBonus = new Gson().fromJson(logEvent.getInfo(), new TypeToken<Map<Integer, GameCfgClanWar.MapBonus>>() {
                        }.getType());
                    }
                }
            } else if (mBonus == null) {
                LogEventEntity logEvent = dbGetLogEvent(curStatus.getLastEventId());
                if (logEvent.getId() > 0 && !StringHelper.isEmpty(logEvent.getInfo())) {
                    mBonus = new Gson().fromJson(logEvent.getInfo(), new TypeToken<Map<Integer, GameCfgClanWar.MapBonus>>() {
                    }.getType());
                }
            }

            // trao thuong
            if (!sendBonus) {// khong trong su kien
                if (timeFromEndEvent >= 60) {
                    LogEventEntity logEvent = dbGetLogEvent(curStatus.getLastEventId());
                    logDebug("sendBonus logEvent " + logEvent);
                    if (logEvent.getId() > 0) {
                        if (StringHelper.isEmpty(logEvent.getData())) {
                            LogBattleBonus logBattleBonus = new LogBattleBonus();
                            logBattleBonus.setTopMap(topMapPlayer);
                            logBattleBonus.setTopEasyMap(topEasyMapPlayer);
                            mRoom.forEach((key, value) -> {
                                logBattleBonus.aId.add(value.roomId);
                                logBattleBonus.aName.add(value.roomName);
                                logBattleBonus.topPlayer.add(value.getTopPlayerId());
                                logBattleBonus.topClan.add(value.getTopClanId());
                            });
                            if (dbSendBonus(logEvent, logBattleBonus)) {
                                sendBonus = true;
                            }
                        } else sendBonus = true;
                    }
                }
            }
        } else if (mBonus == null) { // dang dien ra su kien
            LogEventEntity logEvent = dbGetLogEvent(status.getId());
            logDebug("status > 0  id=" + logEvent.getId());
            if (logEvent.getId() > 0) {
                mBonus = new Gson().fromJson(logEvent.getInfo(), new TypeToken<Map<Integer, GameCfgClanWar.MapBonus>>() {
                }.getType());
            } else if (logEvent.getId() == Constans.DB_STATUS_NOT_FOUND) {
                mBonus = GameCfgClanWar.randomBonus();
                LogEventEntity logNewEvent = new LogEventEntity(serverId);
                logNewEvent.setData("");
                logNewEvent.setInfo(new Gson().toJson(mBonus));
                logNewEvent.setEventId(Constans.EVENT_CLAN_BATTLE);
                logNewEvent.setEventIndex(status.getId());
                if (!dbLogEvent(logNewEvent)) mBonus = null;
            }
        }

        // check event status
        if (info.status.getId() != status.getId()) {
            logDebug("info.status.getId() != status.getId()");
            if (status.getId() >= 0) { // vao su kien
                clearOldEvent = false;
                sendBonus = false;
                timeFromEndEvent = 0;

                restrictedClanIds = new ArrayList<>();
                List<ClanEntity> aTopClan = CfgNewMission.entryLstClanEntity(CfgNewMission.aGet, null, serverId);
                for (int i = 0; i < aTopClan.size(); i++) {
                    if (restrictedClanIds.size() >= 10) break;
                    restrictedClanIds.add(aTopClan.get(i).getId());
                }

                restrictedPlayerIds = new ArrayList<>();
                List<UserEntity> aUser = MainCache.getInstance().getAUserTrophy100().get(serverId);
                if (aUser != null) {
                    for (int i = 0; i < aUser.size(); i++) {
                        if (restrictedPlayerIds.size() >= 10) break;
                        restrictedPlayerIds.add(aUser.get(i).getId());
                    }
                }
            } else {
                timeFromEndEvent = 0;
            }
            info.status = status;
            cacheEventInfo();
        }

        if (info.status.getId() >= 0) { // trong thoi gian su kien -> cache thong tin
            if (timeFromEndEvent % 10 == 0) { // 1.5 minute
                cacheData();
                mRoom.forEach((key, value) -> value.cacheData());
            }
        }
    }

    //region service handler
    void statistic(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        ClanSummary summary = mClanSummary.get(user.getDbUser().getClan());
        if (summary == null) handler.addErrMessage(user.getLang().get(Lang.clan_not_exist));

        List<PlayerSummary> aPlayerSummary = new ArrayList<>(summary.mPlayer.values());
        Collections.sort(aPlayerSummary, (p1, p2) -> new Integer(p2.point).compareTo(new Integer(p1.point)));
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        aPlayerSummary.forEach(player -> {
            GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
            tmp.addANumber(player.point);
            tmp.addANumber(player.numberRoom);
            tmp.addAString(player.name);
            builder.addAVector(tmp);
        });
        handler.addResponse(builder.build());
    }

    void roomPreview(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        int roomId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        ClanRoomBattle room = mRoom.get(roomId);
        room.doAction(handler, user, CBATTLE_TOP_PLAYER, new byte[]{});
        room.doAction(handler, user, CBATTLE_TOP_CLAN, new byte[]{});
        if (mBonus == null || !mBonus.containsKey(roomId)) {
            GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
            builder.addAVector(CommonProto.getCommonLongVectorProto(null, null));
            builder.addAVector(CommonProto.getCommonLongVectorProto(null, null));
            handler.addResponse(builder.build());
        } else {
            GameCfgClanWar.MapBonus mapBonus = mBonus.get(roomId);
            GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
            builder.addAVector(CommonProto.getCommonLongVectorProto(mapBonus.allClan, null));
            builder.addAVector(CommonProto.getCommonLongVectorProto(mapBonus.clan, null));
            handler.addResponse(builder.build());
        }

    }

    void mapStatus(AHandler handler, UserInfo user, EventStatus status, byte[] srcRequest) {
        int mode = srcRequest == null || srcRequest.length == 0 ? MODE_HARD : (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);

        if (!mClanSummary.containsKey(user.getDbUser().getClan())) {
            mClanSummary.put(user.getDbUser().getClan(), new ClanSummary());
        }
        if (!mMapPlayer.containsKey(user.getId())) {
            String clanName = ClanMonitor.getClan(user.getDbUser().getClan()).getName();
            mMapPlayer.put(user.getId(), new PlayerSummary(user.getId(), user.getDbUser().getName(), clanName));
            mEasyMapPlayer.put(user.getId(), new PlayerSummary(user.getId(), user.getDbUser().getName(), clanName));
        }
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        //
        {
            GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
            tmp.addANumber(GameCfgClanWar.getTimeout(status.getIndex())); // timeout

            // my room Id
            if (status.getId() == -1) tmp.addANumber(0);
            else {
                Integer roomId = (Integer) user.getMCache().get(KEY_ROOM + user.getId());
                tmp.addANumber(roomId == null ? 0 : roomId);
            }

            tmp.addAString(GameCfgClanWar.config.changeRoomMessage);
            builder.addAVector(tmp);
        }
        //
        for (int i = 0; i < GameCfgClanWar.config.maps.size(); i++) {
            int mapId = mode == MODE_HARD ? i + 1 : i + 101;
            ClanRoomBattle room = mRoom.get(mapId);
            GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
            tmp.addANumber(room.roomId);
            if (mBonus != null && mBonus.containsKey(mapId)) tmp.addANumber(mBonus.get(mapId).star);
            else tmp.addANumber(1);
            tmp.addAString(room.roomName);
            tmp.addAString(mRoom.get(mapId).getTopClan());
            builder.addAVector(tmp);
        }
        handler.addResponse(builder.build());
    }

    void topMapPlayer(AHandler handler, UserInfo user, EventStatus status, byte[] srcRequest) {
        int mode = srcRequest == null ? MODE_HARD : (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        List<PlayerSummary> aTopPlayer = mode == MODE_HARD ? topMapPlayer : topEasyMapPlayer;
        PlayerSummary point = mode == MODE_HARD ? mMapPlayer.get(user.getId()) : mEasyMapPlayer.get(user.getId());
        if (point == null) {
            if (mode == MODE_HARD)
                mMapPlayer.put(user.getId(), new PlayerSummary(user.getId(), user.getDbUser().getName(), ClanMonitor.getClan(user.getDbUser().getClan()).getName()));
            else
                mEasyMapPlayer.put(user.getId(), new PlayerSummary(user.getId(), user.getDbUser().getName(), ClanMonitor.getClan(user.getDbUser().getClan()).getName()));
            point = mode == MODE_HARD ? mMapPlayer.get(user.getId()) : mEasyMapPlayer.get(user.getId());
        }
        int rank = -1;
        for (int i = 0; i < aTopPlayer.size(); i++) if (aTopPlayer.get(i).id == user.getId()) rank = i;

        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        builder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(point.id, (long) point.point), Arrays.asList(rank == -1 ? "21" : String.valueOf(rank + 1), point.name, point.clanName)));
        for (int i = 0; i < aTopPlayer.size(); i++) {
            builder.addAVector(aTopPlayer.get(i).toProto(i + 1));
        }
        handler.addResponse(builder.build());
    }
    //endregion

    //region logic
    public int addNumberAtk(UserInfo user, long[] aValue) {
        Integer roomId = (Integer) user.getMCache().get(KEY_ROOM + user.getId());
        if (roomId != null) {
            mRoom.get(roomId).addPoint(user, aValue);
        }
        return 0;
    }

    void cacheEventInfo() {
        try {
            MCache.getInstance().set(KEY_INFO, info, MCache.EXPIRE_WEEK);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex), serverId);
        }
    }
    //endregion

    //region proto
    //endregion

    //region Database Access
    boolean dbLogEvent(LogEventEntity logEvent) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.save(logEvent);
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    protected LogEventEntity dbGetLogEvent(int eventIndex) {
        return dbGetLogEvent(Constans.EVENT_CLAN_BATTLE, eventIndex, serverId);
    }

    boolean dbSendBonus(LogEventEntity logEvent, LogBattleBonus logBonus) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            List<UserMessageEntity> aMessage = new ArrayList<>();
            List<ClanBattleBonusEntity> aClanBonus = new ArrayList<>();

            for (int i = 0; i < logBonus.topMap.size(); i++) {
                UserMessageEntity uMessage = new UserMessageEntity();
                uMessage.setTitle(String.format("Phần thưởng top %s hạng cao cấp", (i + 1)));
                uMessage.setDateCreated(new Date());
                uMessage.setUserId(logBonus.topMap.get(i));
                uMessage.setBonus(new Gson().toJson(GameCfgClanWar.config.topMapPlayerBonus.get(i)));
                aMessage.add(uMessage);
            }

            for (int i = 0; i < logBonus.topEasyMap.size(); i++) {
                UserMessageEntity uMessage = new UserMessageEntity();
                uMessage.setTitle(String.format("Phần thưởng top %s hạng thường", (i + 1)));
                uMessage.setDateCreated(new Date());
                uMessage.setUserId(logBonus.topEasyMap.get(i));
                uMessage.setBonus(new Gson().toJson(GameCfgClanWar.config.topEasyMapPlayerBonus.get(i)));
                aMessage.add(uMessage);
            }

            for (int i = 0; i < logBonus.topPlayer.size(); i++) {
                List<Long> aPlayerIds = logBonus.topPlayer.get(i);
                for (int j = 0; j < aPlayerIds.size(); j++) {
                    UserMessageEntity uMessage = new UserMessageEntity();
                    uMessage.setTitle(String.format("Phần thưởng top %s lãnh địa %s", (j + 1), logBonus.aName.get(i)));
                    uMessage.setDateCreated(new Date());
                    uMessage.setUserId(aPlayerIds.get(j));
                    uMessage.setBonus(new Gson().toJson(GameCfgClanWar.config.topPlayerBonus.get(j)));
                    aMessage.add(uMessage);
                }
            }

            for (int i = 0; i < logBonus.topClan.size(); i++) {
                String roomName = logBonus.aName.get(i);
                int roomId = logBonus.aId.get(i);
                int clanId = logBonus.topClan.get(i);
                if (clanId == 0) continue;
                ClanEntity clan = ClanMonitor.getClan(clanId);

                ClanBattleBonusEntity clanBonus = new ClanBattleBonusEntity();
                clanBonus.setClanId(clan.getId());
                clanBonus.setMessage("Phần thưởng chiếm lãnh địa " + roomName);
                clanBonus.setNumber(3);
                clanBonus.setBonus(new Gson().toJson(mBonus.get(roomId).clan));
                clanBonus.setReceiver("");
                aClanBonus.add(clanBonus);

                List<UserEntity> aUser = new ArrayList<>(clan.getAUser());
                String allClanBonus = new Gson().toJson(mBonus.get(roomId).allClan);
                aUser.forEach(user -> {
                    UserMessageEntity uMessage = new UserMessageEntity();
                    uMessage.setTitle(String.format("Phần thưởng chiếm lĩnh lãnh địa %s", roomName));
                    uMessage.setDateCreated(new Date());
                    uMessage.setUserId(user.getId());
                    uMessage.setBonus(allClanBonus);
                    aMessage.add(uMessage);
                });
            }

            session.beginTransaction();
            for (ClanBattleBonusEntity clanBonus : aClanBonus) session.save(clanBonus);
            for (UserMessageEntity message : aMessage) session.save(message);
            logEvent.setData(new Gson().toJson(logBonus));
            session.update(logEvent);
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }
    //endregion

    //region init class
    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler, Map<Integer, AAHandler> mSubGameHandler, GameHandler parent) {
        List<Integer> services = Arrays.asList(CBATTLE_MAP_STATUS, CBATTLE_ROOM_PREVIEW, CBATTLE_JOIN_ROOM, CBATTLE_SHOP_LIST, CBATTLE_TOP_CLAN,
                CBATTLE_TOP_PLAYER, CBATTLE_CHEAR_HISTORY, CBATTLE_CHAT_LIST, CBATTLE_CHAT, CBATTLE_FIND_MATCH, CBATTLE_CANCEL_FIND_MATCH, CBATTLE_STATISTIC,
                CBATTLE_TOP_MAP_PLAYER);

        services.forEach(service -> {
            mGameHandler.put(service, parent);
            for (Integer serverId : Config.lstServerId) {
                mSubGameHandler.put(100000 * serverId + service, getInstance(serverId));
            }
        });
    }

    static Map<Integer, ClanBattle> instance;

    public static synchronized ClanBattle getInstance(int serverId) {
        if (instance == null) {
            instance = new HashMap<>();
        }
        if (instance.get(serverId) == null) {
            instance.put(serverId, new ClanBattle(serverId));
        }
        return instance.get(serverId);
    }

    List<Integer> restrictedClanIds = new ArrayList<>();
    List<Long> restrictedPlayerIds = new ArrayList<>();
    Map<Integer, ClanRoomBattle> mRoom = new HashMap<>();
    Map<Integer, ClanSummary> mClanSummary = new HashMap<>();
    Map<Long, PlayerSummary> mMapPlayer = new HashMap<>();
    Map<Long, PlayerSummary> mEasyMapPlayer = new HashMap<>();
    List<PlayerSummary> topMapPlayer = new ArrayList<>();
    List<PlayerSummary> topEasyMapPlayer = new ArrayList<>();
    Map<Integer, GameCfgClanWar.MapBonus> mBonus;
    int timeFromEndEvent = 0;

    static {
        for (int i = 0; i < Config.lstServerId.size(); i++) {
            int serverId = Config.lstServerId.get(i);
            ClanBattle clanBattle = getInstance(serverId);
            try {
                clanBattle.info = (GameInfo) MCache.getInstance().get(clanBattle.KEY_INFO);
            } catch (Exception ex) {
                Logs.error(Util.exToString(ex), serverId);
            }
            if (clanBattle.info == null) clanBattle.info = new GameInfo();

            GameCfgClanWar.config.maps.forEach(map -> {
                clanBattle.mRoom.put(map.id, new ClanRoomBattle(serverId, map.id, map));
                clanBattle.mRoom.put(map.id + 100, new ClanRoomBattle(serverId, map.id + 100, map));
            });

            try {
                clanBattle.mMapPlayer = (Map<Long, PlayerSummary>) MCache.getInstance().get(clanBattle.KEY_TOP_MAP_PLAYER);
                clanBattle.mEasyMapPlayer = (Map<Long, PlayerSummary>) MCache.getInstance().get(clanBattle.KEY_TOP_EASY_MAP_PLAYER);
                clanBattle.mClanSummary = (Map<Integer, ClanSummary>) MCache.getInstance().get(clanBattle.KEY_CLAN_SUMMARY);
            } catch (Exception ex) {
                Logs.error(Util.exToString(ex), Config.lstServerId.get(i));
            }
            if (clanBattle.mMapPlayer == null) clanBattle.mMapPlayer = new HashMap<>();
            if (clanBattle.mEasyMapPlayer == null) clanBattle.mEasyMapPlayer = new HashMap<>();
            if (clanBattle.mClanSummary == null) clanBattle.mClanSummary = new HashMap<>();
        }
    }

    public PlayerPoint newPlayerPoint(long id, String name, int clanId, String clanName) {
        return new PlayerPoint(id, name, clanId, clanName);
    }

    void cacheData() {
        {
            List<PlayerSummary> aPlayer = new ArrayList<>(mMapPlayer.values());
            Collections.sort(aPlayer, (p1, p2) -> new Integer(p2.point).compareTo(new Integer(p1.point)));
            aPlayer = aPlayer.size() > 20 ? aPlayer.subList(0, 20) : aPlayer;
            topMapPlayer = new Gson().fromJson(new Gson().toJson(aPlayer), new TypeToken<ArrayList<PlayerSummary>>() {
            }.getType());
        }
        {
            List<PlayerSummary> aPlayer = new ArrayList<>(mEasyMapPlayer.values());
            Collections.sort(aPlayer, (p1, p2) -> new Integer(p2.point).compareTo(new Integer(p1.point)));
            aPlayer = aPlayer.size() > 20 ? aPlayer.subList(0, 20) : aPlayer;
            topEasyMapPlayer = new Gson().fromJson(new Gson().toJson(aPlayer), new TypeToken<ArrayList<PlayerSummary>>() {
            }.getType());
        }
        try {
            MCache.getInstance().set(KEY_TOP_MAP_PLAYER, mMapPlayer, MCache.EXPIRE_WEEK);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex), serverId);
        }
        try {
            MCache.getInstance().set(KEY_TOP_EASY_MAP_PLAYER, mEasyMapPlayer, MCache.EXPIRE_WEEK);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex), serverId);
        }
        try {
            MCache.getInstance().set(KEY_CLAN_SUMMARY, mClanSummary, MCache.EXPIRE_WEEK);
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex), serverId);
        }
    }
    //endregion

    public void logDebug(String message) {
        if (serverId == 1) {
            Logs.warn(message);
        }
    }

}
