package com.handler.game;

import com.bem.config.GameCfgHunting;
import com.bem.config.GameCfgSlotMachine;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.object.UserInfo;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MCache;
import com.handler.AHandler;
import com.handler.GameHandler;
import com.k2tek.Constans;
import com.proto.GGProto;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/11/2014.
 */
public class SlotMachine extends AAHandler {

    final static String name = "slot_machine";
    final static String KEY_JOIN = name + ":";

//    List<GGProto.ProtoUser> topHunting = new ArrayList<GGProto.ProtoUser>();
//
//    static {
//        try {
//            getInstance().topHunting = (List<GGProto.ProtoUser>) MCache.getInstance().get(KEY_TOP_HUNTING);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            getInstance().getLogger().error(Util.exToString(ex));
//        }
//    }

    @Override
    public void doAction(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
        if (!GameCfgSlotMachine.enable) {
//            handler.addErrMessage("Sự kiện chưa mở");
            handler.addErrMessage(user.getLang().get(Lang.err_open_event));
            return;
        }
        if (GameCfgSlotMachine.levelRequire > user.getDbUser().getLevel() && user.getDbUser().getVip() <= 0) {
//            handler.addErrMessage(String.format("Đạt cấp %s để tham gia sự kiện. Hoặc là vip 1 trở lên", GameCfgSlotMachine.levelRequire));
            handler.addErrMessage(String.format(user.getLang().get(Lang.level_to_join_slot_machine), GameCfgSlotMachine.levelRequire));
            return;
        }
//        boolean inEvent = GameCfgHunting.inEvent();
//        if (!inEvent && service != HUNT_STATUS) {
//            handler.addResponse(HUNT_END, CommonProto.getCommonLongVectorProto(null, Arrays.asList("Đã kết thúc sự kiện")));
//            return;
//        }
        try {
            switch (service) {
                case GAME_SLOT_MACHINE_STATUS:
                    status(true, handler, user, service, srcRequest);
                    break;
                case GAME_SLOT_MACHINE_QUAY:
                    rotate(true, handler, user, service, srcRequest);
                    break;
                case GAME_SLOT_MACHINE_WINNER:
                    lastWinner(true, handler, user, service, srcRequest);
                    break;
            }
        } catch (Exception ex) {
            getLogger().error(Util.exToString(ex));
            handler.addErrMessage();
        }
    }

    @Override
    public void checkStatus() {

    }

    //region service handler
//    void status(boolean inEvent, AHandler handler, UserInfo user, int service, byte[] srcRequest) {
//        handler.addResponse(protoStatus(getNumberAtk(user)));
//    }

    void lastWinner(boolean inEvent, AHandler handler, UserInfo user, int service, byte[] srcRequest) {
//        if(true) {
//            handler.addErrMessage("Chức năng đang bảo trì");
//            return;
//        }
        JSONArray arr = JSONArray.fromObject(GameCfgSlotMachine.jackpot.getHistory());
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        builder.addAString("Top Jackpot");
        if (arr.isEmpty()) {
            builder.addANumber(0);
//            builder.addAString("Chưa có người trúng thưởng");
            builder.addAString(user.getLang().get(Lang.nobody_get_award));
        } else {
            int size = arr.size() < 20 ? arr.size() : 20;
            for (int i = 0; i < size; i++) {
                JSONObject obj = arr.getJSONObject(i);
                builder.addANumber(obj.getInt("gem"));
                builder.addAString(obj.getString("name"));
            }
        }
        handler.addResponse(builder.build());
    }

    void status(boolean inEvent, AHandler handler, UserInfo user, int service, byte[] srcRequest) {
//        if(true) {
//            handler.addErrMessage("Chức năng đang bảo trì");
//            handler.addErrMessage(user.getLang().get(Lang.err_maintaining));
//            return;
//        }
        List<Long> aLong = new ArrayList<Long>();
        aLong.add(GameCfgSlotMachine.jackpot.getPot());

        for (int i = 0; i < GameCfgSlotMachine.aBet.size(); i++) {
            aLong.add((long) GameCfgSlotMachine.aBet.get(i));
        }
//        System.out.println("aLong---------------->" + aLong.toString());
        handler.addResponse(CommonProto.getCommonLongVectorProto(aLong, null));
    }

    void rotate(boolean inEvent, AHandler handler, UserInfo userInfo, int service, byte[] srcRequest) {
//        if (true) {
//            handler.addErrMessage("Chức năng đang bảo trì");
//            handler.addErrMessage(userInfo.getLang().get(Lang.err_maintaining));
//            return;
//        }
        UserEntity user = userInfo.getDbUser();
//        if (user.getLevel() < GameCfgSlotMachine.levelRequire) {
//            System.out.println("11111111111111111");
//            handler.addErrMessage("Lên cấp " + GameCfgSlotMachine.levelRequire + " để tham gia trò chơi này");
//            handler.addErrMessage(String.format(userInfo.getLang().get(Lang.level_to_play_slot_machine), GameCfgSlotMachine.levelRequire));
//            return;
//        }
        int gem = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);

        if (gem > user.getGem()) {
//            handler.addResponse(IAction.SO_ME_THE_MONEY, handler.getErrorMsg("Không đủ tiền để quay"));
//            handler.addErrMessage("Không đủ tiền để quay");
            handler.addErrMessage(userInfo.getLang().get(Lang.not_enough_gem_to_spin));
            return;
        }

        if (gem > GameCfgSlotMachine.aBet.get(1) || gem < GameCfgSlotMachine.aBet.get(0) || gem % GameCfgSlotMachine.aBet.get(2) != 0) {
//            handler.addResponse(IAction.SO_ME_THE_MONEY, handler.getErrorMsg("Tiền cược không hợp lệ"));
//            handler.addErrMessage("Tiền cược không hợp lệ");
            handler.addErrMessage(userInfo.getLang().get(Lang.not_correct_bet));
            return;
        }

//        LogAction.save(user.getId(), LogAction.GGAME, LogAction.DJACKPOT,
//                LogAction.convertToLogString(Arrays.asList("payout", String.valueOf(GameCfgSlotMachine.jackpot.getPayout()), "jackpot", String.valueOf(GameCfgSlotMachine.jackpot.getPot()))));

        List<Long> result = GameCfgSlotMachine.get(handler, userInfo, gem);

        if (result == null) {
            handler.addShopMessage(userInfo.getLang().get(Lang.err_system_down));
            return;
        }

        int jackPotRate = result.get(0).intValue();
        result.remove(0);
//        System.out.println("resul--->" + result);
        handler.addResponse(CommonProto.getCommonLongVectorProto(result, null));
        userInfo.getDbUser().addNumberRotage(gem / 20);
//        List<Long> aLong = new ArrayList<Long>();
//        aLong.add(user.getGold());
//        aLong.add(user.getGem());
//        UserMoneys moneys = userInfo.getUData().getUserMoneys();
//        aLong.add(moneys.getValue(UserMoneys.MEDAL_BOSS));
//        aLong.add(moneys.getValue(UserMoneys.MEDAL));
//        aLong.add(moneys.getValue(UserMoneys.CLAN));
//        handler.addResponse(IAction.SYNC_MONEYS, CommonProto.getCommonLongVectorProto(aLong, null));


//        LogAction.save(user.getId(), LogAction.GGAME, LogAction.DJACKPOT,
//                LogAction.convertToLogString(Arrays.asList("gold", String.valueOf(user.getGold()), "addGold", String.valueOf(result.get(2) - gold),
//                        "bet", String.valueOf(gold), "rate", String.valueOf(jackPotRate), "result", result.get(3) + "_" + result.get(4) + "_" + result.get(5))));
    }


//    void join(AHandler handler, UserInfo user, int service, byte[] srcRequest) {
//        int levelIndex = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0) - 1;
//        if (levelIndex < 0 || levelIndex > 4) {
//            handler.addErrMessage("Dữ liệu không hợp lệ");
//            handler.addErrMessage(user.getLang().get(Lang.err_param));
//            return;
//        }
//        JSONArray arrAtk = getNumberAtk(user);
//        if (arrAtk.getInt(0) <= 0) {
//            handler.addErrMessage("Bạn đã hết lượt đánh ngày hôm nay, hãy mua thêm lượt đánh hoặc chờ lượt miễn phí");
//            handler.addErrMessage(user.getLang().get(Lang.no_turn_to_play));
//            return;
//        }
//        int coolDown = GameCfgHunting.config.coolDown - (int) (System.currentTimeMillis() / 1000 - arrAtk.getInt(1));
//        if (coolDown > 0) {
//            handler.addErrMessage(String.format("Hãy chờ %s trước khi bắt đầu đánh", DateTime.formatTime(coolDown)));
//            handler.addErrMessage(String.format(user.getLang().get(Lang.time_to_play)));
//            return;
//        }
//        TeamObject team = new TeamObject(TeamObject.GAME_HUNTING, user);
//        BattleDataEntity battle = prepareBattle(team.aUser);
//        if (battle == null) {
//            handler.addErrMessage(user.getLang());
//            return;
//        }
//        CacheBattle cacheBattle = team.getCacheValue(battle.getId());
//        cacheBattle.setLevelIndex(levelIndex);
//        MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
//        team.sendMessage(SERVER_BATTLE_INFO, CfgServer.getBattleServer(cacheBattle));
//
////        arrAtk.set(0, arrAtk.getInt(0) - 1);
////        arrAtk.set(1, System.currentTimeMillis() / 1000);
////        cacheAtk(user, arrAtk.toString());
//    }
    //endregion

    //region logic
    public int addNumberAtk(UserInfo user, long[] aValue) {
        int value = (int) aValue[0];
        JSONArray arrAtk = getNumberAtk(user);
        arrAtk.set(0, arrAtk.getInt(0) + value);
        if (value < 0) {
            arrAtk.set(1, System.currentTimeMillis() / 1000);
        }
        cacheAtk(user, arrAtk.toString());
        return arrAtk.getInt(0);
    }

    public JSONArray getNumberAtk(UserInfo user) {
        String cacheAtk = user.getJCache().get(keyJoin(user.getId()));
        if (cacheAtk == null) {
            cacheAtk = "[" + GameCfgHunting.config.freeAtk + ",0]";
        }
        return JSONArray.fromObject(cacheAtk);
    }

    String keyJoin(long userId) {
        return DateTime.getDateyyyyMMdd(new Date()) + KEY_JOIN + userId;
    }

    void cacheAtk(UserInfo user, String value) {
        user.getJCache().set(keyJoin(user.getId()), value, MCache.EXPIRE_1D);
    }
    //endregion

    //region proto
    GGProto.ProtoGameStatus protoStatus(JSONArray arrAtk) {
        GGProto.ProtoGameStatus.Builder builder = GGProto.ProtoGameStatus.newBuilder();
        builder.setStatus(STATUS_OPEN);
        builder.setNumberAtk(arrAtk.getInt(0));
        int coolDown = GameCfgHunting.config.coolDown - (int) (System.currentTimeMillis() / 1000 - arrAtk.getInt(1));
        builder.setCownDown(coolDown < 0 ? 0 : coolDown);
        builder.setFee(GameCfgHunting.config.feeSkip);

        GGProto.ListCommonVector.Builder infoBuilder = GGProto.ListCommonVector.newBuilder();
        {
            GameCfgHunting.Map map = GameCfgHunting.getMap();
            infoBuilder.addAVector(CommonProto.getCommonVectorProto(map.getMonsterId(), null));
        }
        {
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(1l, 15000l, 1l), null));
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(2l, 75000l, 10l), null));
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(3l, 150000l, 20l), null));
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(4l, 300000l, 30l), null));
            infoBuilder.addAVector(CommonProto.getCommonLongVectorProto(Arrays.asList(5l, 750000l, 40l), null));
        }
        builder.setInfo(infoBuilder);
        return builder.build();
    }
    //endregion

    //region Database Access
    BattleDataEntity prepareBattle(List<UserInfo> aUser) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(TeamObject.GAME_HUNTING);
            session.save(battle);

            for (int i = 0; i < aUser.size(); i++) {
                session.save(new BattleUserEntity(battle.getId(), aUser.get(i).getId(), Bonus.defaultBonusView(Bonus.GAME_NUMBER_ATK, Constans.EVENT_GOLD_HUNTING, -1).toString(), TeamObject.GAME_HUNTING));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

    //region init class
    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler, Map<Integer, AAHandler> mSubGameHandler, GameHandler parent) {
        List<Integer> services = Arrays.asList(GAME_SLOT_MACHINE_STATUS, GAME_SLOT_MACHINE_QUAY, GAME_SLOT_MACHINE_WINNER);

        services.forEach(service -> {
            mGameHandler.put(service, parent);
            mSubGameHandler.put(service, getInstance());
        });
    }

    static SlotMachine instance;

    public static SlotMachine getInstance() {
        if (instance == null) {
            instance = new SlotMachine();
        }
        return instance;
    }
    //endregion
}
