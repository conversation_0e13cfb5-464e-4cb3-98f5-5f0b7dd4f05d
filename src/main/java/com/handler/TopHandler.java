package com.handler;

import com.bem.config.CfgCommon;
import com.bem.config.CfgNewMission;
import com.bem.config.CfgTop;
import com.bem.config.CfgVip;
import com.bem.config.lang.Lang;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.*;
import com.bem.monitor.Bonus;
import com.bem.monitor.ClanMonitor;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.UserInfo;
import com.bem.object.event.TopEventLoop;
import com.bem.object.event.TopTrophy;
import com.bem.object.event.TopWin;
import com.bem.util.ApiServer;
import com.bem.util.CommonProto;
import grep.database.HibernateUtil;
import com.bem.util.Util;
import com.cache.JCache;
import com.cache.MainCache;
import com.google.gson.Gson;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import com.bem.util.ChUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.hibernate.HibernateException;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class TopHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        mGameHandler.put(TOP_PLAYER, getInstance());
        mGameHandler.put(PLAYER_INFOR, getInstance());
        mGameHandler.put(TOP_FRIEND, getInstance());
        mGameHandler.put(BATTLE_HISTORY, getInstance());
        mGameHandler.put(GIFT_CODE, getInstance());
        mGameHandler.put(USER_REPORT, getInstance());
        mGameHandler.put(CHAT_ADMIN, getInstance());
        mGameHandler.put(LIST_CHAT_ADMIN, getInstance());
        mGameHandler.put(TOP_STATUS, getInstance());
        mGameHandler.put(TOP_MONITOR, getInstance());

    }

    static TopHandler instance;

    public static TopHandler getInstance() {
        if (instance == null) {
            instance = new TopHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new TopHandler();
    }

    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case TOP_PLAYER:
                    topPlayer(-1);
                    break;
                case TOP_STATUS:
                    topStatus();
                    break;
                case TOP_MONITOR:
                    topMonitor();
                    break;

                case PLAYER_INFOR:
                    playerInfor();
                    break;
                case TOP_FRIEND:
                    topPlayer(-1);
                    break;
                case BATTLE_HISTORY:
//                    battleHistory();
                    break;
                case GIFT_CODE:
                    giftCode();
                    break;
                case USER_REPORT:
                    report();
                    break;
                case CHAT_ADMIN:
                    chatAdmin();
                    break;
                case LIST_CHAT_ADMIN:
                    listChatAdmin();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void report() {
        long cheaterId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        int reportId = -1;
        try {

            reportId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(1);
        } catch (Exception ex) {
        }
        String reason = null;
        try {
            reason = CommonProto.parseCommonVector(srcRequest).getAString(0);
        } catch (Exception ex) {
        }
        if (reportId <= -1 && (reason == null || reason.trim().equalsIgnoreCase(""))) {
            addErrMessage(getLang(Lang.user_report_no_reason));
            return;
        }
        if (reportId > -1 && reason == null) {
            reason = "";
        }
//        debug("reportifd---->" + reportId);
        if (user.getId() == 2 || user.getId() == 13 || JCache.getInstance().getValue(user.getId() + "" + cheaterId + "report:") == null) {
            reason = reason == null ? "" : reason;
            if (Database2.insert("user_report", Arrays.asList("user_id_report", "cheater_id", "id_reason", "diff_reason"), Arrays.asList(String.valueOf(user.getId()), String.valueOf(cheaterId), String.valueOf(reportId), reason)) != -1) {
                JCache.getInstance().setValue(user.getId() + "" + cheaterId + "report:", "ok", 60 * 15);
                sendMessage(channel, service, CommonProto.getCommonLongVectorProto(null, null));
                if (user.getId() == 2 || user.getId() == 13) {
                    Channel kickChanel = Online.getChannel(cheaterId);
                    if (kickChanel != null) {
                        UserInfo kickUser = (UserInfo) ChUtil.get(kickChanel, Constans.KEY_USER);
                        if (kickUser != null) {
                            Calendar ca = Calendar.getInstance();
                            ca.add(Calendar.YEAR, 10);
                            kickUser.getDbUser().setLockChat(ca.getTime());
                            String session = kickUser.getSession();
                            long id = kickUser.getId();
                            Online.logoutChannel(kickChanel);
                            UserMonitor.mUser.remove(session);
                            UserMonitor.mUserId.remove(id);
                        }
                    }
                    Database2.update("user", Arrays.asList("lock_chat", "2020-01-01 00:00:00"), Arrays.asList("id", String.valueOf(cheaterId)));
                    Database2.insert(CfgCommon.mainDb + "auth_user_details", Arrays.asList("user_id", "block_until", "block_reason"), Arrays.asList(String.valueOf(cheaterId), "2020-01-01 00:00:00",
                            reason));
                }
//            addErrMessage( "");
            }
        } else {
            addErrMessage(getLang(Lang.user_report_quick));
            return;
        }
    }

    void chatAdmin() {
        String msg = CommonProto.parseCommonVector(srcRequest).getAString(0);
        if (msg.length() > 255) {
            addErrMessage(getLang(Lang.err_max_chat));
            return;
        }
        FeedbackEntity feedback = getFeedback(user.getId());
        if (feedback == null) {
            addErrMessage();
            return;
        }
        if (!saveFeedback(feedback, msg)) {
            addErrMessage();
            return;
        }
        sendMessage(channel, service, null);
    }

    void listChatAdmin() {
        FeedbackEntity feedback = getFeedback(user.getId());
        if (feedback == null) {
            addErrMessage();
            return;
        }
        sendMessage(channel, service, feedback.toProto());
    }

    void giftCode() {
        String giftCode = StringHelper.removeInvalidCharacter(CommonProto.parseCommonVector(srcRequest).getAString(0)).toLowerCase();
        UserDAO uDao = new UserDAO();
        int commonCode = uDao.getcommonGiftcode(giftCode);
        if (commonCode > 0) {
            int count = uDao.getUsercommonGiftcode(commonCode, user.getId());
            if (count <= 0) {
                String award = uDao.getcommonGiftcodeAward(giftCode);
                if (award != null) {
                    try {
                        JSONArray data = JSONArray.fromObject(award);
                        List<Long> aBonus = Bonus.receiveListItem(user, data, "gift_code_common");
                        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(aBonus, Arrays.asList(getLang(Lang.giftcode_success))));
                        uDao.insertUserGiftcode(user.getId(), commonCode);
                        return;
                    } catch (Exception ex) {
                        Logs.error(Util.exToString(ex));
                    }
                }
            }
        }
        if (giftCode.length() == 0 || !NumberUtil.isNumber(giftCode)) {
            addErrMessage(getLang(Lang.giftcode_invalid));
            return;
        }
        JSONObject ret = JSONObject.fromObject(ApiServer.consumeGiftCode(user.getId(), giftCode, user.getDbUser().getServer()));
        if (ret.getInt("code") == 0) {
            JSONArray data = ret.getJSONArray("data");
            List<Long> aBonus = Bonus.receiveListItem(user, data, "gift_code");
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(aBonus, Arrays.asList(ret.getString("message"))));
        } else {
            addErrMessage(ret.getString("message"));
        }
    }

    void playerInfor() {
        long userId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        if (userId != user.getId()) {
            UserEntity uEntity = (UserEntity) Database2.getUnique("user", Arrays.asList("id", String.valueOf(userId)), UserEntity.class);
            if (uEntity == null) {
                addErrMessage(getLang(Lang.user_not_exist));
                return;
            }
            UserEventEntity uEvent = (UserEventEntity) Database2.getUnique("user_event", Arrays.asList("user_id", String.valueOf(userId),
                    "event_id", String.valueOf(Constans.EVENT_TOP_TROPHY), "event_index", String.valueOf(TopTrophy.getInstance().getEventIndex()[0])), UserEventEntity.class);
            uEntity.setTrophy((int) (uEvent == null ? 0 : uEvent.getValue()));
            UserEventEntity uEventWin = (UserEventEntity) Database2.getUnique("user_event", Arrays.asList("user_id", String.valueOf(uEntity.getId()),
                    "event_id", String.valueOf(Constans.EVENT_TOP_WIN), "event_index", String.valueOf(0)), UserEventEntity.class);
            UserDataEntity uData = (UserDataEntity) Database2.getUnique("user_data", Arrays.asList("user_id", String.valueOf(userId)), UserDataEntity.class);
            //        user.getUData().getUInt().addValue(UserInt.NUMBER_WIN, UserInt.TYPE_SET,(uEvent== null ? 0 : (int)uEvent.getValue()));
            uEntity.setMaxwin((uEventWin == null ? 0 : (int) uEventWin.getValue()));
            if (uEntity == null) {
                addErrMessage();
                return;
            }
            uEntity.setVip(CfgVip.getVip(uEntity.getGemNap()));
//            uData.getInitUInt(uEntity);
            sendMessage(channel, service, CommonProto.protoUser(uEntity, uData));
        } else {
            sendMessage(channel, service, CommonProto.protoUser(user.getDbUser(), user.getUData()));
        }

//        String winRate = userId == user.getId() ? user.getDbUser().getWinRate() : null;
//        if (winRate == null) {
//            UserEntity uEntity = new UserDAO().getUserEntity(userId);
//            if (uEntity != null) {
//                winRate = uEntity.getWinRate();
//            }
//        }
//        if (winRate == null) {
//            addErrMessage();
//            return;
//        }
//        JSONArray arr = JSONArray.fromObject(winRate);
//        String soloRate = arr.getInt(0) == 0 && arr.getInt(1) == 0 ? "0%" : String.format("%,.2f %%", (100 * (float) arr.getInt(0) / (float) (arr.getInt(0) + arr.getInt(1))));
//        String teamRate = arr.getInt(2) == 0 && arr.getInt(3) == 0 ? "0%" : String.format("%,.2f %%", (100 * (float) arr.getInt(2) / (float) (arr.getInt(2) + arr.getInt(3))));
//        sendMessage(channel, service, CommonProto.getCommonVectorProto(Arrays.asList(arr.getInt(0), arr.getInt(1), arr.getInt(2), arr.getInt(3)), Arrays.asList(soloRate, teamRate)));


    }

    void topMonitor() {
        int type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        int sType = (int) CommonProto.parseCommonVector(srcRequest).getANumber(1);
        if (type == CfgTop.TYPE_USER) {
            topPlayer(sType);
        } else if (type == CfgTop.TYPE_CLAN) {
            topClan(sType);
        } else if (type == CfgTop.TYPE_EVENT) {
            topPlayer(sType);
        } else if (type == CfgTop.TYPE_SIEUTHU) {
            topPlayer(sType);
        } else if (type == CfgTop.TYPE_NHANVAT) {
            topPlayer(sType);
        }
    }

    GGProto.ProtoListClan protoListClan(List<ClanEntity> aClan) {
        GGProto.ProtoListClan.Builder builder = GGProto.ProtoListClan.newBuilder();
        int clanId = user.getDbUser().getClan();
        ClanEntity mclan = null;
        int mytop = -1;
        GGProto.ProtoClan.Builder protomClan = GGProto.ProtoClan.newBuilder();
        if (clanId > 0) {
            mclan = ClanMonitor.getClan(clanId);
            if (mclan != null) {
                mclan.setTrophy(mclan.getClanTrophy());
                protomClan.setId(mclan.getId());
                protomClan.setName(mclan.getName());
                protomClan.setMember(mclan.getMember());
                protomClan.setLevel(mclan.getLevel());
                protomClan.setAvatar(mclan.getAvatar());
                protomClan.addAllJoinRule(Arrays.asList(mclan.getJoinRule(), mclan.getJoinTrophy()));
                protomClan.setTrophy(mclan.getTrophy());
                builder.setMyClan(protomClan);
            }
        }


        for (int i = 0; i < aClan.size(); i++) {
            ClanEntity clan = aClan.get(i);
            GGProto.ProtoClan.Builder protoClan = GGProto.ProtoClan.newBuilder();
            protoClan.setId(clan.getId());
            protoClan.setName(clan.getName());
            protoClan.setMember(clan.getMember());
            protoClan.setLevel(clan.getLevel());
            protoClan.setAvatar(clan.getAvatar());
            protoClan.addAllJoinRule(Arrays.asList(clan.getJoinRule(), clan.getJoinTrophy()));
            protoClan.setTrophy(clan.getTrophy());
            builder.addAClan(protoClan);
            if (mclan != null && clan.getId() == mclan.getId()) {
                mytop = i + 1;
            }
        }
        builder.setMyClanRank(mytop);
        return builder.build();
    }

    void topClan(int type) {
        try {
            if (type < 0) {
                type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
            }
        } catch (Exception ex) {

        }
        if (type == 0) { // top level
            List<ClanEntity> aClan = Database2.getList("clan", Arrays.asList("server_id",String.valueOf(user.getDbUser().getServer())), "order by level desc, exp desc limit 0,100", ClanEntity.class);
            if (aClan == null) {
                addErrMessage();
                return;
            }
            addResponse(protoListClan(aClan));
        } else if (type == 2) { //  top chien tich

        } else if (type == 1) {// top trophy
            List<ClanEntity> lstclan = new ArrayList<>();
            lstclan.addAll(CfgNewMission.entryLstClanEntity(CfgNewMission.aGet, null,user.getDbUser().getServer()));
//            for (int i = 0; i <lstclan.size() ; i++) {
////                System.out.println("tp-->"+lstclan.get(i).getTrophy());
//            }
            if (lstclan != null) {
                addResponse(protoListClan(lstclan));
            }
        }

    }

    void topStatus() {
        List<GGProto.CommonVector> listCommon = new ArrayList<GGProto.CommonVector>();
        for (int i = 0; i < CfgTop.lstJobj.size(); i++) {
            JSONObject obj = CfgTop.lstJobj.get(i);
            try {
                if (obj.getInt("enable") == 0) {
                    continue;
                }
                int id = obj.getInt("id");
                int type = obj.getInt("type");
                String name = obj.getString("name");
                JSONArray arr = obj.getJSONArray("colum");

                List<Long> vLong = new ArrayList<>();
                List<String> vString = new ArrayList<>();
                vLong.add((long) id);
                vLong.add((long) type);
                vString.add(name);
                for (int j = 0; j < arr.size(); j++) {
                    JSONObject sobj = arr.getJSONObject(j);
                    int cId = sobj.getInt("cid");
                    String cname = sobj.getString("cname");
                    vLong.add((long) cId);
                    vString.add(cname);
                }
                listCommon.add(CommonProto.getCommonLongVectorProto(vLong, vString));
//                System.out.println("vlong---<>"+vLong);
//                System.out.println("vString---<>"+vString);
            } catch (Exception ex) {
                ex.printStackTrace();
                continue;
            }
        }
        addResponse(CommonProto.getListCommonLongVectorProto(listCommon));
    }

    void topPlayer(int type) {
        int numberPet = 11;
        int numberHero = 6;
//        System.out.println("type----><>"+type);
        try {
            if (type < 0) {
                type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
            }
        } catch (Exception ex) {
        }
        GGProto.ProtoTopPlayer.Builder builder = GGProto.ProtoTopPlayer.newBuilder();
        builder.setType(type);
        builder.setNextReset(-1);
//        System.out.println("user.getDbUser().getServer()--->"+user.getDbUser().getServer());
        System.out.println(MainCache.getInstance().getAUserTrophy100().size());
        List<UserEntity> luserTrophy = new ArrayList<>(MainCache.getInstance().getAUserTrophy100().get(user.getDbUser().getServer()));
        //luserTrophy.addAll(CfgNewMission.entryLstUserTrophy(CfgNewMission.aGet, null));
        if (type == 0) {//trophy
            builder.setNextReset(TopTrophy.getInstance().nextSession());
            for (int i = 0; i < Math.min(luserTrophy.size(), 100); i++) {
                builder.addAUser(CommonProto.protoGetTopUser(luserTrophy.get(i), luserTrophy.get(i).getTrophy(), luserTrophy, true));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luserTrophy), (int) user.getDbUser().getTrophy()));

        } else if (type == 1) {//lv
            List<UserEntity> luser = CfgNewMission.entryLstUserLevel(CfgNewMission.aGet, null,user.getDbUser().getServer());
            for (int i = 0; i < Math.min(100, luser.size()); i++) {

                builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getTrophy(), luserTrophy, false));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luser), (int) user.getDbUser().getLevel()));
        } else if (type == 2) {//star
            List<UserEntity> luser = CfgNewMission.entryLstUserStar(CfgNewMission.aGet, null,user.getDbUser().getServer());
            for (int i = 0; i < Math.min(100, luser.size()); i++) {
                builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getStarMap(), luserTrophy, false));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luser), (int) user.getDbUser().getStarMap()));
        } else if (type == 3) {//vip
            List<UserEntity> luser = CfgNewMission.entryLstUserVip(CfgNewMission.aGet, null,user.getDbUser().getServer());
            for (int i = 0; i < Math.min(100, luser.size()); i++) {
                luser.get(i).setVip(CfgVip.getVip(luser.get(i).getGemNap()));
                builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getVip(), luserTrophy, false));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luser), (int) user.getDbUser().getVip()));
        } else if (type == 4) {//win
            List<UserEntity> luser = CfgNewMission.entryLstUserWin(CfgNewMission.aGet, null,user.getDbUser().getServer());
            for (int i = 0; i < Math.min(100, luser.size()); i++) {
                builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getWin(), luserTrophy, false));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luser), (int) user.getDbUser().getWin()));
        } else if (type == 5) {// Lien thang
            builder.setNextReset(TopWin.getInstance().nextSession());
            List<UserEntity> luser = new ArrayList<>(MainCache.getInstance().getAUserWin100().get(user.getDbUser().getServer()));
            for (int i = 0; i < Math.min(100, luser.size()); i++) {
                builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getMaxwin(), luserTrophy, false));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luser), (int) user.getDbUser().getMaxwin()));
        } else if (type == 6) {// Zombie
            builder.setNextReset(TopEventLoop.getInstance(Constans.EVENT_ZOMBIE).nextSession());
            List<UserEntity> luser = new ArrayList<>();
            try {
                luser = new ArrayList<>(MainCache.getInstance().getATopEventLoop().get(user.getDbUser().getServer()).get(Constans.EVENT_ZOMBIE));
            } catch (Exception ex) {

            }
            for (int i = 0; i < Math.min(100, luser.size()); i++) {
                builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getMaxwin(), luserTrophy, false));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luser), (int) user.getUEvent().getEvent(Constans.EVENT_ZOMBIE).getEvent().getValue()));
        } else if (type >= Constans.EVENT_PET && type < Constans.EVENT_PET + numberPet) {// PET
            builder.setNextReset(TopEventLoop.getInstance(type).nextSession());
            List<UserEntity> luser = new ArrayList<>();
            try {
                luser = new ArrayList<>(MainCache.getInstance().getATopEventLoop().get(user.getDbUser().getServer()).get(type));
            } catch (Exception ex) {

            }
            for (int i = 0; i < Math.min(100, luser.size()); i++) {
                builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getMaxwin(), luserTrophy, false));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luser), (int) user.getUEvent().getEvent(type).getEvent().getValue()));
        } else if (type==Constans.EVENT_TOP_CAP_DOI){
            builder.setNextReset(TopEventLoop.getInstance(type).nextSession());
            List<UserEntity> luser = new ArrayList<>();
            List<UserEntity> luser2 = new ArrayList<>();

            try {
                luser = new ArrayList<>(MainCache.getInstance().getATopEventLoop().get(user.getDbUser().getServer()).get(type));

                for (int i = 0; i <luser.size() ; i++) {
                    UserEntity us = luser.get(i);
                    UserFriendEntity uf = new UserDAO().dbHasFamily(us.getId());
                    UserEntity u2 = new UserDAO().getUserEntity(uf.getUser2());
                    if(u2!=null) {
                        luser2.add(u2);
                        if(!us.getName().contains(" ♥ "+u2.getName())) {
                        us.setName(us.getName() + " ♥ " + u2.getName());
                        }
                    }else {
                        luser2.add(new UserEntity());
//                        continue;
                    }
                }
            } catch (Exception ex) {

            }
            for (int i = 0; i < Math.min(100, luser.size()); i++) {
                try {
//                System.out.println("luser.get(i).getMaxwin()------>"+luser.get(i).getMaxwin());
                    builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getMaxwin(), luserTrophy, false));
                }catch (Exception ex){

                }
            }
//            System.out.println("user.getUEvent().getEvent(type).getEvent().getValue()---->"+user.getUEvent().getEvent(type).getEvent().getValue());
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getDbUser().getKeyCapdoi(), luser), (int) user.getDbUser().getDiemcapdoi()));

        }else if (type >= Constans.EVENT_CHARACTER_LOOP && type < Constans.EVENT_CHARACTER_LOOP + numberHero) {// thong thao
//            System.out.println("thong thao--->"+type);
            builder.setNextReset(TopEventLoop.getInstance(type).nextSession());
            List<UserEntity> luser = new ArrayList<>();
            try {
                luser = new ArrayList<>(MainCache.getInstance().getATopEventLoop().get(user.getDbUser().getServer()).get(type));
            } catch (Exception ex) {

            }
            for (int i = 0; i < Math.min(100, luser.size()); i++) {
                builder.addAUser(CommonProto.protoGetTopUser(luser.get(i), luser.get(i).getMaxwin(), luserTrophy, false));
            }
            builder.addAllMyRank(Arrays.asList(CfgNewMission.TopMyRank(user.getId(), luser), (int) user.getUEvent().getEvent(type).getEvent().getValue()));
        }
        addResponse(builder.build());
    }

    //<editor-fold desc="Database Access">
    FeedbackEntity getFeedback(long userId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            FeedbackEntity feedback = (FeedbackEntity) session.createSQLQuery("select * from feedback where user_id=" + userId).addEntity(FeedbackEntity.class).uniqueResult();
            if (feedback == null) {
                session.beginTransaction();
                feedback = new FeedbackEntity(userId);
                session.save(feedback);
                session.getTransaction().commit();
            }
            return feedback;
        } catch (HibernateException he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }

    boolean saveFeedback(FeedbackEntity feedback, String msg) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            JSONArray arr = JSONArray.fromObject(feedback.getMessage());
            arr.add(JSONObject.fromObject(new Gson().toJson(feedback.newChat(msg))));
            while (arr.size() > 30) {
                arr.remove(0);
            }
            feedback.setMessage(arr.toString());
            feedback.setHasNew(true);
            session.beginTransaction();
            session.update(feedback);
            session.getTransaction().commit();
            return true;
        } catch (Exception he) {
            String error = Util.exToString(he);
            if (error.contains("Incorrect string value")) Logs.error(error, false,user.getDbUser().getServer());
            else Logs.error(error);
        } finally {
            closeSession(session);
        }
        return false;
    }
    //</editor-fold>

    //region Logic
    //endregion
}
