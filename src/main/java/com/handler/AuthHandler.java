package com.handler;

import com.bem.AbstractTable;
import com.bem.boom.CacheBattle;
import com.bem.boom.Resources;
import com.bem.boom.object.CachePlayer;
import com.bem.boom.object.MyAvatar;
import com.bem.config.*;
import com.bem.config.lang.Lang;
import com.bem.dao.AuthDAO;
import com.bem.dao.SystemDAO;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.*;
import com.bem.matcher.TableMonitor;
import com.bem.monitor.*;
import com.bem.object.UserInfo;
import com.bem.util.Actions;
import com.bem.util.ChUtil;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.MCache;
import com.cache.MainCache;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import com.proto.GGProto.CommonVector;
import com.proto.GGProto.ProtoLoginResponse;
import grep.database.Database2;
import grep.database.HibernateUtil;
import grep.helper.Filer;
import grep.helper.StringHelper;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class AuthHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        List<Integer> actions = Arrays.asList(LOGIN_FACEBOOK, LOGIN_QUICK, LOGIN_SESSION, ACCOUNT_CONNECT_FACEBOOK, ACCOUNT_CONNECT, LOG_OUT, CHOOSE_FIRST_AVATAR,
                LOGIN_NOTIFY, LOGIN_BOT, REGISTER, UPDATE_INFORMATION, CHANGE_PASSWORD, JOIN_BATTLE, BOT_JOIN_BATTLE, LOGIN_BOT_OK, LANGUAGE_IN_GAME);
        actions.forEach(action -> mGameHandler.put(action, getInstance()));
    }

    static AuthHandler instance;

    public static AuthHandler getInstance() {
        if (instance == null) {
            instance = new AuthHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new AuthHandler();
    }

    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case LOGIN_SESSION:
                    loginSession();
                    break;
                case REGISTER:
                    register();
                    break;
                case UPDATE_INFORMATION:
                    updateInformation();
                    break;
                case CHANGE_PASSWORD:
                    changePassword();
                    break;
                case LOGIN_QUICK:
                    loginQuick(CommonProto.parseCommonVector(srcRequest));
                    break;
                case LOGIN_BOT:
                    loginBot();
                    break;
                case LOGIN_BOT_OK:
                    loginBotOk();
                    break;
                case LOGIN_FACEBOOK:
                    loginFacebook();
                    break;
                case ACCOUNT_CONNECT_FACEBOOK:
                    accountConnectFB();
                    break;
                case ACCOUNT_CONNECT:
                    accountConnect();
                    break;
                case LOG_OUT:
                    logOut();
                    break;
                case CHOOSE_FIRST_AVATAR:
                    chooseFirstAvatar();
                    break;
                case LOGIN_NOTIFY:
                    loginNotify(user);
                    break;
                case LOGIN_INFORM:
                    loginInform(user);
                    break;
                case JOIN_BATTLE:
                    joinBattle();
                    break;
                case BOT_JOIN_BATTLE:
                    botJoinBattle();
                    break;
                case LANGUAGE_IN_GAME:
                    changeLanguage();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void botJoinBattle() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String username = cmm.getAString(0);
        String battleKey = cmm.getAString(1);
        CacheBattle cBattle = (CacheBattle) MCache.getInstance().getNormal(battleKey);
        if (cBattle == null) {
            addResponse(BOT_JOIN_BATTLE_FAIL, null);
            getLogger().warn("battle key -> " + username + " " + battleKey);
            return;
        }
        boolean inTeam = false;
        for (CachePlayer player : cBattle.getAPlayer()) {
            if (player.getUsername().equals(username)) {
                inTeam = true;
                break;
            }
        }
        if (!inTeam) {
            addResponse(BOT_JOIN_BATTLE_FAIL, null);
            getLogger().warn("battle key 1 -> " + username + " " + battleKey);
            return;
        }
        AbstractTable table = TableMonitor.getTable(battleKey, cBattle);
        if (table.doAction(channel, JOIN_BATTLE, CommonProto.getCommonLongVectorProto(null, Arrays.asList(username)).toByteArray(), System.currentTimeMillis()) == 0) {
            // fail roi lam gi day
            addResponse(BOT_JOIN_BATTLE_FAIL, null);
        } else {
        }
    }

    void joinBattle() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String session = cmm.getAString(0);
        String username = cmm.getAString(1);
        String battleKey = cmm.getAString(2);
        String cacheSession = (String) MCache.getInstance().getNormal("s:" + username);

        System.out.println("battleKey = " + battleKey);

        //        int severid =1;
        //        if (session == null || cacheSession == null) {
        //            addResponse(LOGIN_FAIL, CommonProto.getErrorMsg("Chưa đăng nhập"));
        //            getLogger().warn("not login -> " + username + " " + session);
        //            return;
        //        }
        //        if (!cacheSession.equals(session)) {
        //            addResponse(LOGIN_FAIL, CommonProto.getErrorMsg("Đăng nhập không hợp lệ"));
        //            getLogger().warn("login fail -> " + username + " " + session);
        //            return;
        //        }
        CacheBattle cBattle = (CacheBattle) MCache.getInstance().getNormal(battleKey);
        if (cBattle == null) {
            addErrMessage("Trận đấu không hợp lệ");
            addResponse(BOT_JOIN_BATTLE_FAIL, null);
            getLogger().warn("battle key -> " + username + " " + battleKey);
            return;
        }
        boolean inTeam = false;
        for (CachePlayer player : cBattle.getAPlayer()) {
            if (CfgCluster.getRealUsername(player.getUsername()).equals(username)) {
                username = player.getUsername();
                inTeam = true;
                break;
            }
        }
        if (!inTeam) {
            addErrMessage("Mã trận đấu không hợp lệ");
            addResponse(BOT_JOIN_BATTLE_FAIL, null);
            getLogger().warn("battle key 1 -> " + username + " " + battleKey);
            return;
        }
        AbstractTable table = TableMonitor.getTable(battleKey, cBattle);
        if (table == null) {
            System.out.println("table null");
        } else {
            System.out.println("table != null");
        }
        if (table.doAction(channel, JOIN_BATTLE, CommonProto.getCommonLongVectorProto(null, Arrays.asList(username)).toByteArray(), System.currentTimeMillis()) == 0) {
            // fail roi lam gi day
            addErrMessage("Kết nối trận đấu thất bại");
            getLogger().warn("table fail -> " + username);
            System.out.println("loi xay ra trong tran danh");
            addResponse(BOT_JOIN_BATTLE_FAIL, null);
        } else {
            System.out.println("aaaaaaaa");
        }
    }

    void changePassword() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String newPass = cmm.getAString(0);
        String hpass = Util.getHPass(newPass);

        if (!Database2.update(CfgCommon.mainDb + "auth_user", Arrays.asList("hpass", hpass), Arrays.asList("id", String.valueOf(user.getId())))) {
            addErrMessage();
            return;
        }

        addPopupMessage(getLang(Lang.common_success));
    }

    void updateInformation() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String hoten = cmm.getAString(0);
        String cmnd = cmm.getAString(1);
        String ngaysinh = cmm.getAString(2);
        String email = cmm.getAString(3);
        String mobile = cmm.getAString(4);
        JSONArray arr = JSONArray.fromObject(String.format("[%s,%s,%s]", hoten, cmnd, ngaysinh));
        if (!Database2.update(CfgCommon.mainDb + "auth_user", Arrays.asList("mobile", mobile, "email", email, "info", arr.toString()), Arrays.asList("id", String.valueOf(user.getId())))) {
            addErrMessage();
            return;
        }
        addPopupMessage(getLang(Lang.common_success));
    }

    void register() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String username = cmm.getAString(0); // salt / md5(salt + udid + name)
        String password = cmm.getAString(1);
        String mobile = cmm.getAString(2);
        String udid = cmm.getAString(3);
        String cp = cmm.getAString(4);
        String subcp = cmm.getAString(5);
        String os = cmm.getAString(6);
        String osVersion = cmm.getAString(7);
        String clientVersion = cmm.getAString(8);

        if (udid.length() < 32) {
            addErrMessage();
            return;
        }

        // check username
        if (!Util.isValidUsername(username) || username.length() < 6) {
            addErrMessage("Tên đăng nhập không được chứa ký tự đặc biệt và tối thiểu 6 ký tự");
            return;
        }

        // check password
        if (!Util.isValidPassword(password) || password.length() < 4) {
            addErrMessage("Mật khẩu không được chứa ký tự đặc biệt và tối thiểu 4 ký tự");
            return;
        }

        AuthDAO aDAO = new AuthDAO();
        if (aDAO.existUsername(username)) {
            addErrMessage("Tài khoản này đã được người khác đăng ký. Hãy chọn tài khoản khác");
            return;
        }

        if (aDAO.register(username, Util.getHPass(password), udid, mobile, cp, subcp, os, osVersion, clientVersion) != null) {
            addResponse(service, CommonProto.getCommonLongVectorProto(null, Arrays.asList("Đăng ký thành công")));
        } else {
            addErrMessage();
        }
    }

    void loginNotify(UserInfo user) {
        addResponse(LOGIN_NOTIFY, CommonProto.getCommonLongVectorProto(CfgNotify.loginNotify(user), null));
    }

    void loginInform(UserInfo user) {
        if (CfgServer.isSubmit()) {
            return;
        }
        var values = MainCache.getInstance().getAMessage().get(user.getDbUser().getServer());
        List<SystemMessageEntity> aMessage = values == null ? new ArrayList<>() : new ArrayList<>(values);
        try {
            for (int i = 0; i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {
                if (CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).getStatus() == 1 && CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).inTime()) {
                    SystemMessageEntity ms = new SystemMessageEntity();
                    ms.setServer(user.getDbUser().getServer());
                    ms.setId(1000 + i);
                    ms.setEnable(1);
                    if (CfgServer.isVNServer()) {
                        ms.setTitle("Sự kiện: " + CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).getName());
                        ms.setContent("Chào mừng các bạn tham gia sự kiện " + CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).getName() + "\n chi tiết xem tại mục sự kiện!");
                    } else {
                        ms.setTitle("Event: " + CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).getName());
                        ms.setContent("Event " + CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).getName() + "\n more information in menu Event!");
                    }
                    ms.setActionType(0);
                    ms.setType(0);

                    Calendar c = Calendar.getInstance();
                    c.add(Calendar.DATE, -1);  // number of days to add
                    ms.setDateBegin(c.getTime());
                    ms.setDateCreated(c.getTime());
                    c = Calendar.getInstance();
                    c.add(Calendar.DATE, 1);  // number of days to add
                    ms.setDateEnd(c.getTime());
                    ms.setVersion("");
                    ms.setOs("");
                    ms.setCp("");
                    ms.setCp("");
                    ms.setActionData(new String());
                    aMessage.add(ms);
                }
            }
        } catch (Exception ex) {

        }
        Collections.shuffle(aMessage);
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        for (SystemMessageEntity message : aMessage) {
            if (message.getEnable() == 1 && message.getDateBegin().getTime() < System.currentTimeMillis() && System.currentTimeMillis() < message.getDateEnd().getTime()) {
                if (message.getVersion().equals("") || message.getVersion().equals(user.getVersion())) {
                    if (message.getOs().equals("") || message.getOs().equals(user.getOs())) {
                        if (message.getCp().equals("") || message.getCp().equals(user.getAuthUser().getCp())) {
                            builder.addAVector(message.toProto());
                        }
                    }
                }
            }
        }
        addResponse(LOGIN_INFORM, builder.build());
    }

    void functionSetting(UserInfo user) {
        List<Long> aLong = new ArrayList<>();
        List<String> aStr = new ArrayList<>();
        // rank
        aLong.add(1L);
        aLong.add(1L);
        // time
        JSONObject obj = new JSONObject();
        if (CfgServer.isVNServer()) {
            obj.put("desc", "8h - 24h hàng ngày");
            obj.put("time", JSONArray.fromObject("[28800, 86400]"));//
        } else {
            obj.put("desc", "");
            obj.put("time", JSONArray.fromObject("[0, 86400]"));//
        }
        aStr.add(obj.toString());
        //
        addResponse(SETTING_FUNCTION, CommonProto.getCommonLongVectorProto(aLong, aStr));
    }

    void functionSettingNew(UserInfo user) {
        List<Long> aLong = new ArrayList<>();
        List<String> aStr = new ArrayList<>();
        if (CfgServer.isVNServer()) {
            // rank
            aLong.add(1L);
            aLong.add(1L);
            // time
            JSONObject obj = new JSONObject();
            obj.put("desc", "0h - 2h và 8h - 24h hàng ngày");

            JSONArray arr = new JSONArray();
            JSONObject obj1 = new JSONObject();
            JSONObject obj2 = new JSONObject();
            obj2.put("time", JSONArray.fromObject("[0, 3600]"));//
            obj1.put("time", JSONArray.fromObject("[28800, 86400]"));//
            arr.add(obj1);
            arr.add(obj2);

            obj.put("lsttime", arr);
            aStr.add(obj.toString());
        } else {
            // rank
            aLong.add(1L);
            // time
            JSONObject obj = new JSONObject();
            obj.put("desc", "");

            JSONArray arr = new JSONArray();
            JSONObject obj1 = new JSONObject();
            obj1.put("time", JSONArray.fromObject("[0, 86400]"));//
            arr.add(obj1);

            obj.put("lsttime", arr);
            aStr.add(obj.toString());
        }
        //
        addResponse(SETTING_FUNCTION_NEW, CommonProto.getCommonLongVectorProto(aLong, aStr));
    }

    //    void functionSettingNew(UserInfo user) {
    //        List<Long> aLong = new ArrayList<>();
    //        List<String> aStr = new ArrayList<>();
    //        // rank
    //        aLong.add(1L);
    //        aLong.add(1L);
    //        // time
    //        JSONObject obj = new JSONObject();
    //        try {
    //            int hourOfday = Integer.parseInt(Util.HourFormatTime(new Date()));
    //            if ((hourOfday >= 0 && hourOfday <= 2) || (hourOfday >= 8 && hourOfday < 24)) {
    //                obj.put("desc", "<color='#02DE45'>0h - 2h và 8h - 24h hàng ngày</color>");//mo
    //            } else {
    //                obj.put("desc", "<color='#FF2323'>0h - 2h và 8h - 24h hàng ngày</color>");//dong
    //            }
    ////            obj.put("desc", "<color='#FF2323'>0h - 2h và 8h - 24h hàng ngày</color>");//dong
    //            obj.put("time", JSONArray.fromObject("[28800, 86400]"));//
    //            aStr.add(obj.toString());
    //        } catch (Exception ex) {
    //            obj.put("desc", "<color='#FF2323'>0h - 2h và 8h - 24h hàng ngày</color>");//dong
    //            obj.put("time", JSONArray.fromObject("[28800, 86400]"));//
    //        }
    //        //
    //        addResponse(SETTING_FUNCTION, CommonProto.getCommonLongVectorProto(aLong, aStr));
    //    }

    void chooseFirstAvatar() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        int heroId = (int) cmm.getANumber(0);
        String name = StringHelper.removeNonePrintChars(cmm.getAString(0));

        if (heroId != 1 && heroId != 3) {
            addErrMessage("Nhân vật không hợp lệ");
            return;
        }
        if (user.getDbUser().getMAvatar().getHero() != 0) {
            addErrMessage("Bạn đã chọn nhân vật đầu tiên rôi");
            return;
        }
        if (name.length() < 3 || name.length() > 15) {
            addErrMessage("Tên không hợp lệ. Tối thiểu 3 ký tự, tối đa 15 ký tự");
            return;
        }
        if (existName(name)) {
            addErrMessage("Tên nhân vật đã tồn tại");
            return;
        }
        //

        user.getDbUser().getMAvatar().setAvatar(MyAvatar.HERO, heroId);
        //        int iconId = Resources.aIcon.get(new Random().nextInt(Resources.aIcon.size())).getId();
        //        int iconId = Resources.aIcon.get(new Random().nextInt(Resources.aIcon.size())).getId();
        int iconId = new Random().nextInt(5) + 1;
        //        System.out.println("iconId--->"+iconId);
        if (dbFirstUpdate(user, name, heroId, iconId)) {
            user.getDbUser().setName(name);
            addResponse(service, CommonProto.getCommonVectorProto(user.getDbUser().getMAvatar().toList(), Arrays.asList(user.getDbUser().getName())));
            // Tutorial
            //            UserInfo fakeUser = new UserInfo();
            //            UserEntity userEntity = new UserEntity();
            //            userEntity.setName("Tutorial Admin");
            //            userEntity.setAvatar(newUser.toList().toString());
            //            userEntity.setBattleItem("[]");
            //            fakeUser.setDbUser(userEntity);
            //
            //            TeamObject team = new TeamObject(TeamObject.TRAIN, newUser);
            //            ChUtil.set(channel, Constans.KEY_USER_TEAM, team);
            //            team.addUser(fakeUser);
            //            new TableTutorial(Arrays.asList(team), TeamObject.TUTORIAL, 2005);
        } else {
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.HERO, 0);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.BOMB_ID, 0);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.BOMB_IMAGE, 0);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.CLOTHES, 0);
            user.getMAvatar().setAvatar(MyAvatar.USER_AVATAR, 0);
            user.getMAvatar().setAvatar(MyAvatar.ACCESS_ID1, 0);
            user.getMAvatar().setAvatar(MyAvatar.ACCESS_IMAGE1, 0);
            addErrMessage();
        }
    }

    void logOut() {
        Online.logoutChannel(channel);
        addResponse(CommonProto.getCommonLongVectorProto(null, Arrays.asList("")));
    }

    void accountConnectFB() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String fbId = cmm.getAString(0);
        String token = cmm.getAString(1);

        int count = hasAccountFacebook(fbId);
        if (count == -1) {
            addErrMessage();
            return;
        }
        if (count > 0) {
            addErrMessage("Tài khoản facebook này đã được sử dụng");
            return;
        }

        String result = Filer.facebookLogin(fbId, token);
        if (result.startsWith("1")) {
            addErrMessage(result);
            return;
        }
        if (Database2.update(CfgCommon.mainDb + "auth_user", Arrays.asList("facebook", fbId), Arrays.asList("id", String.valueOf(user.getId())))) {
            Database2.update("user", Arrays.asList("facebook", fbId), Arrays.asList("id", String.valueOf(user.getId())));
            addResponse(service, null);
        } else {
            addErrMessage(getLang(Lang.err_system_down));
        }
    }

    void accountConnect() {
        List<String> aStr = CommonProto.parseCommonVector(srcRequest).getAStringList();
        String username = aStr.get(0);
        String pass = aStr.get(1);

        // check username
        if (!Util.isValidUsername(username) || username.length() < 6) {
            addErrMessage("Tên đăng nhập không được chứa ký tự đặc biệt và tối thiểu 6 ký tự");
            return;
        }

        if (!Util.isValidPassword(pass) || pass.length() < 4) {
            addErrMessage("Mật khẩu không được chứa ký tự đặc biệt và tối thiểu 4 ký tự");
            return;
        }

        AuthDAO aDAO = new AuthDAO();
        if (aDAO.existUsername(username)) {
            addErrMessage("Tài khoản này đã được người khác đăng ký. Hãy chọn tài khoản khác");
            return;
        }

        String hpass = Util.getHPass(pass);
        if (!aDAO.connectAccount(user, hpass)) {
            addErrMessage();
            return;
        }
        addResponse(service, null);
    }

    void loginUsername() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String checksum = cmm.getAString(0); // salt / md5(salt + email + md5pass)
        String username = cmm.getAString(1);
        String md5Pass = cmm.getAString(2);
        String udid = cmm.getAString(3);
        String cp = cmm.getAString(4);
        String subcp = cmm.getAString(5);
        String os = cmm.getAString(6);
        String osVersion = cmm.getAString(7);
        String clientVersion = cmm.getAString(8);

        if (!validChecksum(checksum, username + md5Pass)) {
            addErrMessage("Invalid request");
            return;
        }

        AuthUserEntity authUser = (AuthUserEntity) Database2.getUnique("auth_user", Arrays.asList("username", username), AuthUserEntity.class);
        if (authUser == null || !validAuth(authUser.getHpass(), md5Pass)) {
            addErrMessage("Tên đăng nhập hoặc mật khẩu không chính xác");
            return;
        }
        loginResponseOld(channel, service, authUser);
    }

    void loginFacebook() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String checksum = cmm.getAString(0); // salt / md5(salt + fbId + token)
        String fbId = cmm.getAString(1);
        String token = cmm.getAString(2);
        String udid = cmm.getAString(3);
        String cp = cmm.getAString(4);
        String subcp = cmm.getAString(5);
        String os = cmm.getAString(6);
        String osVersion = cmm.getAString(7);
        String clientVersion = cmm.getAString(8);

        if (!validChecksum(checksum, fbId + token)) {
            addErrMessage("Invalid request");
            return;
        }

        String result = Filer.facebookLogin(fbId, token);
        if (result.startsWith("1")) {
            addErrMessage(result);
            return;
        }
        JSONObject obj = JSONObject.fromObject(result);
        String fName = obj.getString("name");
        //
        AuthUserEntity authUser = dbAuthUserByFacebook(fbId);
        if (authUser == null) {
            addErrMessage();
        } else {
            if (authUser.getId() == -1) { // new newUser
                authUser = new UserDAO().saveAuthUser(fName, fbId, udid, cp, subcp, os, osVersion, clientVersion);
                if (authUser == null) {
                    addErrMessage();
                    return;
                }
            }
            loginResponseOld(channel, service, authUser);
        }
    }

    void loginBotOk() {
        System.out.println("loginBotOk");
        BotMonitor.addBot(channel, user);
        addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId()), null));
    }

    void loginBot() {
        System.out.println("loginBot");
        if (!CfgBot.isOkIp(channel)) {
            return;
        }
        CommonVector.Builder cmm = CommonProto.parseCommonVector(srcRequest).toBuilder();
        ChUtil.set(channel, Constans.KEY_BOT_UDID, cmm.getAString(1));
        cmm.setAString(1, CfgBot.config.udid);
        loginQuick(cmm.build());
    }

    void loginQuick(CommonVector cmm) {
        System.out.println("loginQuick");
        String checksum = cmm.getAString(0); // salt / md5(salt + udid + name)
        String udid = cmm.getAString(1);
        String name = cmm.getAString(2);
        String cp = cmm.getAString(3);
        String subcp = cmm.getAString(4);
        String os = cmm.getAString(5);
        String osVersion = cmm.getAString(6);
        String clientVersion = cmm.getAString(7);

        boolean isVN = true;
        int serverId = 1;
        if (cmm.getANumberCount() > 0) isVN = cmm.getANumber(0) != 1;
        if (cmm.getANumberCount() > 1) serverId = (int) cmm.getANumber(1);

        String langCode = cmm.getAStringCount() >= 9 ? cmm.getAString(8) : "en";
        //        JCache.getInstance().setValue("LANGUAGE:" + user.getId(), langCode);
        if (ChUtil.get(channel, Constans.KEY_BOT_UDID) == null && !validChecksum(checksum, udid + name)) {
            addErrMessage("Invalid request");
            return;
        }
        //
        AuthDAO dao = new AuthDAO();
        AuthUserEntity authUser = dao.getUserByUsername("mashi", serverId);//dao.getUserByUdid(udid);
        if (authUser == null) {
            addErrMessage();
            return;
        }
        if (authUser.getId() == -1) { // new newUser
            authUser = dao.saveAuthUser(name, "", udid, cp, subcp, os, osVersion, clientVersion);
            if (authUser == null) {
                addErrMessage();
                return;
            }
        }
        authUser.setOs(os);
        authUser.setVersion(clientVersion);
        authUser.setOsVersion(osVersion);
        authUser.setCp(cp);
        loginResponseOld(channel, LOGIN_QUICK, authUser);
    }

    void loginSession() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String session = cmm.getAString(0);
        String username = cmm.getAString(1);
        String version = cmm.getAString(2);
        String osType = cmm.getAString(3).toLowerCase();
        String cp = cmm.getAString(4);
        String deviceType = cmm.getAStringCount() > 5 ? cmm.getAString(5) : "";
        String udid = cmm.getAStringCount() > 6 ? cmm.getAString(6) : "";
        String langCode = cmm.getAStringCount() > 7 ? cmm.getAString(7) : CfgServer.config.mainLanguage;
        boolean isVN = true;
        int serverId = 1;
        if (cmm.getANumberCount() > 0) isVN = cmm.getANumber(0) != 1;
        if (cmm.getANumberCount() > 1) serverId = (int) cmm.getANumber(1);
        Lang lang = Lang.instance(langCode);

        AuthUserSession userSession = new AuthDAO().getUserSession(username);
        if (session == null || userSession == null) {
            addResponse(LOGIN_FAIL, CommonProto.getErrorMsg(getLang(Lang.login_session_not_exist)));
            return;
        }
        if (!userSession.getSessionId().equals(session)) {
            addResponse(LOGIN_FAIL, CommonProto.getErrorMsg(getLang(Lang.login_session_wrong)));
            return;
        }
        loginResponse(channel, session, username, version, osType, deviceType, udid, cp, lang, isVN, serverId);
    }

    void loginResponse(Channel channel, String session, String username, String version, String osType, String deviceType, String udid, String cp, Lang lang, boolean isVN, int serverId) {
        ChUtil.set(channel, "keydebug", version + " " + osType + " " + cp + " " + username);
        if (CfgServer.isTest()) {
            if (CfgAccount.config.debugAccount.containsKey(username)) {
                username = CfgAccount.config.debugAccount.get(username);
            }
            //            if(!username.contains("leanhvu")&&!username.contains("buavao")&&!username.contains("nhdang")){
            //                return;
            //            }
            if (username.contains("gvt")) {
                return;
            }
        }
        AuthDAO aDAO = new AuthDAO();
        AuthUserEntity authUser = aDAO.getUserByUsername(username, serverId);
        if (authUser.getId() == -1) {
            addErrMessage(lang.get(Lang.login_account_not_exist));
            return;
        }
        UserEntity uEntity = aDAO.getLoginUserEntity(authUser, cp, serverId);
        if (uEntity == null) {
            addErrMessage();
            return;
        }
        UserInfo user = new UserInfo(channel, authUser, uEntity);
        if (!user.loadInventory()) {
            addErrMessage();
            return;
        }
        Channel ch = CfgBot.isBot(authUser.getUdid()) ? null : Online.getChannel(user.getId());
        if (ch != null) {
            Online.logoutChannel(ch);
            if (!ch.id().equals(channel.id())) {
                sendMessage(ch, DUPLICATE_LOGIN, null);
            }
        }

        ChUtil.set(channel, Constans.KEY_USER, user);
        Online.addChannel(user.getId(), channel);

        //        UserEventEntity uEvent = (UserEventEntity) Database.getUnique("user_event", Arrays.asList("user_id", String.valueOf(uEntity.getId()),
        //                "event_id", String.valueOf(Constans.EVENT_TOP_WIN), "event_index", String.valueOf(0)), UserEventEntity.class);
        // //        user.getUData().getUInt().addValue(UserInt.NUMBER_WIN, UserInt.TYPE_SET,(uEvent== null ? 0 : (int)uEvent.getValue()));
        //        user.getDbUser().setMaxwin((uEvent== null ? 0 : (int)uEvent.getValue()));
        //        String tableId = JCache.getInstance().getValue(Constans.KEY_RESUME);
        //        if (TaskMonitor.joinTable(tableId, channel) <= 0) {
        //        user.setIpVN(CfgIp.isIpVN(Util.getUserIPAdress(channel)));
        user.setVN(CfgIp.isIpVN(Util.getUserIPAdress(channel)) ? true : isVN);
        user.setOs(osType);
        user.setCp(cp);
        user.setVersion(version);
        user.setLang(lang);
        //
        loginNotify(user);
        loginInform(user);
        functionSetting(user);
        functionSettingNew(user);
        user.getMAvatar().checkUfo(user.getId());
        //        user.loadEvent();
        BattleUserEntity.getInstance().getBattleBonus(user);
        user.syncMoneys(this);
        ProtoLoginResponse.Builder builder = ProtoLoginResponse.newBuilder();
        builder.setUser(CommonProto.protoUser(uEntity, CfgEnergy.getMyEnergy(user), user.getUData()));
        addResponse(LOGIN_SESSION, builder.build());
        user.setSession(session);
        UserMonitor.saveUser(user);
        if (user.getDbUser().getClan() > 0) {
            ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
            clan.updateUserEntity(user.getDbUser());
        }
        JSONArray arr = new JSONArray();
        try {
            if (!user.getRes().getMUfo().containsKey(Integer.valueOf(1))) {
                arr.addAll(Arrays.asList(Bonus.UFO, 1));
                Bonus.receiveListItem(user, arr, "add first UFO");
            }
        } catch (Exception ex) {
        }
        Actions.save(user.getDbUser(), Actions.GUSER, Actions.DLOGIN, Actions.convertToLogString(Arrays.asList("u", username, "ip", Util.getUserIPAdress(channel), "cp", cp, "version",
                version, "os", osType, "device", deviceType, "udid", udid, "vn", isVN ? "1" : "0")));
        checkGroupUdid(user, udid);
        new Thread(() -> checkSystemMail(user.getId())).start();
    }

    void checkSystemMail(long userId) {
        SystemDAO systemDAO = new SystemDAO();
        UserDAO userDAO = new UserDAO();
        List<SystemSendMessage> messages = systemDAO.getListSystemMessage();
        for (SystemSendMessage message : messages) {
            UserReceiveMessage userReceiveMessage = userDAO.getUserReceiveMessage(userId, message.getId());
            if (userReceiveMessage == null) {
                if (userDAO.addUserReceiveMessage(userId, message.getId())) {
                    systemDAO.sendMail(userId, message.getTitle(), message.getMessage(), message.getBonus());
                }
            }
        }
    }

    void loginResponseOld(Channel channel, int service, AuthUserEntity authUser) {
        UserEntity uEntity = new AuthDAO().getLoginUserEntity(authUser, authUser.getCp(), 1);
        if (uEntity == null) {
            addErrMessage();
            return;
        }
        UserInfo user = new UserInfo(channel, authUser, uEntity);
        if (!user.loadInventory()) {
            addErrMessage();
            return;
        }
        Channel ch = CfgBot.isBot(authUser.getUdid()) ? null : Online.getChannel(user.getId());
        if (ch != null) {
            Online.logoutChannel(ch);
            if (ch.id() != channel.id()) addResponse(DUPLICATE_LOGIN, null);
        }
        ChUtil.set(channel, Constans.KEY_USER, user);
        //        Online.addChannel(user.getId(), channel);
        // check diemdanh
        CfgDiemdanh.getMyAttendance(user);
        CfgMapAward.getMyAward(user);
        CfgEnergy.getMyEnergy(user);
        user.getUData().getInitUInt(uEntity);
        //
        if (CfgBot.isBot(authUser.getUdid())) {
            String udid = (String) ChUtil.get(channel, Constans.KEY_BOT_UDID);
            uEntity.setUsername(udid);
            authUser.setUdid(udid);
            user.setSession(udid);
            user.setOs(authUser.getOs());
            user.setCp(authUser.getCp());
            user.setVersion(authUser.getVersion());
            UserMonitor.saveUser(user);
        }
        ProtoLoginResponse.Builder builder = ProtoLoginResponse.newBuilder();
        builder.setUser(CommonProto.protoUser(uEntity, CfgEnergy.getMyEnergy(user), user.getUData()));
        addResponse(service, builder.build());
        loginNotify(user);
    }

    void checkGroupUdid(UserInfo user, String udid) {
        JSONArray arrGroup = JSONArray.fromObject(user.getDbUser().getUdidLogin());
        arrGroup.remove(udid);
        arrGroup.add(0, udid);
        while (arrGroup.size() > 10) {
            arrGroup.remove(arrGroup.size() - 1);
        }
        if (user.getDbUser().getUdidGroup() == 0) {
        }

        updateLogin(user, arrGroup.toString());
        //        Database.rawSQL("update user set last_login=now() where id=" + user.getId());
    }

    //<editor-fold desc="Database Access">
    public void updateLogin(UserInfo user, String udidLogin) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user set last_login=now(), udid_login=:udidLogin where id=" + user.getId());
            query.setString("udidLogin", udidLogin);
            query.executeUpdate();
            session.getTransaction().commit();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex) + " " + udidLogin);
        } finally {
            closeSession(session);
        }
    }

    public boolean existName(String name) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select count(*) from user where name=:name");
            query.setString("name", name);
            Integer count = Integer.parseInt(query.uniqueResult().toString());
            return count > 0;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return true;
    }

    public int hasAccountFacebook(String fbId) {
        return Database2.count("user", Arrays.asList("facebook", fbId));
    }

    public boolean dbFirstUpdate(UserInfo user, String name, int heroId, int userAvatar) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            UserHeroEntity uHero = new UserHeroEntity(user.getId(), heroId);
            session.save(uHero);
            //
            UserBombEntity uBomb = new UserBombEntity(user.getId(), 1);
            session.save(uBomb);
            //
            int avatarId = Resources.getHero(heroId).getAvatarId();
            UserAvatarEntity uAvatar = new UserAvatarEntity(user.getId(), avatarId, 1);
            session.save(uAvatar);
            //
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.BOMB_IMAGE, (int) uBomb.getBombId());
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.BOMB_ID, (int) uBomb.getId());
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.CLOTHES, avatarId);
            user.getMAvatar().setAvatar(MyAvatar.USER_AVATAR, userAvatar);

            //
            SQLQuery query = session.createSQLQuery("update user set name=:name, avatar=:avatar where id=" + user.getId());
            query.setString("name", name);
            query.setString("avatar", user.getDbUser().getMAvatar().toString());
            query.executeUpdate();
            UserIconEntity uIcon = new UserIconEntity(user.getId(), userAvatar);
            session.save(uIcon);
            session.getTransaction().commit();

            user.getRes().getBombs().add(uBomb);
            user.getRes().getHeroes().add(uHero);
            user.getRes().getAvatars().add(uAvatar);
            user.getRes().getIcons().add(uIcon);
            user.getRes().mBomb.put(uBomb.getId(), uBomb);
            user.getRes().mHero.put(uHero.getHeroId(), uHero);
            user.getRes().mAvatar.put(uAvatar.getAvatarId(), uAvatar);
            user.getRes().mIcon.put(uIcon.getIconId(), uIcon);
            return true;
        } catch (Exception he) {
            String error = Util.exToString(he);
            if (error.contains("Incorrect string value")) Logs.error(error, false, user.getDbUser().getServer());
            else Logs.error(error);
        } finally {
            closeSession(session);
        }
        return false;
    }

    public AuthUserEntity dbAuthUserByFacebook(String fbId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from auth_user where facebook=:facebook").addEntity(AuthUserEntity.class);
            query.setString("facebook", fbId);
            AuthUserEntity authUser = (AuthUserEntity) query.uniqueResult();
            if (authUser == null) {
                return new AuthUserEntity(-1);
            }
            return authUser;
        } catch (HibernateException he) {
            Logs.error(Util.exToString(he));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //</editor-fold>

    //region Logic
    boolean validChecksum(String checksum, String data) {
        String salt = checksum.substring(0, checksum.indexOf(","));
        checksum = checksum.substring(checksum.indexOf(",") + 1);
        return checksum.equals(Util.getMD5(salt + data));
    }

    boolean validAuth(String hpass, String checkData) {
        String[] value = hpass.split(",");
        if (value[0].equals("1")) {
            return value[3].equals(Util.getMD5(checkData + value[2]));
        }
        return false;
    }
    //endregion

    void changeLanguage() {
        CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String langCode = cmm.getAString(0);
        Lang lang = Lang.instance(langCode);
        user.setLang(lang);
        addResponse(service, null);
    }
}
