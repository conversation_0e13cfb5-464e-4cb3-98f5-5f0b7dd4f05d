package com.handler;

import com.bem.boom.BoomConfig;
import com.bem.boom.Resources;
import com.bem.boom.object.MissionObject;
import com.bem.boom.object.MyAvatar;
import com.bem.boom.object.SystemSlide;
import com.bem.config.CfgMapAward;
import com.bem.config.CfgNewMission;
import com.bem.config.CfgTienhoapet;
import com.bem.config.GameCfgGraftItem;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.*;
import com.bem.monitor.Bonus;
import com.bem.object.ResPet;
import com.bem.object.ResShopItem;
import com.bem.object.UserInfo;
import com.bem.util.Actions;
import com.bem.util.CommonProto;
import grep.database.HibernateUtil;
import com.bem.util.Util;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import grep.database.Database2;
import net.sf.json.JSONArray;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/11/2014.
 */
public class UpgradeHandler extends AHandler {

    private int oldUserAvatar;

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        mGameHandler.put(MATERIAL_PRICE, getInstance());
        mGameHandler.put(BUY_MATERIAL, getInstance());
        mGameHandler.put(UPGRADE_BOMB, getInstance());
        mGameHandler.put(SELL_MATERIAL, getInstance());
        mGameHandler.put(UPGRADE_AVATAR, getInstance());
        mGameHandler.put(UPGRADE_PET_STAR, getInstance());
        mGameHandler.put(FEED_PET_FOOD, getInstance());
        mGameHandler.put(USE_AVATAR, getInstance());
        mGameHandler.put(CHOOSE_AVATAR, getInstance());
        mGameHandler.put(CHOOSE_AVATAR_STATUS, getInstance());
        mGameHandler.put(MAP_AWARD_STATUS, getInstance());
        mGameHandler.put(MAP_GET_AWARD, getInstance());
        mGameHandler.put(UPGRADE_ACCESSORIES, getInstance());
        mGameHandler.put(GHEP_BOMB, getInstance());
        mGameHandler.put(LIST_SYMBOL, getInstance());
        mGameHandler.put(USE_SYMBOL, getInstance());
//        mGameHandler.put(USE_UFO, getInstance());
        mGameHandler.put(LIST_STONE_UFO, getInstance());

        mGameHandler.put(LIST_UFO, getInstance());
        mGameHandler.put(UPGRDE_STONE_UFO, getInstance());

        mGameHandler.put(LIST_GRAFT_TABLE, getInstance());
        mGameHandler.put(LIST_GRAFT_DETAIL, getInstance());
        mGameHandler.put(LIST_GRAFT_COMPONENT, getInstance());
        mGameHandler.put(GRAFT_ITEM, getInstance());
        mGameHandler.put(TIENHOA_PET, getInstance());
    }

    static UpgradeHandler instance;

    public static UpgradeHandler getInstance() {
        if (instance == null) {
            instance = new UpgradeHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new UpgradeHandler();
    }

    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case MATERIAL_PRICE:
                    materialPrice();
                    break;
                case BUY_MATERIAL:
                    buyMaterial();
                    break;
                case UPGRADE_BOMB:
                    upgradeBomb();
                    break;
                case UPGRADE_ACCESSORIES:
                    upgradeAccessories();
                    break;
                case SELL_MATERIAL:
                    sellMaterial();
                    break;
                case UPGRADE_PET_STAR:
                    upgradePetStar();
                    break;
                case FEED_PET_FOOD:
                    feedPetFood();
                    break;
                case UPGRADE_AVATAR:
                    upgradeAvatar();
                    break;
                case USE_AVATAR:
                    useAvatar();
                    break;

                case CHOOSE_AVATAR:
                    chooseAvatar();
                    break;
                case CHOOSE_AVATAR_STATUS:
                    chosseAvataStatus();
                    break;
                case MAP_AWARD_STATUS:
                    mapAwardStatus(service, 1, true);
                    break;
                case MAP_GET_AWARD:
                    mapGetAward();
                    break;
                case LIST_SYMBOL:
                    listSymbol();
                    break;
                case USE_SYMBOL:
                    useSymbol();
                    break;
//                case USE_UFO:
//                    useUfo();
//                    break;
                case LIST_UFO:
                    ListUfo();
                    break;
                case LIST_STONE_UFO:
                    ListStoneUfo();
                    break;
                case UPGRDE_STONE_UFO:
                    upgradeStoneUfo();
                    break;
                case LIST_GRAFT_TABLE:
                    listGraft();
                    break;
                case LIST_GRAFT_DETAIL:
                    listGraftDetail();
                    break;
                case LIST_GRAFT_COMPONENT:
                    listGraftComponent(service);
                    break;
                case GRAFT_ITEM:
                    graftItem();
                    break;
                case TIENHOA_PET:
                    tienhoaPet();
                    break;

            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void tienhoaPet() {
        if (CfgTienhoapet.aConfig.enable <= 0) {
            addErrMessage("Chức năng đang bảo trì!");
            return;
        }
        int petId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        UserPetEntity pet = user.getRes().getMPet().get(petId);
        if (pet == null || pet.getLevel() == 0) {
            addErrMessage("Thú cưng chưa sở hữu");
            return;
        }
        ResPet resPet = Resources.mPet.get(petId);
        if (pet.getTienhoalv() >= resPet.evolutionAvatar.size()) {
            addErrMessage("Pet đã đặp cấp tiến hóa cao nhất!");
            return;
        }
        UserItemEntity item = user.getRes().getMItem().get(ResShopItem.GEM_PET_UPGRADE);
        int numberMegaRiqure = resPet.evolutionMega.get(pet.getTienhoalv());
        int numberStartRiqure = resPet.evolutionStar.get(pet.getTienhoalv());

        if (pet.getStar() < numberStartRiqure) {
            addErrMessage("Pet của bạn cần đạt cấp sao " + numberStartRiqure + " để tiến hóa!");
            return;
        }

        if (item == null || item.getNumber() < numberMegaRiqure) {
            addErrMessage("Số lượng đá mega ko đủ");
            return;
        }
        if (Database2.update("user_pet", Arrays.asList("tienhoalv", String.valueOf(pet.getTienhoalv() + 1)), Arrays.asList("user_id", String.valueOf(user.getId()), "pet_id", String.valueOf(petId)))) {
            if (Database2.update("user_item", Arrays.asList("number", String.valueOf(item.getNumber() - numberMegaRiqure)), Arrays.asList("user_id", String.valueOf(user.getId()), "item_id", String.valueOf(item.getItemId())))) {
                pet.setTienhoalv(pet.getTienhoalv() + 1);
                item.setNumber(item.getNumber() - numberMegaRiqure);
//                SystemSlide.addMessage("<color=\"#00b939\">" + user.getDbUser().getName() + "</color>" + " nâng tiến hóa cho thú cưng " + "<color=\"#00b939\">" + Resources.getPet(petId).getName() + "</color>" + " thành công lên siêu thú cấp" + (pet.getTienhoalv()));
                SystemSlide.addMessage(String.format(getLang(Lang.upgrade_pet_wish_from_system), user.getDbUser().getName(), Resources.getPet(petId).getName(), pet.getTienhoalv()));
                sendMessage(channel, service, CommonProto.getCommonVectorProto(Arrays.asList(petId, pet.getTienhoalv(), item.getNumber()), null));
                Actions.save(user.getDbUser(), "upgrade_Tienhoa_pet", "upgrade_Tienhoa_pet: ", Actions.convertToLogString(Arrays.asList("desc", "petId:" + petId + "; petTienHoa: " + (pet.getTienhoalv()) + "; omega number remain: " + item.getNumber())));
                user.getMAvatar().setAvatar(MyAvatar.PET_TIENHOA, pet.getTienhoalv());
                user.getMAvatar().update(user.getId());
            }
        }
    }


    void listGraft() {
        List<Long> vLong = new ArrayList<>();
        List<String> vString = new ArrayList<>();
        vLong.add(1l);
        vString.add("Sự kiện Trung thu");
        addResponse(CommonProto.getCommonLongVectorProto(vLong, vString));
    }

    void listGraftDetail() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int eventId = aLong.get(0).intValue();
        List<Long> vLong = new ArrayList<>();
        List<String> vString = new ArrayList<>();
        for (int i = 0; i < GameCfgGraftItem.data.size(); i++) {
            if (GameCfgGraftItem.data.get(i).enable == 1 && GameCfgGraftItem.data.get(i).eventId == eventId) {
                for (int j = 0; j < GameCfgGraftItem.data.get(i).id.size(); j++) {
                    vLong.add((long) GameCfgGraftItem.data.get(i).id.get(j));
                }

            }
        }
        addResponse(CommonProto.getCommonLongVectorProto(vLong, vString));
    }

    void listGraftComponent(int service) {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int type = aLong.get(0).intValue();
        int id = aLong.get(1).intValue();

        List<Long> vLong = new ArrayList<>();
        List<String> vString = new ArrayList<>();
        for (int i = 0; i < GameCfgGraftItem.data.size(); i++) {
            if (GameCfgGraftItem.data.get(i).enable == 1 && GameCfgGraftItem.data.get(i).id.get(0) == type && GameCfgGraftItem.data.get(i).id.get(1) == id) {
                for (int j = 0; j < GameCfgGraftItem.data.get(i).graft.length; j++) {
                    for (int k = 0; k < GameCfgGraftItem.data.get(i).graft[j].length; k++) {
                        vLong.add((long) GameCfgGraftItem.data.get(i).graft[j][k]);

                    }
                    try {
                        vLong.add((long) user.getRes().getMItem().get(GameCfgGraftItem.data.get(i).graft[j][1]).getNumber());
                    } catch (Exception ex) {
                        vLong.add((long) 0);
                    }
                }
                break;
            }
        }
        System.out.println("vlong-->" + vLong);
        addResponse(service, CommonProto.getCommonLongVectorProto(vLong, vString));
    }

    void graftItem() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int type = aLong.get(0).intValue();
        int id = aLong.get(1).intValue();
        ;
        List<Long> vLong = new ArrayList<>();
        List<String> vString = new ArrayList<>();
        for (int i = 0; i < GameCfgGraftItem.data.size(); i++) {
            if (GameCfgGraftItem.data.get(i).enable == 1 && GameCfgGraftItem.data.get(i).id.get(0) == type && GameCfgGraftItem.data.get(i).id.get(1) == id) {
                for (int j = 0; j < GameCfgGraftItem.data.get(i).graft.length; j++) {
                    if (user.getRes().getMItem().get(GameCfgGraftItem.data.get(i).graft[j][1]) == null || user.getRes().getMItem().get(GameCfgGraftItem.data.get(i).graft[j][1]).getNumber() < GameCfgGraftItem.data.get(i).graft[j][2]) {
//                        addErrMessage("Không đủ nguyên liệu");
                        addErrMessage(getLang(Lang.not_enough_material));
                        return;
                    }
                }
                for (int j = 0; j < GameCfgGraftItem.data.get(i).graft.length; j++) {
//                    for (int k = 0; k < GameCfgGraftItem.data.get(i).graft[j].length; k++) {
                    if (!user.getAction().addItem(Arrays.asList(GameCfgGraftItem.data.get(i).graft[j][1]), -GameCfgGraftItem.data.get(i).graft[j][2])) {
//                        addErrMessage("Không đủ nguyên liệu");
                        addErrMessage(getLang(Lang.not_enough_material));
                        return;
                    }
                }

//                        vLong.add((long)GameCfgGraftItem.data.get(i).graft[j][k]);
//                }
                JSONArray array = new JSONArray();
                array.addAll(JSONArray.fromObject(String.format("[%s,%s,%s]", Bonus.ITEM, id, 1)));
                vLong.addAll(Bonus.receiveListItem(user, array, "graft_item"));
                break;
            }
        }
        addResponse(service, CommonProto.getCommonLongVectorProto(vLong, vString));
        listGraftComponent(IAction.LIST_GRAFT_COMPONENT);
    }

    void listSymbol() {
        List<Long> vlongAble = new ArrayList<>();
        List<String> vStringAble = new ArrayList<>();
        List<Long> vlongDisable = new ArrayList<>();
        List<String> vStringDisable = new ArrayList<>();
        user.getDbUser().addSymBol(user);
        for (int i = 0; i < Resources.aSymbol.size(); i++) {
            if (user.getRes().getMSymbol().containsKey(Resources.aSymbol.get(i).getId())) {
                vlongAble.add((long) Resources.aSymbol.get(i).getId());
//                if(user.getMAvatar().getSymbol()==Resources.aSymbol.get(i).getId()){
//                    vlongAble.add(2l);
//                }else {
                vlongAble.add(1l);
//                }
                vlongAble.add(0l);
                vlongAble.add(0l);

                vStringAble.add(Resources.aSymbol.get(i).getName());
                vStringAble.add(Resources.aSymbol.get(i).getDes());
                vStringAble.add(Resources.aSymbol.get(i).getEffect());
            } else {
//                vlongDisable.add((long) Resources.aSymbol.get(i).getId());
//                vlongDisable.add(0l);

                vStringDisable.add(Resources.aSymbol.get(i).getName());
                vStringDisable.add(Resources.aSymbol.get(i).getDes());
                vStringDisable.add(Resources.aSymbol.get(i).getEffect());
            }
        }
        vlongDisable.addAll(user.getDbUser().process(user));
//        "vlongDisable--->" + vlongDisable.size());
        vlongAble.addAll(vlongDisable);
        vStringAble.addAll(vStringDisable);
//        System.out.println("vlong--->" + vlongAble);System.out.println(
        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(vlongAble, vStringAble));

    }

    void useSymbol() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int symbolId = aLong.get(0).intValue();
        if (user.getRes().getMSymbol().containsKey(symbolId)) {
            addErrMessage(getLang(Lang.symbol_not_own));
            return;
        }
        if (user.getMAvatar().getSymbol() == symbolId) {
            return;
        }
        int oldUserSymbol = user.getDbUser().getMAvatar().getSymbol();
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.SYMBOL, symbolId);
        if (Database2.update("user", Arrays.asList("avatar", String.valueOf(user.getDbUser().getMAvatar().toString())), Arrays.asList("id", String.valueOf(user.getId())))) {
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) symbolId), null));
            Actions.save(user.getDbUser(), "use_symbol", "use_symbol: ", Actions.convertToLogString(Arrays.asList("desc", "symbolId:" + symbolId)));

        } else {


            user.getDbUser().getMAvatar().setAvatar(MyAvatar.SYMBOL, oldUserSymbol);
        }
    }

    void upgradeStoneUfo() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
//        System.out.println("along--->"+aLong);
        int ufoId = aLong.get(0).intValue();
        List<Integer> stoneIds = new ArrayList<>();
        List<Integer> numbers = new ArrayList<>();

        for (int i = 1; i < aLong.size(); i++) {
            if (i % 2 == 1) {
                stoneIds.add(aLong.get(i).intValue());
            } else {
                numbers.add(aLong.get(i).intValue());
            }
        }
//        System.out.println("stoneids---->"+stoneIds);
//        System.out.println("number---->"+numbers);

        if (stoneIds.size() != numbers.size()) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        if (user.getRes().getMUfo().get(ufoId) == null) {
            addErrMessage(getLang(Lang.ufo_not_own));
            return;
        }
        int addAtk = 0;
        int addHp = 0;
//        for (int i = 0; i <  stoneIds.size(); i++) {
////            if (Resources.ufoUpgrade.getMStone().get(stoneIds.get(i).intValue()) == null){
////                System.out.println("nulll id-->"+stoneIds.get(i));
////            }
////            if(user.getRes().getMStoneUfo().get(stoneIds.get(i)) == null){
////                System.out.println("nulll id 2-->"+stoneIds.get(i));
////            }
////            if(user.getRes().getMStoneUfo().get(stoneIds.get(i)).getNumber() < numbers.get(i)){
////                System.out.println("nulll id 3-->"+stoneIds.get(i));
////            }
//        }
        for (int i = 0; i < stoneIds.size(); i++) {
            if (user.getRes().getMStoneUfo().get(stoneIds.get(i).intValue()) == null || user.getRes().getMStoneUfo().get(stoneIds.get(i).intValue()).getNumber() < numbers.get(i) || Resources.ufoUpgrade.getMStone().get(stoneIds.get(i).intValue()) == null) {
                addErrMessage(getLang(Lang.ufo_stone_not_own));
                return;
            } else {
                addAtk += Resources.ufoUpgrade.getMStone().get(stoneIds.get(i)).atk * numbers.get(i);
                addHp += Resources.ufoUpgrade.getMStone().get(stoneIds.get(i)).hp * numbers.get(i);

            }
        }
        List<String> sqls = new ArrayList<String>();
        int newAtk = user.getRes().getMUfo().get(ufoId).getAtk() + addAtk;
        int newHp = user.getRes().getMUfo().get(ufoId).getHp() + addHp;
//        System.out.println("newchi so-->"+newAtk+"|"+newHp);
        if (newAtk + newHp > Resources.ufoUpgrade.getMaxValue()) {
//            addErrMessage(getLang("Vượt quá giới hạn ép"));
            addErrMessage(getLang(Lang.over_upgrade_limit));
            return;
        }

        sqls.add(Database2.getUpdateQuery("user_ufo", Arrays.asList("atk", String.valueOf(newAtk), "hp", String.valueOf(newHp)), Arrays.asList("user_id", String.valueOf(user.getId()), "ufo_id", String.valueOf(ufoId))));
        List<Long> newNumbers = new ArrayList<>();
        for (int i = 0; i < stoneIds.size(); i++) {
            long newNumber = user.getRes().getMStoneUfo().get(stoneIds.get(i)).getNumber() - numbers.get(i);
            newNumbers.add(newNumber);
            sqls.add(Database2.getUpdateQuery("user_stone_ufo", Arrays.asList("number", String.valueOf(newNumber)), Arrays.asList("user_id", String.valueOf(user.getId()), "stone_id", String.valueOf(stoneIds.get(i)))));
        }
        if (Database2.listQuery(sqls)) {
            List<Long> rs = new ArrayList<>();
            rs.add((long) ufoId);
            user.getRes().getMUfo().get(ufoId).setAtk(newAtk);
            user.getRes().getMUfo().get(ufoId).setHp(newHp);
            rs.add((long) newAtk);
            rs.add((long) newHp);
            for (int i = 0; i < numbers.size(); i++) {
                user.getRes().getMStoneUfo().get(stoneIds.get(i)).setNumber(newNumbers.get(i).intValue());
                rs.add((long) stoneIds.get(i));
                rs.add((long) newNumbers.get(i));

            }
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(rs, null));
//            System.out.println("rs------------>"+rs);
            Actions.save(user.getDbUser(), "update_ufo", "sale_item: ", Actions.convertToLogString(Arrays.asList("desc", "")));
        }


    }

    void ListStoneUfo() {
        List<Long> vLongs = new ArrayList<>();
//        System.out.println("user.getRes().getStoneUfos()-->"+user.getRes().getStoneUfos());

        for (int i = 0; i < user.getRes().getStoneUfos().size(); i++) {
            if (Resources.ufoUpgrade.getMStone().containsKey(user.getRes().getStoneUfos().get(i).getStoneId())) {
                vLongs.add((long) user.getRes().getStoneUfos().get(i).getStoneId());
                vLongs.add((long) user.getRes().getStoneUfos().get(i).getNumber());
            }
        }
//        System.out.println("vlong-->"+vLongs);
        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(vLongs, null));
    }

    void ListUfo() {
        List<Long> vLongs = new ArrayList<>();
        for (int i = 0; i < Resources.aUfo.size(); i++) {
            int status = 0;
            int atk = 0;
            int hp = 0;
            if (user.getRes().getMUfo().containsKey(Resources.aUfo.get(i).getId())) {
                status = 1;
                atk = user.getRes().getMUfo().get(Resources.aUfo.get(i).getId()).getAtk();
                hp = user.getRes().getMUfo().get(Resources.aUfo.get(i).getId()).getHp();
            } else {
                atk = Resources.aUfo.get(i).getAtk();
                hp = Resources.aUfo.get(i).getHp();

            }
            vLongs.add((long) Resources.aUfo.get(i).getId());
            vLongs.add((long) atk);
            vLongs.add((long) hp);
            vLongs.add((long) Resources.aUfo.get(i).getPrice_type());
            vLongs.add((long) Resources.aUfo.get(i).getPrice());
            vLongs.add((long) status);
        }
//        for (int i = 0; i < user.getRes().getUfos().size(); i++) {
//        if (Resources.mUfo.containsKey(user.getRes().getUfos().get(i).getUfoId())) {
//            vLongs.add((long) user.getRes().getUfos().get(i).getUfoId());
//            vLongs.add((long) user.getRes().getUfos().get(i).getAtk());
//            vLongs.add((long) user.getRes().getUfos().get(i).getHp());
//            vLongs.add((long) user.getRes().getUfos().get(i).getHp());
//            vLongs.add((long) user.getRes().getUfos().get(i).getHp());
//        }
//        }
//        System.out.println(vLongs);
        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(vLongs, null));

//        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
//        int ufoId = aLong.get(0).intValue();
//        if (user.getRes().getMUfo().containsKey(ufoId)) {
//            addErrMessage(getLang(Lang.ufo_not_own));
//            return;
//        }
//        if (user.getMAvatar().getUfo() == ufoId) {
//            return;
//        }
//        int oldUserUfo = user.getDbUser().getMAvatar().getUfo();
//        user.getDbUser().getMAvatar().setAvatar(MyAvatar.UFO, ufoId);
//        if (Database.update("user", Arrays.asList("avatar", String.valueOf(user.getDbUser().getMAvatar().toString())), Arrays.asList("id", String.valueOf(user.getId())))) {
//            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) ufoId), null));
//            Actions.save(user.getDbUser(), "use_Ufo", "use_Ufo: ", Actions.convertToLogString(Arrays.asList("desc", "ufoId:" + ufoId)));
//        } else {
//            user.getDbUser().getMAvatar().setAvatar(MyAvatar.UFO, oldUserUfo);
//        }
    }

    void useUfo() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int ufoId = aLong.get(0).intValue();
        if (user.getRes().getMUfo().containsKey(ufoId)) {
            addErrMessage(getLang(Lang.ufo_not_own));
            return;
        }
        if (user.getMAvatar().getUfo() == ufoId) {
            return;
        }
        int oldUserUfo = user.getDbUser().getMAvatar().getUfo();
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.UFO, ufoId);
        if (Database2.update("user", Arrays.asList("avatar", String.valueOf(user.getDbUser().getMAvatar().toString())), Arrays.asList("id", String.valueOf(user.getId())))) {
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) ufoId), null));
            Actions.save(user.getDbUser(), "use_Ufo", "use_Ufo: ", Actions.convertToLogString(Arrays.asList("desc", "ufoId:" + ufoId)));
        } else {
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.UFO, oldUserUfo);
        }

    }

    void mapAwardStatus(int service, int typeMap, boolean hasRq) {
        int numberMap = 10;
        List<Integer> rs = new ArrayList<>();
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        try {
            if (hasRq) {
                typeMap = aLong.get(0).intValue();
            }
        } catch (Exception ex) {

        }
        List<Integer> map = user.getUData().getMap().getKindOfMap(typeMap);
        List<Integer> myMap = new ArrayList<>();
        CfgMapAward.validMapAward(user);
        if (typeMap == CfgMapAward.normal && user.getUData().myAward.award != null) {
            myMap.addAll(user.getUData().myAward.award);
        } else if (typeMap == CfgMapAward.danger && user.getUData().myAward.awarddanger != null) {
            myMap.addAll(user.getUData().myAward.awarddanger);
        }
        for (int i = 0; i < map.size() / numberMap; i++) {
            int ck = 2;
            for (int j = i * numberMap; j < i * numberMap + numberMap; j++) {
                if (map.get(j) <= 0) {

                    ck = 0;
                    break;
                } else if (map.get(j) <= 2) {
                    ck = 1;
                }
            }
            if (typeMap == CfgMapAward.normal && (user.getUData().myAward.award == null || user.getUData().myAward.award.size() <= i)) {
//                CfgMapAward.validMapAward(user);
                myMap.addAll(user.getUData().myAward.award);
            }
            if (typeMap == CfgMapAward.danger && (user.getUData().myAward.awarddanger == null || user.getUData().myAward.awarddanger.size() <= i)) {
//                CfgMapAward.validMapAward(user);
                myMap.addAll(user.getUData().myAward.awarddanger);
            }
            if (myMap.get(i) == 3) {// nhận hết
                rs.add(0);
            } else if (myMap.get(i) == 0) {
                rs.add(ck);
            } else {
                if (myMap.get(i) == ck) {
                    rs.add(ck - 1);
                } else {
                    rs.add(ck);
                }
            }

        }
        sendMessage(channel, service, CommonProto.getCommonVectorProto(rs, null));
    }

    void mapGetAward() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int mapId = aLong.get(0).intValue();
        int awardId = aLong.get(1).intValue();
        int typeMap = aLong.get(2).intValue();
        List<Integer> map = user.getUData().getMap().getKindOfMap(typeMap);
        int numberMap = 10;
        List<Long> rs = new ArrayList<>();
        int ck = 2;
        List<Integer> myMap = new ArrayList<>();
        if (typeMap == CfgMapAward.normal) {
            myMap = user.getUData().myAward.award;
        } else {
            myMap = user.getUData().myAward.awarddanger;
        }
        for (int j = mapId * numberMap; j < mapId * numberMap + numberMap; j++) {
            if (map.get(j) <= 0) {
                ck = 0;
                break;
            } else if (map.get(j) <= 2) {
                ck = 1;
            }
        }
        if (awardId <= 0 || awardId > 2) {
            addErrMessage(getLang(Lang.err_bonus_not_exist));
            return;
        }
        if (ck <= 0 || ck > 2) {
            addErrMessage(getLang(Lang.err_bonus_receive_condition));
            return;
        }
        CfgMapAward.validMapAward(user);
        if (myMap.size() <= mapId) {
            addErrMessage(getLang(Lang.err_bonus_receive_condition));
            return;
        }
        if (myMap.get(mapId) == 3) {// nhận hết
            addErrMessage(getLang(Lang.err_bonus_already_received));
            return;
        } else if (myMap.get(mapId) <= 0) {// chua nhan gi
            if (ck < awardId) {
                addErrMessage(getLang(Lang.err_bonus_receive_condition));
                return;
            }
            JSONArray arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).award);
            if (typeMap == CfgMapAward.normal) {
                arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).award);
            } else if (typeMap == CfgMapAward.danger) {
                arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).awardDanger);
            }
            if (ck == 2) {
//                arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).award3);
                if (typeMap == CfgMapAward.normal) {
                    arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).award3);
                } else if (typeMap == CfgMapAward.danger) {
                    arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).awardDanger3);
                }
            }
            rs.addAll(Bonus.receiveListItem(user, arr, "map_award ; mapId:" + mapId + " ; type: " + typeMap + " ; award: " + awardId));
            myMap.set(mapId, awardId);
            user.getUData().myAward.update(user.getId());
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(rs, null));
        } else {
            if (myMap.get(mapId) == awardId) {// da nhan roi
                addErrMessage(getLang(Lang.err_bonus_already_received));
                return;
            } else {
                if (ck < awardId) {// khong phai phan thuong
                    addErrMessage(getLang(Lang.err_bonus_receive_condition));
                    return;
                } else {

                    JSONArray arr = new JSONArray();
                    if (awardId == CfgMapAward.award) {
                        if (typeMap == CfgMapAward.normal) {
                            arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).award);
                        } else if (typeMap == CfgMapAward.danger) {
                            arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).awardDanger);
                        }
                    } else if (ck == 2 && awardId == CfgMapAward.award3) {
                        if (typeMap == CfgMapAward.normal) {
                            arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).award3);
                        } else if (typeMap == CfgMapAward.danger) {
                            arr = JSONArray.fromObject(CfgMapAward.data.get(mapId).awardDanger3);
                        }
                    }
                    rs.addAll(Bonus.receiveListItem(user, arr, "map_award ; mapId:" + mapId + " ; type: " + typeMap + " ; award: " + awardId));
                    myMap.set(mapId, 3);
                    user.getUData().myAward.update(user.getId());
                    sendMessage(channel, service, CommonProto.getCommonLongVectorProto(rs, null));
                }
            }
        }

        mapAwardStatus(IAction.MAP_AWARD_STATUS, typeMap, false);


    }

    void upgradePetStar() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int idPet = aLong.get(0).intValue();
        int numberFragment = user.getRes().getMPet().get(idPet).getFragment();
        int curStar = user.getRes().getMPet().get(idPet).getStar();
        if (user.getRes().getMPet().get(idPet) == null) {
            addErrMessage(getLang(Lang.pet_not_own));
            return;
        }
        if (curStar >= Resources.aPetStarUpgrade.size()) {
            addErrMessage(getLang(Lang.err_max_level));
            return;
        }
//        int[] requireFragment = {10, 10, 20, 30, 40, 50, 50};
        int rate = 1;
        if (BoomConfig.rankpet == 0) {
            rate = Resources.getPet(idPet).getRank();
        }

        int requireFragment = Resources.mPetStarUpgrade.get(curStar + 1).getNumber() * rate;

        boolean mapPet = false;
        if (user.getRes().getMPet().get(idPet).getLevel() == 0) {
            mapPet = true;
        }
        if (numberFragment < requireFragment) {
            addErrMessage(getLang(Lang.err_not_enough));
            return;
        }
        if (mapPet) {
            if (Database2.update("user_pet", Arrays.asList("level", String.valueOf(1), "exp", String.valueOf(0), "fragment", String.valueOf(numberFragment - requireFragment)), Arrays.asList("user_id", String.valueOf(user.getId()), "pet_id", String.valueOf(idPet)))) {
                user.getRes().addStar(0, -requireFragment, idPet);
                user.getRes().getMPet().get(idPet).setLevel(1);
                sendMessage(channel, service, CommonProto.getCommonVectorProto(Arrays.asList(idPet, curStar, numberFragment - requireFragment), null));
                Actions.save(user.getDbUser(), "upgrade_star_pet", "upgrade_star_pet: ", Actions.convertToLogString(Arrays.asList("desc", "petId:" + idPet + "; petstar: " + (curStar + 1) + "; remainFragment: " + user.getRes().getMPet().get(idPet).getFragment())));
            }
        } else if (Database2.update("user_pet", Arrays.asList("star", String.valueOf(curStar + 1), "fragment", String.valueOf(numberFragment - requireFragment)), Arrays.asList("user_id", String.valueOf(user.getId()), "pet_id", String.valueOf(idPet)))) {
            user.getRes().addStar(1, -requireFragment, idPet);
//            SystemSlide.addMessage("<color=\"#00b939\">" + user.getDbUser().getName() + "</color>" + " nâng cấp sao cho thú cưng " + "<color=\"#00b939\">" + Resources.getPet(idPet).getName() + "</color>" + " thành công lên " + (curStar + 1) + " sao");
            SystemSlide.addMessage(String.format(getLang(Lang.upgrade_pet_star_wish_from_system), user.getDbUser().getName(), Resources.getPet(idPet).getName(), (curStar + 1)));
            sendMessage(channel, service, CommonProto.getCommonVectorProto(Arrays.asList(idPet, curStar + 1, numberFragment - requireFragment), null));
            Actions.save(user.getDbUser(), "upgrade_star_pet", "upgrade_star_pet: ", Actions.convertToLogString(Arrays.asList("desc", "petId:" + idPet + "; petstar: " + (curStar + 1) + "; remainFragment: " + user.getRes().getMPet().get(idPet).getFragment())));

        }
    }

    void feedPetFood() {
        try {
            List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
            int idPet = aLong.get(0).intValue();

//            List<Integer> lstIdFood = new ArrayList<Integer>();
//            List<Integer> lstNumberFood = new ArrayList<Integer>();
            UserPetEntity pet = user.getRes().getMPet().get(idPet);
            ResPet temPes = Resources.mPet.get(idPet);
            int curlevel = pet.getLevel();
            if (pet == null || curlevel <= 0) {
                addErrMessage(getLang(Lang.pet_not_own));
                return;
            }
            if (curlevel >= Resources.aPetLevel.get(Resources.aPetLevel.size() - 1).getId()) {
                addErrMessage(getLang(Lang.err_max_level));
                return;
            }
            if (aLong.size() % 2 == 0) {
                addErrMessage(getLang(Lang.err_param));
                return;
            }
            List<Long> rs = new ArrayList<Long>();
            int oldLv = user.getRes().getMPet().get(idPet).getLevel();
            String decs = "";
            for (int i = 1; i < aLong.size() - 1; i = i + 2) {
                if (curlevel >= Resources.aPetLevel.get(Resources.aPetLevel.size() - 1).getId()) {
                    addErrMessage(getLang(Lang.err_max_level));
                    break;
                }

//                int idFood = aLong.get(1).intValue();
//                int numberFood = aLong.get(2).intValue();
                int idFood = aLong.get(i).intValue();
                int numberFood = aLong.get(i + 1).intValue();
                if (numberFood < 0) {
                    addErrMessage(getLang(Lang.pet_food_not_enough));
                    return;
                }
                if (user.getRes().getMPetFood().get(idFood).getNumber() < numberFood) {
                    addErrMessage(getLang(Lang.pet_food_not_enough));
                    return;
                }
                if (!temPes.getFood().contains(idFood)) {
                    addErrMessage(getLang(Lang.pet_food_invalid));
                    return;
                }
//            newUser.getRes().addlvlPet(numberFood * Resources.aPetFood.get(idFood - 1).getExp(), idPet);
                long exp = pet.addExp(numberFood * Resources.aPetFood.get(idFood - 1).getExp());
                int refundNumber = 0;
                if (exp > 0) {
                    refundNumber = (int) (exp / Resources.aPetFood.get(idFood - 1).getExp());
                }
                if (refundNumber < 0) {
                    refundNumber = 0;
                }
                numberFood -= refundNumber;
                if (Database2.update("user_pet", Arrays.asList("level", String.valueOf(user.getRes().getMPet().get(idPet).getLevel()), "exp", String.valueOf(user.getRes().getMPet().get(idPet).getExp())), Arrays.asList("user_id", String.valueOf(user.getId()), "pet_id", String.valueOf(idPet)))) {
                    if (Database2.update("user_pet_food", Arrays.asList("number", String.valueOf(user.getRes().getMPetFood().get(idFood).getNumber() - numberFood)), Arrays.asList("user_id", String.valueOf(user.getId()), "food_id", String.valueOf(idFood)))) {
                        user.getRes().getMPetFood().get(idFood).setNumber(user.getRes().getMPetFood().get(idFood).getNumber() - numberFood);
//                        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) newUser.getRes().getMPet().get(idPet).getLevel(), newUser.getRes().getMPet().get(idPet).getExp(), (long) newUser.getRes().getMPetFood().get(idFood).getNumber()), null));
                        rs.add((long) idFood);
                        rs.add((long) user.getRes().getMPetFood().get(idFood).getNumber());
                    } else {
                        break;
                    }
                    user.getDbUser().addSymBol(user);
                } else {
                    break;
                }
                decs += "idFood: " + idFood + "; numberFoodRemain:" + user.getRes().getMPetFood().get(idFood).getNumber() + "; ";
            }
            UserPetEntity uPet = user.getRes().getMPet().get(idPet);
            rs.add(0, uPet.getExp());
            rs.add(0, (long) uPet.getLevel());
            rs.add(0, (long) idPet);
            CfgNewMission.addMission(user.getDbUser(), MissionObject.FEEDPETMISSION, 1);
            if (oldLv < user.getRes().getMPet().get(idPet).getLevel() && user.getRes().getMPet().get(idPet).getLevel() >= 10) {
//                SystemSlide.addMessage("<color=\"#00b939\">" + user.getDbUser().getName() + "</color>" + " nâng cấp thú cưng " + "<color=\"#00b939\">" + Resources.mPet.get(idPet).getName() + "</color>" + " thành công lên cấp " + user.getRes().getMPet().get(idPet).getLevel());
                SystemSlide.addMessage(String.format(getLang(Lang.upgrade_pet_level_wish_from_system), user.getDbUser().getName(), Resources.mPet.get(idPet).getName(), user.getRes().getMPet().get(idPet).getLevel()));
            }
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(rs, null));
            Actions.save(user.getDbUser(), "upgrade_exp_pet", "upgrade_Sexp_pet: ", Actions.convertToLogString(Arrays.asList("petId", String.valueOf(idPet), "petLevel", String.valueOf(uPet.getLevel()), "desc", decs)));

        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
    }

    void upgradeAvatar() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int heroId = aLong.get(0).intValue();
        int avataId = aLong.get(1).intValue();
        if (user.getRes().getMAvatar().get(avataId) == null) {
            addErrMessage(getLang(Lang.avatar_not_own));
            return;
        }
        if (user.getRes().getMAvatar().get(avataId).getLevel() >= Resources.aAvatarUpgrade.get(Resources.aAvatarUpgrade.size() - 1).getLevel()) {
            addErrMessage(getLang(Lang.err_max_level));
            return;
        }

        int levelExpect = user.getRes().getMAvatar().get(avataId).getLevel() + 1;
        int rate = 1;
        if (BoomConfig.rankclother == 0) {
            rate = Resources.getAvatar(avataId).getRank();
        }
        int flagmentRequire = Resources.mAvatarUpgrade.get(levelExpect).getNumber() * rate;
        long goldRequire = Resources.mAvatarUpgrade.get(levelExpect).getGold() * rate;

        if (user.getDbUser().getGold() < goldRequire) {
            addErrMessage(getLang(Lang.err_not_enough_gold));
            return;
        }
        if (user.getRes().getMAvatar().get(avataId).getFragment() < flagmentRequire) {
            addErrMessage(getLang(Lang.avatar_fragment_not_enough));
            return;
        }
        int percent = new Random().nextInt(100) + 1;
        boolean success = false;
        if (percent <= Resources.mAvatarUpgrade.get(levelExpect).getPercent()) {
            success = true;
        }
        if (goldRequire != 0) {
            JSONArray arr = user.getAction().addMoney(this, Constans.PRICE_GOLD, -goldRequire);
            if (arr == null) {
                return;
            }
            List<Long> tmp = Bonus.receiveListItem(user, arr, "upgrade_avatar");
            if (tmp.isEmpty()) {
                addErrMessage();
                return;
            }
        }
        List<Long> rs = new ArrayList<Long>();
        if (success) {
            rs.add(1l);
            if (Database2.update("user_avatar", Arrays.asList("level", String.valueOf(levelExpect), "fragment", String.valueOf(user.getRes().getMAvatar().get(avataId).getFragment() - flagmentRequire)), Arrays.asList("user_id", String.valueOf(user.getId()), "avatar_id", String.valueOf(avataId)))) {
                user.getRes().getMAvatar().get(avataId).setLevel(levelExpect);
                user.getRes().getMAvatar().get(avataId).setFragment(user.getRes().getMAvatar().get(avataId).getFragment() - flagmentRequire);

            } else {
                addErrMessage(getLang(Lang.err_system_down));
                return;
            }
        } else {
            rs.add(0l);
            if (Database2.update("user_avatar", Arrays.asList("fragment", String.valueOf(user.getRes().getMAvatar().get(avataId).getFragment() - flagmentRequire)), Arrays.asList("user_id", String.valueOf(user.getId()), "avatar_id", String.valueOf(avataId)))) {
                user.getRes().getMAvatar().get(avataId).setFragment(user.getRes().getMAvatar().get(avataId).getFragment() - flagmentRequire);
            } else {
                addErrMessage(getLang(Lang.err_system_down));
                return;
            }
        }
        rs.add((long) avataId);
        rs.add(user.getDbUser().getGold());
        rs.add((long) user.getRes().getMAvatar().get(avataId).getLevel());
        rs.add((long) user.getRes().getMAvatar().get(avataId).getFragment());
        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(rs, null));
        String desc = "avatarId:" + avataId + "; avatarLv: " + (user.getRes().getMAvatar().get(avataId).getLevel()) + "; remainFragment: " + user.getRes().getMAvatar().get(avataId).getFragment();
        Actions.save(user.getDbUser(), "upgrade_closing", "upgrade_closing: ", Actions.convertToLogString(Arrays.asList("desc", desc)));

    }

    void useAvatar() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
//        if(user.getDbUser().getUsername().equalsIgnoreCase("leanhvut1")){
//            MainCache.sendAwardTopTrophyTime(Arrays.asList(user.getDbUser()));
//        }
        int heroId = aLong.get(0).intValue();
        int bombId = aLong.get(1).intValue();
        int avataId = aLong.get(2).intValue();
        int petId = aLong.get(3).intValue();
        int bombImage = aLong.get(4).intValue();
        List<Integer> accessories = new ArrayList<>();
        List<Integer> accId = new ArrayList<>();
        int count = 0;
//        System.out.println("along--->"+aLong);
//        System.out.println("aLong--->"+aLong);
        for (int i = MyAvatar.ACCESS_ID1; i < MyAvatar.ACCESS_IMAGE6; i = i + 2) {
            if (aLong.get(i).intValue() >= 0) {
                boolean isOk = true;

                if (aLong.get(i).intValue() > 0) {
//                    boolean isOk = true;
                    if (!Resources.mAccessories.containsKey(aLong.get(i + 1).intValue())) {
//                        addErrMessage("Không có phụ kiện này");
//                        return;
                        isOk = false;
                    } else if (!user.getRes().getMAccessories().containsKey(aLong.get(i).intValue())) {
//                        addErrMessage("Bạn không có phụ kiện này");
//                        return;
                        isOk = false;
                    }
                    if (isOk) {
                        accId.add(aLong.get(i).intValue());
                        count++;
                    }
                }
                if (isOk) {
//                        accId.add(aLong.get(i).intValue());
                    accessories.add(aLong.get(i).intValue());
                    accessories.add(aLong.get(i + 1).intValue());

                }
//                }
            }
        }
        if (accessories.size() % 2 != 0) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        if (accId.size() >= 2) {
            Collections.sort(accId);
            for (int i = 0; i < accId.size() - 1; i = i + 2) {
                if (accId.get(i) == accId.get(i + 1)) {
                    addErrMessage(getLang(Lang.err_param));
                    return;
                }
            }
        }
        int symbolId = aLong.get(MyAvatar.SYMBOL).intValue();
        int ufoId = 0;
        try {
            ufoId = aLong.get(MyAvatar.UFO).intValue();
        } catch (Exception ex) {
        }
//        System.out.println("accessories-->" + accessories);
        if (accessories.size() > 0 && Resources.accessoriesUpgrade.getSlotLevelRequire().get(accessories.size() / 2) == null) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        if (Resources.accessoriesUpgrade.getSlotLevelRequire().get(count / 2) != null && Resources.accessoriesUpgrade.getSlotLevelRequire().get(count / 2) > user.getDbUser().getLevel()) {
            addErrMessage(String.format(getLang(Lang.accessory_user_level_required), count / 2));
            return;
        }
        if (!user.getRes().getMHero().containsKey(heroId)) {
            addErrMessage(getLang(Lang.hero_not_own));
            return;
        }
        if (!user.getRes().getMPet().containsKey(petId) && petId > 0) {
            addErrMessage(getLang(Lang.pet_not_own));
            return;
        }
        if (!user.getRes().getMBomb().containsKey(bombId) && bombId > 0) {
            addErrMessage(getLang(Lang.bomb_not_own));
            return;
        }

        UserAvatarEntity ava = user.getRes().getMAvatar().get(avataId);
        if ((ava == null || ava.getLevel() < 1) && avataId != user.getDbUser().getMAvatar().getClothes()) {
            addErrMessage(getLang(Lang.avatar_not_own));
            return;
        }
        if (!user.getRes().getMAvatar().containsKey(avataId) && avataId > 0) {
            addErrMessage(getLang(Lang.avatar_not_own));
            return;
        }
//        System.out.println("symbolId--->"+symbolId);
//        System.out.println("user symbol --->"+user.getRes().getMSymbol().toString());

        if (!user.getRes().getMSymbol().containsKey(symbolId) && symbolId > 0) {
            addErrMessage(getLang(Lang.symbol_not_own));
            return;
        }
        if (!user.getRes().getMUfo().containsKey(ufoId) && ufoId > 0) {
            addErrMessage(getLang(Lang.ufo_not_own));
            return;
        }
//        if(user.getMAvatar().getSymbol()==symbolId){
//            return;
//        }
        int oldUserSymbol = user.getDbUser().getMAvatar().getSymbol();
        int oldUserUfo = user.getDbUser().getMAvatar().getUfo();
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.SYMBOL, symbolId);
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.UFO, ufoId);

//        if (Database.update("user", Arrays.asList("avatar", String.valueOf(user.getDbUser().getMAvatar().toString())), Arrays.asList("id", String.valueOf(user.getId())))) {
//            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) symbolId), null));
//        } else {
//            user.getDbUser().getMAvatar().setAvatar(MyAvatar.SYMBOL, oldUserSymbol);
//        }

        int oldheroId = user.getDbUser().getMAvatar().getHero();
        int oldpetId = user.getDbUser().getMAvatar().getPet();
        int oldAvataId = user.getDbUser().getMAvatar().getClothes();
        int oldBombId = user.getDbUser().getMAvatar().getIdBomb();
        int oldBombImage = user.getDbUser().getMAvatar().getBombImage();
        List<Integer> lstOldAccess = new ArrayList<>();
        lstOldAccess.addAll(user.getDbUser().getMAvatar().getListAccessories());

        user.getDbUser().getMAvatar().setAvatar(MyAvatar.PET, petId);
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.CLOTHES, avataId);
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.BOMB_IMAGE, bombImage);
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.HERO, heroId);
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.BOMB_ID, bombId);
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.PET_TIENHOA, petId == 0 ? 0 : user.getRes().getMPet().get(petId).getTienhoalv());
        if (!user.getDbUser().getMAvatar().setListAccessories(accessories)) {
            addErrMessage(getLang(Lang.err_system_down));
            return;
        }

        if (Database2.update("user", Arrays.asList("avatar", String.valueOf(user.getDbUser().getMAvatar().toString())), Arrays.asList("id", String.valueOf(user.getId())))) {
//            System.out.println("user.getDbUser().getMAvatar().toList()-->"+user.getDbUser().getMAvatar().toList());
            addResponse(CommonProto.getCommonVectorProto(user.getDbUser().getMAvatar().toList(), null));

            Actions.save(user.getDbUser(), "use_myavatar", "use_myavatar", Actions.convertToLogString(Arrays.asList("desc", user.getDbUser().getMAvatar().toList().toString())));

//            System.out.println(user.getDbUser().getMAvatar().toList());
        } else {
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.PET, oldpetId);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.CLOTHES, oldAvataId);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.BOMB_IMAGE, oldBombImage);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.HERO, oldheroId);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.BOMB_ID, oldBombId);
            user.getDbUser().getMAvatar().setListAccessories(lstOldAccess);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.SYMBOL, oldUserSymbol);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.UFO, oldUserUfo);
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.PET_TIENHOA, oldpetId == 0 ? 0 : user.getRes().getMPet().get(oldpetId).getTienhoalv());
        }
    }


    void chosseAvataStatus() {
        List<Long> aLong = new ArrayList<Long>();
        debug("newUser.getRes().getMIcon().size-->" + user.getRes().getMIcon().size());
        debug("newUser.getRes().getIcon().size-->" + user.getRes().getIcons().size());
        for (int i = 0; i < Resources.aIcon.size(); i++) {
            if ((long) Resources.aIcon.get(i).getTypePrice() != 0) {
                aLong.add((long) Resources.aIcon.get(i).getId());
                aLong.add((long) Resources.aIcon.get(i).getTypePrice());

                if (user.getRes().getMIcon().get(Resources.aIcon.get(i).getId()) == null) {
                    aLong.add(Resources.aIcon.get(i).getPrice());
                } else {
                    aLong.add(0l);
                }
            }
        }
        debug("statsu--->" + aLong.toString());
        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(aLong, null));
    }


    void chooseAvatar() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int newAvatar = aLong.get(0).intValue();
        long goldRequire = 0;
        if ((long) Resources.mIcon.get(newAvatar).getTypePrice() == 0) {
            addErrMessage(getLang(Lang.not_sale));
            return;
        }
        if (user.getRes().getMIcon().get(newAvatar) == null && Resources.mIcon.get(newAvatar).getPrice() > user.getDbUser().getGold()) {
            addErrMessage(getLang(Lang.err_not_enough_gold));
            return;
        } else if (user.getRes().getMIcon().get(newAvatar) == null) {
            goldRequire = Resources.mIcon.get(newAvatar).getPrice();
        }
        int oldUserAvatar = user.getDbUser().getMAvatar().getUserAvatar();
        user.getDbUser().getMAvatar().setAvatar(MyAvatar.USER_AVATAR, newAvatar);
        if (updateAvatar(user, newAvatar, goldRequire)) {
            user.getRes().getMIcon().put(newAvatar, new UserIconEntity(user.getId(), newAvatar));
            user.getDbUser().addGold(-goldRequire);
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) newAvatar, (long) Resources.mIcon.get(newAvatar).getTypePrice(), user.getDbUser().getGold()), null));
            Actions.save(user.getDbUser(), "chose_icon", "chose_icon: ", Actions.convertToLogString(Arrays.asList("desc", "IconId: " + newAvatar)));
        } else {
            user.getDbUser().getMAvatar().setAvatar(MyAvatar.USER_AVATAR, oldUserAvatar);
            addErrMessage();
        }
    }

    void materialPrice() {
//        long aotomic = newUser.getDbUser().getAtomic();
        List<Long> vLong = new ArrayList<Long>();
        List<String> vString = new ArrayList<String>();
        for (int i = 0; i < Resources.aMaterial.size(); i++) {
            vLong.add((long) Resources.aMaterial.get(i).getId());
            vLong.add((long) Resources.aMaterial.get(i).getAtomic());
            vLong.add((long) Resources.aMaterial.get(i).getAtomic());
        }
        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(vLong, null));
    }

    void buyMaterial() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        List<Integer> lstId = new ArrayList<Integer>();
        List<Integer> lstNumber = new ArrayList<Integer>();
        for (int i = 0; i < aLong.size() - 1; i = i + 2) {
            if (aLong.get(i + 1).intValue() <= 0) {
                addErrMessage(getLang(Lang.err_param));
                return;
            }
            lstId.add(aLong.get(i).intValue());
            lstNumber.add(aLong.get(i + 1).intValue());
        }
        if (lstId.size() != lstNumber.size()) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        int totalAtomicRequire = 0;
        for (int i = 0; i < lstId.size(); i++) {
            totalAtomicRequire += Resources.mMaterial.get(lstId.get(i)).getAtomic() * lstNumber.get(i);
        }
        if (totalAtomicRequire > user.getDbUser().getAtomic()) {
            addErrMessage(getLang(Lang.err_not_enough));
            return;
        }
        int atomicRemain = user.getDbUser().getAtomic() - totalAtomicRequire;
        List<Integer> newLstNumber = new ArrayList<Integer>();
        for (int i = 0; i < lstNumber.size(); i++) {
            int oldnumber = 0;
            try {
                oldnumber = user.getRes().getMMaterial().get(lstId.get(i)).getNumber();
            } catch (Exception ex) {

            }
            if (oldnumber < 0) {
                oldnumber = 0;
            }
            newLstNumber.add(lstNumber.get(i) + oldnumber);
        }
        if (Database2.update("user", Arrays.asList("atomic", String.valueOf(atomicRemain)), Arrays.asList("id", String.valueOf(user.getId())))) {
            for (int i = 0; i < lstId.size(); i++) {
                List<UserMaterialEntity> uMaterial = Database2.getList("user_material", Arrays.asList("user_id", String.valueOf(user.getId()), "material_id", String.valueOf(lstId.get(i))), "", UserMaterialEntity.class);
                if (uMaterial != null && uMaterial.size() > 0) {
//                        || Database.getList("user_material", Arrays.asList("user_id", String.valueOf(newUser.getId()), String.valueOf(lstId.get(i))), "", UserMaterialEntity.class).size() == 0) {
//                    if(Database.update("newUser", Arrays.asList("atomic", String.valueOf(atomicRemain)), Arrays.asList("id", String.valueOf(newUser.getId())))){
                    if (Database2.update("user_material", Arrays.asList("number", String.valueOf(newLstNumber.get(i))), Arrays.asList("user_id", String.valueOf(user.getId()), "material_id", String.valueOf(lstId.get(i))))) {
                        user.getDbUser().setAtomic(atomicRemain);
                        user.getRes().getMMaterial().get(lstId.get(i)).setNumber(newLstNumber.get(i));
                    } else {
                        addErrMessage(getLang(Lang.err_system_down));
                        return;
                    }
//                    }
//                    sendMessage(channel, service, CommonProto.getCommonVectorProto(Arrays.asList(100, 1, 100, 2, 100, 3, 100, 4, 100, 5, 100), null));
                } else {
//                    if(Database.update("newUser", Arrays.asList("atomic", String.valueOf(atomicRemain)), Arrays.asList("id", String.valueOf(newUser.getId())))){
                    if (Database2.insert("user_material", Arrays.asList("number", "user_id", "material_id"), Arrays.asList(String.valueOf(newLstNumber.get(i)), String.valueOf(user.getId()), String.valueOf(lstId.get(i)))) != -1) {
                        user.getDbUser().setAtomic(atomicRemain);
                        UserMaterialEntity material = new UserMaterialEntity();
                        material.setNumber(newLstNumber.get(i));
                        material.setMaterialId(lstId.get(i).byteValue());
                        material.setUserId(user.getId());
                        user.getRes().getMMaterial().put(lstId.get(i), material);
                    } else {
                        addErrMessage(getLang(Lang.err_system_down));
                        return;
                    }
//                    }
                }
            }
            List<Long> rs = new ArrayList<Long>();
            rs.add((long) user.getDbUser().getAtomic());
            String desc = "numBerAtomic: " + user.getDbUser().getAtomic() + ";";
            for (int i = 0; i < lstId.size(); i++) {
                desc += " materialId: ";
                rs.add((long) lstId.get(i));
                desc += lstId.get(i);
                desc += "; Number: ";
                rs.add((long) newLstNumber.get(i));
                desc += newLstNumber.get(i) + ";";
            }
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(rs, null));
            Actions.save(user.getDbUser(), "buy_material", "buy_material: ", Actions.convertToLogString(Arrays.asList("desc", desc)));
        } else {
            addErrMessage();
            return;
        }
    }

    void upgradeAccessories() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int accessId = aLong.get(0).intValue();
        boolean upgrade = false;
        if (user.getRes().getMAccessories().get(accessId) == null || user.getRes().getMAccessories().get(accessId).getLevel() <= 0) {
            addErrMessage(getLang(Lang.accessory_not_own));
            return;
        }
        if (user.getRes().getMAccessories().get(accessId).getLevel() >= Resources.accessoriesUpgrade.getLevelMax()) {
            addErrMessage(getLang(Lang.err_max_level));
            return;
        }
        if (Resources.mAccessories.get(user.getRes().getMAccessories().get(accessId).getAccessoriesId()) == null) {
            addErrMessage(getLang(Lang.accessory_not_own));
            return;
        }
        int levelAccept = user.getRes().getMAccessories().get(accessId).getLevel() + 1;
        long goldRequire = Resources.mAccessories.get(user.getRes().getMAccessories().get(accessId).getAccessoriesId()).getRank() * levelAccept * Resources.accessoriesUpgrade.getGoldBase();
        //
        if (goldRequire != 0) {
            JSONArray arr = user.getAction().addMoney(this, Constans.PRICE_GOLD, -goldRequire);
            if (arr == null) {
                return;
            }
            List<Long> tmp = Bonus.receiveListItem(user, arr, "upgrade_accessory");
            if (tmp.isEmpty()) {
                addErrMessage();
                return;
            }
        }
        List<Long> rs = new ArrayList<Long>();
        if (Database2.update("user_accessories", Arrays.asList("level", String.valueOf(levelAccept)), Arrays.asList("id", String.valueOf(accessId)))) {
            user.getRes().getMAccessories().get(accessId).setLevel(levelAccept);
        } else {
            addErrMessage();
            return;
        }

        rs.add((long) accessId);
        rs.add((long) levelAccept);
        rs.add(user.getDbUser().getGold());
//            if (upgrade) {
//                CfgNewMission.addMission(user.getDbUser(), user.getChannel(), MissionObject.UPGRADEBOOMMISSION, 1);
//            }
        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(rs, null));
        String desc = "accessoryId:" + accessId + "; accessoryImage: " + user.getRes().getMAccessories().get(accessId).getAccessoriesId() + "; BombLevel: " + user.getRes().getMAccessories().get(accessId).getLevel() + "";
        Actions.save(user.getDbUser(), "upgrade_accesory", "upgrade_accesory: ", Actions.convertToLogString(Arrays.asList("desc", desc)));

    }

    void upgradeBomb() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int bombId = aLong.get(0).intValue();
        int amulet = aLong.get(1).intValue();
        if (aLong.size() <= 2) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        if (aLong.size() > 5) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        boolean upgrade = false;
        UserItemEntity amuletItem = user.getRes().getMItem().get(ResShopItem.LUCKY_AMULET);
        if (amulet < 0) {
            addErrMessage(getLang(Lang.err_not_enough));
            return;
        }
        if (amulet > 0 && (amuletItem == null || amuletItem.getNumber() < amulet)) {
            addErrMessage(getLang(Lang.err_not_enough));
            return;
        }
        if (user.getRes().getMBomb().get(bombId) == null) {
            addErrMessage(getLang(Lang.bomb_not_own));
            return;
        }
        if (user.getRes().getMBomb().get(bombId).getLevel() > 0) {
            upgrade = true;
        }
        Map<Integer, Integer> mMaterial = new HashMap<Integer, Integer>();
        int bombType = Resources.getBomb(user.getRes().getMBomb().get(bombId).getBombId()).getType();
        for (int i = 2; i < aLong.size(); i++) {
            if (aLong.get(i).intValue() <= 0) {
                addErrMessage(getLang(Lang.err_param));
                return;
            }
            if (bombType != Resources.getMaterial(aLong.get(i).intValue()).getType()) {
                addErrMessage(getLang(Lang.material_not_own));
                return;
            }
            if (mMaterial.containsKey(aLong.get(i).intValue())) {
                mMaterial.put(aLong.get(i).intValue(), mMaterial.get(aLong.get(i).intValue()) + 1);
            } else {
                mMaterial.put(aLong.get(i).intValue(), 1);
            }
        }

        //
        int rate = 1;
        if (BoomConfig.rankboom == 0) {
            rate = Resources.getBomb(user.getRes().getMBomb().get(bombId).getBombId()).getRank();
        }
        long goldRequire = Resources.mBombUpgrade.get(user.getRes().getMBomb().get(bombId).getLevel() + 1).getGold() * rate;
        if (goldRequire > user.getDbUser().getGold()) {
            addErrMessage(getLang(Lang.err_not_enough_gold));
            return;
        }
        if (user.getRes().getMBomb().get(bombId).getLevel() >= Resources.aBombUpgrade.get(Resources.aBombUpgrade.size() - 1).getLevel()) {
            addErrMessage(getLang(Lang.err_max_level));
            return;
        }
        int point = 0;
        for (Map.Entry<Integer, Integer> entry : mMaterial.entrySet()) {
            point += Resources.mMaterial.get(entry.getKey()).getPoint() * entry.getValue();
            if (entry.getValue() > user.getRes().getMMaterial().get(entry.getKey()).getNumber()) {
                addErrMessage(getLang(Lang.err_not_enough));
                return;
            }
        }
        point += (amulet * 30 * (Resources.mBombUpgrade.get(user.getRes().getMBomb().get(bombId).getLevel() + 1).getRequirePoint())) / 100;
        int percent = new Random().nextInt(Resources.mBombUpgrade.get(user.getRes().getMBomb().get(bombId).getLevel() + 1).getRequirePoint()) + 1;
        JSONArray arr = user.getAction().addMoney(this, Constans.PRICE_GOLD, -goldRequire);
        if (arr == null) {
            addErrMessage();
            return;
        }
        List<Long> tmp = Bonus.receiveListItem(user, arr, "upgrade_bomb");
        if (tmp.isEmpty()) {
            addErrMessage();
            return;
        }
        if (amulet > 0) {
            if (Database2.update("user_item", Arrays.asList("number", String.valueOf(user.getRes().getMItem().get(ResShopItem.LUCKY_AMULET).getNumber() - amulet)), Arrays.asList("user_id", String.valueOf(user.getId()), "item_id", String.valueOf(ResShopItem.LUCKY_AMULET)))) {
                user.getRes().getMItem().get(ResShopItem.LUCKY_AMULET).setNumber(user.getRes().getMItem().get(ResShopItem.LUCKY_AMULET).getNumber() - amulet);
            } else {
                addErrMessage();
                return;
            }
        }
        for (Map.Entry<Integer, Integer> entry : mMaterial.entrySet()) {
            if (!Database2.update("user_material", Arrays.asList("number", String.valueOf(user.getRes().getMMaterial().get(entry.getKey()).getNumber() - entry.getValue())), Arrays.asList("user_id", String.valueOf(user.getId()), "material_id", String.valueOf(entry.getKey())))) {
                addErrMessage();
                return;
            } else {
                user.getRes().getMMaterial().get(entry.getKey()).setNumber(user.getRes().getMMaterial().get(entry.getKey()).getNumber() - entry.getValue());
            }
        }
        List<Long> rs = new ArrayList<Long>();
        if (point >= percent) {
            if (Database2.update("user_bomb", Arrays.asList("level", String.valueOf(user.getRes().getMBomb().get(bombId).getLevel() + 1)), Arrays.asList("id", String.valueOf(bombId)))) {
                rs.add(1l);
                user.getRes().getMBomb().get(bombId).setLevel(user.getRes().getMBomb().get(bombId).getLevel() + 1);
                if (user.getRes().getMBomb().get(bombId).getLevel() + 1 >= 10) {
//                    SystemSlide.addMessage("<color=\"#00b939\">" + user.getDbUser().getName() + "</color>" + " nâng cấp boom " + "<color=\"#00b939\">" + Resources.getBomb(user.getRes().getMBomb().get(bombId).getBombId()).getName() + "</color>" + " thành công lên cấp " + user.getRes().getMBomb().get(bombId).getLevel());
                    SystemSlide.addMessage(String.format(getLang(Lang.upgrade_boom_level_wish_from_system), user.getDbUser().getName(), Resources.getBomb(user.getRes().getMBomb().get(bombId).getBombId()).getName(), user.getRes().getMBomb().get(bombId).getLevel()));
                }
            } else {
                addErrMessage();
                return;
            }
        } else {
            rs.add(0l);
        }

        rs.add((long) user.getRes().getMBomb().get(bombId).getLevel());
        rs.add(user.getDbUser().getGold());
        rs.add((long) (amuletItem == null ? 0 : amuletItem.getNumber()));
        String desc = "; ";
        for (Map.Entry<Integer, Integer> entry : mMaterial.entrySet()) {

            rs.add((long) entry.getKey());
            desc += "materialId: " + entry.getKey();
            rs.add((long) user.getRes().getMMaterial().get(entry.getKey()).getNumber());
            desc += ": number: " + user.getRes().getMMaterial().get(entry.getKey()).getNumber() + "; ";
        }
        if (upgrade) {
            CfgNewMission.addMission(user.getDbUser(), MissionObject.UPGRADEBOOMMISSION, 1);
        }
        Actions.save(user.getDbUser(), "upgrade_bomb", "upgrade_bomb: ", Actions.convertToLogString(Arrays.asList("desc", "BombId:" + bombId + "; BombImage: " + user.getRes().getMBomb().get(bombId).getBombId() + "; BombLevel: " + user.getRes().getMBomb().get(bombId).getLevel() + desc)));
        addResponse(CommonProto.getCommonLongVectorProto(rs, null));
    }

    void sellMaterial() {

        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        List<Integer> lstId = new ArrayList<Integer>();
        List<Integer> lstNumber = new ArrayList<Integer>();
        for (int i = 0; i < aLong.size() - 1; i = i + 2) {
            if (aLong.get(i + 1).intValue() <= 0) {
                addErrMessage(getLang(Lang.err_param));
                return;
            }
            lstId.add(aLong.get(i).intValue());
            lstNumber.add(aLong.get(i + 1).intValue());
        }
        if (lstId.size() != lstNumber.size()) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        for (int i = 0; i < lstId.size(); i++) {
            if (user.getRes().getMMaterial().get(lstId.get(i)).getNumber() < lstNumber.get(i)) {
                addErrMessage(getLang(Lang.err_not_enough));
                return;
            }
        }
        int totalAtomic = 0;
        for (int i = 0; i < lstId.size(); i++) {
            totalAtomic += Resources.mMaterial.get(lstId.get(i)).getAtomic() * lstNumber.get(i);
        }
//            if(totalAtomicRequire>newUser.getDbUser().getAtomic()){
//                addErrMessage( "Bạn không đủ nguyên liệu!");
//                return;
//            }
        debug("totalAtomic-->" + totalAtomic);
        int atomicnew = user.getDbUser().getAtomic() + totalAtomic;
        List<Integer> newLstNumber = new ArrayList<Integer>();
        for (int i = 0; i < lstNumber.size(); i++) {
            newLstNumber.add(user.getRes().getMMaterial().get(lstId.get(i)).getNumber() - lstNumber.get(i));
            debug("nl new --->" + (user.getRes().getMMaterial().get(lstId.get(i)).getNumber() - lstNumber.get(i)));
        }
        if (Database2.update("user", Arrays.asList("atomic", String.valueOf(atomicnew)), Arrays.asList("id", String.valueOf(user.getId())))) {
            for (int i = 0; i < lstId.size(); i++) {
                List<UserMaterialEntity> uMaterial = Database2.getList("user_material", Arrays.asList("user_id", String.valueOf(user.getId()), "material_id", String.valueOf(lstId.get(i))), "", UserMaterialEntity.class);
                if (uMaterial != null && uMaterial.size() > 0) {
                    if (Database2.update("user_material", Arrays.asList("number", String.valueOf(newLstNumber.get(i))), Arrays.asList("user_id", String.valueOf(user.getId()), "material_id", String.valueOf(lstId.get(i))))) {
                        user.getDbUser().setAtomic(atomicnew);
                        user.getRes().getMMaterial().get(lstId.get(i)).setNumber(newLstNumber.get(i));
                    } else {
                        addErrMessage();
                        return;
                    }
//                        }
//                    sendMessage(channel, service, CommonProto.getCommonVectorProto(Arrays.asList(100, 1, 100, 2, 100, 3, 100, 4, 100, 5, 100), null));
                } else {
//                        if(Database.update("newUser", Arrays.asList("atomic", String.valueOf(atomicRemain)), Arrays.asList("id", String.valueOf(newUser.getId())))){
//                    if (Database.insert("user_material", Arrays.asList("number", String.valueOf(newLstNumber.get(i))), Arrays.asList("user_id", String.valueOf(newUser.getId()), "material_id", String.valueOf(lstId.get(i)))) != -1) {
                    if (Database2.insert("user_material", Arrays.asList("number", "user_id", "material_id"), Arrays.asList(String.valueOf(newLstNumber.get(i)), String.valueOf(user.getId()), String.valueOf(lstId.get(i)))) != -1) {

                        user.getDbUser().setAtomic(atomicnew);
                        user.getRes().getMMaterial().get(lstId.get(i)).setNumber(newLstNumber.get(i));
                    } else {
                        addErrMessage();
                        return;
                    }
//                        }
                }
            }
            List<Long> rs = new ArrayList<Long>();
            rs.add((long) user.getDbUser().getAtomic());
            String desc = "";
            for (int i = 0; i < lstId.size(); i++) {
                desc += "materialId: " + lstId.get(i) + "; numberRemain: " + newLstNumber.get(i) + " ;";
                rs.add((long) lstId.get(i));
                rs.add((long) newLstNumber.get(i));
            }
            debug(rs.toString());
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(rs, null));
            Actions.save(user.getDbUser(), "sale_material", "sale_material: ", Actions.convertToLogString(Arrays.asList("desc", desc)));

        } else {
            addErrMessage();
            return;
        }

//        sendMessage(channel, service, CommonProto.getCommonVectorProto(Arrays.asList(100, 1, 100, 2, 100, 3, 100, 4, 100, 5, 100), null));
    }


    //<editor-fold desc="Database Access">
    public boolean updateAvatar(UserInfo user, int newAvatar, long goldRequired) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update user set avatar=:avatar, gold=gold-:gold where id=" + user.getId());
            query.setString("avatar", String.valueOf(user.getDbUser().getMAvatar().toString()));
            query.setLong("gold", goldRequired);
            query.executeUpdate();
            //
            if (goldRequired > 0) session.save(new UserIconEntity(user.getId(), newAvatar));
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }
    //</editor-fold>

    //<editor-fold desc="Proto">
    //</editor-fold>
}
