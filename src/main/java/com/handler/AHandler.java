package com.handler;

import com.bem.boom.object.MessageSlide;
import com.bem.boom.object.SystemSlide;
import com.bem.config.CfgServer;
import com.bem.config.lang.Lang;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.UserInfo;
import com.bem.util.ChUtil;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.google.protobuf.AbstractMessage;
import com.google.protobuf.GeneratedMessage;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.slib_Logger;
import com.proto.GGProto;
import grep.helper.StringHelper;
import io.netty.channel.Channel;
import org.hibernate.Session;
import org.slf4j.Logger;

import java.util.*;

/**
 * Created by <PERSON>shi on 12/5/2014.
 */
public abstract class AHandler extends IAction {
    static List<Integer> loginServices = Arrays.asList(LOGIN_FACEBOOK, LOGIN_QUICK, LOGIN_SESSION, INDEX, RELOAD_CONFIG, LOGIN_BOT, JOIN_BATTLE, BOT_JOIN_BATTLE, LOG_OUT);

    public abstract void initAction(Map<Integer, AHandler> mGameHandler);

    public abstract AHandler newInstance();

    GGProto.ResponseData.Builder response = GGProto.ResponseData.newBuilder();
    protected int service = 0;
    protected String session;
    protected byte[] srcRequest;
    protected Channel channel;
    protected UserInfo user;

    public Channel getChannel() {
        return channel;
    }

    public void doAction(Channel channel, String session, int service, byte[] srcRequest) {
        this.session = session;
        this.service = service;
        this.channel = channel;
        this.srcRequest = srcRequest;

        if (!StringHelper.isEmpty(session)) {
            user = UserMonitor.getUser(session);
            if (user == null && !loginServices.contains(service)) {
                addResponse(LOGIN_REQUIRE, null);
                return;
            }
            if (user != null && user.getId() != 8) {
                Channel oldChannel = Online.getChannel(user.getId());
                if (oldChannel == null) Online.addChannel(user.getId(), channel);
                if (oldChannel != null && !oldChannel.id().equals(channel.id())) {
                    Online.clearChannel(oldChannel);
                    Online.addChannel(user.getId(), channel);
                    ChUtil.set(channel, Constans.KEY_USER, user);
                }
            }
            doSubAction();
        }
    }

    public void checkSlideMessage() {
        try {
            if (!CfgServer.isSubmit() && user != null && user.getDbUser() != null) {
                List<MessageSlide> aSlide = new ArrayList<>();
                aSlide.addAll(SystemSlide.entryList(null));
                //                String[] ms = {". Kinh thật!", ". Chúc mừng nào!", ". Thật đáng chúc mừng!", ". Thật không thể tin được!", ". Thật có thực lực!", ". Thật là phong cách!", ". Làm cả server choáng váng rồi!", ". Cũng ra gì đấy!", ". Cho lên sóng an ủi tí!", ". Người người ghen tị!"};
                String[] ms = {". Amazing!", ". Congratulations!", ". Truly congratulations!", ". You've stunned the whole server!", ". Not bad at all!", ". Give some comfort on the stream!", ". Everyone's jealous!", ". Unbelievable!", ". Truly capable!", ". So stylish!"};
                int count = 0;
                for (int i = 0; i < aSlide.size(); i++) {
                    if (aSlide.get(i).time >= user.getTimeLastReceiveSlide()) {
                        count++;
                        addResponse(MSG_SLIDE, CommonProto.getCommonVectorProto(null, Arrays.asList(aSlide.get(i).message + ms[new Random().nextInt(ms.length)])));
                    }
                    //                    if (!user.getOs().equalsIgnoreCase("android") && count == 2) {
                    //                        addResponse(MSG_SLIDE, CommonProto.getCommonVectorProto(null, Arrays.asList("Nạp thẻ điện thoại/tin nhắn vào game tại http://boomba.vn/nap")));
                    //                    }
                }

                user.setTimeLastReceiveSlide(System.currentTimeMillis());
            }
        } catch (Exception ex) {
        }
    }

    public abstract void doSubAction();

    protected Logger getLogger() {
        return slib_Logger.root();
    }

    protected void closeSession(Session session) {
        try {
            if (session != null) {
                session.close();
            }
        } catch (Exception ex) {
        }
    }

    protected void sendListMessage(Channel channel, List<Integer> services, List<GeneratedMessage> aMsg) {
        GGProto.ListAction.Builder builder = GGProto.ListAction.newBuilder();
        for (int i = 0; i < services.size(); i++) {
            GGProto.Action.Builder action = GGProto.Action.newBuilder();
            action.setService(services.get(i));
            action.setData(aMsg.get(i).toByteString());
            builder.addAAction(action);
        }
        Util.sendProtoData(channel, builder.build(), LIST_ACTION, System.currentTimeMillis());
    }

    protected void addTopupMessage(String msg) {
        addResponse(TOP_UP_REQUIRE, CommonProto.getCommonVectorProto(null, Arrays.asList("Thông báo", msg)));
    }

    protected void sendStrMessage(Channel channel, int service, String msg) {
        Util.sendStrData(channel, msg, System.currentTimeMillis());
    }

    //    protected void sendMessage(Channel channel, int service, GeneratedMessage msg) {
    //        Util.sendProtoData(channel, msg, service, System.currentTimeMillis());
    //    }

    public void addSlideMessage(String msg) {
        addResponse(MSG_SLIDE, CommonProto.getCommonVectorProto(null, Arrays.asList(msg)));
    }

    public void addPopupMessage(String msg) {
        addResponse(MSG_POPUP, CommonProto.getCommonVectorProto(null, Arrays.asList(msg)));
    }

    protected void sendToastMessage(Channel channel, String msg) {
        Util.sendProtoData(channel, CommonProto.getCommonVectorProto(null, Arrays.asList(msg)), MSG_TOAST, System.currentTimeMillis());
    }

    public void addErrMessage() {
        //        addErrMessage(user.getLang().get(Lang.err_system_down, user.getDbUser().getSelectLang()));
        addErrMessage(getLang(Lang.err_system_down));
    }

    public void addErrMessage(String msg) {
        addResponse(MSG_TOAST, CommonProto.getCommonLongVectorProto(null, Arrays.asList(msg)));
    }

    public void addShopMessage(String msg) {
        addResponse(SHOP_REQUIRE, CommonProto.getCommonVectorProto(null, Arrays.asList(getLang(Lang.err_system_down), msg)));
    }

    public GGProto.ResponseData.Builder getResponse() {
        return response;
    }

    public void addAction(GGProto.Action action) {
        debug(" <--- " + action.toString());
        response.addActions(action);
    }

    public void addResponse(AbstractMessage msg) {
        addResponse(service, msg);
    }

    public void addResponse(int service, AbstractMessage msg) {
        GGProto.Action.Builder builder = GGProto.Action.newBuilder();
        builder.setService(service);
        if (msg != null) {
            builder.setData(msg.toByteString());
        }
        if (msg == null) {
            debug(" <--- " + service + " --- no data");
        } else {
            debug(" <--- " + service + " --- \n" + msg.toString());
        }
        response.addActions(builder.build());
    }

    public void sendMessage(Channel channel, int service, AbstractMessage msg) {
        if (msg == null) {
            debug(" <--- " + service + " --- no data");
        } else {
            debug(" <--- " + service + " --- \n" + msg.toString());
        }
        Util.sendProtoData(channel, msg, service, System.currentTimeMillis());
    }

    static void debug(String msg) {
        if (CfgServer.debug)
            slib_Logger.root().debug(msg);
    }

    public String getLang(Lang lang, String key) {
        return lang.get(key);
    }

    public String getLang(String key) {
        Lang lang = user != null ? user.getLang() : CfgServer.lang;
        if (lang == null) lang = CfgServer.lang;
        return getLang(lang, key);
    }
}
