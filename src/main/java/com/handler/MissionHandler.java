package com.handler;

import com.bem.boom.object.AchievementInfo;
import com.bem.boom.object.MissionObject;
import com.bem.config.CfgAchievement;
import com.bem.config.CfgEvent;
import com.bem.config.CfgMissionFB;
import com.bem.config.CfgNewMission;
import com.bem.dao.mapping.UserEntity;
import com.bem.monitor.Bonus;
import com.bem.object.MissionInviteFb;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.cache.MainCache;
import com.google.gson.Gson;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import net.sf.json.JSONArray;

import java.util.*;

/**
 * Created by <PERSON><PERSON> on 12/11/2014.
 */
public class <PERSON><PERSON><PERSON><PERSON> extends <PERSON><PERSON><PERSON><PERSON> {

    @Override
    public void initAction(Map<Integer, <PERSON>Hand<PERSON>> mGameHandler) {
        List<Integer> services = Arrays.asList(MISSION_STATUS, RECEIVE_MISSION, NOTIFY_MISSION,
                ACHIEVEMENT_STATUS, ACHIEVEMENT_STATUS_DETAIL, ACHIEVEMENT_STATUS_DETAIL_NEWSTRING, RECIEVE_ACHIEVEMENT, NOTIFY_ACHIEVEMENT, RANK_TROPHY_STATUS, VIEW_ARWARD_RANK_TROPHY,
                INVITE_FB_STATUS, INVITE_FB_SEND, INVITE_FB_RECEIVE);
        services.forEach(service -> mGameHandler.put(service, getInstance()));
    }

    static MissionHandler instance;

    public static MissionHandler getInstance() {
        if (instance == null) {
            instance = new MissionHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new MissionHandler();
    }

    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case MISSION_STATUS:
                    missionStatus();
                    break;
                case RECEIVE_MISSION:
                    recieveAwardMission();
                    break;
                case ACHIEVEMENT_STATUS:
                    achievementStatus();
                    break;
                case ACHIEVEMENT_STATUS_DETAIL:
                    achievementStatusDetail();
                    break;
                case ACHIEVEMENT_STATUS_DETAIL_NEWSTRING:
                    achievementStatusDetailNew();
                    break;
                case RANK_TROPHY_STATUS:
                    rankTrophyStatus();
                    break;
                case RECIEVE_ACHIEVEMENT:
                    recieveAwardAchievement();
                    break;
                case INVITE_FB_STATUS:
                    inviteFBStatus();
                    break;
                case INVITE_FB_SEND:
                    inviteFBSend();
                    break;
                case INVITE_FB_RECEIVE:
                    inviteFBReceive();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void inviteFBStatus() {
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        MissionInviteFb inviteFb = user.getUData().getInviteFb();
        int lastMissionNumber = 0;
        for (int i = 0; i < CfgMissionFB.config.invite.size(); i++) {
            CfgMissionFB.MissionFB mission = CfgMissionFB.config.invite.get(i);
            GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
            tmp.addAString(mission.title);
            tmp.addANumber(i + 1);
            if (inviteFb.receive >= i + 1) { // da nhan nhiem vu nay roi
                tmp.addANumber(CfgMissionFB.MISSION_STATUS_RECEIVED);
                tmp.addANumber(mission.number);
            } else if (inviteFb.invites.size() >= mission.number) {
                tmp.addANumber(CfgMissionFB.MISSION_STATUS_WAIT_RECEIVE);
                tmp.addANumber(mission.number);
            } else if (lastMissionNumber <= inviteFb.invites.size() && inviteFb.invites.size() < mission.number) {
                tmp.addANumber(CfgMissionFB.MISSION_STATUS_OPEN);
                tmp.addANumber(inviteFb.invites.size());
            } else {
                tmp.addANumber(CfgMissionFB.MISSION_STATUS_CLOSE);
                tmp.addANumber(inviteFb.invites.size());
            }
            tmp.addANumber(mission.number);
            tmp.addAllANumber(mission.bonus);
            lastMissionNumber = mission.number;
            builder.addAVector(tmp);
        }
        addResponse(INVITE_FB_STATUS, builder.build());
    }

    void inviteFBSend() {
        List<String> fbIds = CommonProto.parseCommonVector(srcRequest).getAStringList();
        MissionInviteFb inviteFb = user.getUData().getInviteFb();
        int oldSize = inviteFb.invites.size();
        fbIds.forEach(ids -> {
            if (!inviteFb.invites.contains(ids)) inviteFb.invites.add(ids);
        });
        if (inviteFb.invites.size() != oldSize) inviteFb.update(user.getId());
        inviteFBStatus();
    }

    void inviteFBReceive() {
        int missionId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        MissionInviteFb inviteFb = user.getUData().getInviteFb();
        if (missionId <= inviteFb.receive) addErrMessage("Bạn đã nhận thưởng nhiệm vụ này rồi");
        else {
            CfgMissionFB.MissionFB mission = CfgMissionFB.config.invite.get(inviteFb.receive);
            if (mission.number > inviteFb.invites.size()) addErrMessage("Chưa hoàn thành nhiệm vụ");
            else {
                inviteFb.receive++;
                if (inviteFb.update(user.getId())) {
                    List<Long> aLong = Bonus.receiveListItem(user, JSONArray.fromObject(new Gson().toJson(mission.bonus)), "invite_fb");
                    if (aLong.isEmpty()) addErrMessage();
                    else {
                        addResponse(CommonProto.getCommonLongVectorProto(aLong, null));
                        inviteFBStatus();
                    }
                } else inviteFb.receive--;
            }
        }
    }

    void rankTrophyStatus() {
        int type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        int number = 50;
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        builder.addANumber((long) type);
        if (type == 0) {
            CfgNewMission.dailyRankTrophy();
            builder.addANumber((long) 43200);
            for (int i = 0; i < Math.min(CfgNewMission.lstHm.size(), number); i++) {
                long id = (long) CfgNewMission.lstHm.get(i).getId();
                if (id > 3) {
                    id = id - 3;
                }
                builder.addANumber(id);
                builder.addANumber((long) (100 - new Random().nextInt(200)));
                builder.addANumber((long) (1 + new Random().nextInt(5)));
                builder.addANumber((long) (1 + new Random().nextInt(15)));
                builder.addANumber((long) CfgNewMission.lstHm.get(i).getTrophy());
                if (CfgNewMission.lstHm.get(i).getScreenName() != null && !CfgNewMission.lstHm.get(i).getScreenName().trim().equalsIgnoreCase("")) {
                    builder.addAString(CfgNewMission.lstHm.get(i).getScreenName());
                } else {
                    builder.addAString(CfgNewMission.lstHm.get(i).getUsername());
                }
                builder.addAString("AllStar");

            }
//        }else if( type == 1){
//            CfgNewMission.dailyRankTrophy();
//            List<ClanEntity> lstClan= new ClanWarDAO().selectTopTrophyClan();
//            for (int i = 0; i < Math.min(lstClan.size(), number); i++) {
//                long id = ((long)i+1);
//                builder.addALong(id);
//                builder.addALong((long) (100 - new Random().nextInt(200)));
//                builder.addALong((long) (1 + new Random().nextInt(5)));
//                builder.addALong((long) lstClan.get(i).getMember());
//                builder.addALong((long) 50);
//                builder.addALong((long) lstClan.get(i).getTrophy());
//                builder.addAString(lstClan.get(i).getName());
//            }
        }
        sendMessage(channel, service, builder.build());
    }


    public void achievementStatus() {
        user.loadEvent();

        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        JSONArray aChieveStatus = JSONArray.fromObject(user.getDbUser().getAchievementStatus());
        JSONArray aChieveRecieve = JSONArray.fromObject(user.getDbUser().getAchievementReceive());
        if (aChieveStatus.size() < CfgAchievement.aAch.get(user.getDbUser().getServer()).size()) {
            for (int i = aChieveStatus.size(); i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {
                aChieveStatus.add(0);
            }
            Database2.update("user", Arrays.asList("achievement_status", aChieveStatus.toString()), Arrays.asList("id", String.valueOf(user.getId())));
        }

        if (aChieveRecieve.size() < CfgAchievement.aAch.get(user.getDbUser().getServer()).size()) {
            for (int i = aChieveRecieve.size(); i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {
                aChieveRecieve.add(0);
            }
            user.getDbUser().setAchievementReceive(aChieveRecieve.toString());
            if (!Database2.update("user", Arrays.asList("achievement_receive", aChieveRecieve.toString()), Arrays.asList("id", String.valueOf(user.getId())))) {
                addErrMessage();
                return;
            }
        }
        List<Long> vlongF = new ArrayList<>();
        List<Long> vlongS = new ArrayList<>();
        List<String> vStringF = new ArrayList<>();
        List<String> vStringS = new ArrayList<>();

        for (int i = 0; i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {
            List<Long> vlong = new ArrayList<>();
            List<String> vString = new ArrayList<>();
            AchievementInfo aInf = CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i);
            if (aInf.getStatus() <= 0 || (aInf.getStatus() == 1 && !aInf.inTime())) {
                Logs.warn(user.getId() + "---- id=%s status=%s %s ".formatted(aInf.getId(), aInf.getStatus(), String.valueOf(aInf.inTime())));
                continue;
            }
            if (CfgAchievement.newMission.contains(aInf.getId())) {
                if (user.getVersion().compareToIgnoreCase("1.2.3") <= 0) {
                    continue;
                }
            }
            long curentNumber = CfgAchievement.getCurentNumberAchievement(user, aInf, i);
            int hasRecieveNumber = CfgAchievement.getRecieveNumberAchievement(user, aInf, i);

            int status = 0;
            if ((hasRecieveNumber < aInf.getARequired().length) && curentNumber >= aInf.getARequired()[hasRecieveNumber]) {
                status = 1;
            }
            if (aInf.getStatus() > 0) {
                vlong.add((long) aInf.getId());
                vlong.add((long) curentNumber);
                vlong.add((long) status);
                if (!CfgAchievement.newMission.contains(aInf.getId())) {
                    vString.add(aInf.getName());
//                    vString.add(aInf.getName());
                    if (aInf.getId() >= CfgAchievement.CHARACTER + 6) {
                        vString.add(aInf.getName() + "\n + Điểm tích lũy hiện tại: " + curentNumber);
                    } else {
                        vString.add(aInf.getName());
                    }
//                    }
                } else {
                    if (aInf.getId() == CfgAchievement.NAP_X2) {
                        vString.add("Nạp X2");
                        vString.add("Chúc mừng năm mới");
                    } else if (aInf.getId() == CfgAchievement.VIP) {
                        vString.add("VIP BONUS!");
                        vString.add(aInf.getName() + "\n + Current Vip: " + user.getDbUser().getVip());
                    } else if (aInf.getId() == CfgAchievement.ROTAGEJACKPOT) {
                        vString.add("Quay thưởng tích lũy!");
                        vString.add(aInf.getName() + "\n + quay tích lũy hiện tại: " + user.getDbUser().checkHasNumberRotage());
                    } else if (aInf.getId() == CfgAchievement.NAP) {
                        vString.add("Nạp Tích lũy!");
                        vString.add(aInf.getName() + "\n + mức tích lũy nạp hiện tại: " + user.getDbUser().getSumGemIntEvent());
                    } else if (aInf.getId() == CfgAchievement.TOP_TROPHY) {
                        String desc = aInf.getName() + "\n + Bảng top cúp tích lũy người chơi: \n";
                        desc += aInf.getName() + "\n";
                        List<UserEntity> lst = MainCache.getInstance().aUserTrophyTime;
                        for (int j = 0; j < lst.size(); j++) {
                            desc += (j + 1) + ": " + lst.get(j).getName() + " : " + lst.get(j).getTrophy() + " cúp\n";
                        }
                        vString.add(aInf.getName());
                        vString.add(desc);
                    } else if (aInf.getId() == CfgAchievement.TOP_STAR_MAP) {
                        String desc = "";
                        JSONArray arr = JSONArray.fromObject(aInf.getDesc());
                        if (arr != null && arr.size() > 0) {
                            desc += arr.getString(0);
                        }
                        vString.add(aInf.getName());
                        vString.add(desc);
                    } else if (aInf.getId() == CfgAchievement.LIENTHANG) {
                        vString.add("Sự Kiện Liên Thắng!");
                        vString.add(aInf.getName() + "\n + liên thắng cao nhất hiện tại: " + user.getDbUser().getMaxwin());
                    } else if (aInf.getId() == CfgAchievement.CAPDO) {
                        vString.add("Rocky Event!");
                        vString.add(aInf.getName() + "\n + Current Point: " + user.getUEvent().getEvent(Constans.EVENT_TOP_CAPDO).getEvent().getValue());
                    } else if (aInf.getId() == CfgAchievement.SAOVANG) {
                        vString.add("Sự Kiện " + aInf.getName() + "!");
                        vString.add(aInf.getName() + "\n + Số lượng hiện tại: " + user.getUEvent().getEvent(Constans.EVENT_SAOVANG).getEvent().getValue());
                    }
                }

                if (aInf.getStatus() == 1) {
                    vString.add(aInf.getDateBegin() + ";" + aInf.getDateEnd());
                } else {
                    if (aInf.getId() == CfgAchievement.ACHIEVEMENT_ENERGY) {
                        String msg = "";
                        msg += aInf.getTime1() + ":00 - " + (aInf.getTime1() + 1) + ":00;";
                        msg += aInf.getTime2() + ":00 - " + (aInf.getTime2() + 1) + ":00;";
                        msg += aInf.getTime3() + ":00 - " + (aInf.getTime3() + 1) + ":00";
                        vString.add(msg);
                    } else {
                        vString.add("");
                    }
                }
            }
            if (CfgAchievement.newMission.contains(aInf.getId())) {
                vlongF.addAll(vlong);
                vStringF.addAll(vString);
                if (user.getVersion().compareTo("1.3.0") >= 0) {
                    vlongF.add((long) aInf.getImage());
                }
            } else {
                vlongS.addAll(vlong);
                vStringS.addAll(vString);
                if (user.getVersion().compareTo("1.3.0") >= 0) {
                    vlongS.add((long) aInf.getImage());
                }
            }
        }
        builder.addAllANumber(vlongF);
        builder.addAllANumber(vlongS);
        builder.addAllAString(vStringF);
        builder.addAllAString(vStringS);
        if (user.getVersion().compareToIgnoreCase("1.2.3") > 0) {
            for (int i = 0; i < CfgEvent.config.eventForLitmitUser.size(); i++) {
                if (CfgEvent.config.eventForLitmitUser.get(i).enable == 1 && CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id,user.getDbUser()) != null && CfgEvent.getEventForLimitUserInTime(CfgEvent.config.eventForLitmitUser.get(i).id, user, (int) (CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id,user.getDbUser()).getMiliStart() / (1000 * 60)))) {
                    List<Long> vlong = new ArrayList<>();
                    List<String> vString = new ArrayList<>();
                    AchievementInfo aInf = CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id,user.getDbUser());
                    long curentNumber = CfgAchievement.getCurentNumberAchievement(user, aInf, i);
                    int hasRecieveNumber = CfgAchievement.getRecieveNumberAchievement(user, aInf, i);

                    int status = 0;
                    if ((hasRecieveNumber < aInf.getARequired().length) && curentNumber >= aInf.getARequired()[hasRecieveNumber]) {
                        status = 1;
                    }
                    if (aInf.getStatus() > 0) {
                        vlong.add((long) aInf.getId());
                        vlong.add((long) curentNumber);
                        vlong.add((long) status);
                        vString.add(aInf.getName());
                        vString.add(aInf.getName() + "\n + mức tích lũy hiện tại: " + user.getDbUser().getSumGemIntEvent());
                        if (aInf.getStatus() == 1) {
                            vString.add(aInf.getDateBegin() + ";" + aInf.getDateEnd());
                        }
                    }
                    if (user.getVersion().compareTo("1.3.0") >= 0) {
                        vlongF.add((long) aInf.getImage());
                    }
                    builder.addAllANumber(vlong);
                    builder.addAllAString(vString);
                }
            }

        }

//        System.out.println("bulder-->" + builder.getANumberList().toString());
        sendMessage(channel, service, builder.build());
    }

    public void achievementStatusDetail() {
        int id = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        JSONArray aChieveStatus = JSONArray.fromObject(user.getDbUser().getAchievementStatus());
        JSONArray aChieveRecieve = JSONArray.fromObject(user.getDbUser().getAchievementReceive());
        if (id == CfgAchievement.ACHIEVEMENT_ENERGY) {

            builder.addANumber((long) CfgAchievement.aAch.get(user.getDbUser().getServer()).get(CfgAchievement.ACHIEVEMENT_ENERGY).getId());
            String rw = CfgAchievement.getEnergy(user.getDbUser());
            if (rw == null) {
                builder.addANumber((long) 1);
            } else {
                builder.addANumber((long) 2);
            }
            builder.addANumber(CfgAchievement.aAch.get(user.getDbUser().getServer()).get(CfgAchievement.ACHIEVEMENT_ENERGY).getARequired()[0]);
            var ach = CfgAchievement.aAch.get(user.getDbUser().getServer()).get(CfgAchievement.ACHIEVEMENT_ENERGY);
            for (int k = 0; k < ach.getABonus().get(0).size(); k++) {
                builder.addANumber(ach.getABonus().get(0).get(k));
            }
            builder.addANumber((long) -2);
        } else {

            for (int i = 1; i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {

                AchievementInfo aInf = CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i);
                if (aInf.getId() == id && aInf.getStatus() > 0) {
                    int curentNumber = aChieveStatus.getInt(i);
                    if (i == CfgAchievement.WIN_MAP) {
                        curentNumber = 0;
                        for (int j = 0; j < user.getUData().getMap().getNormal().size(); j++) {
                            if (user.getUData().getMap().getNormal().get(j) > 0) {
                                curentNumber++;
                            }
                        }
                        debug("winmap-->" + curentNumber);
                    } else if (i == CfgAchievement.COLLECT_STAR) {

                        curentNumber = user.getUData().getMap().getStar();
                        debug("COLECT_STAR-->" + curentNumber);
                    } else if (i == CfgAchievement.XEP_HANG) {
                        curentNumber = user.getDbUser().getRankId();
                    }
                    builder.addANumber((long) aInf.getId());

                    for (int j = 0; j < aInf.getARequired().length; j++) {
                        int notify = 0;// 0 chưa nhân , 1 có thể nhận , 2 nhận rồi
                        if (j < aChieveRecieve.getInt(i)) {
                            notify = 2;
                        } else if (curentNumber >= aInf.getARequired()[j] && j == aChieveRecieve.getInt(i)) {
                            notify = 1;
                        }

                        builder.addANumber((long) notify);
                        builder.addANumber(aInf.getARequired()[j]);
                        for (int k = 0; k < aInf.getABonus().get(j).size(); k++) {
                            builder.addANumber(aInf.getABonus().get(j).get(k));
                        }
                        builder.addANumber((long) -2l);
                    }

                    break;
                }
            }
        }
        sendMessage(channel, service, builder.build());

    }

    public void achievementStatusDetailNew() {
        int id = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        if (id == CfgAchievement.ACHIEVEMENT_ENERGY) {
            builder.addANumber((long) CfgAchievement.aAch.get(user.getDbUser().getServer()).get(CfgAchievement.ACHIEVEMENT_ENERGY).getId());
            String rw = CfgAchievement.getEnergy(user.getDbUser());
            if (rw == null) {
                builder.addANumber((long) 1);
            } else {
                builder.addANumber((long) 2);
            }
            JSONArray arr = JSONArray.fromObject(CfgAchievement.aAch.get(user.getDbUser().getServer()).get(CfgAchievement.ACHIEVEMENT_ENERGY).getDesc());
            builder.addAString("<color=black>" + arr.getString(0) + "</color>");
            var ach = CfgAchievement.aAch.get(user.getDbUser().getServer());
            for (int k = 0; k < ach.get(CfgAchievement.ACHIEVEMENT_ENERGY).getABonus().get(0).size(); k++) {
                builder.addANumber(CfgAchievement.aAch.get(user.getDbUser().getServer()).get(CfgAchievement.ACHIEVEMENT_ENERGY).getABonus().get(0).get(k));
            }
            builder.addANumber((long) -2);
        } else {
            boolean hasShow = false;
            for (int i = 0; i < CfgEvent.config.eventForLitmitUser.size(); i++) {
                if (CfgEvent.config.eventForLitmitUser.get(i).enable == 1 && CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id,user.getDbUser()) != null && id == CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id,user.getDbUser()).getId() && CfgEvent.getEventForLimitUserInTime(CfgEvent.config.eventForLitmitUser.get(i).id, user, (int) (CfgAchievement.getAchievement(CfgEvent.config.eventForLitmitUser.get(i).id,user.getDbUser()).getMiliStart() / (1000 * 60)))) {
                    hasShow = true;
                    break;
                }
            }


            for (int i = 1; i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {
                AchievementInfo aInf = CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i);

                if (aInf.getId() == id && (aInf.getStatus() > 0) || hasShow) {
                    long curentNumber = CfgAchievement.getCurentNumberAchievement(user, aInf, i);
                    builder.addANumber((long) aInf.getId());
//                    if (aInf.getId() != CfgAchievement.VIP && aInf.getId() != CfgAchievement.NAP) {
                    int hasRecieveNumber = CfgAchievement.getRecieveNumberAchievement(user, aInf, i);
                    for (int j = 0; j < aInf.getARequired().length; j++) {
                        int notify = 0;// 0 chưa nhân , 1 có thể nhận , 2 nhận rồi

                        if (curentNumber >= aInf.getARequired()[j] && j < hasRecieveNumber) {
                            notify = 2;
                        } else if (curentNumber >= aInf.getARequired()[j] && j == hasRecieveNumber) {
                            notify = 1;
                        }
                        JSONArray arr = JSONArray.fromObject(aInf.getDesc());
                        builder.addANumber((long) notify);
                        if (id == CfgAchievement.TOP_STAR_MAP || id == CfgAchievement.TOP_TROPHY) {
                        } else {
                            for (int k = 0; k < aInf.getABonus().get(j).size(); k++) {
                                builder.addANumber(aInf.getABonus().get(j).get(k));
                            }
                            builder.addAString("<color=black>" + arr.getString(j) + "</color>");
                            builder.addANumber((long) -2l);
                        }

                    }
                    break;
                }
            }
        }
        sendMessage(channel, service, builder.build());

    }

    public void missionStatus() {
        List<MissionObject> lstMission = CfgNewMission.dailyListMission(user.getDbUser());

//        // add cho mission hoan thanh tat ca mission
        int count = 0;
        for (int i = 0; i < lstMission.size(); i++) {
            if (lstMission.get(i).getNotifyStatus() > 0 && lstMission.get(i).getMissionType() != MissionObject.TOTALMISSION) {
                count++;
            }
        }
        for (int i = 0; i < lstMission.size(); i++) {
            if (lstMission.get(i).getMissionType() == MissionObject.TOTALMISSION && lstMission.get(i).getNotifyStatus() <= 0) {
                lstMission.get(i).increaseValue(count);
                break;
            }
        }
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        for (int i = 0; i < lstMission.size(); i++) {
            builder.addANumber((long) i);
            builder.addANumber((long) lstMission.get(i).getMissionType());
            builder.addANumber(lstMission.get(i).getNotifyStatus());
            debug("lstMission.get(i).getNotifyStatus()---->" + lstMission.get(i).getNotifyStatus());
            builder.addANumber(lstMission.get(i).getCurrentValue());
            builder.addANumber(lstMission.get(i).getRequiredValue());
            for (int j = 0; j < lstMission.get(i).getAward().size(); j++) {
                builder.addANumber(lstMission.get(i).getAward().getInt(j));
            }
            builder.addANumber(-2);
            builder.addAString(lstMission.get(i).getDesc());
        }
        sendMessage(channel, service, builder.build());
    }

    public void recieveAwardAchievement() {
        AchievementInfo achievement = null;
        int achievementId = -1;
        int index = -1;
        try {
            achievementId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
            for (int i = 0; i < CfgAchievement.aAch.get(user.getDbUser().getServer()).size(); i++) {
                if (CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i).getId() == achievementId) {
                    achievement = CfgAchievement.aAch.get(user.getDbUser().getServer()).get(i);
                    index = i;
                    break;
                }
            }
            if (index == -1) {
                addErrMessage("Không tồn tại thành tựu này");
                return;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            addErrMessage("Không tồn tại thành tựu này");
            return;
        }
        if (achievement == null) {
            addErrMessage("Không tồn tại thành tựu này");
            return;
        }
        if (achievement.getStatus() <= 0 || (achievement.getStatus() == 1 && !achievement.inTime())) {
            addErrMessage("Đã kết thúc sự kiện này");
            return;
        }
        if (achievementId == CfgAchievement.ACHIEVEMENT_ENERGY) {
            int hour = Integer.parseInt(Util.HourFormatTime(new Date()));
            if (hour == achievement.getTime1()) {
                hour = 1;
            } else if (hour == achievement.getTime2()) {
                hour = 2;
            } else if (hour == achievement.getTime3()) {
                hour = 3;
            } else {
                hour = -1;
            }
            String rw = null;
            if (hour >= 1) {
                rw = JCache.getInstance().getValue(hour + "" + user.getUsername() + ":rewardEventEnergy:");
            } else {
                rw = "true";
            }
            if (rw == null) {
                if (user.getUData().myEnergy.energy <= Math.max(100, user.getUData().myEnergy.maxEnergy)) {
                    GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
                    builder.addANumber((long) achievementId);
                    builder.addAllANumber(Bonus.receiveListItem(user, Bonus.convertListToJson(achievement.getABonus().get(0)), "achievement"));
                    JCache.getInstance().setValue(hour + "" + user.getUsername() + ":rewardEventEnergy:", "false", 12 * 60 * 60);
                    sendMessage(channel, service, builder.build());
                } else {
                    if (user.getUData().myEnergy.maxEnergy > 100) {
                        addErrMessage("Bạn đã nhận quá tối đa giới hạn thể lực miễn phí. Hãy tham gia thám hiểm để tiêu bớt thể lực nhé!");
                    } else {
                        addErrMessage("Bạn đã nhận quá tối đa 100 thể lực miễn phí. Hãy tham gia thám hiểm để tiêu bớt thể lực nhé!");


                    }
                }
            }
        } else {
            long curentNumber = CfgAchievement.getCurentNumberAchievement(user, achievement, index);
            boolean hasAward = false;
            JSONArray aw = new JSONArray();
            int countGet = CfgAchievement.getRecieveNumberAchievement(user, achievement, index);
            if (curentNumber >= achievement.getARequired()[countGet]) {
                try {
                    aw.addAll(achievement.getABonus().get(countGet));
                    hasAward = true;
                } catch (Exception ex) {
                    hasAward = false;
                }
            }
            if (hasAward) {
                GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
                builder.addANumber((long) achievementId);
                if (user.getDbUser().addAchievementReceive(achievementId, 1, user)) {
                    builder.addAllANumber(Bonus.receiveListItem(user, aw, "achievement|" + achievementId));

                    sendMessage(channel, service, builder.build());
                } else {
                    addErrMessage("Có lỗi xảy ra trong quá trình nhận thưởng bạn hãy thoát ra vào nhận lại");
                    return;
                }
            } else {
                addErrMessage("Thành tựu chưa hoàn thành");
                return;
            }
        }
    }

    public void recieveAwardMission() {
        MissionObject mission = null;
        int misionId = -1;
        try {
            misionId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
            debug("misionId---->" + misionId);
            List<MissionObject> lstMission = CfgNewMission.dailyListMission(user.getDbUser());
            mission = lstMission.get(misionId);
        } catch (Exception ex) {
            ex.printStackTrace();
            addErrMessage("Không tồn tại nhiệm vụ này");
            return;
        }
        if (mission == null) {
            addErrMessage("Không tồn tại nhiệm vụ này");
            return;
        }
        if (mission.isComplete()) {
            if (mission.isReceive(mission.getMissionType())) {

                GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
                builder.addANumber((long) misionId);
                builder.addAllANumber(Bonus.receiveListItem(user, mission.getAward(), "mission"));
                sendMessage(channel, service, builder.build());
            } else {
                addErrMessage("Đã nhận thưởng rồi");
                return;
            }
        } else {
            addErrMessage("Nhiệm vụ chưa hoàn thành");
            return;
        }

    }

}
