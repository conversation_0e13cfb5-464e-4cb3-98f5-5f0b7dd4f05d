package com.handler;

import com.bem.boom.BoomConfig;
import com.bem.boom.Resources;
import com.bem.config.*;
import com.bem.config.lang.Lang;
import com.bem.dao.UserDAO;
import com.bem.monitor.Bonus;
import com.bem.object.*;
import com.bem.util.Actions;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import net.sf.json.JSONArray;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by <PERSON>shi on 12/11/2014.
 */
public class MiniGameHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        mGameHandler.put(LUCKY_SPIN_STATUS, getInstance());
        mGameHandler.put(LUCKY_SPIN_QUAY, getInstance());
        mGameHandler.put(EXCHANGE_STAR, getInstance());
        mGameHandler.put(CHEST_STATUS, getInstance());
        mGameHandler.put(CHEST_QUAY, getInstance());
        mGameHandler.put(ONLINE_ATTENDANCE_STATUS, getInstance());
        mGameHandler.put(ONLINE_ATTENDANCE_GET, getInstance());
        mGameHandler.put(ONLINE_GIFT_GET, getInstance());
        mGameHandler.put(ONLINE_GIFT_STATUS, getInstance());

    }

    static MiniGameHandler instance;

    public static MiniGameHandler getInstance() {
        if (instance == null) {
            instance = new MiniGameHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new MiniGameHandler();
    }

    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case LUCKY_SPIN_STATUS:
                    quayStatus();
                    break;
                case LUCKY_SPIN_QUAY:
                    quay();
                    break;
                case EXCHANGE_STAR:
                    exchangeStar();
                    break;
                case CHEST_STATUS:
                    chestStatus();
                    break;
                case CHEST_QUAY:
                    openChest();
                    break;
                case ONLINE_ATTENDANCE_GET:
                    diemdanh();
                    break;
                case ONLINE_ATTENDANCE_STATUS:
                    diemdanhStatus();
                    break;
                case ONLINE_GIFT_GET:
                    onlineGiftGet();
                    break;
                case ONLINE_GIFT_STATUS:
                    onlineGiftStatus(ONLINE_GIFT_STATUS);
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void exchangeStar() {
        int id = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        debug("star-------->" + id);
//        int star = GameCfgLuckySpin.data.exchange_star_fee.get(id);
        int star = GameCfgLuckySpin.data.exchange_star_new[id][0];

        if (id >= 0 && star > 0 && user.getDbUser().getStar() >= star) {
//            JSONArray array = GameCfgLuckySpin.exchangeStar(id);
            List<Integer> rs = new ArrayList<>();
            for (int i = 1; i < GameCfgLuckySpin.data.exchange_star_new[id].length; i++) {
                rs.add(GameCfgLuckySpin.data.exchange_star_new[id][i]);
            }
            JSONArray array = JSONArray.fromObject(rs.toString());
            if (array != null) {
                if (new UserDAO().updateUserMoney(user.getId(), 0, 0, -star)) {
                    user.getDbUser().addStar(-star);
                    //
                    List<Long> aLong = new ArrayList<Long>();
                    aLong.add((long) user.getDbUser().getStar());
                    //
                    List<Long> aBonus = Bonus.receiveListItem(user, array, "exchange_star");
                    aLong.addAll(aBonus);
                    addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
                    Actions.save(user.getDbUser(), Actions.GRECEIVE, "exchange_star", Actions.convertToLogString(Arrays.asList("type", "star",
                            "value", String.valueOf(user.getDbUser().getStar()), "addValue", String.valueOf(-star))));
                }
            } else {
                addErrMessage(getLang(Lang.err_param));
            }
        } else {
            addErrMessage(getLang(Lang.err_not_enough_gold));
        }
    }

    void quay() {
        if (user.getDbUser().getGem() < GameCfgLuckySpin.data.feeGem) {
            addErrMessage(getLang(Lang.err_not_enough_gem));
            return;
        }
        if (new UserDAO().updateUserMoney(user.getId(), -GameCfgLuckySpin.data.feeGem, 0, GameCfgLuckySpin.data.bonusStar)) {
            user.getDbUser().addMoney(0, -GameCfgLuckySpin.data.feeGem);
            user.getDbUser().addStar(GameCfgLuckySpin.data.bonusStar);
            //
            JSONArray array = GameCfgLuckySpin.getLuckySpinnew(user);
            int result = array.getInt(0);
            array.remove(0);
            //
            List<Long> aLong = new ArrayList<Long>();
            aLong.addAll(Arrays.asList(user.getDbUser().getGem(), (long) user.getDbUser().getStar(), (long) result));
            //
            List<Long> aBonus = Bonus.receiveListItem(user, array, "quay");
            aLong.addAll(aBonus);
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(aLong, null)); //1 |userGold|addgold
            Actions.save(user.getDbUser(), Actions.GRECEIVE, "quay", Actions.convertToLogString(Arrays.asList("type", "gem",
                    "value", String.valueOf(user.getDbUser().getGem()), "addValue", String.valueOf(-GameCfgLuckySpin.data.feeGem))));
        } else {
            addErrMessage();
        }
    }

    void quayStatus() {
        List<Integer> aLong = new ArrayList<Integer>();
        aLong.add(GameCfgLuckySpin.data.feeGem);
        aLong.add(user.getDbUser().getStar());
        aLong.add(GameCfgLuckySpin.getTime());
//        System.out.println("GameCfgLuckySpin.getTime()-->" + GameCfgLuckySpin.getTime());

        GameCfgLuckySpin.getAwardSpin(1);
//        System.out.println("GameCfgLuckySpin.award.size--->" + GameCfgLuckySpin.award.size());
        for (int j = 0; j < GameCfgLuckySpin.lstaward.size(); j++) {
            List<Integer> rs = new ArrayList<>();
            rs.addAll(GameCfgLuckySpin.lstaward.get(j));
            rs.remove(0);
            aLong.addAll(rs);

        }
//            aLong.add(-2);
        for (int i = 0; i < GameCfgLuckySpin.data.exchange_star_new.length; i++) {
            for (int j = 1; j < GameCfgLuckySpin.data.exchange_star_new[i].length; j++) {
                aLong.add(GameCfgLuckySpin.data.exchange_star_new[i][j]);
            }
        }
//        for (int i = 0; i < GameCfgLuckySpin.data.exchange_star_fee.size(); i++) {
//            aLong.add(i);
//            aLong.add(GameCfgLuckySpin.data.exchange_star_fee.get(i));
//        }
//        aLong.addAll(GameCfgLuckySpin.data.exchange_star_fee);
//
        List<String> aStr = new ArrayList<String>();
        for (int i = 0; i < GameCfgLuckySpin.data.exchange_star_new.length; i++) {
            aStr.add(GameCfgLuckySpin.data.exchange_star_new[i][0] + "");
        }
//        aStr.addAll(GameCfgLuckySpin.data.spin_data);
//        aStr.addAll(GameCfgLuckySpin.data.exchange_star);

//        Arrays.asList("spin_gold", "vàng", "spin_gem", "Ngọc", "spin_clothes", "Tramg phục", "spin_boom", "boom",
//                "spin_pet", "Thú cưng", "spin_item", "Vật Phẩm",
//                "exchange_boom", "boom ngẫu nhiên Rank A-S", "exchange_clothes", "Trang phục ngẫu nhiên Rank A-S", "exchange_pet", "Thú cưng ngẫu nhiên Rank A-S");
        addResponse(service, CommonProto.getCommonVectorProto(aLong, aStr));
    }

    List<Long> viewChestGold() {
//        List<UserDataEntity> lst = new UserDAO().getListUserData();
//        System.out.println("size user data--"+lst.size());
//        for (int i = 0; i <lst.size() ; i++) {
//            UserDataEntity ud = lst.get(i);
////            try {
////                System.out.println("ud--->" + ud.getMapLevel().toString());
////            }catch (Exception ex){
////
////            }
////            ud.getMap();
//
//
//            List<Integer> mapNomal = ud.getMap().getNormal();
//            int starMap = 0;
//            for (int j = 0; j < mapNomal.size(); j++) {
//
//                if (mapNomal.get(j) > 0) {
//                    starMap += mapNomal.get(j);
//                }
//            }
////            System.out.println("star_map--->"+starMap);
////            System.out.println("ud.getUserId()-->"+ud.getUserId())ia;
////            System.out.println("mapNomal--->"+mapNomal.toString());
//            Database.update("user", Arrays.asList("star_map", String.valueOf(starMap)), Arrays.asList("id", String.valueOf(ud.getUserId())));
//
//        }
//        List<UserEntity> lst = new UserDAO().getListUserEntity();
//        System.out.println("size-->"+lst.size());
//        for (int i = 0; i < lst.size(); i++) {
//            long win = lst.get(i).getWin();
//            Database.update("user", Arrays.asList("win", String.valueOf(win)), Arrays.asList("id", String.valueOf(lst.get(i).getId())));
//        }

        List<Long> array = new ArrayList<Long>();

        ResMaterial material = Resources.getRandomMaterialLevel(2);
        array.addAll(Arrays.asList((long) Bonus.MATERIAL, (long) material.getId(), 1l));
        material = Resources.getRandomMaterialLevel(3);
        array.addAll(Arrays.asList((long) Bonus.MATERIAL, (long) material.getId(), (long) 1));
        List<ResAvatar> ava = Resources.getRankAvatarMaxId(Resources.RANK_B, 2);
        for (int i = 0; i < ava.size(); i++) {
            array.addAll(Arrays.asList((long) Bonus.AVATAR_FRAGMENT, (long) ava.get(i).getId(), (long) 1));
        }
        List<ResPetFood> petFood = Resources.getRankPetFoodMaxId(Resources.RANK_B, 2);
        for (int i = 0; i < petFood.size(); i++) {
            array.addAll(Arrays.asList((long) Bonus.PET_FOOD, (long) petFood.get(i).getId(), (long) 1));
        }
        List<ResBomb> bomb = Resources.getRankBombMaxId(Resources.RANK_B, 2);
        for (int i = 0; i < bomb.size(); i++) {
            array.addAll(Arrays.asList((long) Bonus.BOMB, (long) bomb.get(i).getId(), (long) 1));
        }
        return array;
    }

    List<Long> viewChestGem() {
        List<Long> array = new ArrayList<Long>();

        ResMaterial material = Resources.getRandomMaterialLevel(Resources.RANK_A);
        array.addAll(Arrays.asList((long) Bonus.MATERIAL, (long) material.getId(), 1l));
        material = Resources.getRandomMaterialLevel(Resources.RANK_S);
        array.addAll(Arrays.asList((long) Bonus.MATERIAL, (long) material.getId(), (long) 1));
        List<ResAvatar> ava = Resources.getRankAvatarMaxId(Resources.RANK_S, 4);
//        for (int i = 0; i < ava.size(); i++) {
//            array.addAll(Arrays.asList((long) Bonus.AVATAR_FRAGMENT, (long) ava.get(i).getId(), (long) 1));
//        }
        array.addAll(Arrays.asList((long) Bonus.AVATAR_FRAGMENT, (long) 49, (long) 1));
        array.addAll(Arrays.asList((long) Bonus.AVATAR_FRAGMENT, (long) 50, (long) 1));

        List<ResPetFood> petFood = Resources.getRankPetFoodMaxId(Resources.RANK_A, 2);
        for (int i = 0; i < petFood.size(); i++) {
            array.addAll(Arrays.asList((long) Bonus.PET_FOOD, (long) petFood.get(i).getId(), (long) 1));
        }
        List<ResBomb> bomb = Resources.getRankBombMaxId(Resources.RANK_S, 2);
        for (int i = 0; i < bomb.size(); i++) {
            array.addAll(Arrays.asList((long) Bonus.BOMB, (long) bomb.get(i).getId(), (long) 1));
        }

        List<ResPet> pet = Resources.getRankPetMaxId(2);
        for (int i = 0; i < bomb.size(); i++) {
            array.addAll(Arrays.asList((long) Bonus.PET, (long) pet.get(i).getId(), (long) 1, (long) 1));
        }
        return array;
    }

    void openChest() {
        int type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        int number = (int) CommonProto.parseCommonVector(srcRequest).getANumber(1);
        int priceGold = 0;
        int priceGem = 0;
        int countRemain = GameCfgChest.data.get(0).maxDay;
        long time = System.currentTimeMillis();
        if (number <= 0) {
            addErrMessage("Dữ liệu lỗi");
            return;
        }
        try {
            Calendar ca = Calendar.getInstance();
            countRemain = Integer.parseInt(JCache.getInstance().getValue(Util.formatDateDAy(ca.getTime()) + user.getUsername() + ":countdaygoldchest"));
        } catch (Exception ex) {
        }

        if (type == GameCfgChest.CHEST_TYPE_SILVER && number == 1) {
            try {
                priceGold = GameCfgChest.data.get(type).fee;
            } catch (Exception ex) {
                priceGold = 1000;
            }
            if (countRemain > 0 && (time - user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GOLD_CHEST)) / 1000 >= GameCfgChest.data.get(0).timeFree) {
                priceGold = -1;

            }
        } else if (type == GameCfgChest.CHEST_TYPE_SILVER && number == 10) {
            try {
                priceGold = GameCfgChest.data.get(type).fee10;
            } catch (Exception ex) {
                priceGold = 9000;
            }

        } else if (type == GameCfgChest.CHEST_TYPE_GOLD && number == 1) {
            try {
                priceGem = GameCfgChest.data.get(type).fee;
            } catch (Exception ex) {
                priceGem = 100;
            }
            if ((time - user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GEM_CHEST)) / 1000 >= GameCfgChest.data.get(1).timeFree) {
                priceGem = -1;
            }
        } else if (type == GameCfgChest.CHEST_TYPE_GOLD && number == 10) {
            try {
                priceGem = GameCfgChest.data.get(type).fee10;
            } catch (Exception ex) {
                priceGem = 900;
            }
        }

        if (priceGold == 0 && priceGem == 0) {
            addErrMessage();
            return;
        }

        if (type == GameCfgChest.CHEST_TYPE_GOLD && user.getDbUser().getGem() < priceGem) {
            addErrMessage(getLang(Lang.err_not_enough_gem));
            return;
        }
        if (type == GameCfgChest.CHEST_TYPE_SILVER && user.getDbUser().getGold() < priceGold) {
            addErrMessage(getLang(Lang.err_not_enough_gold));
            return;
        }
        if (priceGem == -1 || priceGold == -1 || new UserDAO().updateUserMoney(user.getId(), -priceGem, -priceGold, 0)) {
            boolean firstGemChest = false;
            if (priceGold == -1) {
                countRemain--;
                user.getDbUser().setSettingsIndex(BoomConfig.SETTING_TIME_GOLD_CHEST, time);
                long setting = user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GOLD_CHEST);
                user.getDbUser().setSettingsIndex(BoomConfig.SETTING_TIME_GOLD_CHEST, time);
                if (Database2.update("user", Arrays.asList("settings", user.getDbUser().getSettings()), Arrays.asList("id", String.valueOf(user.getId())))) {
                    Calendar ca = Calendar.getInstance();
                    JCache.getInstance().setValue(Util.formatDateDAy(ca.getTime()) + user.getUsername() + ":countdaygoldchest", countRemain + "", 24 * 60 * 60);
                } else {
                    user.getDbUser().setSettingsIndex(BoomConfig.SETTING_TIME_GOLD_CHEST, setting);
                    addErrMessage();
                }
            } else if (priceGem == -1) {
                user.getDbUser().setSettingsIndex(BoomConfig.SETTING_TIME_GEM_CHEST, time);
                long oldTime = user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GEM_CHEST);
                user.getDbUser().setSettingsIndex(BoomConfig.SETTING_TIME_GEM_CHEST, time);
                if (Database2.update("user", Arrays.asList("settings", user.getDbUser().getSettings()), Arrays.asList("id", String.valueOf(user.getId())))) {
                    if (user.getDbUser().getSettingsIndex(BoomConfig.SETTING_FIRST_GEM_CHEST) == 0) {
                        user.getDbUser().setSettingsIndex(BoomConfig.SETTING_FIRST_GEM_CHEST, 1);
                        if (Database2.update("user", Arrays.asList("settings", user.getDbUser().getSettings()), Arrays.asList("id", String.valueOf(user.getId())))) {
                            firstGemChest = true;
                        } else {
                            user.getDbUser().setSettingsIndex(BoomConfig.SETTING_FIRST_GEM_CHEST, 0);
                            addErrMessage();
                        }
                    }
                } else {
                    user.getDbUser().setSettingsIndex(BoomConfig.SETTING_TIME_GEM_CHEST, oldTime);
                    addErrMessage();
                }
            }
            if (priceGem < 0) {
                priceGem = 0;
            }
            if (priceGold < 0) {
                priceGold = 0;
            }
            user.getDbUser().addMoney(-priceGold, -priceGem);
            JSONArray array = GameCfgChest.getGoldChest(number, type, firstGemChest);
            List<Long> aLong = new ArrayList<Long>();
            long value = user.getDbUser().getGem();
            if (type == 0) {
                value = user.getDbUser().getGold();
            }
            long timeremain = GameCfgChest.data.get(0).timeFree - ((time - user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GOLD_CHEST)) / 1000);
            if (type == 1) {
                timeremain = GameCfgChest.data.get(1).timeFree - ((time - user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GEM_CHEST)) / 1000);
                countRemain = -1;
            }
            if (countRemain < 0) {
                countRemain = 0;
            }
            if (timeremain < 0) {
                timeremain = 0;
            }
            aLong.addAll(Arrays.asList((long) type, value, timeremain, (long) countRemain));
//            System.out.println("ar--->"+array);
            List<Long> aBonus = Bonus.receiveListItem(user, array, "chest");
            aLong.addAll(aBonus);
            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(aLong, null)); //1 |userGold|addgold
            if (priceGem != 0)
                Actions.save(user.getDbUser(), Actions.GRECEIVE, "chest", Actions.convertToLogString(Arrays.asList("type", "gem",
                        "value", String.valueOf(user.getDbUser().getGem()), "addValue", String.valueOf(-priceGem))));
            else if (priceGold != 0)
                Actions.save(user.getDbUser(), Actions.GRECEIVE, "chest", Actions.convertToLogString(Arrays.asList("type", "gold",
                        "value", String.valueOf(user.getDbUser().getGold()), "addValue", String.valueOf(-priceGold))));
            else if (priceGem == 0 && priceGold == 0)
                Actions.save(user.getDbUser(), Actions.GRECEIVE, "chest", Actions.convertToLogString(Arrays.asList("type", "free",
                        "value", String.valueOf(type))));
        } else {
            addErrMessage();
        }
    }

    void chestStatus() {
        List<Long> aLong = new ArrayList<Long>();
        long timeGoldChest = user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GOLD_CHEST);
        long timeGemChest = user.getDbUser().getSettingsIndex(BoomConfig.SETTING_TIME_GEM_CHEST);
        if (timeGoldChest > 0) {
            timeGoldChest = GameCfgChest.data.get(0).timeFree - ((System.currentTimeMillis() - timeGoldChest) / 1000);
        } else {
//            timeGoldChest = System.currentTimeMillis();
            timeGoldChest = 0;
        }
        if (timeGemChest > 0) {
            timeGemChest = GameCfgChest.data.get(1).timeFree - ((System.currentTimeMillis() - timeGemChest) / 1000);
            debug("time gem chest---->" + timeGemChest);
        } else {
//            timeGemChest = System.currentTimeMillis();
            timeGemChest = 0;
        }

        int countRemain = GameCfgChest.data.get(0).maxDay;
        try {
            Calendar ca = Calendar.getInstance();
            countRemain = Integer.parseInt(JCache.getInstance().getValue(Util.formatDateDAy(ca.getTime()) + user.getUsername() + ":countdaygoldchest"));
        } catch (Exception ex) {
//            ex.printStackTrace();
        }
        aLong.addAll(Arrays.asList((long) GameCfgChest.data.get(0).fee, (long) GameCfgChest.data.get(0).fee10, Math.max(timeGoldChest, 0), (long) Math.max(countRemain, 0), (long) GameCfgChest.data.get(1).fee, (long) GameCfgChest.data.get(1).fee10, Math.max(timeGemChest, 0)));
        List<Long> viewGold = viewChestGold();
        List<Long> viewGem = viewChestGem();

        for (int i = 0; i < viewGold.size(); i++) {
            aLong.add(viewGold.get(i));
        }
//        aLong.addAll(viewChestGold());
        aLong.add((long) -2);
        for (int i = 0; i < viewGem.size(); i++) {
            aLong.add(viewGem.get(i));
        }
        debug("aLong---:>" + aLong);
//        aLong.addAll(viewChestGold());

        List<String> aStr = new ArrayList<String>();
//                Arrays.asList("spin_item", "item", "spin_boom", "boom", "spin_clothes", "clothes", "spin_gold", "gold",
//                "spin_gem", "gem", "spin_pet", "pet", "exchange_boom", "boom", "exchange_clothes", "clothes", "exchange_item", "item");
        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(aLong, aStr));
    }

    //diem danh moi
    void onlineGiftStatus(int service) {
        List<Long> vLong = new ArrayList<Long>();
        user.getUData().myAttendance.checkStatusOnline(user, false);
        if (user.getUData().myAttendance.timeOnline > -1) {
//            CfgOnlinegift.data.get(user.getUData().myAttendance.numberonlineReceive);
            int time = CfgOnlinegift.data.get(user.getUData().myAttendance.numberonlineReceive).time;
            vLong.add((long) (time - user.getUData().myAttendance.timeOnline));
        } else {
            vLong.add((long) (user.getUData().myAttendance.timeOnline));
        }
        if (CfgServer.isVNServer() && user.getUData().myAttendance.timeOnline >= 4 * 60 * 60) {
            addSlideMessage("Bạn đã chơi game quá 4h");
        }
        addResponse(service, CommonProto.getCommonLongVectorProto(vLong, null));
    }

    void onlineGiftGet() {
        user.getUData().myAttendance.checkStatusOnline(user, false);
        if (user.getUData().getMyAttendance().statusOnline == CfgDiemdanh.COTHENHAN) {

            List<Long> aLong = new ArrayList<Long>();
            JSONArray array = JSONArray.fromObject(CfgOnlinegift.data.get(user.getUData().getMyAttendance().numberonlineReceive).award);
            //
            List<Long> aBonus = Bonus.receiveListItem(user, array, "gift_online");
            aLong.addAll(aBonus);
            user.getUData().getMyAttendance().statusOnline = CfgDiemdanh.CHUANHAN;
            user.getUData().getMyAttendance().numberonlineReceive++;
            user.getUData().myAttendance.timeOnlineLastMinisecond = System.currentTimeMillis();
            user.getUData().getMyAttendance().update(user.getId());
            addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
            onlineGiftStatus(ONLINE_GIFT_STATUS);
//            sendMessage(channel, service, CommonProto.getCommonLongVectorProto(aLong, null));
        } else {
            addErrMessage("Bạn chưa được mở phần thưởng này");
            return;
        }
        if (CfgServer.isVNServer() && user.getUData().myAttendance.timeOnline >= 4 * 60 * 60) {
            addSlideMessage("Bạn đã chơi game quá 4h");
        }

//        sendMessage(channel, service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) new Random().nextInt(5), 1l, 30000l, 300l), null));
    }

    void diemdanhStatus() {
        List<List<Long>> listComon = new ArrayList<List<Long>>();
        List<GGProto.CommonVector> listCommon = new ArrayList<GGProto.CommonVector>();
        CfgDiemdanh.MyAttendance myAtten = user.getUData().myAttendance;
//        if(myAtten.numberDateOnline>myAtten.dateReceive&& myAtten.dateReceive>0){
//            myAtten.numberDateOnline=myAtten.dateReceive;
//        }


//       int index = new Random().nextInt(30)+1;
        int index = myAtten.numberReceive;

        debug("CfgDiemdanh.data.size()--->" + CfgDiemdanh.data.size());
        for (int i = 0; i < CfgDiemdanh.data.size(); i++) {

            List<Long> vLong = new ArrayList<Long>();
            vLong.add((long) CfgDiemdanh.data.get(i).day);

            if (i == index && myAtten.status == CfgDiemdanh.COTHENHAN) {
                vLong.add(2l);// co the nhan
            } else if (i < index) {
                vLong.add(1l);// da nhan
            } else if (i >= index) {
                vLong.add(0l);// chua nhan
            }
            if (CfgDiemdanh.data.get(i).vipDouble > 0) {
                vLong.add(1l);
            } else {
                vLong.add(0l);
            }
            vLong.add((long) CfgDiemdanh.data.get(i).vipDouble + 1);
            JSONArray award = JSONArray.fromObject(CfgDiemdanh.data.get(i).award);
//            debug("award-------->"+award);
            for (int j = 0; j < award.size(); j++) {
                vLong.add(award.getLong(j));
            }
//            debug("vlong- diem danh status--->" + vLong);
//            vLong.add((long) (new Random().nextInt(6)));
//            vLong.add(1l);
//            vLong.add((long) ((new Random().nextInt(6) + 1) * 10000));
            listCommon.add(CommonProto.getCommonLongVectorProto(vLong, null));
        }

        addResponse(ONLINE_ATTENDANCE_STATUS, CommonProto.getListCommonLongVectorProto(listCommon));

    }

    void diemdanh() {
        if (user.getUData().getMyAttendance().status == CfgDiemdanh.COTHENHAN) {

            List<Long> aLong = new ArrayList<Long>();
            JSONArray array = JSONArray.fromObject(CfgDiemdanh.data.get(user.getUData().getMyAttendance().numberReceive).award);
            if (CfgDiemdanh.data.get(user.getUData().getMyAttendance().numberReceive).vipDouble > 0 && user.getDbUser().getVip() >= CfgDiemdanh.data.get(user.getUData().getMyAttendance().numberReceive).vipDouble) {
                JSONArray arrbonus = new JSONArray();
                arrbonus.addAll(array);
                array.addAll(arrbonus);
//                for (int i = 0; i < array.size(); i++) {
//                    array.add(array.get(i));
//                }
            }
//            System.out.println(array);
            //
            List<Long> aBonus = Bonus.receiveListItem(user, array, "diemdanh");
            aLong.addAll(aBonus);
            user.getUData().getMyAttendance().status = CfgDiemdanh.CHUANHAN;
            user.getUData().getMyAttendance().numberReceive++;
            user.getUData().myAttendance.dateLastOnline = Integer.parseInt(new SimpleDateFormat("dd").format(System.currentTimeMillis()));

            user.getUData().getMyAttendance().update(user.getId());
            addResponse(CommonProto.getCommonLongVectorProto(aLong, null));
//            System.out.println(user.getUData().getMyAttendance().toString());
            diemdanhStatus();
        }
    }


    //<editor-fold desc="Database Access">
    //<editor-fold>

    //<editor-fold desc="Proto">
    //</editor-fold>
}
