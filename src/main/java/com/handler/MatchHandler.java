package com.handler;

import com.bem.TableSoloBoomBackup;
import com.bem.boom.CacheBattle;
import com.bem.boom.map.MapObject;
import com.bem.boom.map.MapResource;
import com.bem.boom.object.InviteBattle;
import com.bem.boom.object.MissionObject;
import com.bem.config.*;
import com.bem.config.lang.Lang;
import com.bem.dao.mapping.BattleDataEntity;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.dao.mapping.MapEntity;
import com.bem.dao.mapping.UserItemEntity;
import com.bem.matcher.TeamMatcher;
import com.bem.matcher.TeamObject;
import com.bem.monitor.Bonus;
import com.bem.monitor.Online;
import com.bem.object.MapLevel;
import com.bem.object.ResShopItem;
import com.bem.object.TopDetail;
import com.bem.object.UserInfo;
import com.bem.util.ChUtil;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.cache.JService;
import com.cache.MCache;
import com.google.gson.Gson;
import com.handler.game.Arena;
import com.handler.game.BossGlobal;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class MatchHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        mGameHandler.put(CREATE_TEAM, getInstance());
        mGameHandler.put(SEARCH_GAME, getInstance());
        mGameHandler.put(JOIN_TEAM, getInstance());
        mGameHandler.put(LEAVE_TEAM, getInstance());
        mGameHandler.put(INVITE_TEAM, getInstance());
        mGameHandler.put(CHOSSE_MAP_STATUS, getInstance());
        mGameHandler.put(CANCEL_GAME, getInstance());
        mGameHandler.put(TEAM_STATUS, getInstance());
        mGameHandler.put(KICK_PLAYER, getInstance());
        mGameHandler.put(TEAM_CHAT, getInstance());
        mGameHandler.put(TEAM_EMOTION, getInstance());
        mGameHandler.put(HAVE_SEARCH_TEAM, getInstance());
        mGameHandler.put(TOP_BOSS, getInstance());
        mGameHandler.put(TEAM_READY, getInstance());
        mGameHandler.put(BOSS_QUICK_ATK, getInstance());
        mGameHandler.put(BOSS_LEVEL_STATUS, getInstance());
        mGameHandler.put(REJOIN_TEAM_OBJECT, getInstance());
        mGameHandler.put(INVITE_SUPPORT_STATUS, getInstance());
        mGameHandler.put(BUY_FREE_INVITE, getInstance());
    }

    static MatchHandler instance;

    public static MatchHandler getInstance() {
        if (instance == null) {
            instance = new MatchHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new MatchHandler();
    }

    public MatchHandler() {
    }

    //region Action
    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case CREATE_TEAM:
                    createTeam();
                    break;
                case JOIN_TEAM:
                    joinTeam();
                    break;
                case INVITE_TEAM:
                    inviteTeam();
                    break;
                case SEARCH_GAME:
                    searchGame();
                    break;
                case LEAVE_TEAM:
                    leaveTeam();
                    break;
                case CHOSSE_MAP_STATUS:
                    chosseMapStatus();
                    break;
                case CANCEL_GAME:
                    cancelGame();
                    break;
                case TEAM_STATUS:
                    teamStatus();
                    break;
                case KICK_PLAYER:
                    kickPlayer();
                    break;
                case TEAM_CHAT:
                case TEAM_EMOTION:
                    chat();
                    break;
                case TOP_BOSS:
                    top();
                    break;
                case TEAM_READY:
                    readyGame();
                    break;
                case BOSS_QUICK_ATK:
                    quickAtk();
                    break;
                case BOSS_LEVEL_STATUS:
                    bossLevelStatus();
                    break;
                case REJOIN_TEAM_OBJECT:
                    rejoinGameServer();
                    break;
                case INVITE_SUPPORT_STATUS:
                    inviteSupportStatus();
                    break;
                case BUY_FREE_INVITE:
                    buyFreeInvite();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void rejoinGameServer() {
        try {
            Online.addChannel(user.getId(), channel);
            ChUtil.set(channel, Constans.KEY_USER, user);
            //
            boolean leaveTable = true;
            try {
                leaveTable = CommonProto.parseCommonVector(srcRequest).getANumber(0) == 1;
            } catch (Exception ex) {
            }
            long oldTrophy = user.getDbUser().getTrophy();
            BattleUserEntity.BattleBonusResult result = BattleUserEntity.getInstance().getBattleBonus(user);

            if (user.getDbUser().getTrophy() != oldTrophy) {
                result.aBonus.addAll(Arrays.asList((long) Bonus.RANK_ID, (long) user.getDbUser().getRank()));
            }
            System.out.println("bonus------------------->" + result.aBonus.toString());
            addResponse(SYNC_BONUS, CommonProto.getCommonLongVectorProto(result.aBonus, null));
            switch (result.lastGameType) {
                case TeamObject.BOSS:
                case TeamObject.TRAIN:
                case TeamObject.RANK: {
                    if (result.lastGameType == TeamObject.RANK) {
                        user.getDbUser().addAchievementStatus(CfgAchievement.ACHIEVEMENT_TEAM, 1, user);
                        CfgNewMission.addMission(user.getDbUser(), MissionObject.SOLOPLAYMISSION, 1);
                    } else if (result.lastGameType == TeamObject.BOSS) {
                        CfgNewMission.addMission(user.getDbUser(), MissionObject.BOSSPLAYMISSION, 1);
                        user.getDbUser().addAchievementStatus(CfgAchievement.WIN_MAP, 1, user);
                        user.getDbUser().addAchievementStatus(CfgAchievement.COLLECT_STAR, 1, user);
                    }
                    TeamObject team = user.getTeam();
                    if (team != null) {
                        team.status = TeamObject.STATUS_NONE;
                        if (leaveTable) {
                            team.removeUser(user.getId());
                            user.setTeam(null);
                        } else if (result.lastGameType == TeamObject.BOSS && !team.isHost(user)) {
                            leaveTeam();
                            user.setTeam(null);
                        }
                    } else {
//                    leaveTeam();
                    }
                    break;
                }
                case TeamObject.GAME_BOSS:
                    BossGlobal.getInstance(user.getDbUser().getServer()).updateTopPlayer(user, result.totalDamage);
                    break;
                case TeamObject.ARENA:
//<<<<<<< .mine
                    Arena.getInstance(user.getDbUser().getServer()).updateTopPlayer(this, user, result.totalDamage);
//||||||| .r754
//                    Arena.getInstance().updateTopPlayer(this,user, result.totalDamage);
//=======
//                    Arena.getInstance().updateTopPlayer(this, user, result.totalDamage);
//>>>>>>> .r770
                    break;
                default:
                    TeamObject team = user.getTeam();
                    if (team != null) team.status = TeamObject.STATUS_NONE;
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addResponse(SYNC_BONUS, CommonProto.getCommonLongVectorProto(null, null));
        }
    }

    void bossLevelStatus() {
        int type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
        if (type == MapLevel.MODE_NORMAL) {
            builder.addAVector(CommonProto.getCommonVectorProto(user.getUData().getMap().getNormal(), null));
        } else if (type == MapLevel.MODE_INSANE) {
            user.getUData().getMap().getNumberAtk(1); // check reset
            builder.addAVector(CommonProto.getCommonVectorProto(user.getUData().getMap().getInsane(), null));
            int numberMap = user.getUData().getMap().getInsane().size() + 1;
            if (numberMap > MapLevel.NUMBER_MAP) numberMap = MapLevel.NUMBER_MAP;
            {
                List<Integer> aInt = new ArrayList<>();
                for (int i = 0; i < numberMap; i++) {
                    aInt.add(user.getUData().getMap().getNumberAtk().get(i));
                }
                builder.addAVector(CommonProto.getCommonVectorProto(aInt, null));
            }
        }
        addResponse(builder.build());
    }

    void quickAtk() {
        List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
        int mapId = aLong.get(0).intValue();
        int mode = aLong.get(1).intValue();
        int numberAtk = aLong.get(2).intValue();
        int curNumberAtk = 0;
        MapEntity map = MapResource.getMap(mapId);
        MapLevel mapLevel = user.getUData().getMap();

        if (mode == MapLevel.MODE_INSANE) {
            curNumberAtk = mapLevel.getNumberAtk(mapId);
            if (curNumberAtk + numberAtk > map.getTimesAtk()) {
                addErrMessage(getLang(Lang.match_err_number_attack_per_day));
                return;
            }
        }
        if (numberAtk <= 0) {
            addErrMessage(getLang(Lang.match_err_number_attack));
            return;
        }
        CfgEnergy.MyEnergy myEnergy = CfgEnergy.getMyEnergy(user);
        if (myEnergy.energy < numberAtk * map.getEnergy()) {
            addErrMessage(getLang(Lang.err_not_enough_energy));
            return;
        }
        int curMap = user.getUData().getMap().getCurMap(mode);
        if (curMap < mapId % 1000 || mapId <= 0) {
            addErrMessage(getLang(Lang.match_err_only_sweep_completed));
            return;
        }
        int star = user.getUData().getMap().getStar(mode, mapId);
        if (star < 3) {
            addErrMessage(getLang(Lang.match_err_only_sweep_3_star));
            return;
        }
        UserItemEntity uItem = user.getRes().mItem.get(ResShopItem.ATOM_BOMB);
        if (uItem == null || uItem.getNumber() < numberAtk) {
            addErrMessage(getLang(Lang.match_err_not_enough_atomic_bomb));
            return;
        }
        if (mode == MapLevel.MODE_INSANE) {
            int oldValue = mapLevel.getNumberAtk(mapId);
            mapLevel.setNumberAtk(mapId, oldValue + numberAtk);
            if (!mapLevel.update(user.getId(), true)) {
                mapLevel.setNumberAtk(mapId, oldValue);
                addErrMessage();
                return;
            }
        }
        JSONArray bonus = new JSONArray();
        bonus.addAll(Arrays.asList((long) Bonus.EXP, map.getExp() * numberAtk));
        bonus.addAll(Bonus.defaultBonusView(Bonus.GOLD, map.getGold() * numberAtk));
        bonus.addAll(Arrays.asList((long) Bonus.HERO_EXP, user.getMAvatar().getHero(), map.getExp() * numberAtk));
        for (int i = 0; i < numberAtk; i++) {
            bonus.addAll(MapEntity.getBonus(map.getDropItem()));
        }
        bonus.addAll(Bonus.defaultBonusView(Bonus.ITEM, ResShopItem.ATOM_BOMB, -numberAtk));
        bonus.addAll(Bonus.defaultBonusView(Bonus.USER_ENERGY, -numberAtk * map.getEnergy()));
        aLong = Bonus.receiveListItem(user, bonus, "boss_atk");
        if (aLong.isEmpty()) {
            addErrMessage();
            return;
        }
        addResponse(service, CommonProto.getCommonLongVectorProto(aLong, null));
    }

    void chat() {
        String lockChat = Util.lockChat(user.getDbUser().getLockChat());
        if (lockChat != null) {
            addErrMessage(lockChat);
            return;
        }
        TeamObject team = user.getTeam();
        if (team != null) {
            String text = CommonProto.parseCommonVector(srcRequest).getAString(0);
            team.sendMessage(service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getId(), (long) user.getDbUser().getMAvatar().getUserAvatar()), Arrays.asList(user.getDbUser().getName(), text)), user.getId());
        } else {
            String text = CommonProto.parseCommonVector(srcRequest).getAString(0);
            Arena.getInstance(user.getDbUser().getServer()).chat(user, text);
        }
    }

    void top() {
        try {
            int type = 0;
            try {
                type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
            } catch (Exception ex) {

            }

            TeamObject team = user.getTeam();
            int bossId = 1;
            try {
                bossId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(1);
            } catch (Exception ex) {

            }
            bossId = (((bossId - 1) / 3) * 3) + 1;
            String keyResis = JService.KEY_TOP_BOSS;
            String keyDb = "top";
            if (type != 0) {
                keyResis += "pre:";
                keyDb = "top_pre";
            }
            if (team != null) {
                String top = "";
                try {
                    top = JCache.getInstance().getValue(keyResis + bossId);
                } catch (Exception ex1) {
                    debug("loi null hoi kho hieu");
                }
//                int time = JCache.getInstance().getIntValue(JedisService.KEY_TIME_TOP_BOSS + team.map);
                if (top == null || top.equalsIgnoreCase("")) {
                    debug("bossId db---->" + bossId);
                    top = Database2.getUniqueColumn(CfgCommon.mainDb + "map", Arrays.asList("id", String.valueOf(bossId)), keyDb);
                    debug("top---->" + top);
                }
                if (top != null) {
                    List<TopDetail> lstTop = new TopDetail().getListTopBoss(top);
                    for (int i = 0; i < lstTop.size(); i++) {
                        for (int j = 0; j < lstTop.get(i).user.size(); j++) {
                            lstTop.get(i).user.get(j).setTopBossMax("{}");
                            lstTop.get(i).user.get(j).setTopBossCur("{}");
                        }
                    }
                    debug("lst befo size--->" + lstTop.size());
                    if (type == 0) {
                        TopDetail myTopCur = new TopDetail().getTopBoss(user.getDbUser().getTopBossCur());
                        TopDetail myTopMax = myTopCur.getTopBoss(user.getDbUser().getTopBossMax());

                        lstTop.add(myTopCur);
                        lstTop.add(myTopMax);
                        debug("mytopcur----->" + new Gson().toJson(myTopCur));
                        debug("mytopmax----->" + new Gson().toJson(myTopMax));
                        debug("top.size---->" + lstTop.size());
                    }
                    debug("listTop---------------->" + new TopDetail().toString(lstTop));
                    addResponse(service, CommonProto.protoListTopBoss(lstTop));
                } else {
                    debug("null top db");
                    addResponse(service, CommonProto.protoListTopBoss(new ArrayList<TopDetail>()));
                }
            } else {
                debug("chua co team");
//                addErrMessage("Chưa có team");
                addErrMessage(getLang(Lang.dont_have_team));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    void kickPlayer() {
        TeamObject team = user.getTeam();
        if (team != null && team.hostId == user.getId()) {
            long kickId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
            UserInfo userKick = null;
            for (int i = 0; i < team.aUser.size(); i++) {
                if (team.aUser.get(i).getId() == kickId) {
                    userKick = team.aUser.get(i);
                    break;
                }
            }
            if (kickId != user.getId() && userKick != null) {
                team.sendMessage(service, CommonProto.getCommonLongVectorProto(Arrays.asList(kickId), null));
                team.removeUser(kickId);
                userKick.setTeam(null);
            } else {
                addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList(kickId), null));
            }
        } else {
            addErrMessage(getLang(Lang.match_err_not_host_player));
        }
    }

    void teamStatus() {
        TeamObject team = user.getTeam();
        if (team != null) {
            inviteSupportStatus();
            addResponse(service, team.protoTeamStatus());
        } else {
            leaveTeam();
        }
    }

    void chosseMapStatus() {
        TeamObject team = user.getTeam();
        if (user.getId() != team.hostId) {
            addErrMessage(getLang(Lang.match_err_not_host_player));
            return;
        }
        int idMap = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        if ((idMap < 2000 && team != null && team.type == TeamObject.BOSS) || (idMap >= 2000 && team != null && team.type == TeamObject.TRAIN)) {
            MapObject map = MapResource.getMap(idMap).getMapObject();
            if (map == null) {
//                addErrMessage("Map không tồn tại");
                addErrMessage(getLang(Lang.not_exist_map));
                return;
            }
            if (user.getDbUser().getLevel() < map.unlockLevel) {
//                addErrMessage(String.format("Đạt cấp độ %s để mở map này", map.unlockLevel));
                addErrMessage(String.format(getLang(Lang.level_to_unlock_map), map.unlockLevel));
                return;
            }
            team.map = idMap;
            for (int i = 0; i < team.aUser.size(); i++) {
                sendMessage(Online.getChannel(team.aUser.get(i).getId()), service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) idMap), null));
            }
        } else {
//            addErrMessage("map id sai");
            addErrMessage(getLang(Lang.err_map_id));
        }
    }

    void createTeam() {
//        if (user.getMAvatar().getHero() == 7) {
//            addErrMessage("Nhân vật này đang bảo trì. Bạn hãy thay nhân vật khác để chơi bình thường.");
//            return;
//        }
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        int type = (int) cmm.getANumber(0);
        if (type == TeamObject.RANK && CfgServer.isRankClosed()) { // 8h - 11h
            addErrMessage(getLang(Lang.closed_rank_match));
            return;
        }
        if (!TeamObject.PLAY_TYPE.contains(type)) {
            addErrMessage(getLang(Lang.not_correct_style));

            return;
        }
//        if (type == TeamObject.RANK && user.getDbUser().getLevel() < CfgCommon.config.rankLevelRequired) {
//            addErrMessage(String.format(getLang(Lang.level_to_join_match), CfgCommon.config.rankLevelRequired));
//            return;
//        }
        //
        TeamObject team = user.getTeam();
        if (team != null && team.type == TeamObject.BOSS) {
            team.removeUser(user.getId());
            team.sendMessage(service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), team.hostId), null));
        } else if (team != null) {
            team.removeUser(user.getId());
            team.sendMessage(service, CommonProto.getCommonLongVectorProto(Arrays.asList((long) user.getId(), (long) team.hostId), null));
            team.sendMessage(CANCEL_GAME, null);
        }
        team = new TeamObject(type, user);
        System.out.println("team.username-------->" + team);
        TeamMatcher.addTeam(team);
        addResponse(service, team.protoTeamStatus());
    }

    void joinTeam() {
        TeamObject team = user.getTeam();
        if (team != null) {
//            addErrMessage("Hãy rời đội trước khi nhận lời mời");
            addErrMessage(getLang(Lang.leave_team_before_accept));
            return;
        }

        int teamId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        boolean foundReq = false;
        List<InviteBattle> aInvite = user.getAInvite();
        for (int i = 0; i < aInvite.size(); i++) {
            if (aInvite.get(i).getTeamId() == teamId) {
                foundReq = true;
                aInvite.remove(i);
                break;
            }
        }
        if (foundReq) {
            team = TeamMatcher.getTeam(teamId);
            if (team == null || team.aUser.isEmpty()) {
//                addErrMessage("Đội không tồn tại");
                addErrMessage(getLang(Lang.not_exist_team));
                if (team != null && team.aUser.isEmpty()) TeamMatcher.removeTeam(team.id);
                return;
            }
            if (team.status != TeamObject.STATUS_NONE) {
//                addErrMessage("Đội đã tham gia trận đánh");
                addErrMessage(getLang(Lang.team_is_playing));
                return;
            }
            if (team.type == TeamObject.RANK && CfgSolo.inEvent()) {
//                addErrMessage("Ngày hôm nay chỉ được đánh solo");
                addErrMessage(getLang(Lang.only_solo));
                return;
            }
            int result = team.addUser(user);
            if (result == 3 || result == 2) {
//                addErrMessage("Đội đã đầy thành viên");
                addErrMessage(getLang(Lang.team_full_members));
                return;
            }
            team.sendMessage(NEW_TEAM_PLAYER, CommonProto.protoUser(user.getDbUser(), user.getUData()), user.getId());
            addResponse(service, team.protoTeamStatus());
        }
    }

    void leaveTeam() {
        TeamObject team = user.getTeam();
        //
        if (team != null) {
            if (team.type == TeamObject.BOSS) {
                team.removeUser(user.getId());
                team.sendMessage(service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), team.hostId), null));
                addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), team.hostId), null));
            } else {
                team.removeUser(user.getId());
                team.sendMessage(service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), team.hostId), null));
                team.sendMessage(CANCEL_GAME, null);
                team.status = TeamObject.STATUS_NONE;
                addResponse(service, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), team.hostId), null));
            }
        } else {
            addResponse(IAction.LEAVE_TEAM, CommonProto.getCommonLongVectorProto(Arrays.asList(user.getId(), 0L), null));
        }
    }

    void inviteTeam() {
        TeamObject team = user.getTeam();
        if (team == null) {
            return;
        }
        if (user.getId() != team.hostId) {
//            addErrMessage("Bạn không phải chủ phòng");
            addErrMessage(getLang(Lang.is_not_room_master));
            return;
        }
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        long inviteId = cmm.getANumber(0);
        String inviteName = cmm.getAString(0);
        Channel inviteChannel = Online.getChannel(inviteId);
        if (inviteChannel == null) {
//            addErrMessage("Người chơi không online");
            addErrMessage(getLang(Lang.user_not_online));
            return;
        }
        if (team.type == TeamObject.BOSS && getBossSupport().getInt(0) <= 0) {
//            addErrMessage("Bạn đã hết lượt mời trong ngày");
            addErrMessage(getLang(Lang.no_turn_to_invite));
            return;
        }
        //
        if (team.status == TeamObject.STATUS_NONE) {
//            addErrMessage("Đã mời người chơi");
            addErrMessage(getLang(Lang.has_invited_player));
            UserInfo friendUser = (UserInfo) ChUtil.get(inviteChannel, Constans.KEY_USER);
            if (friendUser != null) {
                friendUser.getAInvite().add(new InviteBattle(user.getDbUser().getName(), user.getId(), team.id));
            }
            sendMessage(inviteChannel, INVITE_TEAM_REQ, CommonProto.protoInviteBattle(user.getId(), user.getDbUser().getName(), team.id));
        } else {
//            addErrMessage("Đang trong trận đấu không thể mời người chơi");
            addErrMessage(getLang(Lang.is_playing_not_invite_player));
        }
    }

    /*
     * Add team to search queue
     */
    void cancelGame() {
        TeamObject team = user.getTeam();
        if (team.type == TeamObject.ARENA) {
            getLogger().warn("ARENA_CANCEL_GAME --->" + user.getUsername() + "||" + user.getDbUser().getName());
            return;
        }
        if (team == null) {
            leaveTeam();
            return;
        }
        TeamMatcher.removeQueue(team);
        team.sendMessage(service, null);
        team.status = TeamObject.STATUS_NONE;
    }

    void searchGame() {
        TeamObject team = user.getTeam();
        if (team == null) {
            leaveTeam();
            return;
        }
        if (team.hostId != user.getId()) {
//            addErrMessage("Chủ phòng mới được quyền bắt đầu");
            addErrMessage(getLang(Lang.permission_start_game));
            return;
        }
        if (team.type != TeamObject.SOLO && team.userReady.size() < team.aUser.size() - 1) {
            addErrMessage(getLang(Lang.all_members_not_ready));
            return;
        }
        team.lastAction = System.currentTimeMillis();
        if (team.type == TeamObject.BOSS) {
            searchGameBoss(channel, user, team, srcRequest);
        } else if (team.type == TeamObject.RANK || team.type == TeamObject.TRAIN) {
            if (team.type == TeamObject.RANK && CfgServer.isRankClosed()) { // 8h - 11h
                addErrMessage(getLang(Lang.closed_rank_match));
                return;
            }
            searchGameTeam(channel, user, team, srcRequest);
        }
    }

    void searchGameTeam(Channel channel, UserInfo user, TeamObject team, byte[] srcRequest) {
        if (team.status == TeamObject.STATUS_NONE) {
            if (CfgSolo.inEvent() && team.aUser.size() > 1) {
//                addErrMessage("Ngày hôm nay chỉ được đánh solo");
                addErrMessage(getLang(Lang.only_solo));
                return;
            }
            TeamMatcher.addQueue(team);
            team.sendMessage(HAVE_SEARCH_TEAM, null);
        } else {
//            addErrMessage("Đang chơi hoặc ở trong hàng chờ");
            addErrMessage(getLang(Lang.is_playing_or_pending));
        }
    }

    void searchGameBoss(Channel channel, UserInfo user, TeamObject team, byte[] srcRequest) {
        if (srcRequest == null || srcRequest.length == 0) {
            addErrMessage(getLang(Lang.err_param));
            return;
        }
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        team.map = (int) cmm.getANumber(0);
        team.mode = (int) cmm.getANumber(1);
        if (team.status == TeamObject.STATUS_NONE) {
            if (team.type == TeamObject.BOSS) {
                String checkMapLevel = team.isEnoughMapLevel(team.mode, team.map);
                if (checkMapLevel.length() > 0) {
//                    addPopupMessage(checkMapLevel + " chưa đánh tới ải này");
                    addPopupMessage(String.format(getLang(Lang.is_not_allowed_map), checkMapLevel));
                    return;
                }
                MapEntity map = MapResource.getMap(team.map);
                if (map == null) {
//                    addErrMessage("Bản đồ không hợp lệ");
                    addErrMessage(getLang(Lang.not_correct_map));
                    return;
                }
                String checkEnergy = team.isEnoughEnergy(map.getEnergy());
                if (checkEnergy.length() > 0) {
//                    addPopupMessage(checkEnergy + " không đủ thể lực");
                    addPopupMessage(String.format(getLang(Lang.check_not_enough_energy), checkEnergy));
                    return;
                }
                String checkNumberAtk = team.isEnoughNumberAtk(team.mode, team.map, map.getTimesAtk());
                if (checkNumberAtk.length() > 0) {
//                    addPopupMessage(checkNumberAtk + " đã hết lượt đánh");
                    addPopupMessage(String.format(getLang(Lang.check_not_enough_turn_attack), checkNumberAtk));
                    return;
                }
                BattleDataEntity battle = dbUseEnergy(team.aUser, Arrays.asList(-map.getEnergy(), -map.getEnergy()));
                if (battle == null) {
//                    addErrMessage("Không tạo được bàn chơi");
                    addErrMessage(getLang(Lang.dont_create_game));
                    return;
                }
                try {
                    // tru the luc, start battle history
                    team.status = TeamObject.STATUS_PLAY;
                    CacheBattle cacheBattle = team.getCacheValue(battle.getId());
//                    System.out.println("cacheBattle--->"+cacheBattle.getKey().toString());
                    cacheBattle.setCurStar(user.getUData().getMap().getStar(team.mode, team.map));
                    MCache.getInstance().setNormal(cacheBattle.getKey(), cacheBattle, MCache.EXPIRE_5M);
                    //
                    team.sendBattleInfo(cacheBattle.getKey());
                    if (team.aUser.size() >= 2) useFreeAtk();
                } catch (Exception ex) {
                    if (battle != null) {
                        dbCancelBattle(battle.getId());
                    }
                    team.status = TeamObject.STATUS_NONE;
//                    addErrMessage("Không tạo được bàn chơi");
                    addErrMessage(getLang(Lang.dont_create_game));
                    ex.printStackTrace();
                    Logs.error(Util.exToString(ex));
                }
//                new TableBossBoom(Arrays.asList(team), TeamObject.BOSS, Arrays.asList(team).get(0).map);
            } else if (team.type == TeamObject.TRAIN) {
                if (team.aUser.size() > 1) {
                    team.status = TeamObject.STATUS_PLAY;
                    new TableSoloBoomBackup(Arrays.asList(team), TeamObject.TRAIN, Arrays.asList(team).get(0).map);
                } else {
//                    addErrMessage("Phải có ít nhất 2 người để bắt đầu huấn luyện");
                    addErrMessage(getLang(Lang.minimum_player_to_train));
                    return;
                }
            }
        } else {
//            addErrMessage("Đang tạo bàn chơi");
            addErrMessage(getLang(Lang.game_is_creating));
            return;
        }
    }

    void readyGame() {
        TeamObject team = user.getTeam();
        if (team == null) {
            leaveTeam();
            return;
        }
        if (team.hostId == user.getId()) {
//            addErrMessage("Bạn là chủ phòng");
            addErrMessage(getLang(Lang.is_room_master));
            return;
        }
        if (team.type != TeamObject.SOLO) {
            if (team.status == TeamObject.STATUS_NONE) {
                List<Long> aLong = new ArrayList<Long>();
                if (team.userReady.contains(user.getUsername())) {
                    aLong.add(0l);
                    team.userReady.remove(user.getUsername());
                } else {
                    team.userReady.add(user.getUsername());
                    aLong.add(1l);
                }
                for (int i = 0; i < team.aUser.size(); i++) {
                    sendMessage(Online.getChannel(team.aUser.get(i).getId()), service, CommonProto.getCommonLongVectorProto(aLong, Arrays.asList(user.getUsername())));
                }
            } else {
//                addErrMessage("Đang tạo bàn chơi");
                addErrMessage(getLang(Lang.game_is_creating));
            }
        }
    }

    void inviteSupportStatus() {
        addResponse(INVITE_SUPPORT_STATUS, protoBossSupport());
    }

    void buyFreeInvite() {
        JSONArray arr = getBossSupport();
        int freeAtk = arr.getInt(0);
        long countdown = arr.getLong(1);
        int timeRequired = 15 * 60000;
        int fee = 0, newFee = 0, newFreeAtk = freeAtk;
        if (freeAtk > 0 && System.currentTimeMillis() - countdown < timeRequired) {
            fee = INVITE_FEE_BUY_COUNTDOWN;
        } else {
            fee = arr.getInt(2) + INVITE_FEE_BUY;
            newFee = fee;
            newFreeAtk += 1;
        }
        if (fee > 0) {
            JSONArray tmp = user.getAction().addMoney(this, Constans.PRICE_GEM, -fee);
            if (tmp == null) return;
            List<Long> aLong = Bonus.receiveListItem(user, tmp, "free_invite");
            if (aLong.isEmpty()) {
                addErrMessage();
                return;
            }
//            System.out.println("newFee = " + newFee);
            arr.set(0, newFreeAtk);
            arr.set(1, 0);
            arr.set(2, newFee);
//            System.out.println("arr.toString() = " + arr.toString());
            saveBossSupport(arr.toString());
        }
        addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList(user.getDbUser().getGem()), null));
    }

    void useFreeAtk() {
        JSONArray arr = getBossSupport();
        arr.set(0, arr.getInt(0) - 1);
        arr.set(1, System.currentTimeMillis());
        saveBossSupport(arr.toString());
    }

    static int INVITE_FEE_BUY_COUNTDOWN = 20;
    static int INVITE_FEE_BUY = 20;

    GGProto.CommonVector protoBossSupport() {
        JSONArray arr = getBossSupport();
        int freeAtk = arr.getInt(0);
        long countdown = arr.getLong(1);
        int timeRequired = 15 * 60000;
        GGProto.CommonVector.Builder builder = GGProto.CommonVector.newBuilder();
        if (freeAtk > 0) {
            if (System.currentTimeMillis() - countdown < timeRequired) {
                builder.addANumber(1);
                builder.addANumber((timeRequired - System.currentTimeMillis() + countdown) / 1000);
                builder.addANumber(0);
            } else {
                builder.addANumber(0);
                builder.addANumber(0);
                builder.addANumber(0);
            }
        } else {
            builder.addANumber(2);
            builder.addANumber(0);
            builder.addANumber(arr.getInt(2) + INVITE_FEE_BUY);
        }
        builder.addANumber(INVITE_FEE_BUY_COUNTDOWN);
        return builder.build();
    }

    void saveBossSupport(String value) {
        JCache.getInstance().setValue(DateTime.getDateyyyyMMdd(new Date()) + "boss_support:" + user.getId(), value);
    }

    JSONArray getBossSupport() {
        String value = JCache.getInstance().getValue(DateTime.getDateyyyyMMdd(new Date()) + "boss_support:" + user.getId());
//        System.out.println("value = " + value);
        if (value == null) {
            value = "[3,0,0]"; // freeSpp, countdown, fee
        }
        return JSONArray.fromObject(value);
    }
    //endregion

    //region Database Access
    void dbCancelBattle(int battleId) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.createSQLQuery("delete from battle_history where id=" + battleId).executeUpdate();
            session.getTransaction().commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
    }

    BattleDataEntity dbUseEnergy(List<UserInfo> aUser, List<Integer> aEnergy) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            BattleDataEntity battle = new BattleDataEntity(TeamObject.BOSS);
            session.save(battle);

            for (int i = 0; i < aUser.size(); i++) {
                session.save(new BattleUserEntity(battle.getId(), aUser.get(i).getId(), Bonus.defaultBonusView(Bonus.USER_ENERGY, aEnergy.get(i)).toString(), TeamObject.BOSS));
            }

            session.getTransaction().commit();
            return battle;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion
}
