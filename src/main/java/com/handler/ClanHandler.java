package com.handler;

import com.bem.config.CfgClan;
import com.bem.config.CfgCommon;
import com.bem.config.CfgNewMission;
import com.bem.config.lang.Lang;
import com.bem.dao.ClanDAO;
import com.bem.dao.UserDAO;
import com.bem.dao.mapping.ClanBattleBonusEntity;
import com.bem.dao.mapping.ClanEntity;
import com.bem.dao.mapping.UserEntity;
import com.bem.dao.mapping.UserMessageEntity;
import com.bem.monitor.Bonus;
import com.bem.monitor.ClanMonitor;
import com.bem.monitor.Online;
import com.bem.monitor.UserMonitor;
import com.bem.object.UserInfo;
import com.bem.object.UserMoneys;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.cache.JCache;
import com.cache.MCache;
import com.k2tek.Constans;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import grep.helper.StringHelper;
import io.netty.channel.Channel;
import net.sf.json.JSONArray;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.util.*;

/**
 * Created by Mashi on 12/11/2014.
 */
public class ClanHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mGameHandler) {
        List<Integer> services = Arrays.asList(CLAN_CREATE, CLAN_SETTING, CLAN_JOIN_RULE, CLAN_JOIN,
                CLAN_KICK, CLAN_CHANGE_NAME, CLAN_AVATAR_LIST, CLAN_AVATAR_BUY, CLAN_AVATAR_CHANGE,
                CLAN_CHAT_LIST, CLAN_CHAT, CLAN_DIEMDANH_STATUS, CLAN_DIEMDANH, CLAN_SHOP_LIST,
                CLAN_SHOP_BUY, CLAN_STATUS, CLAN_MEMBER_LIST, CLAN_SEARCH, CLAN_TOP,
                CLAN_LEAVE, CLAN_PROMOTE, CLAN_DEMOTE, CLAN_SET_POSITION, CLAN_SUGGESTION, CLAN_OWN_INFO,
                CLAN_ANSWER_REQ_JOIN, CLAN_INVITE_JOIN, CLAN_ANSWER_INVITE_JOIN, CLAN_BONUS_LIST, CLAN_BONUS_SEND);
        services.forEach(service -> {
            mGameHandler.put(service, getInstance());
        });
    }

    static ClanHandler instance;

    public static ClanHandler getInstance() {
        if (instance == null) {
            instance = new ClanHandler();
        }
        return instance;
    }

    @Override
    public AHandler newInstance() {
        return new ClanHandler();
    }

    @Override
    public void doSubAction() {
        try {
            switch (service) {
                case CLAN_CREATE:
                    create();
                    break;
                case CLAN_SEARCH:
                    search();
                    break;
                case CLAN_SUGGESTION:
                    suggestion();
                    break;
                case CLAN_JOIN:
                    join();
                    break;
                case CLAN_STATUS:
                    status(service, ClanMonitor.getClan((int) CommonProto.parseCommonVector(srcRequest).getANumber(0)), user);
                    break;
                case CLAN_MEMBER_LIST:
                    memberList((int) CommonProto.parseCommonVector(srcRequest).getANumber(0));
                    break;
                case CLAN_TOP:
                    top();
                    break;
                case CLAN_OWN_INFO:
                    ownInfo();
                    break;
                case CLAN_ANSWER_INVITE_JOIN:
                    answerInviteJoin();
                    break;
                default:
                    if (user.getDbUser().getClan() == 0) {
                        addErrMessage(getLang(Lang.clan_no_clan));
                        return;
                    }
                    switch (service) {
                        case CLAN_BONUS_LIST:
                            bonusList();
                            break;
                        case CLAN_CHANGE_NAME:
                            changeName();
                            break;
                        case CLAN_AVATAR_LIST:
                            break;
                        case CLAN_AVATAR_BUY:
                            break;
                        case CLAN_AVATAR_CHANGE:
                            break;
                        case CLAN_CHAT_LIST:
                            chatList(CommonProto.parseCommonVector(srcRequest).getANumber(0));
                            break;
                        case CLAN_CHAT:
                            chat();
                            break;
                        case CLAN_DIEMDANH_STATUS:
                            break;
                        case CLAN_DIEMDANH:
                            diemdanh();
                            break;
                        case CLAN_SHOP_LIST:
                            break;
                        case CLAN_SHOP_BUY:
                            break;
                        case CLAN_LEAVE:
                            leave();
                            break;
                        case CLAN_ANSWER_REQ_JOIN:
                            answerReqJoin();
                            break;
                        default:
                            if (user.getDbUser().getClanPosition() != Constans.CLAN_LEADER && user.getDbUser().getClanPosition() != Constans.CLAN_CO_LEADER) {
                                addErrMessage(getLang(Lang.clan_only_co_and_leader));
                                return;
                            }
                            switch (service) {
                                case CLAN_BONUS_SEND:
                                    sendBonus();
                                    break;
                                case CLAN_SETTING:
                                    setting();
                                    break;
                                case CLAN_KICK:
                                    kick();
                                    break;
                                case CLAN_JOIN_RULE:
                                    break;
                                case CLAN_PROMOTE:
                                    promote();
                                    break;
                                case CLAN_DEMOTE:
                                    demote();
                                    break;
                                case CLAN_SET_POSITION:
                                    setPosition();
                                    break;
                                case CLAN_INVITE_JOIN:
                                    inviteJoin();
                                    break;
                            }
                            break;
                    }
                    break;
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
            addErrMessage();
        }
    }

    void answerInviteJoin() {
        int clanId = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
        Integer saveId = MCache.getInstance().getInt("invite:" + clanId + "_" + user.getId());
//        System.out.println(saveId + " " + clanId);
        if (saveId == null || saveId != clanId) {
            addErrMessage(getLang(Lang.clan_invite_expire));
            return;
        }
        if (user.getDbUser().getLevel() < CfgClan.config.levelRequired) {
            addErrMessage(String.format(getLang(Lang.clan_join_level_required), CfgClan.config.levelRequired));
            return;
        }
        if (user.getDbUser().getClan() > 0) {
            addErrMessage(getLang(Lang.clan_leave_first));
            return;
        }
        ClanEntity clan = ClanMonitor.getClan(clanId);
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        String canJoin = CfgClan.canJoin(user.getId());
        if (canJoin.equals("")) {
            String result = clan.forceJoinClan(user);
            if (!StringHelper.isEmpty(result)) {
                addErrMessage(result);
                return;
            }
            addResponse(null);
        } else {
            addErrMessage(canJoin);
        }
    }

    void inviteJoin() {
        long inviteId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        Channel inviteChannel = Online.getChannel(inviteId);
        if (inviteChannel == null) {
            addErrMessage(getLang(Lang.user_not_online));
            return;
        }
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        if (clan.getMember() >= CfgClan.maxMember(clan.getLevel())) {
            addErrMessage(getLang(Lang.clan_max_member));
            return;
        }
        if (new UserDAO().isFriend(user.getId(), inviteId) == 1) {
            addErrMessage(getLang(Lang.common_success));
            MCache.getInstance().set("invite:" + clan.getId() + "_" + inviteId, clan.getId(), MCache.EXPIRE_1M);
            sendMessage(inviteChannel, CLAN_INVITE_JOIN_REQ, CommonProto.getCommonLongVectorProto(Arrays.asList((long) clan.getId()), Arrays.asList(user.getDbUser().getName(), clan.getName())));
        } else {
            addErrMessage(getLang(Lang.user_invite_friend_only));
        }
    }

    void sendBonus() {
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        if (user.getDbUser().getClanPosition() != Constans.CLAN_LEADER)
            addErrMessage(getLang(Lang.clan_only_leader));
        else {
            List<Long> aLong = CommonProto.parseCommonVector(srcRequest).getANumberList();
            long userId = aLong.get(0);
            int bonusId = aLong.get(1).intValue();
            ClanBattleBonusEntity bonus = (ClanBattleBonusEntity) Database2.getUnique("clan_battle_bonus", Arrays.asList("id", String.valueOf(bonusId)), ClanBattleBonusEntity.class);
            if (bonus == null) addErrMessage(getLang(Lang.err_bonus_not_exist));
            else if (bonus.getNumberBonusAvailable() <= 0)
                addErrMessage(getLang(Lang.clan_bonus_out_of_stock));
            else {
                UserEntity receiver = new UserDAO().getUserEntity(userId);
                if (receiver == null) addErrMessage(getLang(Lang.user_not_exist));
                else {
                    if (receiver.getClan() != clan.getId()) addErrMessage(getLang(Lang.clan_member_leave));
                    else if (dbSendBonus(user.getLang(), user.getDbUser().getName(), bonus, receiver))
                        addResponse(null);
                    else addErrMessage();
                }
            }
        }
    }

    void bonusList() {
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        List<ClanBattleBonusEntity> aBonus = Database2.getList("clan_battle_bonus", Arrays.asList("clan_id", String.valueOf(clan.getId())), "order by date_created desc limit 0,20",
                ClanBattleBonusEntity.class);
        if (aBonus == null) addErrMessage();
        else {
            GGProto.ListCommonVector.Builder builder = GGProto.ListCommonVector.newBuilder();
            aBonus.forEach(bonus -> builder.addAVector(bonus.toProto()));
            addResponse(builder.build());
        }
    }

    void changeName() {
        String name = CommonProto.parseCommonVector(srcRequest).getAString(0);
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        if (user.getDbUser().getClanPosition() != Constans.CLAN_LEADER) {
            addErrMessage(getLang(Lang.clan_only_leader));
            return;
        }
        if (name.length() < CfgClan.config.nameLength[0] || name.length() > CfgClan.config.nameLength[1]) {
            addErrMessage(String.format(getLang(Lang.clan_err_setting_name), CfgClan.config.nameLength[0], CfgClan.config.nameLength[1]));
            return;
        }
        JSONArray arr = user.getAction().checkMoney(this, Constans.PRICE_GEM, -CfgClan.config.feeChangeName);
        if (arr == null) return;
        if (Bonus.receiveListItem(user, arr, "clan_name").isEmpty()) {
            addErrMessage();
            return;
        }
        if (new ClanDAO().changeName(clan.getId(), name)) {
            clan.setName(name);
            addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList(user.getDbUser().getGem()), null));
        } else addErrMessage();
    }

    void answerReqJoin() {
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        if (user.getDbUser().getClanPosition() == Constans.CLAN_MEMBER) {
            addErrMessage(getLang(Lang.clan_only_elder));
            return;
        }
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        long lastReq = cmm.getANumber(0);
        long userId = cmm.getANumber(1);
        boolean isAccept = cmm.getANumber(2) == 1;
        String result = clan.answerReqJoin(user.getLang(), userId, isAccept);
        if (!StringHelper.isEmpty(result)) {
            addErrMessage(result);
            return;
        }
        chatList(lastReq);
        if (isAccept) addResponse(null);
    }

    void diemdanh() {
        int timeDiemdanh = CfgClan.timeDiemdanh(user.getDbUser());
        if (timeDiemdanh > 0) {
            addErrMessage(getLang(Lang.clan_err_time_diemdanh));
            return;
        }
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        Integer numberDiemdanh = JCache.getInstance().getIntValue(DateTime.getDateyyyyMMdd(new Date()) + "diemdanh:" + clan.getId());
        if (numberDiemdanh == null) numberDiemdanh = 0;
        if (numberDiemdanh > CfgClan.maxMember(clan.getLevel())) {
            addErrMessage(getLang(Lang.clan_max_diemdanh));
            return;
        }
        if (!clan.diemdanh(user)) {
            addErrMessage();
            return;
        }
        JCache.getInstance().setValue(DateTime.getDateyyyyMMdd(new Date()) + "diemdanh:" + clan.getId(), String.valueOf(++numberDiemdanh));
        addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList((long) CfgClan.config.diemdanh[2],
                (long) user.getUData().getUserMoneys().getValue(UserMoneys.CLAN), clan.getExp(), (long) clan.getLevel()), null));
    }

    void top() {
        int type = (int) CommonProto.parseCommonVector(srcRequest).getANumber(0);
//        System.out.println("type----------->" + type);
        if (type == 0) { // top level
            List<ClanEntity> aClan = Database2.getList("clan", Arrays.asList("server_id", String.valueOf(user.getDbUser().getServer())), "order by level desc, exp desc limit 0,100", ClanEntity.class);
            if (aClan == null) {
                addErrMessage();
                return;
            }
            addResponse(protoListClan(aClan));
        } else if (type == 2) { //  top chien tich

        } else if (type == 1) {// top trophy
            List<ClanEntity> lstclan = new ArrayList<>();
            lstclan.addAll(CfgNewMission.entryLstClanEntity(CfgNewMission.aGet, null, user.getDbUser().getServer()));
//            for (int i = 0; i <lstclan.size() ; i++) {
////                System.out.println("tp-->"+lstclan.get(i).getTrophy());
//            }
            if (lstclan != null) {
                addResponse(protoListClan(lstclan));
            }
        }

    }

    void promote() {
        long promoteId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        String result = clan.promote(user, promoteId);
        if (!StringHelper.isEmpty(result)) {
            addErrMessage(result);
            return;
        }
        addResponse(null);
    }

    void setPosition() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        long userId = cmm.getANumber(0);
        int newPosition = (int) cmm.getANumber(1);
        if (newPosition < Constans.CLAN_MEMBER || newPosition > Constans.CLAN_LEADER) {
            addErrMessage(getLang(Lang.clan_err_position));
            return;
        }
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        String result = clan.setPosition(user, userId, newPosition);
        if (!StringHelper.isEmpty(result)) {
            addErrMessage(result);
            return;
        }
        addResponse(null);
    }

    void demote() {
        long demoteId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        String result = clan.demote(user, demoteId);
        if (!StringHelper.isEmpty(result)) {
            addErrMessage(result);
            return;
        }
        addResponse(null);
    }

    void kick() {
        long kickId = CommonProto.parseCommonVector(srcRequest).getANumber(0);
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        String result = clan.kick(user, kickId);
        if (!StringHelper.isEmpty(result)) {
            addErrMessage(result);
            return;
        }
        UserInfo kickUser = UserMonitor.getUser(kickId);
        if (kickUser != null) {
            kickUser.getDbUser().setClan(0);
            kickUser.getDbUser().setClanPosition(0);
        }
        Channel kickChannel = Online.getChannel(kickId);
        if (kickChannel != null) {
            sendMessage(kickChannel, CLAN_KICK_NOTIFY, null);
        }
        addResponse(null);
    }

    void memberList(int clanId) {
        ClanEntity clan = ClanMonitor.getClan(clanId);
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        List<UserEntity> aUser = new ArrayList<UserEntity>(clan.getAUser());
        Collections.sort(aUser, (u1, u2) -> new Integer(u2.getTrophy()).compareTo(new Integer(u1.getTrophy())));
        addResponse(CLAN_MEMBER_LIST, protoMember(clan, aUser));
    }

    void leave() {
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage();
            return;
        }
        if (clan.leaveClan(user)) {
            addResponse(null);
        } else {
            addErrMessage();
        }
    }

    void chat() {
        String lockChat = Util.lockChat(user.getDbUser().getLockChat());
        if (lockChat != null) {
            addErrMessage(lockChat);
            return;
        }
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        long reqTime = cmm.getANumber(0);
        String msg = cmm.getAString(0);
        if (msg.length() < CfgCommon.config.chat.length[0] || msg.length() > CfgCommon.config.chat.length[1]) {
            addErrMessage(String.format(getLang(Lang.common_chat_length), CfgCommon.config.chat.length[1]));
            return;
        }
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage();
            return;
        }
        clan.addChat(user, msg);
        chatList(reqTime);
    }

    void chatList(long reqTime) {
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage();
            return;
        }
        addResponse(CLAN_CHAT_LIST, clan.getChatHistory(reqTime));
    }

    void join() {
        if (user.getDbUser().getLevel() < CfgClan.config.levelRequired) {
            addErrMessage(String.format(getLang(Lang.clan_join_level_required), CfgClan.config.levelRequired));
            return;
        }
        if (user.getDbUser().getClan() > 0) {
            addErrMessage(getLang(Lang.clan_leave_first));
            return;
        }
        ClanEntity clan = ClanMonitor.getClan((int) CommonProto.parseCommonVector(srcRequest).getANumber(0));
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        if (clan.getServerId() != user.getDbUser().getServer()) {
            addErrMessage(getLang(Lang.err_clan_not_same_server));
            return;
        }
        String canJoin = CfgClan.canJoin(user.getId());
        if (canJoin.equals("")) {
            String result = clan.joinClan(user);
            if (!StringHelper.isEmpty(result)) {
                addErrMessage(result);
                return;
            }
            status(CLAN_OWN_INFO, clan, user);
        } else {
            addErrMessage(canJoin);
        }
    }

    void ownInfo() {
        int clanId = user.getDbUser().getClan();
        if (clanId == 0) {
            addResponse(CLAN_KICK_NOTIFY, null);
            return;
        }
        ClanEntity clan = ClanMonitor.getClan(clanId);
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        addResponse(protoClan(clan, user));
    }

    void status(int service, ClanEntity clan, UserInfo user) {
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        addResponse(service, protoClan(clan, user));
    }

    void search() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        ClanEntity clan = null;
        if (cmm.getANumberList().size() > 0) {
            clan = ClanMonitor.getClan((int) cmm.getANumber(0));
            if (clan != null && clan.getServerId() != user.getDbUser().getServer()) clan = null;
        } else if (cmm.getAStringList().size() > 0) {
            clan = (ClanEntity) Database2.getUnique("clan", Arrays.asList("name", cmm.getAString(0), "server_id", String.valueOf(user.getDbUser().getServer())), ClanEntity.class);
        }
        if (clan == null) {
            addErrMessage(getLang(Lang.clan_not_exist));
            return;
        }
        addResponse(protoClan(clan, null));
    }

    void suggestion() {
        List<ClanEntity> aClan = dbSuggestion(user.getDbUser().getTrophy());
        if (aClan == null) {
            addErrMessage();
            return;
        }
//        for (int i = 0; i < aClan.size(); i++) {
//            ClanEntity clan = ClanMonitor.getClan(aClan.get(i).getId());
//            if (clan != null) {
//                aClan.set(i, clan);
//            }
//        }
        addResponse(protoListClan(aClan));
    }

    void setting() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);

        int joinRule = (int) cmm.getANumber(0);
        int joinTrophy = (int) cmm.getANumber(1);
        int clanAvatar = (int) cmm.getANumber(2);
        String slogan = cmm.getAString(0);

        if (slogan.length() < CfgClan.config.sloganLength[0] || slogan.length() > CfgClan.config.sloganLength[1]) {
            addErrMessage(String.format(getLang(Lang.clan_err_setting_desc), CfgClan.config.sloganLength[0], CfgClan.config.sloganLength[1]));
            return;
        }

        if (!Arrays.asList(Constans.CLAN_JOIN_CLOSED, Constans.CLAN_JOIN_INVITE_ONLY, Constans.CLAN_JOIN_OPEN).contains(joinRule)) {
            joinRule = Constans.CLAN_JOIN_OPEN;
        }

        if (joinTrophy < 0) {
            addErrMessage(getLang(Lang.clan_err_setting_trophy));
            return;
        }
        ClanEntity clan = ClanMonitor.getClan(user.getDbUser().getClan());
        if (clan == null) {
            addErrMessage();
            return;
        }
        if (Database2.update("clan", Arrays.asList("slogan", slogan, "join_rule", String.valueOf(joinRule), "join_trophy", String.valueOf(joinTrophy), "avatar", String.valueOf(clanAvatar)),
                Arrays.asList("id", String.valueOf(user.getDbUser().getClan())))) {
            clan.setSlogan(slogan);
            clan.setAvatar(clanAvatar);
            clan.setJoinRule(joinRule);
            clan.setJoinTrophy(joinTrophy);
            addResponse(service, null);
        } else {
            addErrMessage();
        }
    }

    void create() {
        GGProto.CommonVector cmm = CommonProto.parseCommonVector(srcRequest);
        String name = StringHelper.removeNonePrintChars(cmm.getAString(0));
        String slogan = cmm.getAString(1);
        int avatar = (int) cmm.getANumber(0);
        if (avatar < 1) {
            addErrMessage(getLang(Lang.clan_err_symbol));
            return;
        }
        if (slogan.length() < CfgClan.config.sloganLength[0] || slogan.length() > CfgClan.config.sloganLength[1]) {
            addErrMessage(String.format(getLang(Lang.clan_err_setting_desc), CfgClan.config.sloganLength[0], CfgClan.config.sloganLength[1]));
            return;
        }
        if (name.length() < CfgClan.config.nameLength[0] || name.length() > CfgClan.config.nameLength[1]) {
            addErrMessage(String.format(getLang(Lang.clan_err_setting_name), CfgClan.config.nameLength[0], CfgClan.config.nameLength[1]));
            return;
        }
        if (user.getDbUser().getClan() > 0) {
            addErrMessage(getLang(Lang.clan_leave_first));
            return;
        }
        String canJoin = CfgClan.canJoin(user.getId());
        if (canJoin.length() > 0) {
            addErrMessage(canJoin);
            return;
        }
        if (dbExistName(name)) {
            addErrMessage(getLang(Lang.clan_name_exist));
            return;
        }
        JSONArray arr = user.getAction().addMoney(this, Constans.PRICE_GEM, -CfgClan.config.feeCreate);
        if (arr == null) {
            addErrMessage();
            return;
        }
        List<Long> tmp = Bonus.receiveListItem(user, arr, "create_clan");
        if (tmp.isEmpty()) {
            addErrMessage();
            return;
        }
        ClanEntity clan = dbCreateClan(user.getDbUser(), name, slogan, avatar);
        if (clan != null) {
            ClanMonitor.addClan(clan);
            user.getDbUser().setClan(clan.getId());
            user.getDbUser().setClanPosition(Constans.CLAN_LEADER);
            addResponse(CommonProto.getCommonLongVectorProto(Arrays.asList(user.getDbUser().getGem()), null));
            status(CLAN_OWN_INFO, ClanMonitor.getClan(clan.getId()), user);
        } else {
            Bonus.receiveListItem(user, Bonus.defaultBonusView(Bonus.GEM, CfgClan.config.feeCreate), "create_clan_fail");
            addErrMessage();
        }
    }

    //region logic

    //endregion

    //<editor-fold desc="Database Access">
    public boolean dbSendBonus(Lang lang, String leaderName, ClanBattleBonusEntity bonus, UserEntity receiver) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();

            UserMessageEntity uMessage = new UserMessageEntity();
            uMessage.setTitle(String.format(lang.get(Lang.clan_gift), leaderName));
            uMessage.setMessage(bonus.getMessage());
            uMessage.setDateCreated(new Date());
            uMessage.setUserId(receiver.getId());
            uMessage.setBonus(bonus.getBonus());
            session.save(uMessage);

            bonus.addReceiver(receiver.getName());
            session.update(bonus);

            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public List<ClanEntity> dbSuggestion(int trophy) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select * from clan where member<max_member and (join_rule = 0 or join_rule = 1) and join_trophy <= " + trophy + " order by rand() limit 0, 10");
            return query.addEntity(ClanEntity.class).list();
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public boolean dbExistName(String name) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select count(*) from clan where name=:name");
            query.setString("name", name);
            Integer count = Integer.parseInt(query.uniqueResult().toString());
            return count > 0;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return true;
    }

    public ClanEntity dbCreateClan(UserEntity user, String name, String slogan, int avatar) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            ClanEntity clan = new ClanEntity();
            clan.setServerId(user.getServer());
            clan.setName(name);
            clan.setSlogan(slogan);
            clan.setAvatar(avatar);
            session.save(clan);
            //
            session.createSQLQuery("update user set clan=" + clan.getId() + ", clan_position=" + Constans.CLAN_LEADER + " where id=" + user.getId()).executeUpdate();
            session.getTransaction().commit();
            return clan;
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //<editor-fold>

    //<editor-fold desc="Proto">
    GGProto.ProtoListClan protoListClan(List<ClanEntity> aClan) {
        GGProto.ProtoListClan.Builder builder = GGProto.ProtoListClan.newBuilder();
        int clanId = user.getDbUser().getClan();
        ClanEntity mclan = null;
        int mytop = -1;
        GGProto.ProtoClan.Builder protomClan = GGProto.ProtoClan.newBuilder();
        if (clanId > 0) {
            mclan = ClanMonitor.getClan(clanId);
            if (mclan != null) {
                mclan.setTrophy(mclan.getClanTrophy());
                protomClan.setId(mclan.getId());
                protomClan.setName(mclan.getName());
                protomClan.setMember(mclan.getMember());
                protomClan.setLevel(mclan.getLevel());
                protomClan.setAvatar(mclan.getAvatar());
                protomClan.addAllJoinRule(Arrays.asList(mclan.getJoinRule(), mclan.getJoinTrophy()));
                protomClan.setTrophy(mclan.getTrophy());
                builder.setMyClan(protomClan);
            }
        }


        for (int i = 0; i < aClan.size(); i++) {
            ClanEntity clan = aClan.get(i);
            GGProto.ProtoClan.Builder protoClan = GGProto.ProtoClan.newBuilder();
            protoClan.setId(clan.getId());
            protoClan.setName(clan.getName());
            protoClan.setMember(clan.getMember());
            protoClan.setLevel(clan.getLevel());
            protoClan.setAvatar(clan.getAvatar());
            protoClan.addAllJoinRule(Arrays.asList(clan.getJoinRule(), clan.getJoinTrophy()));
            protoClan.setTrophy(clan.getTrophy());
            builder.addAClan(protoClan);
            if (mclan != null && clan.getId() == mclan.getId()) {
                mytop = i + 1;
            }
        }
        builder.setMyClanRank(mytop);
        return builder.build();
    }

    GGProto.ProtoClan protoClan(ClanEntity clan, UserInfo user) {
        GGProto.ProtoClan.Builder builder = GGProto.ProtoClan.newBuilder();
        builder.setId(clan.getId());
        builder.setName(clan.getName());
        builder.setMember(clan.getMember());
        builder.setSlogan(clan.getSlogan());
        builder.setLevel(clan.getLevel());
        builder.setExp(clan.getExp());
        builder.setAvatar(clan.getAvatar());
        builder.addAllJoinRule(Arrays.asList(clan.getJoinRule(), clan.getJoinTrophy()));
        if (user == null) {
            builder.setPosition(Constans.CLAN_NOT_MEMBER);
        } else {
            builder.setPosition(user.getDbUser().getClanPosition());
            builder.addMyInfo(CfgClan.timeDiemdanh(user.getDbUser()));
            builder.addMyInfo(user.getUData().getUserMoneys().getValue(UserMoneys.CLAN));
        }
        builder.setTrophy(clan.getTrophy());
        return builder.build();
    }

    GGProto.ProtoListUser protoMember(ClanEntity clan, List<UserEntity> aUser) {
        long curTime = System.currentTimeMillis();
        GGProto.ProtoListUser.Builder builder = GGProto.ProtoListUser.newBuilder();
        aUser.forEach(user -> {
            GGProto.ProtoUser.Builder protoUser = GGProto.ProtoUser.newBuilder();
            protoUser.setId(user.getId());
            protoUser.setName(user.getName());
            protoUser.setRank(user.getRank());
            protoUser.setLvl(user.getLevel());
            protoUser.setExp(user.getExp());
            protoUser.setTrophy(user.getTrophy());
            protoUser.addInfor(user.getVip());
            protoUser.addInfor(user.getGemNap());
            protoUser.addClan(user.getClan());
            protoUser.addClan(user.getClanPosition());
            protoUser.addClan(clan.getAvatar());
            protoUser.addClan(JSONArray.fromObject(user.getDiemdanh()).getInt(0));
            protoUser.addAllAvatar(user.getMAvatar().toList());
            protoUser.setOnline(Online.isOnline(user.getId()) ? 1 : (user.getLogout() - curTime) / 1000);
            builder.addAUser(protoUser);
        });
        return builder.build();
    }
    //</editor-fold>
}
