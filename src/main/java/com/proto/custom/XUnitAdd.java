package com.proto.custom;

import com.bem.boom.BoomConfig;
import com.proto.GGProto;
import io.netty.buffer.ByteBuf;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 12/21/2016.
 */
public class XUnitAdd extends XAbstract {
    byte type;
    int id;
    int[] avatar;
    float posX, posY;
    boolean add;
    XPlayerInfo playerInfo;

    public static void parseProtoBuffer(ByteBuf buffer, List<GGProto.ProtoUnitAdd> aUnitAdd) {
        if (aUnitAdd.size() > 0) {
            buffer.writeByte(TYPE_ADD);
            buffer.writeByte(aUnitAdd.size());
            for (int i = 0; i < aUnitAdd.size(); i++) {
                GGProto.ProtoUnitAdd tmp = aUnitAdd.get(i);
                buffer.writeByte(tmp.getType());
                buffer.writeInt(tmp.getId());
                buffer.writeBoolean(tmp.getAdd());
                if (tmp.getAdd()) {
                    buffer.writeFloat(tmp.getPos().getX());
                    buffer.writeFloat(tmp.getPos().getY());
                    buffer.writeByte(tmp.getAvatarCount());
                    for (int j = 0; j < tmp.getAvatarCount(); j++) {
                        buffer.writeInt(tmp.getAvatar(j));
                    }
                    if (tmp.getType() == BoomConfig.TYPE_PLAYER) {
                        GGProto.ProtoPlayerInfo infor = tmp.getPlayerInfo();
                        buffer.writeLong(infor.getId());
                        buffer.writeByte(infor.getTeam());
                        //
                        buffer.writeByte(infor.getAvatarCount());
                        for (int j = 0; j < infor.getAvatarCount(); j++) {
                            buffer.writeInt(infor.getAvatar(j));
                        }
                        buffer.writeByte(infor.getAItemCount());
                        for (int j = 0; j < infor.getAItemCount(); j++) {
                            buffer.writeInt(infor.getAItem(j));
                        }
                        buffer.writeByte(infor.getAPointCount());
                        for (int j = 0; j < infor.getAPointCount(); j++) {
                            buffer.writeFloat(infor.getAPoint(j));
                        }
                        byte[] data;
                        try {
                            data = infor.getName().getBytes("UTF-8");
                        } catch (Exception ex) {
                            data = new byte[0];
                        }
                        buffer.writeByte(data.length);
                        if (data.length > 0) {
                            buffer.writeBytes(data);
                        }
                    }
                }
            }
        }
    }

    public static List<XUnitAdd> parseList(ByteBuf buffer) {
        List<XUnitAdd> aUnitAdd = new ArrayList<XUnitAdd>();
        byte size = buffer.readByte();
        for (int i = 0; i < size; i++) {
            aUnitAdd.add(parse(buffer));
        }
        return aUnitAdd;
    }

    public static XUnitAdd parse(ByteBuf buffer) {
        XUnitAdd obj = new XUnitAdd();
        obj.type = buffer.readByte();
        obj.id = buffer.readInt();
        obj.add = buffer.readBoolean();
        if (obj.add) {
            obj.posX = buffer.readFloat();
            obj.posY = buffer.readFloat();
            byte avatarSize = buffer.readByte();
            obj.avatar = new int[avatarSize];
            for (int i = 0; i < avatarSize; i++) {
                obj.avatar[i] = buffer.readInt();
            }
            if (obj.type == BoomConfig.TYPE_PLAYER) {
                XPlayerInfo playerInfo = new XPlayerInfo();
                playerInfo.id = buffer.readLong();
                playerInfo.team = buffer.readByte();
                playerInfo.speed = buffer.readFloat();
                int avatarCount = buffer.readByte();
                playerInfo.avatars = new int[avatarCount];
                for (int i = 0; i < avatarCount; i++) {
                    playerInfo.avatars[i] = buffer.readInt();
                }
                int itemCount = buffer.readByte();
                playerInfo.items = new int[itemCount];
                for (int i = 0; i < itemCount; i++) {
                    playerInfo.items[i] = buffer.readInt();
                }
                byte[] nameData = new byte[buffer.readableBytes()];
                buffer.readBytes(nameData);
                try {
                    playerInfo.name = new String(nameData, "UTF-8");
                } catch (Exception ex) {
                    playerInfo.name = "";
                }
                obj.playerInfo = playerInfo;
            }
        }
        return obj;
    }


}
