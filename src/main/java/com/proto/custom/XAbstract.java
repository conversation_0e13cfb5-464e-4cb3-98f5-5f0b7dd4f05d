package com.proto.custom;

import com.proto.GGProto;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 12/21/2016.
 */
public abstract class XAbstract {
    protected static final int TYPE_ADD = 1;
    protected static final int TYPE_POS = 2;
    protected static final int TYPE_BOOM = 3;
    protected static final int TYPE_JUMP = 4;
    protected static final int TYPE_PLAYER_STATE = 5;
    protected static final int TYPE_MONSTER_STATE = 6;

    //
    public static final int PROTO_UPDATE_BOOM = 1;
    public static final int PROTO_UPDATE_PLAYER = 2;
    public static final int PROTO_UPDATE_MONSTER = 3;
    public static final int PROTO_UPDATE_JUMP = 4;

    public static byte[] convertProtoBuffToCustom(GGProto.ProtoInitTable proto) throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        buffer.writeInt(proto.getMapId());
        List<GGProto.ProtoUnitAdd> aUnitAdd = new ArrayList<GGProto.ProtoUnitAdd>();
        aUnitAdd.addAll(proto.getAUnitAddList());
        aUnitAdd.addAll(proto.getAItemAddList());
        XUnitAdd.parseProtoBuffer(buffer, aUnitAdd);
        byte[] bytes = new byte[buffer.readableBytes()];
        buffer.readBytes(bytes);
        return bytes;
    }

    public static byte[] convertProtoBuffToCustom(GGProto.ProtoTableState proto) throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        //
        buffer.writeFloat(proto.getServerTime());
        XUnitAdd.parseProtoBuffer(buffer, proto.getAUnitAddList());
        XUnitPos.parseProtoBuffer(buffer, proto.getAUnitPosList());
        //
        int size = proto.getAUnitUpdateCount();
        for (int i = 0; i < size; i++) {
            GGProto.ProtoUnitUpdate update = proto.getAUnitUpdate(i);
            switch (update.getType()) {
                case PROTO_UPDATE_BOOM:
                    GGProto.ProtoListBoom aBoom = GGProto.ProtoListBoom.parseFrom(update.getData(0).toByteArray());
                    XBoom.parseProtoBuffer(buffer, aBoom.getABoomList());
                    break;
                case PROTO_UPDATE_PLAYER:
                    GGProto.ProtoListPlayerState aPlayerState = GGProto.ProtoListPlayerState.parseFrom(update.getData(0).toByteArray());
                    XPlayerState.parseProtoBuffer(buffer, aPlayerState.getAPlayerStateList());
                    break;
                case PROTO_UPDATE_MONSTER:
                    GGProto.ProtoListMonsterState aMonsterState = GGProto.ProtoListMonsterState.parseFrom(update.getData(0).toByteArray());
                    XMonsterState.parseProtoBuffer(buffer, aMonsterState.getAMonsterStateList());
                    break;
                case PROTO_UPDATE_JUMP:
                    GGProto.ProtoListUnitJumpPos aJump = GGProto.ProtoListUnitJumpPos.parseFrom(update.getData(0).toByteArray());
                    XJumpPos.parseProtoBuffer(buffer, aJump.getAJumpList());
                    break;
            }
        }
        byte[] bytes = new byte[buffer.readableBytes()];
        buffer.readBytes(bytes);
//        debug(String.format("new=%s, old=%s", bytes.length, proto.toByteArray().length));
        return bytes;
    }

    public static void parseTableState(byte[] data) {
        ByteBuf buffer = Unpooled.wrappedBuffer(data);
        float serverTime = buffer.readFloat();
        while (buffer.readableBytes() > 0) {
            byte type = buffer.readByte();
            switch (type) {
                case TYPE_POS:
                    XUnitPos.parseList(buffer);
                    break;
                case TYPE_ADD:
                    XUnitAdd.parseList(buffer);
                    break;
                case TYPE_BOOM:
                    XBoom.parseList(buffer);
                    break;
                case TYPE_JUMP:
                    XJumpPos.parseList(buffer);
                    break;
                case TYPE_MONSTER_STATE:
                    XMonsterState.parseList(buffer);
                    break;
                case TYPE_PLAYER_STATE:
                    XPlayerState.parseList(buffer);
                    break;
            }
        }
    }
}
