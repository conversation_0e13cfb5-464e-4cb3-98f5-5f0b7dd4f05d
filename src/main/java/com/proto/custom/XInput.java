package com.proto.custom;

import com.bem.boom.Input;
import com.k2tek.common.slib_Logger;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 12/21/2016.
 */
public class XInput {
    public int seq, clientTime;
    public List<Byte> keys;

    public static XInput parse(byte[] data) {
        XInput obj = new XInput();
        ByteBuf buffer = Unpooled.wrappedBuffer(data);
        obj.seq = buffer.readInt();
        obj.clientTime = buffer.readInt();
        int size = buffer.readableBytes();
        obj.keys = new ArrayList<>();
        if (size <= 5) {
            for (int i = 0; i < size; i++) {
                byte key = buffer.readByte();
                if (key != Input.NONE) {
                    obj.keys.add(key);
                }
            }
        }
//        System.out.println("  obj.clientTime  = " + obj.clientTime);
        return obj;
    }

    public static void main(String[] args) {
        byte[] data = new byte[8];
        data[0] = 1;
        data[1] = 1;
        data[2] = 1;
        data[3] = 1;
        data[4] = 1;
        data[5] = 1;
        data[6] = 1;
        data[7] = 1;
        ByteBuf buffer = Unpooled.wrappedBuffer(data);
        System.out.println(buffer.readableBytes());
        buffer.readInt();
        System.out.println(buffer.readableBytes());
    }
}
