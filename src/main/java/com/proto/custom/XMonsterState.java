package com.proto.custom;

import com.proto.GGProto;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 12/21/2016.
 */
public class XMonsterState extends XAbstract {
    int id;
    int[] status;
    float[] info;

    public static void parseProtoBuffer(ByteBuf buffer, List<GGProto.ProtoMonsterState> aMonsterState) {
        if (aMonsterState.size() > 0) {
            buffer.writeByte(TYPE_MONSTER_STATE);
            buffer.writeByte(aMonsterState.size());
            for (int i = 0; i < aMonsterState.size(); i++) {
                GGProto.ProtoMonsterState tmp = aMonsterState.get(i);
                buffer.writeInt((int) tmp.getId());
                buffer.writeByte(tmp.getStatusCount());
                for (int j = 0; j < tmp.getStatusCount(); j++) {
                    buffer.writeByte(tmp.getStatus(j));
                    buffer.writeFloat(tmp.getInfo(j));
                }
            }
        }
    }

    public static List<XMonsterState> parseList(ByteBuf buffer) {
        List<XMonsterState> aMonsterState = new ArrayList<XMonsterState>();
        byte size = buffer.readByte();
        for (int i = 0; i < size; i++) {
            aMonsterState.add(parse(buffer));
        }
        return aMonsterState;
    }

    public static XMonsterState parse(ByteBuf buffer) {
        XMonsterState obj = new XMonsterState();
        obj.id = buffer.readInt();
        int statusCount = buffer.readByte();
        obj.status = new int[statusCount];
        obj.info = new float[statusCount];
        for (int i = 0; i < statusCount; i++) {
            obj.status[i] = buffer.readByte();
            obj.info[i] = buffer.readFloat();
        }
        return obj;
    }
}
