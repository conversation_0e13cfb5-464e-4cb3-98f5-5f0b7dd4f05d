package com.proto.custom;

import com.bem.boom.unit.Bomb;
import com.proto.GGProto;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 12/21/2016.
 */
public class XBoom extends XAbstract {
    byte status;
    int id, imageId, playerUnitId;
    byte posX, posY;
    byte[] bombLength;

    public static void parseProtoBuffer(ByteBuf buffer, List<GGProto.ProtoBoom> aBoom) {
        if (aBoom.size() > 0) {
            buffer.writeByte(TYPE_BOOM);
            buffer.writeByte(aBoom.size());
            for (int i = 0; i < aBoom.size(); i++) {
                GGProto.ProtoBoom tmp = aBoom.get(i);
                buffer.writeByte(tmp.getStatus());
                buffer.writeInt(tmp.getId());
                switch (tmp.getStatus()) {
                    case Bomb.STATUS_LANDING:
                    case Bomb.STATUS_JUMP:
                        buffer.writeByte(tmp.getMPos().getX());
                        buffer.writeByte(tmp.getMPos().getY());
                        break;
                    case Bomb.STATUS_JUMP_CONTINUE:
                        break;
                    case Bomb.STATUS_REMOVE:
                        buffer.writeByte(tmp.getImageId());
                        buffer.writeByte(tmp.getBombLength(0));
                        buffer.writeByte(tmp.getBombLength(1));
                        buffer.writeByte(tmp.getBombLength(2));
                        buffer.writeByte(tmp.getBombLength(3));
                        break;
                    case Bomb.STATUS_BOSS_EXPLODE:
                        buffer.writeByte(tmp.getImageId());
                        //
                        buffer.writeByte(tmp.getMPos().getX());
                        buffer.writeByte(tmp.getMPos().getY());
                        //
                        buffer.writeByte(tmp.getBombLength(0));
                        buffer.writeByte(tmp.getBombLength(1));
                        buffer.writeByte(tmp.getBombLength(2));
                        buffer.writeByte(tmp.getBombLength(3));
                        break;
                }
            }
        }
    }

    public static List<XBoom> parseList(ByteBuf buffer) {
        List<XBoom> aBoom = new ArrayList<XBoom>();
        byte size = buffer.readByte();
        for (int i = 0; i < size; i++) {
            aBoom.add(parse(buffer));
        }
        return aBoom;
    }

    public static XBoom parse(ByteBuf buffer) {
        XBoom obj = new XBoom();
        obj.status = buffer.readByte();
        obj.id = buffer.readInt();
        switch (obj.status) {
            case Bomb.STATUS_LANDING:
            case Bomb.STATUS_JUMP:
                obj.posX = buffer.readByte();
                obj.posY = buffer.readByte();
                break;
            case Bomb.STATUS_JUMP_CONTINUE:
                break;
            case Bomb.STATUS_REMOVE:
                obj.bombLength = new byte[4];
                for (int i = 0; i < 4; i++) {
                    obj.bombLength[i] = buffer.readByte();
                }
                break;
        }
        return obj;
    }
}
