package com.proto.custom;

import com.proto.GGProto;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 12/21/2016.
 */
public class XJumpPos extends XAbstract {
    int id;
    byte type;
    float posX, posY;

    public static void parseProtoBuffer(ByteBuf buffer, List<GGProto.ProtoUnitJumpPos> aJumpPos) {
        if (aJumpPos.size() > 0) {
            buffer.writeByte(TYPE_JUMP);
            buffer.writeByte(aJumpPos.size());
            for (int i = 0; i < aJumpPos.size(); i++) {
                GGProto.ProtoUnitJumpPos tmp = aJumpPos.get(i);
                buffer.writeByte(tmp.getType());
                buffer.writeInt(tmp.getId());
                buffer.writeFloat(tmp.getPosX());
                buffer.writeFloat(tmp.getPosY());
            }
        }
    }

    public static List<XJumpPos> parseList(ByteBuf buffer) {
        List<XJumpPos> aJump = new ArrayList<XJumpPos>();
        byte size = buffer.readByte();
        for (int i = 0; i < size; i++) {
            aJump.add(parse(buffer));
        }
        return aJump;
    }

    public static XJumpPos parse(ByteBuf buffer) {
        XJumpPos obj = new XJumpPos();
        obj.type = buffer.readByte();
        obj.id = buffer.readInt();
        obj.posX = buffer.readFloat();
        obj.posY = buffer.readFloat();
        return obj;
    }
}
