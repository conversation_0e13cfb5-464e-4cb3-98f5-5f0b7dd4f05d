package com.proto.custom;

import com.proto.GGProto;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 12/21/2016.
 */
public class XUnitPos extends XAbstract {
    int id;
    float posX, posY;
    int lastInputSeq;

    public static void parseProtoBuffer(ByteBuf buffer, List<GGProto.ProtoUnitPos> aUnitPos) {
        if (aUnitPos.size() > 0) {
            buffer.writeByte(TYPE_POS);
            buffer.writeByte(aUnitPos.size());
            for (int i = 0; i < aUnitPos.size(); i++) {
                GGProto.ProtoUnitPos tmp = aUnitPos.get(i);
                buffer.writeInt(tmp.getId());
                buffer.writeInt((int) tmp.getLastInputSeq());
                buffer.writeFloat(tmp.getPosX());
                buffer.writeFloat(tmp.getPosY());
            }
        }
    }

    public static List<XUnitPos> parseList(ByteBuf buffer) {
        List<XUnitPos> aUnitPos = new ArrayList<XUnitPos>();
        byte size = buffer.readByte();
        for (int i = 0; i < size; i++) {
            aUnitPos.add(parse(buffer));
        }
        return aUnitPos;
    }

    public static XUnitPos parse(ByteBuf buffer) {
        XUnitPos obj = new XUnitPos();
        obj.id = buffer.readInt();
        obj.lastInputSeq = buffer.readInt();
        obj.posX = buffer.readFloat();
        obj.posY = buffer.readFloat();
        return obj;
    }
}
