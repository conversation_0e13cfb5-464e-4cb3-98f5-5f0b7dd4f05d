package com.proto.custom;

import com.proto.GGProto;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by vieth_000 on 12/21/2016.
 */
public class XPlayerState extends XAbstract {
    int id;
    int[] status;
    float[] info;

    public static void parseProtoBuffer(ByteBuf buffer, List<GGProto.ProtoPlayerState> aPlayerState) {
        if (aPlayerState.size() > 0) {
            buffer.writeByte(TYPE_PLAYER_STATE);
            buffer.writeByte(aPlayerState.size());
            for (int i = 0; i < aPlayerState.size(); i++) {
                GGProto.ProtoPlayerState tmp = aPlayerState.get(i);
                buffer.writeInt((int) tmp.getId());
                buffer.writeByte(tmp.getStatusCount());
                for (int j = 0; j < tmp.getStatusCount(); j++) {
                    buffer.writeByte(tmp.getStatus(j));
                }
                buffer.writeByte(tmp.getInfoCount());
                for (int j = 0; j < tmp.getInfoCount(); j++) {
                    buffer.writeFloat(tmp.getInfo(j));
                }
            }
        }
    }

    public static List<XPlayerState> parseList(ByteBuf buffer) {
        List<XPlayerState> aPlayerState = new ArrayList<XPlayerState>();
        byte size = buffer.readByte();
        for (int i = 0; i < size; i++) {
            aPlayerState.add(parse(buffer));
        }
        return aPlayerState;
    }

    public static XPlayerState parse(ByteBuf buffer) {
        XPlayerState obj = new XPlayerState();
        obj.id = buffer.readInt();
        int statusCount = buffer.readByte();
        obj.status = new int[statusCount];
        for (int i = 0; i < statusCount; i++) {
            obj.status[i] = buffer.readByte();
        }
        int infoCount = buffer.readByte();
        obj.info = new float[infoCount];
        for (int i = 0; i < infoCount; i++) {
            obj.info[i] = buffer.readFloat();
        }
        return obj;
    }
}
