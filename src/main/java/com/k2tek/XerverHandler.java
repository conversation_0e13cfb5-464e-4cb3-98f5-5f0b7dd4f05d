package com.k2tek;

import com.bem.config.CfgCommon;
import com.bem.monitor.Online;
import com.bem.util.Util;
import com.k2tek.codec.RequestMessage;
import com.k2tek.codec.ResponseMessage;
import com.k2tek.common.slib_Logger;
import com.k2tek.service.BaseServiceMessage;
import com.bem.util.ChUtil;
import io.netty.channel.*;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import org.slf4j.Logger;

public class XerverHandler extends ChannelDuplexHandler {

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof RequestMessage) {
            ResponseMessage resp = new ResponseMessage((RequestMessage) msg, ctx.channel());

            if (resp.getService() == BaseServiceMessage.PING) {
                return;
            }

            Object returnObject = resp.getResponse();
            if (returnObject != null) {
                ctx.channel().writeAndFlush(returnObject).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
            }
        } else {
            getLogger().info("Force Close Channel");
            ctx.channel().close();
        }
    }

    public static Logger getLogger() {
        return slib_Logger.root();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        super.exceptionCaught(ctx, cause);
        ctx.channel().close();
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent e = (IdleStateEvent) evt;
            Channel channel = ctx.channel();
            if (e.state() == IdleState.READER_IDLE) {
                Integer idle = ChUtil.getInteger(channel, "idle");
                idle = idle == null ? 0 : idle;
                if (idle == 0) {
                    Util.sendProtoData(channel, null, IAction.PING_IDLE, System.currentTimeMillis());
                    ChUtil.set(channel, "idle", ++idle);
                } else if (CfgCommon.config.killIdle == 1){
//                    getLogger().error("ping idle " + ChUtil.get(channel, "keydebug"));
//                    Util.sendProtoData(channel, null, IAction.LOGIN_REQUIRE, System.currentTimeMillis());
                    channel.close();
                }
            } else if (e.state() == IdleState.WRITER_IDLE) {
//                debug("writer idle");
//                ctx.writeAndFlush(new PingMessage());
            }
        }
    }

//    @Override
//    public void channelIdle(ChannelHandlerContext ctx, IdleStateEvent e) {
//        /*int idleCount = Util.getIntAttribute(e.getChannel(), Constans.KEY_PING_COUNT);
//        if (idleCount == -1) {
//			idleCount = 0;
//		}
//		if (!e.getChannel().isConnected()) {
//			e.getChannel().close();
//		}
//		if (idleCount == 0) {
//			idleCount++;
//			e.getChannel().setAttribute(Constans.KEY_PING_COUNT, String.valueOf(idleCount));
//			Util.sendProtoData(e.getChannel(), null, BaseServiceMessage.PING, System.currentTimeMillis());
//		} else {
//			e.getChannel().close();
//		}*/
//
//        TableBalloon table = (TableBalloon) e.getChannel().getAttribute("table");
//        table.playerDisconnected(e.getChannel());
//
//		/*
//         * if (e.getState() == IdleState.READER_IDLE) { e.getChannel().close(); } else if (e.getState() == IdleState.WRITER_IDLE) { e.getChannel().write(new PingMessage()); }
//		 */
//    }

    //    @Override
//    public void channelConnected(ChannelHandlerContext ctx, ChannelStateEvent e) throws Exception {
//        super.channelConnected(ctx, e);
//        Channel ch = e.getChannel();
//
//        String sessionId = UUID.randomUUID().toString();
//        ch.setAttribute(Constans.KEY_SESSION, sessionId);
//        getLogger().info("A channel is connected " + count);
//
//    }
//
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        super.channelActive(ctx);
//        debug("channel active");
    }

    @Override
    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        super.channelRegistered(ctx);
//        debug("register");
        Online.channels.add(ctx.channel());
        getLogger().info("channelRegister " + Online.channels.size());
    }

    @Override
    public void close(ChannelHandlerContext ctx, ChannelPromise promise) throws Exception {
        super.close(ctx, promise);
//        debug("channel closed");
        Online.channels.remove(ctx.channel());
        getLogger().info("channelClose: " + Online.channels.size());
//        Online.logoutChannel(ctx.channel());
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        super.channelInactive(ctx);
        getLogger().info("channel inactive");
    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) throws Exception {
//        debug("unregsiter");
        Online.channels.remove(ctx.channel());
        getLogger().info("channelUnregister: " + Online.channels.size());
        Online.logoutChannel(ctx.channel());
        super.channelUnregistered(ctx);
    }


}
