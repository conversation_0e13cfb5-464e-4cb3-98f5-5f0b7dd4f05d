package com.k2tek;

import com.handler.game.*;
import com.k2tek.service.BaseServiceMessage;

import java.text.SimpleDateFormat;
import java.util.*;

public class Constans {
    /*
     * Những service chặn ko cho request nhiều
     */
    public static List<Integer> FLOOD_SERVICE = Arrays.asList(BaseServiceMessage.BEM_TABLE_CHAT, BaseServiceMessage.SPEAKER_GLOBAL, BaseServiceMessage.SPEAKER_ROOM);

    /*
     * Những service không cần synchronized
     */
    public static List<Integer> UNSYNC_SERVICE = Arrays.asList();

    public static int[] pos = {0, 9, 6, 3};
    public static final List<String> MAGICS = Arrays.asList("K2", "K3", "K4");
    public static final String OUT_GAME_MAGIC = "K2";
    public static final String IN_GAME_MAGIC = "K3";
    public static final String STRING_MAGIC = "K4";

    /*
     * Constants Exp and money
     */
    public static Integer WINNER_EXP = 5;
    public static Integer LOOSER_EXP = 1;
    public static final int NEW_AVATAR = 44;
    public static final String NEW_VERSION = "2.2.6";
    public static final int NEW_NEW_AVATAR = 84;
    public static final String NEW_NEW_VERSION = "2.9.9";

    public static final int PROTOCOL_TCP = 1;
    public static final int PROTOCOL_WEB_SOCKET = 2;

    /*
     * **************************************
     * ********BEM SESSION KEY*************** **************************************
     */
    public static final String KEY_PROTOCOL = "protocol";

    public static final String KEY_SESSION = "SESSION_ID";
    public static final String KEY_CAPTCHA = "REGISTER_CAPTCHA";
    public static final String KEY_CAPTCHA_TYPE = "REGISTER_CAPTCHA_TYPE";
    public static final String KEY_PING_COUNT = "ping_count";
    public static final String KEY_GAME_NAME = "game_name";
    public static final String KEY_GAME = "game";
    public static final String KEY_QUAY = "quay";
    public static final String KEY_BAR = "bar";
    public static final String KEY_PARK = "park";
    public static final String KEY_ROOMID = "roomId";
    // public static final String KEY_ROOM_RANGE = "room_range";
    public static final String KEY_TABLEID = "tableId";
    public static final String KEY_ROOM = "roomObj";

    public static final String KEY_HOST = "host";
    public static final String KEY_CODE = "code";
    public static final String KEY_USERNAME = "username";
    public static final String KEY_BALANCE = "balance";
    public static final String KEY_USERID = "userid";
    public static final String KEY_USER = "user";
    public static final String KEY_CP = "cp";
    public static final String KEY_SUBCP = "subcp";
    public static final String KEY_ACTIVE_NUMBER = "activenumber";
    public static final String KEY_ACTIVE_CODE = "activecode";
    public static final String KEY_GUEST_NAME = "guestname";
    public static final String KEY_VERSION = "version";
    public static final String KEY_OS_TYPE = "ostype";
    public static final String KEY_SPEAKER = "speakerTime";
    public static final String KEY_JOIN_BAR_STATUS = "joinBarStatus";
    public static final String TOP_FIGHTER = "fight";
    public static final String TOP_BEAUTY = "beauty";
    public static final String KEY_GUILD_REQUEST = "guild_request";
    public static final String KEY_ALLOW_VIEW_PIC = "viewpic";
    public static final String KEY_KICK_TIME = "kicktime";
    public static final String KEY_KICK_TABLE = "kicktable";
    public static final String KEY_SPAM_CREATE_TABLE = "spam_create_table";
    public static final String KEY_SPAM_AUTO_JOIN = "spam_auto_join";
    public static final String KEY_LOCALE = "locale";
    // public static final String KEY_DEBUG_SERVICE = "debug_service";
    // public static final String KEY_DEBUG_TIME = "debug_time";

    /*
     * **************************************
     * *********BEM MESSAGE****************** **************************************
     */
    public static final String MSG_PARAM_ROOMID_NOT_FOUND = "Chưa nhập roomId";
    public static final String MSG_PARAM_TABLEID_NOT_FOUND = "Chưa nhập tableId";
    public static final String MSG_PARAM_CONTENT_NOT_FOUND = "Chưa nhập lời nhắn";
    public static final String MSG_ROOM_NOT_FOUND = "Room không tồn tại";
    public static final String MSG_TABLE_NOT_FOUND = "Table không tồn tại";
    public static final String MSG_LOGIN_FAIL = "Nhập mật khẩu không chính xác";
    public static final String MSG_SUCCESS = "Thành công";
    public static final String MSG_STEAL_ERROR = "Không có phỏm phù hợp với quân bài này";
    public static final String MSG_UNKOWN = "Lỗi không xác định";
    public static final String MSG_REGISTER_DUPLICATE = "Tên đăng nhập đã tồn tại";
    public static final String MSG_TABLE_LIMITED = "Bàn đã đủ người. Hãy tham gia bàn khác.";
    public static final String MSG_IS_PLAY = "Bàn đang chơi, không truy cập được";
    public static final String MSG_LOGOUT = "Chúc bạn có những phút giây vui vẻ!";
    public static final String MSG_DUPLICATE_LOGIN = "Có người đăng nhập bằng tài khoản này!";
    public static final String MSG_NOT_LOGIN = "Chưa đăng nhập!";
    /*
     * **************************************
     * *********BEM Other MESSAGE****************** **************************************
     */
    /*
     * **************************************
     * ******BEM SESSION KEY FOR GAME******** **************************************
     */
    public static final String KEY_USER_READY = "user_ready";
    public static final String KEY_USER_CARDS = "cards"; // cards in hand
    public static final String KEY_USER_CARDS_STEAL = "steal_cards"; // steal cards
    public static final String KEY_USER_CARDS_PLAY = "play_cards"; // play cards
    public static final String KEY_USER_CARDS_SHOW = "show_cards"; // play cards
    public static final String KEY_USER_BLIND = "user_blind";
    /*
     * **************************************
     * *********BEM MESSAGE FOR GAME********* **************************************
     */
    public static final String MSG_START_FAIL = "Tất cả người chơi phải sẵn sàng";
    public static final String MSG_CARD_NOT_FOUND = "Không tìm thấy quân bài tương ứng";
    public static final String MSG_CARD_RELATED = "Quân này tạo phỏm với một quân ăn nên ko đánh đi được";
    public static final String MSG_CARD_STEAL = "Không được đánh quân ăn";
    public static final String MSG_CARD_ENOUGH = "Đã đủ bài, không được bốc thêm nữa";
    public static final String MSG_CARD_NOT_ENOUGH = "Chưa bốc bài";
    public static final String MSG_TURN_ERR = "Chưa đến lượt";
    public static final String MSG_NOT_YOUR_TURN = "Chưa đến lượt!";
    public static final String MSG_NOT_ALLOW_ACTION = "Chưa đến lượt action này!";
    public static final String MSG_GAME_NOT_START = "Ván bài chưa bắt đầu!";
    public static final String MSG_SHOW_HAND_ERR_PARAM = "Thông tin chưa bao gồm phỏm";
    public static final String MSG_SHOW_HAND_ERR_GROUP = "Phỏm không hợp lệ";
    public static final String MSG_NOT_BIG_WINNER = "Vẫn còn bài, chưa ù!!!";
    public static final String MSG_GROUP_INVALID = "Các quân bài ko lập thành bộ";
    public static final String MSG_GROUP_NOT_FOUND = "Không tìm thấy bộ này trong bài";

    // Constans Config
    public static final int ROOM_NUMBER_TABLE = 30;

    //region CHANNEL ATTRIBUTE KEY
//    public static final String KEY_USER_TEAM = "team";
    public static final String KEY_PLAYER = "player";
    public static final String KEY_TABLE = "table";
    public static final String KEY_BOT_UDID = "bot_udid";

    //region CACHE KEY
    public static final String KEY_RESUME = "resume";

    //
    public static final int PRICE_NONE = 0;
    public static final int PRICE_GOLD = 1;
    public static final int PRICE_GEM = 2;
    public static final int PRICE_MEDAL_BOSS = 3;
    public static final int PRICE_MEDAL = 4;

    //
    public static final int CLAN_NOT_MEMBER = -1;
    public static final int CLAN_MEMBER = 0;
    public static final int CLAN_ELDER = 1;
    public static final int CLAN_CO_LEADER = 2;
    public static final int CLAN_LEADER = 3;
    public static final String[] CLAN_POSITION_NAME = new String[]{"Thành viên", "Kỳ cựu", "Phó bang", "Bang chủ"};

    public static final int[] CLAN_PROMOTE = new int[]{CLAN_ELDER, CLAN_LEADER, CLAN_LEADER, CLAN_CO_LEADER};
    public static final int[] CLAN_DEMOTE = new int[]{CLAN_MEMBER, CLAN_CO_LEADER, CLAN_ELDER, CLAN_MEMBER};

    public static final int CLAN_JOIN_OPEN = 0;
    public static final int CLAN_JOIN_CLOSED = 1;
    public static final int CLAN_JOIN_INVITE_ONLY = 2;

    public static final int CLAN_CHAT_MSG = 0;
    public static final int CLAN_CHAT_JOIN = 1;
    public static final int CLAN_CHAT_LEAVE = 2;
    public static final int CLAN_CHAT_PROMOTE = 3;
    public static final int CLAN_CHAT_DEMOTE = 4;
    public static final int CLAN_CHAT_REQUEST_JOIN = 5;
    public static final int CLAN_CHAT_DELETE = 6;
    public static final int CLAN_CHAT_BATTLE_RESULT = 7;

    public static final int CHANNEL_GLOBAL = 0;
    public static final int CHANNEL_CLAN = 1;
    public static final int CHANNEL_ADMIN = 2;

    public static final int PLAYER_STATUS_ENDGAME = 0;
    public static final int PLAYER_STATUS_LEAVE = 1;
    public static final String[] GAME_LOG_ENDGAME = {"", "RANK_END", "BOSS_END", "TRAIN_END", "TUTORIAL_END", "GAME_GOLD_END", "GAME_MATERIAL_END", "GAME_BOSS_END", "", "", "", "GAME_ARENA_END"};
    public static final String[] GAME_LOG_LEAVE = {"", "RANK_LEAVE", "BOSS_LEAVE", "TRAIN_LEAVE", "TUTORIAL_LEAVE", "GAME_GOLD_LEAVE", "GAME_MATERIAL_LEAVE", "GAME_BOSS_LEAVE", "", "", "", "GAME_ARENA_LEAVE"};

    public static final int EVENT_GOLD_HUNTING = 1;
    public static final int EVENT_BOSS_GLOBAL = 2;
    public static final int EVENT_MATERIAL = 3;
    public static final int EVENT_TOP_TROPHY = 4;
    public static final int EVENT_CLAN_BATTLE = 5;
    public static final int EVENT_SOLO = 6;
    public static final int EVENT_ZOMBIE = 7;
    public static final int EVENT_PET = 8;// tu 8 den 50;
    public static final int EVENT_CHARACTER_LOOP = 10001;//tu 10001 den 10100;
    public static final int EVENT_SAOVANG = 2000;//
    public static final int EVENT_TOP_CAP_DOI = 106;


    public static final int EVENT_FOR_LIMIT_USER = 3000;//


    public static final int EVENT_TOP_TROPHY_TIME = 100;
    public static final int EVENT_TOP_STAR_MAP = 101;
    public static final int EVENT_TOP_WIN = 102;
    public static final int EVENT_TOP_CAPDO = 103;
    public static final int EVENT_NAP = 104;
    public static final int EVENT_ARENA = 105;


    // khong tuan tu mo theo thoi gian
    public static final int EVENT_CHARACTERPLUS = 1000;//tu 1000 den 1100;
    public static final int EVENT_PETPLUS = 1101;//tu 1101 den 1200;

    public static final Map<Integer, AAHandler> mEvent = new HashMap<Integer, AAHandler>() {{
        put(EVENT_GOLD_HUNTING, GoldHunting.getInstance());
        put(EVENT_MATERIAL, MaterialHunting.getInstance());
        Config.lstServerId.forEach(serverId -> {
            put(serverId * 100 + EVENT_BOSS_GLOBAL, BossGlobal.getInstance(serverId));
            put(serverId * 100 + EVENT_CLAN_BATTLE, ClanBattle.getInstance(serverId));
        });
    }};

    public static final String SERVER_BATTLE_HOST_156 = "battle3-boomba.hardcookie.com";

    public static final String SERVER_BATTLE_HOST = "battle2-boomba.hardcookie.com";
    public static final String SERVER_BATTLE_PORT = "6665";

    public static final String SERVER_BATTLE_HOST_TEST = "battle-boomba.hardcookie.com";
    public static final String SERVER_BATTLE_PORT_TEST = "6666";

    public static final int DB_STATUS_EXCEPTION = -1;
    public static final int DB_STATUS_NOT_FOUND = -2;
}
