package com.k2tek;

import com.bem.boom.Resources;
import com.bem.boom.TaskMonitor;
import com.bem.boom.map.MapResource;
import com.bem.config.*;
import com.bem.dao.SystemDAO;
import com.bem.dao.mapping.ConfigEntity;
import com.bem.dao.mapping.ConfigLocalEntity;
import com.bem.monitor.*;
import com.bem.object.IConfig;
import com.bem.object.ITimer;
import com.bem.object.TurnInfor;
import com.bem.object.event.TopTrophy;
import com.bem.util.JobSchedule;
import com.bem.util.TurnCounter;
import com.bem.util.Util;
import com.cache.ConfigEventReload;
import com.cache.JCache;
import com.cache.MCache;
import com.cache.MainCache;
import com.k2tek.common.Logs;
import com.k2tek.common.slib_Logger;
import com.k2tek.net.tcp.RequestDecoder;
import com.k2tek.net.tcp.TCPHandler;
import grep.database.Database2;
import grep.database.HibernateUtil;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import org.apache.log4j.xml.DOMConfigurator;
import org.slf4j.Logger;

import java.lang.reflect.Method;
import java.util.*;

public class Xerver { //extends AbstractXerver
    public static final String DEFAULT_CONFIG_FILE = Config.getConfigPath() + "config.xml";
    public static final String DEFAULT_BOT_FILE = Config.getConfigPath() + "bot.txt";

    public static Logger getLogger() {
        return slib_Logger.root();
    }

//    bootstrap.setOption("child.sendBufferSize", 2048);
//    bootstrap.setOption("child.receiveBufferSize", 32768);
//    bootstrap.setOption("child.keepAlive", true);
//    bootstrap.setOption("child.tcpNoDelay", true);

//    bootstrap.setOption("soLinger", 20000);
//    bootstrap.setOption("reuseAddress", true);
//    bootstrap.setOption("tcpNoDelay", true);
//    bootstrap.setOption("keepAlive", true);
//    bootstrap.setOption("sendBufferSize", 2048);
//    bootstrap.setOption("receiveBufferSize", 43690);

    public static void main(String[] args) throws Exception {
        DOMConfigurator.configure("log4j.xml");
        Config.load(DEFAULT_CONFIG_FILE);
        initDB();
        REMOTE_CONNECT_TIMEOUT = Config.getRemoteConnectTimeout();
        REMOTE_TIMEOUT = Config.getRemoteTimeout();
        REMOTE_TIMEOUT_SHORT = Config.getRemoteTimeoutShort();
        CfgServer.SERVER_ID = Config.getServerId();
        CfgServer.debug = Config.getBoolean("config.monitor.debug");
        CfgServer.cacheCCU = Config.getBoolean("config.monitor.useronline");
        CfgCluster.runningPort = Config.getInt("config.server.port");
        CfgCluster.loadConfig(new SystemDAO().getConfigLocal("config_cluster").getValue());
        JCache.getInstance();
        MCache.getInstance();
        initMyConfig();
        initGameConfig();
        new Thread(() -> ConfigEventReload.getInstance().doExpireTurn(0)).start();
        if (!CfgServer.isBattle()) new Thread(() -> MainCache.getInstance().doExpireTurn(0)).start();
        else TaskMonitor.getInstance().doExpireTurn(0);

        if (Config.containKey("config.monitor.event")) {
            EventMonitor.getInstance().init();
            TopTrophy.getInstance().doExpireTurn(0);
        }
        int processors = Runtime.getRuntime().availableProcessors();
        if (processors < 8) processors = 8;
        System.out.println("processors = " + processors);
        new Thread(() -> {
            {// Configure the server -> socket server
                try {
                    EventLoopGroup bossGroup = new NioEventLoopGroup(1);
                    EventLoopGroup workerGroup = new NioEventLoopGroup();
                    try {
                        ServerBootstrap b = new ServerBootstrap();
                        b.option(ChannelOption.SO_SNDBUF, 2048);
                        b.option(ChannelOption.SO_RCVBUF, 32768);
                        b.option(ChannelOption.SO_KEEPALIVE, true);
                        b.option(ChannelOption.TCP_NODELAY, true);

                        b.option(ChannelOption.SO_LINGER, 20000);
                        b.option(ChannelOption.SO_REUSEADDR, true);

                        b.group(bossGroup, workerGroup)
                                .channel(NioServerSocketChannel.class)
                                .option(ChannelOption.SO_BACKLOG, 100)
                                .handler(new LoggingHandler(LogLevel.INFO))
                                .childHandler(new ChannelInitializer<SocketChannel>() {
                                    @Override
                                    public void initChannel(SocketChannel ch) throws Exception {
                                        ChannelPipeline p = ch.pipeline();
                                        p.addLast("decoder", new RequestDecoder());
                                        p.addLast(new IdleStateHandler(180, 180, 0)); //readerIdleTime , writerIdleTime , allIdleTime
                                        p.addLast(new TCPHandler());
                                    }
                                });
                        getLogger().info("Starting server at Port: " + Config.getStringServerPort());
                        // Start the server.
                        ChannelFuture f = b.bind(Integer.parseInt(Config.getStringServerPort())).sync();
                        // Wait until the server socket is closed.
                        f.channel().closeFuture().sync();
                    } finally {
                        // Shut down all event loops to terminate all threads.
                        bossGroup.shutdownGracefully();
                        workerGroup.shutdownGracefully();
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }).start();

//        new Thread(() -> {
//            {// Configure the server -> web socket server
//                try {
//                    boolean SSL = false;
//                    int PORT = 6665;// Integer.parseInt(Config.getStringServerPort()) + 100;
//                    final SslContext sslCtx;
//                    if (SSL) {
//                        SelfSignedCertificate ssc = new SelfSignedCertificate();
//                        sslCtx = SslContextBuilder.forServer(ssc.certificate(), ssc.privateKey()).build();
//                    } else {
//                        sslCtx = null;
//                    }
//
//                    EventLoopGroup bossGroup = new NioEventLoopGroup(1);
//                    EventLoopGroup workerGroup = new NioEventLoopGroup();
//                    try {
//                        ServerBootstrap b = new ServerBootstrap();
//                        b.group(bossGroup, workerGroup)
//                                .channel(NioServerSocketChannel.class)
//                                .handler(new LoggingHandler(LogLevel.INFO))
//                                .childHandler(new WebSocketServerInitializer(sslCtx));
//                        Channel ch = b.bind(PORT).sync().channel();
//                        System.out.println("Open your web browser and navigate to " + (SSL ? "https" : "http") + "://127.0.0.1:" + PORT + '/');
//                        ch.closeFuture().sync();
//                    } finally {
//                        bossGroup.shutdownGracefully();
//                        workerGroup.shutdownGracefully();
//                    }
//                } catch (Exception ex) {
//                    ex.printStackTrace();
//                }
//            }
//        }).start();
    }

    // JobSchedule
    public static JobSchedule mJob = new JobSchedule(3000);
    public static LoginTimesMonitorDB moLoginTimes;
    public static NotifyMonitorDB moNotifyUser;
    public static TextEventMonitor textEvent;
    public static ImageConfig moImage;
    public static FacebookFriendMonitor FBMonitor;
    // JobSchedule

    public static Map<Integer, TurnCounter> mCounter = new HashMap<Integer, TurnCounter>();
    public static List<Integer> aCounter = Arrays.asList(30, 15, 5, 3, 1);

    public static void initMyConfig() throws Exception {
        for (Integer value : aCounter) {
            TurnCounter counter = new TurnCounter(value);
            mCounter.put(value, counter);
            counter.start();
        }
        com.bem.boom.unit.Item.importListItem();
        new BotMonitor().timer();
        if (true) {
            return;
        }

        (new KeepAliveMySQL()).start();

        moLoginTimes = new LoginTimesMonitorDB(1000 * 60);
        mJob.addQueue(moLoginTimes);
        moNotifyUser = new NotifyMonitorDB(1000 * 60);
        mJob.addQueue(moNotifyUser);
        textEvent = new TextEventMonitor(30 * 1000);
        mJob.addQueue(textEvent);
        moImage = new ImageConfig(1000 * 15);
        mJob.addQueue(moImage);
        FBMonitor = new FacebookFriendMonitor(1000 * 60);
        mJob.addQueue(FBMonitor);
        // Tách xử lý db ra thread riêng
    }

    public static void initGameConfig() {
        // Game config
        MapResource.init();
        String tblConfig = CfgServer.isTest() ? "config_test" : CfgCommon.tableConfig;
        System.out.println("tblConfig = " + tblConfig);
        List<ConfigEntity> aConfig = Database2.getList(CfgCommon.mainDb + tblConfig, ConfigEntity.class);
        List<ConfigLocalEntity> aLocalConfig = CfgServer.isBattle() ? new ArrayList<>() : Database2.getList(CfgCommon.tableConfigLocal, ConfigLocalEntity.class);
        for (ConfigEntity config : aConfig) {
            boolean hasLocalConfig = false;
            for (ConfigLocalEntity localConfig : aLocalConfig) {
                if (config.getKey().equals(localConfig.getKey())) hasLocalConfig = true;
            }
            if (!hasLocalConfig) loadDbConfig(config);
        }
        aLocalConfig.forEach(config -> loadDbConfig(config));
        Resources.init();
    }

    static void loadDbConfig(IConfig config) {
        try {
            if (config.getKey().contains(":config_achievement")) {
                String cfg = config.getKey();
                String[] arr = cfg.split(":");
                int severId = Integer.parseInt(arr[0]);
                if (!cfg.startsWith("config")) cfg = cfg.substring(cfg.indexOf(":") + 1);
                cfg = cfg.substring(cfg.indexOf("_") + 1);
                cfg = cfg.substring(0, 1).toUpperCase() + cfg.substring(1);
                try {
                    Method m = Class.forName("com.bem.config.Cfg" + cfg).getDeclaredMethod("loadConfig", String.class, String.class);
//                m.invoke(null, config.getValue());
                    System.out.println(m.getName());
                    Object[] parameters = {config.getValue(), String.valueOf(severId)};
                    m.invoke(null, parameters);
//                m.invoke(null, new Object[]{config.getValue(), severId+""});
                } catch (Exception ex) {
                    getLogger().error(Util.exToString(ex));
                    ex.printStackTrace();
                }
            } else {
                for (int i = 0; i < Config.lstServerId.size(); i++) {
                    if (config.getKey().startsWith("config") || config.getKey().startsWith(Config.lstServerId.get(i) + ":config")) {
                        String cfg = config.getKey();
                        if (!cfg.startsWith("config")) cfg = cfg.substring(cfg.indexOf(":") + 1);
                        cfg = cfg.substring(cfg.indexOf("_") + 1);
                        cfg = cfg.substring(0, 1).toUpperCase() + cfg.substring(1);
                        try {
                            Method m = Class.forName("com.bem.config.Cfg" + cfg).getDeclaredMethod("loadConfig", String.class);
                            m.invoke(null, config.getValue());
                        } catch (Exception ex) {
                            getLogger().error(Util.exToString(ex));
                            ex.printStackTrace();
                        }
                        return;
                    }
                }
//            else
                if (config.getKey().startsWith("game")) {
                    String cfg = config.getKey();
                    cfg = cfg.substring(cfg.indexOf("_") + 1);
                    cfg = cfg.substring(0, 1).toUpperCase() + cfg.substring(1);
                    try {
                        Method m = Class.forName("com.bem.config.GameCfg" + cfg).getDeclaredMethod("loadConfig", String.class);
                        m.invoke(null, config.getValue());
                    } catch (Exception ex) {
                        getLogger().error(Util.exToString(ex));
                        ex.printStackTrace();
                    }
                } else if (config.getKey().startsWith("event")) {
                    String event = config.getKey();
                    event = event.substring(event.indexOf("_") + 1);
                    event = event.substring(0, 1).toUpperCase() + event.substring(1);
                    try {
                        Method m = Class.forName("com.bem.config.Event" + event).getDeclaredMethod("loadConfig", String.class);
                        m.invoke(null, config.getValue());
                    } catch (Exception ex) {
                        getLogger().error(Util.exToString(ex));
                        ex.printStackTrace();
                    }
                } else if (config.getKey().startsWith("api")) {
                    String api = config.getKey();
                    api = api.substring(api.indexOf("_") + 1);
                    api = api.substring(0, 1).toUpperCase() + api.substring(1);
                    try {
                        Method m = Class.forName("com.bem.util.Api" + api).getDeclaredMethod("loadConfig", String.class);
                        m.invoke(null, config.getValue());
                    } catch (Exception ex) {
                        getLogger().error(Util.exToString(ex));
                        ex.printStackTrace();
                    }
                } else if (config.getKey().startsWith("res_")) {
                    Resources.load(config.getKey(), config.getValue());
                    CfgShop.load(config.getKey(), config.getValue());
                } else {
                }
            }
        } catch (Exception ex) {
            Logs.error(Util.exToString(ex));
        }
//    }
    }

    public static void initDB() {
        System.out.println("init db");
        String user = Config.getString("config.server.user");
        String pass = Config.getString("config.server.pass");
        String db = Config.getString("config.server.db");
        String dbMain = Config.getString("config.server.dbMain");
        String host = Config.getString("config.server.host");
        HibernateUtil.init(user, pass, db, dbMain, host);
        System.out.println("init db done");
    }

    private static class KeepAliveMySQL extends Thread {

        boolean isRunning = true;

        public void stopRunning() {
            isRunning = false;
        }

        @Override
        public void run() {
            while (isRunning) {
                try {
                    Thread.sleep(5 * 60 * 1000);
                } catch (Exception ex) {
                }
                Database2.getList(CfgCommon.mainDb + "config", ConfigEntity.class);
            }
        }
    }


    public static int REMOTE_CONNECT_TIMEOUT = 10;
    public static int REMOTE_TIMEOUT_SHORT = 10;
    public static int REMOTE_TIMEOUT = 10;

    public static void timer(ITimer iTimer, int id, int seconds) {
        for (Integer value : aCounter) {
            if (seconds % value == 0) {
                Xerver.mCounter.get(value).addQueue(new TurnInfor(iTimer, id, seconds));
                return;
            }
        }
        Xerver.mCounter.get(1).addQueue(new TurnInfor(iTimer, id, seconds));
    }
}
