package com.k2tek.net;

import com.bem.AbstractTable;
import com.bem.util.CommonProto;
import com.bem.util.Util;
import com.handler.*;
import com.k2tek.Constans;
import com.k2tek.IAction;
import com.k2tek.common.slib_Logger;
import com.k2tek.service.BaseServiceMessage;
import com.k2tek.service.UnknowService;
import com.proto.GGProto;
import com.bem.util.ChUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResponseMessage {

    private RequestMessage request;
    private BaseServiceMessage service = UnknowService.UNKNOW_SERVICE;
    private Channel channel;
    static Map<Integer, AHandler> mGameHandler;

    static {
        mGameHandler = new HashMap<Integer, AHandler>();
        AuthHandler.getInstance().initAction(mGameHandler);
        MatchHandler.getInstance().initAction(mGameHandler);
        ShopHandler.getInstance().initAction(mGameHandler);
        FriendHandler.getInstance().initAction(mGameHandler);
        MissionHandler.getInstance().initAction(mGameHandler);
        MiniGameHandler.getInstance().initAction(mGameHandler);
        TopHandler.getInstance().initAction(mGameHandler);
        UpgradeHandler.getInstance().initAction(mGameHandler);
        GameHandler.getInstance().initAction(mGameHandler);
        ClanHandler.getInstance().initAction(mGameHandler);
        //
        MonitorHandler.getInstance().initAction(mGameHandler);
    }

    GGProto.ResponseData.Builder responseData = GGProto.ResponseData.newBuilder();

    public ResponseMessage(RequestMessage request, Channel channel) {
        this.request = request;
        this.channel = channel;

        ChUtil.remove(channel, "idle");
        AbstractTable table = (AbstractTable) ChUtil.get(channel, "table");
        if (table != null) {
            if (request.getMagic().equals(Constans.IN_GAME_MAGIC)) {
                table.doAction(channel, IAction.CLIENT_INPUT, request.getBody(), System.currentTimeMillis());
            } else {
                GGProto.RequestData reqData = CommonProto.parseRequestData(request.getBody());
                if (reqData != null) {
                    List<GGProto.Action> actions = reqData.getActionsList();
                    for (int i = 0; i < actions.size(); i++) {
                        if (actions.get(i).getService() == IAction.PING) {
                            Util.sendProtoData(channel, null, IAction.PING, System.currentTimeMillis());
                        } else {
                            table.doAction(channel, actions.get(i).getService(), actions.get(i).getData().toByteArray(), System.currentTimeMillis());
                        }
                    }
                }
            }
        } else if (request.getMagic().equals(Constans.OUT_GAME_MAGIC)) {
            String session = null;
            try {
                GGProto.RequestData reqData = CommonProto.parseRequestData(request.getBody());
                if (reqData != null) {
                    session = reqData.getSession();
                    List<GGProto.Action> actions = reqData.getActionsList();
                    for (int i = 0; i < actions.size(); i++) {
                        int srv = actions.get(i).getService();
                        if (IAction.UNSYNC_ACTION.contains(srv)) {
                            if (mGameHandler.containsKey(srv)) {
                                AHandler handler = mGameHandler.get(srv).newInstance();
                                handler.doAction(channel, session, srv, actions.get(i).getData().toByteArray());
                                handler.checkSlideMessage();
                                responseData.addAllActions(handler.getResponse().getActionsList());
                            }
                        } else if (validateSequenceRequest(session)) {
                            if (mGameHandler.containsKey(srv)) {
                                AHandler handler = mGameHandler.get(srv).newInstance();
                                handler.doAction(channel, session, srv, actions.get(i).getData().toByteArray());
                                handler.checkSlideMessage();
                                responseData.addAllActions(handler.getResponse().getActionsList());
                            }
                            endRequest(session);
                        }
                    }
                }
            } catch (Exception ex) {
                endRequest(session);
                getLogger().error(Util.exToString(ex));
            }
        } else if (request.getMagic().equals(Constans.STRING_MAGIC)) {
            MonitorHandler.getInstance().newInstance().doAction(channel, "session", IAction.RELOAD_CONFIG, request.getBody());
            responseData = null;
        }
    }

    static Map<String, Integer> handlerReq = new HashMap<String, Integer>();

    public static synchronized boolean validateSequenceRequest(String session) {
        if (session == null || session.length() == 0) {
            return true;
        }

        if (handlerReq.containsKey(session)) {
            return false;
        }
        handlerReq.put(session, 1);
        return true;
    }

    public static void endRequest(String session) {
        if (session != null && session.length() > 0) {
            handlerReq.remove(session);
        }
    }

    /**
     * @return
     */
    public int getLength() {
        return service.getSrcRequest().length;
    }

    public int getService() {
        return service.getService();
    }

    public RequestMessage getRequest() {
        return request;
    }

    public Object getResponse() {
        if (responseData != null) {
            try {
                byte[] body = responseData.build().toByteArray();
                ByteBuf buffer = Unpooled.buffer();
                buffer.writeBytes(Constans.OUT_GAME_MAGIC.getBytes()); // K2
                buffer.writeInt(body.length);
                buffer.writeBytes(body);
                return buffer;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
//        if (service.getResponse() != null) {
//            // debug("Content size send to client: " +
//            // service.getBody().size());
//            try {
//                byte[] body = service.getResponse().toByteArray();
//                ByteBuf buffer = Unpooled.buffer();
//                buffer.writeBytes(Constans.MAGIC.getBytes()); // K2
//                buffer.writeInt(getService());
//                buffer.writeInt(body.length);
//                buffer.writeBytes(body);
//                return buffer;
//            } catch (Exception ex) {
//            }
//        } else if (service.isMonitor()) {
//            try {
//                byte[] data = service.getBody().toByteArray();
//                ByteBuf buffer = Unpooled.buffer();
//                buffer.writeBytes(Constans.MAGIC.getBytes()); // K2
//                buffer.writeInt(getService());
//                buffer.writeInt(data.length);
//                buffer.writeBytes(data);
//                // debug("<- magic: K2 - length:" + data.length + " - service:" + getService());
//                return buffer;
//            } catch (Exception ex) {
//            }
//        }

        return null;
    }

    public static Logger getLogger() {
        return slib_Logger.root();
    }
}
