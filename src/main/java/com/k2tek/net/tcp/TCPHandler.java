package com.k2tek.net.tcp;

import com.k2tek.Constans;
import com.k2tek.net.AbstractHandler;
import com.bem.util.ChUtil;
import io.netty.channel.ChannelHandlerContext;

public class TCPHandler extends AbstractHandler {
    @Override
    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        super.channelRegistered(ctx);
        ChUtil.set(ctx.channel(), Constans.KEY_PROTOCOL, Constans.PROTOCOL_TCP);
    }
}
