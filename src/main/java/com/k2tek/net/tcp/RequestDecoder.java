package com.k2tek.net.tcp;

import java.net.InetSocketAddress;
import java.util.List;

import com.k2tek.net.RequestMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;

import com.k2tek.Constans;

public class RequestDecoder extends ByteToMessageDecoder {

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf buffer, List<Object> out) {
        if (buffer.readableBytes() < 6) {
            return;
        }
        // Mark the current buffer position before reading the length field
        // because the whole frame might not be in the buffer yet.
        // We will reset the buffer position to the marked position if
        // there's not enough bytes in the buffer.
        String address = ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress();
        while (buffer.readableBytes() >= 6) {
            if (!parsePackage(buffer, out, address)) {
                break;
            }
        }
        /*
        int readables = buffer.readableBytes();
        debug(buffer.readableBytes());
        byte[] header = new byte[2];
        buffer.readBytes(header);
        String magic = new String(header); // K2
        int service = buffer.readInt();
        int length = buffer.readInt();
        if (service == 4) {
//            Filer.append(magic + " -> " + service + " -> " + length);
        }
        debug(buffer.readableBytes());
        debug("-> magic:" + magic + " - length:" + length + " - service:" + service);
        if (magic.equals("K2")) {
            if (length > 0) {
                if (buffer.readableBytes() < length) {
                    // debug("buffer.readableBytes()<"+length +
                    // ":"+buffer.readableBytes());
                    // The whole bytes were not received yet - return null.
                    // This method will be invoked again when more packets are
                    // received and appended to the buffer.
                    // Reset to the marked position to read the length field again
                    // next time.

                    buffer.resetReaderIndex();
                    return;
                }

                byte[] data = new byte[length];
                buffer.readBytes(data);
                out.add(new RequestMessage(Constans.MAGIC, service, data, ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress()));
                if (service == 4) {
                    long newSeq = CommonProto.parseProtoInput(data).getSeq();
                    if (newSeq - lastSeq > 1) {
                        debug("false = " + lastSeq + " " + newSeq);
                    }
                    debug("false = " + lastSeq + " " + newSeq);
//                    debug("core receiver --------> " + CommonProto.parseProtoInput(data).getSeq());
//                    Filer.append("core receiver --------> " + CommonProto.parseProtoInput(data).getSeq());
                    lastSeq = newSeq;
                }
                debug(buffer.readableBytes());
            } else {
                out.add(new RequestMessage(Constans.MAGIC, service, null, ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress()));
            }
        } else {
            debug("what is magic");
        }*/
    }

    boolean parsePackage(ByteBuf buffer, List<Object> out, String address) {
        if (buffer.readableBytes() < 6) {
            return false;
        }
        buffer.markReaderIndex();
        byte[] header = new byte[2];
        buffer.readBytes(header);
        String magic = new String(header); // K2
//        int service = buffer.readInt();
        int length = buffer.readInt();
        if (Constans.MAGICS.contains(magic)) {
            if (length > 0) {
                if (buffer.readableBytes() < length) {
                    // debug("buffer.readableBytes()<"+length +
                    // ":"+buffer.readableBytes());
                    // The whole bytes were not received yet - return null.
                    // This method will be invoked again when more packets are
                    // received and appended to the buffer.
                    // Reset to the marked position to read the length field again
                    // next time.

                    buffer.resetReaderIndex();
                    return false;
                }
                byte[] data = new byte[length];
                buffer.readBytes(data);
                out.add(new RequestMessage(magic, 0, data, address));
            } else {
                out.add(new RequestMessage(magic, 0, null, address));
            }
            return true;
        } else {
            return false;
        }
    }

}
