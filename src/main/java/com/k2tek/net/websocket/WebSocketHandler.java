package com.k2tek.net.websocket;

import com.k2tek.Constans;
import com.k2tek.net.AbstractHandler;
import com.k2tek.net.RequestMessage;
import com.k2tek.net.ResponseMessage;
import com.bem.util.ChUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;

import java.net.InetSocketAddress;
import java.util.Locale;

public class WebSocketHandler extends AbstractHandler {
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof TextWebSocketFrame) {
            // Send the uppercase string back.
            String request = ((TextWebSocketFrame) msg).text();
            ctx.channel().writeAndFlush(new TextWebSocketFrame(request.toUpperCase(Locale.US)));
        } else if (msg instanceof BinaryWebSocketFrame) {
            BinaryWebSocketFrame binFrame = (BinaryWebSocketFrame) msg;
            RequestMessage req = decode(ctx.channel(), binFrame.content());
            ResponseMessage resp = new ResponseMessage(req, ctx.channel());
            Object returnObject = resp.getResponse();
            if (returnObject != null) {
                ctx.channel().writeAndFlush(new BinaryWebSocketFrame((ByteBuf) returnObject)).addListener(ChannelFutureListener.CLOSE_ON_FAILURE);
            }
        } else {
            String message = "unsupported frame type: " + msg.getClass().getName();
            throw new UnsupportedOperationException(message);
        }
    }

    RequestMessage decode(Channel channel, ByteBuf buffer) {
        if (buffer.readableBytes() < 6) {
            return null;
        }
        buffer.markReaderIndex();
        byte[] header = new byte[2];
        buffer.readBytes(header);
        String magic = new String(header); // K2
//        int service = buffer.readInt();
        int length = buffer.readInt();
        if (Constans.MAGICS.contains(magic)) {
            if (length > 0) {
                if (buffer.readableBytes() < length) {
                    // debug("buffer.readableBytes()<"+length +
                    // ":"+buffer.readableBytes());
                    // The whole bytes were not received yet - return null.
                    // This method will be invoked again when more packets are
                    // received and appended to the buffer.
                    // Reset to the marked position to read the length field again
                    // next time.

                    buffer.resetReaderIndex();
                    return null;
                }
                byte[] data = new byte[length];
                buffer.readBytes(data);
                return new RequestMessage(magic, 0, data, ((InetSocketAddress) channel.remoteAddress()).getAddress().getHostAddress());
            }
            return new RequestMessage(magic, 0, null, ((InetSocketAddress) channel.remoteAddress()).getAddress().getHostAddress());
        }
        return null;
    }

    @Override
    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        super.channelRegistered(ctx);
        ChUtil.set(ctx.channel(), Constans.KEY_PROTOCOL, Constans.PROTOCOL_WEB_SOCKET);
    }
}
