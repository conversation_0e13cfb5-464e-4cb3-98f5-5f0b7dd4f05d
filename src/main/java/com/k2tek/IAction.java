package com.k2tek;

import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 6/30/2016.
 */
public class IAction {

    public static final int MSG_TOAST = 1;
    public static final int MSG_POPUP = 2;
    public static final int MSG_SLIDE = 3;
    public static final int LIST_ACTION = 4;
    public static final int UPDATE_MONEY = 5;
    public static final int SHOP_REQUIRE = 6;
    public static final int TOP_UP_REQUIRE = 7;
    public static final int LOGIN_REQUIRE = 8;

    //region Game Play
    public static final int CLIENT_DISCONNECTED = 10;
    public static final int CLIENT_RESUME = 11;
    public static final int LEAVE_TABLE = 12;
    public static final int END_GAME = 13;
    public static final int LEAVE_TABLE_1 = 14;
    public static final int CHAT_IN_GAME = 15;

    //
    public static final int CREATE_TEAM = 20;
    public static final int JOIN_TEAM = 21;
    public static final int LEAVE_TEAM = 22;
    public static final int INVITE_TEAM = 23;
    public static final int INVITE_TEAM_REQ = 24;
    public static final int SEARCH_GAME = 25;
    public static final int START_PHYSIC_UPDATE = 26;
    public static final int MAP_DATA = 27;
    public static final int PLAYER_READY = 28;
    public static final int START_GAME = 29;
    public static final int TABLE_STATE = 30;
    public static final int CLIENT_INPUT = 31;
    public static final int KICK_PLAYER = 32;
    public static final int CREATE_TEAM_STATUS = 33;
    public static final int TEAM_STATUS = 34;
    public static final int CHOSSE_MAP_STATUS = 35;
    public static final int CANCEL_GAME = 36;
    public static final int NEW_TEAM_PLAYER = 37;
    public static final int TEAM_CHAT = 38;
    public static final int TEAM_EMOTION = 39;
    public static final int HAVE_SEARCH_TEAM = 40;
    public static final int TOP_BOSS = 41;
    public static final int TEAM_READY = 42;
    public static final int ALERT_SUDDEN_DEATH = 43;
    public static final int BOSS_QUICK_ATK = 44;
    public static final int BOSS_LEVEL_STATUS = 45;
    public static final int REJOIN_TEAM_OBJECT = 46;
    public static final int JOIN_BATTLE = 47;
    public static final int SERVER_BATTLE_INFO = 48;
    public static final int INVITE_SUPPORT_STATUS = 49;
    public static final int BUY_FREE_INVITE = 400;

    //region Auth service 50
    public static final int LOGIN_SESSION = 50;
    public static final int LOGIN_FACEBOOK = 51;
    public static final int LOGIN_QUICK = 52;
    public static final int ACCOUNT_CONNECT_FACEBOOK = 53;
    public static final int ACCOUNT_CONNECT = 54;
    public static final int SETTING_FUNCTION = 55;
    public static final int SETTING_FUNCTION_NEW = 97;

    public static final int LOG_OUT = 56;
    public static final int CHOOSE_FIRST_AVATAR = 57;
    public static final int LOGIN_BOT = 58;
    public static final int LOGIN_FAIL = 59;
    public static final int LOGIN_NOTIFY = 76;
    public static final int LOGIN_INFORM = 78;
    public static final int LOGIN_BOT_OK = 60;

    //region Shop service 60
//    public static final int SHOP_LIST = 60;
    public static final int BUY_ITEM_OLD = 61;
    public static final int BUY_ITEM = 62;
    //    public static final int SHOP_HERO = 62;
    public static final int STORAGE_LIST = 63;
    public static final int SHOP_SPECIAL = 64;
    public static final int SHOP_REFRESH = 65;
    public static final int USE_ITEM = 66;
    public static final int LIST_CHOSE_ITEM = 67;
    public static final int CHOOSE_BATTLE_ITEM = 68;

    public static final int EXCHANGE_GEM_STATUS = 70;
    public static final int EXCHANGE_GEM = 71;
    public static final int TOP_UP_STATUS = 72;
    public static final int TOP_UP_CARD = 73;
    public static final int TOP_UP_IAP = 74;
    public static final int TOP_UP_HISTORY = 75;
    public static final int SALE_ITEM = 77;
    public static final int MONTHLY_CARD_STATUS = 78;
    public static final int MONTHLY_CARD_RECEIVED = 79;
    //endregion

    //region Friend service 80
    public static final int MAIL_LIST = 80;
    public static final int REQUEST_FRIEND = 81;
    public static final int ANSWER_REQUEST_FRIEND = 82;
    public static final int SEARCH_FRIEND = 83;
    public static final int FRIEND_LIST = 84;
    public static final int REMOVE_FRIEND = 85;
    public static final int REQUEST_FRIEND_RECEIVE = 86;
    public static final int REMOVE_INVITE_BATTLE = 87;
    public static final int ANSWER_REQUEST_FRIEND1 = 88;
    public static final int MESSAGE_READ = 89;
    public static final int MESSAGE_RECEIVE_BONUS = 90;
    public static final int LOCK_ADD_FRIEND = 91;
    public static final int FACEBOOK_FRIEND_LIST = 92;
    public static final int GLOBAL_CHAT_HISTORY = 93;
    public static final int GLOBAL_CHAT = 94;
    public static final int LIST_REQUEST_ADDFRIEND = 95;
    public static final int HOME_CHAT_LIST = 96;
    public static final int MARRY = 184;
    public static final int TANG_QUA_BAN_BE = 98;
    public static final int DIVORCE = 99;
    public static final int ACCEPT_MARY = 119;
//    public static final int CANCEL_DIVORCE = 185;


    //region mission service 100
    public static final int MISSION_STATUS = 100;
    public static final int NOTIFY_MISSION = 101;
    public static final int RECEIVE_MISSION = 102;

    //endregion
    public static final int ACHIEVEMENT_STATUS = 103;
    public static final int ACHIEVEMENT_STATUS_DETAIL = 108;
    public static final int ACHIEVEMENT_STATUS_DETAIL_NEWSTRING = 109;

    public static final int NOTIFY_ACHIEVEMENT = 104;
    public static final int RECIEVE_ACHIEVEMENT = 105;
    public static final int RANK_TROPHY_STATUS = 106;
    public static final int VIEW_ARWARD_RANK_TROPHY = 107;


    //region MiniGame service 110
    public static final int LUCKY_SPIN_STATUS = 110;
    public static final int LUCKY_SPIN_QUAY = 111;
    public static final int EXCHANGE_STAR = 112;
    public static final int CHEST_STATUS = 113;
    public static final int CHEST_QUAY = 114;
    public static final int ONLINE_GIFT_STATUS = 115;
    public static final int ONLINE_GIFT_GET = 116;
    public static final int ONLINE_ATTENDANCE_STATUS = 117;
    public static final int ONLINE_ATTENDANCE_GET = 118;
    //endregion

    //region Top Service 120
    public static final int TOP_PLAYER = 120;
    public static final int PLAYER_INFOR = 121;
    public static final int TOP_FRIEND = 122;
    public static final int BATTLE_HISTORY = 123;
    public static final int GIFT_CODE = 124;
    public static final int LIST_CHAT_ADMIN = 125;
    public static final int CHAT_ADMIN = 126;
    public static final int USER_REPORT = 127;
    public static final int VIP_INFOR = 128;
    public static final int TOP_STATUS = 129;

    //endregion

    //region Upgrade
    public static final int MATERIAL_PRICE = 130;
    public static final int BUY_MATERIAL = 131;
    public static final int UPGRADE_BOMB = 132;
    public static final int SELL_MATERIAL = 133;
    public static final int UPGRADE_PET_STAR = 134;
    public static final int UPGRADE_AVATAR = 135;
    public static final int USE_AVATAR = 136;
    public static final int FEED_PET_FOOD = 137;
    public static final int CHOOSE_AVATAR = 138;
    public static final int CHOOSE_AVATAR_STATUS = 139;
    public static final int MAP_AWARD_STATUS = 140;
    public static final int MAP_GET_AWARD = 141;
    public static final int UPGRADE_ACCESSORIES = 142;
    public static final int GHEP_BOMB = 143;
    public static final int LIST_SYMBOL = 144;
    public static final int USE_SYMBOL = 145;
    public static final int TOP_MONITOR = 146;
    public static final int LIST_STONE_UFO = 147;
    public static final int LIST_UFO = 148;
    public static final int UPGRDE_STONE_UFO = 149;

    public static final int TIENHOA_PET = 183;


    //endregion

    //region upgrade new 300
    public static final int LIST_GRAFT_TABLE = 300;
    public static final int LIST_GRAFT_DETAIL = 301;
    public static final int LIST_GRAFT_COMPONENT = 302;
    public static final int GRAFT_ITEM = 303;


    //endregion


    //region clan
    public static final int CLAN_CREATE = 150;
    public static final int CLAN_SETTING = 151;
    public static final int CLAN_JOIN_RULE = 152;
    public static final int CLAN_JOIN = 153;
    public static final int CLAN_KICK = 154;
    public static final int CLAN_CHANGE_NAME = 155;
    public static final int CLAN_AVATAR_LIST = 156;
    public static final int CLAN_AVATAR_BUY = 157;
    public static final int CLAN_AVATAR_CHANGE = 158;
    public static final int CLAN_CHAT_LIST = 159;
    public static final int CLAN_CHAT = 160;
    public static final int CLAN_DIEMDANH_STATUS = 161;
    public static final int CLAN_DIEMDANH = 162;
    public static final int CLAN_SHOP_LIST = 163;
    public static final int CLAN_SHOP_BUY = 164;
    public static final int CLAN_STATUS = 165;
    public static final int CLAN_MEMBER_LIST = 166;
    public static final int CLAN_SEARCH = 167;
    public static final int CLAN_TOP = 168;
    public static final int CLAN_LEAVE = 169;
    public static final int CLAN_SET_POSITION = 170;
    public static final int CLAN_PROMOTE = 171;
    public static final int CLAN_DEMOTE = 172;
    public static final int CLAN_SUGGESTION = 173;
    public static final int CLAN_KICK_NOTIFY = 174;
    public static final int CLAN_OWN_INFO = 175;
    public static final int CLAN_ANSWER_REQ_JOIN = 176;
    public static final int CLAN_JOIN_NOTIFY = 177;
    public static final int CLAN_INVITE_JOIN = 178;
    public static final int CLAN_INVITE_JOIN_REQ = 179;
    public static final int CLAN_ANSWER_INVITE_JOIN = 180;
    public static final int CLAN_BONUS_LIST = 181;
    public static final int CLAN_BONUS_SEND = 182;

    //endregion

    //region auth handler new
    public static final int REGISTER = 500;
    public static final int UPDATE_INFORMATION = 501;
    public static final int CHANGE_PASSWORD = 502;
    public static final int DUPLICATE_LOGIN = 503;
    public static final int SYNC_BONUS = 504;
    public static final int SYNC_MONEYS = 505;
    public static final int TRACKING_MONEY = 506;
    public static final int BOT_JOIN_BATTLE = 507;
    public static final int BOT_JOIN_BATTLE_FAIL = 508;
    public static final int LANGUAGE_IN_GAME = 509;

    //endregion

    //region game
    public static final int ACTIVITY_STATUS = 200;

    public static final int HUNT_STATUS = 210;
    public static final int HUNT_JOIN = 211;
    public static final int MATERIAL_STATUS = 212;
    public static final int MATERIAL_JOIN = 213;
    public static final int BOSS_STATUS = 214;
    public static final int BOSS_JOIN = 215;
    public static final int BOSS_TOP_BONUS = 216;
    public static final int HUNT_SKIP_WAITING = 217;
    public static final int MATERIAL_SKIP_WAITING = 218;
    public static final int CBATTLE_MAP_STATUS = 219;
    public static final int CBATTLE_ROOM_PREVIEW = 220;
    public static final int CBATTLE_JOIN_ROOM = 221;
    public static final int CBATTLE_SHOP_LIST = 222;
    public static final int CBATTLE_TOP_CLAN = 223;
    public static final int CBATTLE_TOP_PLAYER = 224;
    public static final int CBATTLE_CHEAR_HISTORY = 225;
    public static final int CBATTLE_CHAT_LIST = 226;
    public static final int CBATTLE_CHAT = 227;
    public static final int CBATTLE_FIND_MATCH = 228;
    public static final int CBATTLE_CANCEL_FIND_MATCH = 229;
    public static final int CBATTLE_STATISTIC = 230;
    public static final int CBATTLE_MATCH_UP = 234;
    public static final int CBATTLE_TOP_MAP_PLAYER = 235;

    //jack pot
    public static final int GAME_SLOT_MACHINE_STATUS = 231;
    public static final int GAME_SLOT_MACHINE_QUAY = 232;
    public static final int GAME_SLOT_MACHINE_WINNER = 233;

    //Arena
    public static final int ARENA_BATLE = 237;
//    public static final int ARENA_STATUS = 237;
    public static final int ARENA_LIST_USER = 238;
    public static final int ARENA_LIST_USER_KNOCKOUT = 239;
    public static final int ARENA_OUT = 245;
    public static final int ARENA_JOIN = 236;
    public static final int ARENA_ROOMINFOR = 240;
    public static final int ARENA_ATTENTION_BATTLE = 241;
    public static final int ARENA_OUT_BATTLE = 201;

    // invite fb
    public static final int INVITE_FB_STATUS = 242;
    public static final int INVITE_FB_SEND = 243;
    public static final int INVITE_FB_RECEIVE = 244;

    //endregion

    //region Monitor service 10000
    public static final int INDEX = 10000;
    public static final int PING = 10001;
    public static final int RELOAD_CONFIG = 10002;
    public static final int PING_IDLE = 10003;
    public static final int UPDATE_BATTLE_RESULT = 10004;
    public static final int SEND_MSG_ALL = 10005;
    public static final int SEND_MSG_USER = 10006;
    public static final int ADMIN_KICK_PLAYER = 10007;
    public static final int SERVER_STATUS = 10008;
    //endregion

    public static final List<Integer> UNSYNC_ACTION = Arrays.asList(HOME_CHAT_LIST);
}

