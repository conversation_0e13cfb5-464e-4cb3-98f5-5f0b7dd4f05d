package com.k2tek.service;

import com.google.protobuf.AbstractMessage;
import io.netty.channel.Channel;
import net.sf.json.JSONObject;

import java.io.ByteArrayOutputStream;

public abstract class BaseServiceMessage {

    /*
     * **************************************
     * ************Common Service************ **************************************
     */
    public static final int ERROR = 64000;
    public static final int SMS_POPUP = 63999;
    public static final int REGISTER_POPUP = 63998;
    public static final int FAST_LOGIN = 63997;
    public static final int MSG_SLIDER = 63995;
    public static final int MSG_POPUP = 63994;
    public static final int LIST_ACTION = 63993;
    public static final int BOT_REQUEST = 63992;
    public static final int UNKNOW = 0xFF;
    public static final int DISCONNECT = 254;
    public static final int PING = 253;
    public static final int ERROR_SILENT = 252;
    public static final int REGISTER = 0; // 0
    public static final int LOGIN = 1;// 1
    public static final int LIST_ROOM = 2;// 2
    public static final int JOIN_ROOM = 3;// 3
    public static final int LEAVE_ROOM = 4;// 4
    public static final int LIST_TABLE = 5;// 5
    public static final int BROADCAST = 6;// 6
    public static final int LOG_OUT = 7;// 7
    public static final int DUPLICATE_LOGIN = 8;// 8
    public static final int LOGIN_VTCONLINE = 94;
    public static final int LOGIN_APPOTA = 96;
    public static final int LOGIN_FACEBOOK = 100;
    public static final int LOGIN_AZGAME = 356;
    public static final int LOGIN_QUICK = 357;

    /*
     * **************************************
     * *****Other Service******** **************************************
     */
    public static final int TOP_BOARD = 9;// 9
    public static final int PERSONAL_INFO = 10;// 10
    public static final int SEND_MAIL = 11;// 11
    public static final int LIST_USER_MAIL = 12;// 12
    public static final int REMOVE_MAIL = 13;// 13
    public static final int REMOVE_MAIL_LIST = 101;
    public static final int FRIEND_LIST = 14;// 14
    public static final int FRIEND_LIST_CHAT = 42;// 14
    public static final int REQUEST_FRIEND = 15;// 15
    public static final int REQUEST_FRIEND_ANSWER = 16;
    public static final int DELETE_FRIEND = 17;
    public static final int AVATAR_LIST = 18;
    public static final int BUY_AVATAR_OLD = 19;
    public static final int UPDATE_PERSONAL_INFOR = 20;
    public static final int UPDATE_READ_MESSAGE = 21;
    public static final int LIST_SYSTEM_MESSAGE = 22;
    public static final int BONUS = 23;
    public static final int SMS_LIST = 24;
    public static final int CHECK_BALANCE = 25;
    public static final int FRIEND_FACEBOOK = 26;
    public static final int FRIEND_FACEBOOK_UPDATE = 358;
    public static final int FRIEND_LIST_UPDATE = 29;
    public static final int FRIEND_LIST_NEW_NEW = 30;
    public static final int FRIEND_LIST_NEW = 31;// 14
    public static final int NOTIFICATION = 32;
    public static final int CHANGE_PASSWORD = 33;
    public static final int RECOVER_PASSWORD = 34;
    public static final int MESSAGE_CONFIG = 35;
    public static final int CHECK_USERNAME = 36;
    public static final int CHECK_USERNAME_NEW = 27;
    public static final int HAS_MESSAGE_COMMING = 37;
    public static final int TOP_UP = 38;
    public static final int SPEAKER_ROOM = 39;
    public static final int SPEAKER_GLOBAL = 40;
    public static final int UPDATE_GENDER = 41;
    public static final int CHAT_ROOM = 44;
    public static final int OVI_CHARGING = 43;
    public static final int TOP_GAME = 45;
    public static final int GET_RECENT_CHAT = 46;
    public static final int REMOVE_ALL_MESSAGE = 47;
    public static final int USER_CHAT = 48;
    public static final int HAS_REQUEST_FRIEND = 49;

    /**
     * Process Tiny game
     */
    public static final int GAME_SLOT_STATUS = 4000;
    public static final int GAME_SLOT = 4001;
    public static final int GAME_MISSION_STATUS = 4002;
    public static final int GAME_MISSION_RECEIVE = 4003;
    public static final int GAME_ROTATE_STATUS = 4004;
    public static final int GAME_ROTATE = 4005;
    public static final int GAME_MISSION_ACTIVE = 4006;
    public static final int GAME_MISSION_LIKEFB = 4007;
    public static final int GAME_MISSION_INVITEFB = 4008;
    public static final int GAME_MISSION_SHAREFB = 4009;

    /**
     * Process Chat
     */
    public static final int CHAT_LIST = 4100;
    public static final int CHAT_NOTIFY = 4101;
    public static final int CHAT_SEND = 4102;


    /*
     * **************************************
     * *****More Avatar Service******** **************************************
     */
    public static final int CHOOSE_DEFAULT_AVATAR = 250;
    public static final int BUY_NEW_AVATAR = 251;
    public static final int BAR_PLAYER_LEAVE = 260;
    public static final int GET_LIST_ITEM = 261;
    public static final int BUY_ITEM = 262;
    public static final int GET_MY_ITEM = 263;
    public static final int UPDATE_FIRST_PROFILE = 264;
    public static final int PLAYER_CHAT = 265;
    public static final int PLAYER_ACTION = 266;
    public static final int PLAYER_EMOTION = 267;
    public static final int PLAYER_BECOME_ZOMBIE = 268;
    public static final int PLAYER_CATCH_ZOMBIE = 269;
    public static final int PLAYER_ZOMBIE_ALIVE = 270;
    public static final int PLAYER_THROW_ITEM = 271;
    public static final int PLAYER_PICK_ITEM = 272;
    public static final int JOIN_BAR_STATUS = 273;
    public static final int JOIN_BAR_DATA = 274;
    public static final int GET_MY_AVATAR = 275;
    public static final int CHANGE_AVATAR1 = 276;
    public static final int JAKENPON_INVITE = 277;
    public static final int JAKENPON_ANSWER = 278;
    public static final int JAKENPON_RESULT = 279;
    public static final int REJOIN_BAR = 280;
    /**
     * Xo so
     */
    public static final int XOSO_TOP_MENU = 300;
    public static final int XOSO_SUB_MENU = 301;
    public static final int XOSO_VIEW_RESULT = 302;
    public static final int XOSO_QUIT = 303;
    /**
     * Tu vi
     */
    public static final int TUVI_TRONDON_MENU = 305;
    public static final int TUVI_TRONDOI_RESULT = 306;
    public static final int TUVI_DAILY_REGISTER = 307;
    public static final int TUVI_DAILY_MENU = 308;
    /**
     * Social: chụp ảnh
     */
    public static final int PICTURE_SEND = 310;
    public static final int PICTURE_CONFIRM_VIEW = 311;
    public static final int PICTURE_REQUEST_VIEW = 312;
    public static final int PICTURE_GET_PRICE = 313;
    public static final int PICTURE_VIEW = 314;
    /**
     * Game caro
     */
    public static final int CARO_PLAY = 315;
    public static final int CARO_RESULT = 316;

    public static final int TOP_FIGHTER = 317;
    public static final int TOP_BEAUTY = 318;

    /**
     * Poker service
     */
    public static final int POKER_BUY_IN = 500;
    public static final int POKER_STAND_UP = 501;
    public static final int POKER_TIP_DEALER = 502;
    public static final int POKER_POST_BIG_BLIND = 503;
    public static final int POKER_DEAL_HOLE_CARDS = 504;
    public static final int POKER_NOTIFY_BOARD_UPDATE = 505;
    public static final int POKER_CHECK = 506;
    public static final int POKER_CALL = 507;
    public static final int POKER_BET = 508;
    public static final int POKER_RAISE = 509;
    public static final int POKER_FOLD = 510;
    public static final int POKER_TURN = 511;
    public static final int POKER_DEAL_COMMUNITY_CARDS = 512;
    public static final int POKER_END_GAME = 513;
    public static final int POKER_DO_SHOW_DOWN = 514;
    public static final int POKER_POST_DEALER = 515;
    public static final int POKER_REBUYIN = 516;
    public static final int POKER_PING_TOSTART = 517;

    // public static final int WEDDING_GET_PLAYER = 319;
    // public static final int WEDDING_CHOSE_PLAYER = 320;
    // public static final int WEDDING_GET_RINGS = 321;
    // public static final int WEDDING_CHOSE_RING = 322;
    // public static final int WEDDING_CONFIRM = 323;
    // public static final int WEDDING_IN_RELATIONSHIP = 324;
    // public static final int WEDDING_OUT_RELATIONSHIP = 325;
    // public static final int WEDDING_BECOME_BRIDE_GROOM = 326;

    // BlackJack

    public static final int BEM_BJ_HIT = 518;
    public static final int BEM_BJ_STAND = 519;
    public static final int BEM_BJ_DOUBLE = 520;
    public static final int BEM_BJ_SURRENDER = 521;
    public static final int BEM_BJ_SPLIT = 522;
    public static final int BEM_BJ_BLACKJACK = 523;
    public static final int BEM_BJ_INSURANT = 524;
    public static final int BEM_SET_BLINDGUESS = 525;
    public static final int BEM_BJ_TURNHOST = 526;
    public static final int POKER_PING_CHEAT = 527;

    public static final int TOURNAMENT_LIST_GAME = 327;
    public static final int TOURNAMENT_LOOSE = 328;
    public static final int TOURNAMENT_WIN = 329;
    public static final int WEDDING_HONEY = 330;
    public static final int HELP_MENU = 331;
    public static final int HELP_CONTENT = 332;
    public static final int TOURNAMENT_AUTOSTART = 333;

    public static final int BEM_CREATE_TABLE = 350;
    public static final int BEM_AUTO_JOIN = 351;
    public static final int BEM_OPPONENT_INFOR = 352;
    public static final int BEM_TABLE_USER_INFOR = 353;
    public static final int JOIN_ROOM_NEW = 354;
    public static final int LIST_TABLE_NEW = 355;

    // Kien add 2012/10/09
    public static final int BEM_CAPTCHA = 20000;
    public static final int BEM_TYGIA = 20001;
    public static final int BEM_TYGIAIAP = 20004;
//	public static final int BEM_DISPLAYPOPUPVMS = 20002;
//	public static final int BEM_DISPLAY_GAME_BAI_BAC_OVISTORE = 20003;
    // Kien add finish 2012/10/09
    /**
     * Guild
     */
    public static final int GUILD_CREATE = 1000;
    public static final int GUILD_CHECK_EXIST = 1001;
    public static final int GUILD_INFOR = 1002;
    public static final int GUILD_TOP_BOARD = 1003;
    public static final int GUILD_INVITE = 1004;
    public static final int GUILD_RECEIVE_INVITE = 1005;
    public static final int GUILD_ANSWER_INVITE = 1006;
    public static final int GUILD_DONATE = 1007;
    public static final int GUILD_REMOVE_MEMBER = 1008;
    public static final int GUILD_LEAVE = 1009;
    public static final int GUILD_DELETE = 1010;
    public static final int GUILD_LIST_MEMBER = 1011;
    public static final int GUILD_SEND_AVATAR = 1012;
    public static final int GUILD_LEAVE_SUCCESS = 1013;
    public static final int GUILD_LIST_MEMBER_FULL = 1014;
    public static final int GUILD_REMOVED_MEMBER = 1015;
    public static final int GUILD_MEMBER_ACCEPT = 1016;

    /**
     * Blog Service
     */
    // Status
    public static final int BLOG_STATUS_LIST = 1500;
    public static final int BLOG_STATUS_UPDATE = 1501;
    public static final int BLOG_STATUS_DELETE = 1502;
    public static final int BLOG_STATUS_VIEW = 1503;
    public static final int BLOG_STATUS_COMMENT = 1504;
    public static final int BLOG_STATUS_COMMENT_DELETE = 1505;
    public static final int BLOG_STATUS_LIKE = 1506;
    public static final int BLOG_NEW_FEED = 1507;

    // Thông báo
    public static final int BLOG_NOTIFY = 1530;
    public static final int BLOG_NOTIFY_READ = 1531;

    // Kien add 2012/10/05
    public static final int BLOG_NOTIFICATION_MESSAGE_ACCEPTFRIEND = 1532;
    // Kien add finish 2012/10/05

    // Album
    public static final int BLOG_ALBUM_CREATE = 1550;
    public static final int BLOG_ALBUM_DELETE = 1551;
    public static final int BLOG_ALBUM_UPDATE = 1552;
    public static final int BLOG_ALBUM_VIEW = 1553;
    public static final int BLOG_ALBUM_ADD_IMAGE = 1554;
    public static final int BLOG_ALBUM_DELETE_IMAGE = 1555;
    public static final int BLOG_ALBUM_COMMENT_IMAGE = 1556;
    public static final int BLOG_ALBUM_DELETE_COMMENT = 1557;
    public static final int BLOG_ALBUM_LIKE_IMAGE = 1558;
    public static final int BLOG_ALBUM_LIST = 1559;
    public static final int BLOG_ALBUM_IMAGE_VIEW = 1560;
    public static final int BLOG_ALBUM_HOT = 1561;
    public static final int BLOG_ALBUM_NEXT_IMG = 1562;

    /**
     * Farm
     */
    public static final int FARM_LIST_ITEM = 2000;
    public static final int FARM_BUY_ITEM = 2001;
    public static final int FARM_SELL_ITEM = 2002;
    public static final int FARM_GET_MY_FARM = 2003;
    public static final int FARM_GET_MY_ITEM = 2004;
    public static final int FARM_OUT_MY_FARM = 2005;
    public static final int FARM_ACTION_TREE = 2006;
    public static final int FARM_ACTION_ANIMAL = 2007;
    public static final int FARM_ACTION_HARVEST = 2008;
    public static final int FARM_TRADE_KOIN = 2009;
    public static final int FARM_OPEN_SLOT = 2010;
    public static final int FARM_UPDATE = 2011;
    public static final int FARM_FRIEND = 2012;
    public static final int FARM_KICK_FRIEND = 2013;
    public static final int FARM_LIST_FRIEND = 2014;
    public static final int FARM_LIST_HISTORY = 2015;
    public static final int FARM_CAN_HARVEST = 2016;
    public static final int FARM_NEED_FEED = 2017;
    public static final int FARM_PING = 2018;
    public static final int FARM_ITEM_SHOP = 2019;
    public static final int FARM_ITEM_MINE = 2020;
    public static final int FARM_ITEM_MINE_START = 2021;
    public static final int FARM_UPDATE_NEW = 2022;
    public static final int FARM_PING_NEW = 2023;
    public static final int FARM_ACTION_TREE_NEW = 2024;
    public static final int FARM_BUY_ITEM_NEW = 2025;
    public static final int FARM_UPDATE_BALANCE = 2026;
    public static final int FARM_GET_FARM_DATA_NEW = 2027;

    /**
     * Common Service
     */
    public static final int LOAD_IMAGE = 3000;
    public static final int LOAD_IMAGE_ERROR = 3001;
    public static final int WEATHER_STATUS = 3002;
    public static final int UPLOAD_IMAGE = 3003;
    public static final int IPHONE_ADD_KOIN = 3004;
    public static final int IPHONE_ENABLE_TOPUP = 3005;
    public static final int NAP_MESSAGE = 3006;
    public static final int NAP_INFORMATION = 3007;
    public static final int VERIFY_IAP = 3008;
    public static final int FACEBOOK_MONEY = 3009;
    public static final int FACEBOOK_LIKE_FANPAGE = 3010;
    public static final int SMS_COMMAND = 3011;
    public static final int SMS_TYGIA = 3012;
    public static final int REQ_RECOVER_PASS = 3013;
    public static final int JOIN_RANDOM_TABLE = 3014;
    public static final int REQ_RECOMMEND_TABLE = 3015;
    public static final int CHANGE_LANGUAGE = 3016;
    public static final int BOT_JOIN_TABLE = 3017;
    public static final int SUPER_TY_GIA = 3018;
    public static final int PLAY_WITH_TOPPLAYER = 3019;
    public static final int JOIN_CONFIG_TABLE = 3020;

    /**
     * Addition service
     */
    public static final int MISSION_ALL = 3500;
    public static final int COMPLETE_MISSION = 3501;
    public static final int CHECK_SERVER_STATUS = 3502;
    public static final int SERVICE_XENG_HOA_QUA = 3503;
    public static final int MISSION_NEW_STYLE = 3505;
    public static final int AZGAME_REGISTER = 3506;
    public static final int FORUM_CONTENT = 3507;
    public static final int MORE_GAME = 3508;
    public static final int MORE_GAME_MONEY = 3509;

    /*
     * Kien add 2012/10/30 Service hiển thị popup đăng ký VIP của Viettel
     */
    public static final int VIETTEL_POPUP_MESSAGE = 5000; // 5000
    /*
     * **************************************
     * *****Service When Playing Game******** **************************************
     */
    public static final int BEM_TABLE_CHAT = 50; // 50
    public static final int BEM_JOIN_TABLE = 51; // 51
    public static final int BEM_LEAVE_TABLE = 52; // 52
    public static final int BEM_READY = 53; // 53
    public static final int BEM_START_GAME = 54; // 54
    public static final int BEM_GET_CARD = 55; // 55
    public static final int BEM_STEAL_CARD = 56; // 56
    public static final int BEM_PLAY_CARD = 57; // 57
    public static final int BEM_SHOW_HAND = 58; // 58
    public static final int BEM_REQUEST_SHOW_HAND = 59; // 59
    public static final int BEM_KICK_PLAYER = 60; // 60
    public static final int BEM_SEND_CARD = 61; // 61
    public static final int BEM_SHOW_RESULT = 62; // 62
    public static final int BEM_BIG_WINNER = 63; // 63
    public static final int BEM_REQUEST_SEND_CARD = 64; // 64
    public static final int BEM_PLAYER_TIME_OUT = 65; // 65

    public static final int BEM_SET_BLIND = 70; // 70
    public static final int BEM_CHANGE_KEY = 71; // 71
    public static final int BEM_TURN_PASS = 72; // 72
    public static final int BEM_SET_PASSWORD = 73; // 73
    public static final int BEM_SET_NUMBER_PLAYER = 74; // 74
    public static final int BEM_LUCKY_WIN = 77; // 77
    public static final int BEM_PLAYER_BET = 76; // 77
    public static final int BEM_PLAYER_FOLD = 78; // 77
    public static final int BEM_BACAY_CHANGE_TYPE = 79; // 77
    public static final int BEM_BACAY_SHOW_ALL = 80; // 77
    public static final int BEM_BACAY_GA_DEAL_MORE = 81; // 77
    public static final int BEM_SAM_CALL = 85;
    public static final int BEM_BACAY_BET = 86; // 77
    public static final int BEM_SETBLIND_TURN = 88;
    public static final int BEM_ANSWER_BET = 89;

    // Service bên dưới ko chạy trong object AbstracTable
    public static final int BEM_LIST_INVITE_USER = 66; // 66
    public static final int BEM_INVITE_USER = 67; // 67
    public static final int BEM_RANDOM_TABLE = 68; // 68
    public static final int BEM_RECEIVER_INVITE = 69; // 69
    public static final int BEM_LIST_INVITE_USER_NEW = 82; // 66
    public static final int BEM_LIST_INVITE_USER_NEW_NEW = 87; // 66
    public static final int BLOG_SEARCH_FRIEND_CHAT = 83;
    public static final int BEM_REJOIN_TABLE = 84;
    public static final int BEM_FREE_PLAY = 90;
    public static final int BEM_QUICK_PLAY = 91;
    /**
     * ProcessTableService
     */
    public static final int GET_NUMBER_TABLE = 75;

    // Kien add 2012/09/29
    public static final int TLMN_THANGTRANG = 95;
    // Kien finish 2012/09/29

    /**
     * Mau binh service
     */
    public static final int MAUBINH_THANGLUON = 97;
    public static final int MAUBINH_XEPBAIXONG = 98;
    public static final int MAUBINH_DOBAI = 99;

    /**
     * SMS
     */
    public static final int SMS_CONFIG = 150; // 150
    public static final int FEED_BACK = 151; // 150
    public static final int ALLOW_BLIND = 152;
    public static final int SHOW_LOGIN_MESSAGE = 153;
    public static final int POKER_BLIND = 154;
    public static final int BUNDLE_VERSION = 155;
    public static final int ENABLE_SELECT_CONFIG_TABLE = 156;

    /**
     * Ba Cay
     */
    public static final int BACAY_SHOW_CARD = 200; // 200
    public static final int XOCDIA_REQ_DAT_CUA_1 = 201; // yêu cầu đặt cửa lần 1
    public static final int XOCDIA_DAT_CUA_1 = 202; // Đặt cửa lần 1
    public static final int XOCDIA_REQ_DAT_CUA_2 = 203; // yêu cầu đặt cửa lần 2
    public static final int XOCDIA_DAT_CUA_2 = 204; // Đặt cửa lần 2
    public static final int XOCDIA_REQ_CAI_HO = 205; // Yêu cầu cái hô
    public static final int XOCDIA_CAI_HO = 206; // Cái hô
    public static final int XOCDIA_LANG_THUA_THIEU = 207; // Làng thừa thiếu
    public static final int CHOOSE_HOSTPLAYER = 208; // lam cai
    public static final int GET_XOCDIA_RESUL = 209; // lay ket qua
    public static final int XOCDIA_CANCELBLIND = 199; // huy cuoc
    public static final int XOCDIA_LAPLAIBLIND = 198; // dat lai cuoc nhu van truoc
    public static final int XOCDIA_NHANDOIBLIND = 197; // dat gap doi cuoc
    public static final int BACCARAT_COUNTCARD = 244; // so luong bai con


    /**
     * Chan
     */
    public static final int CHAN_REQ_XUONG_U = 240;
    public static final int CHAN_XUONG_U = 241;
    public static final int CHAN_LEAVE_CHIU = 242;
    public static final int CHAN_LEAVE_CARD = 243;
    public static final int CHAN_BAO = 234;
    public static final int CHAN_CHIU = 235;
    public static final int CHAN_SET_U = 236;
    public static final int CHAN_XEM_NOC = 237;
    public static final int CHAN_FEED_CHICKEN = 238;
    public static final int CHAN_XUONG_DUNG = 239;
    /**
     * 3.7.3
     */
    public static final int SET_CONVERSAATION = 30000;
    public static final int GET_CONVERSAATION = 30001;
    public static final int LIST_VIP = 30002;
    public static final int ADD_MISION = 3504;

    /**
     * LONG BEME
     */
    public static final int INVITE_FRIENDS_FACEBOOKNEW = 40000;
    /**
     * Triệu phú
     */
    public static final int TRIEUPHU_ANSWER = 210; // 210
    public static final int TRIEUPHU_SET_QUESTION_NUMBER = 211; // 211
    /**
     * Lướt ván
     */
    public static final int LUOTVAN_SET_TURN_SPEED = 212; // 210
    public static final int LUOTVAN_PLAY_TURN = 213; // 210
    public static final int LUOTVAN_END_GAME = 214; // 210
    /**
     * Nhay cot
     */
    public static final int NHAYCOT_CHANGE_TEAM = 215; // 215
    public static final int NHAYCOT_TURN_PASS = 216; // 216
    public static final int NHAYCOT_TURN_PLAY = 217; // 217
    public static final int NHAYCOT_USE_ITEM = 218; // 218
    public static final int BEM_USER_DISCONNECT = 220; // 218
    public static final int BEM_REJOIN_CARD = 222; // 218
    public static final int BEM_AUTO_STARTGAME = 223; // 218
    // public static final int LUOTVAN_PLAY_TURN = 215; // 210
    // public static final int BACAY_TOGGLE_READY = 0xC9; // 150
    // public static final int BACAY_START_GAME = 0xCA; // 150
    // public static final int BACAY_SHOW_HAND = 0xCB; // 150
    // public static final int BACAY_SHOW_RESULT = 0xCC; // 150
    // public static final int BACAY_KICK_PLAYER = 0xCD; // 150
    // public static final int BACAY_LEAVE_TABLE = 0xCE; // 150

    public static final int BEM_EXPIRE_TURN = 230;
    public static final int BEM_SPEC_TURN = 231;
    public static final int BEM_REJOIN_PLAYER = 232;
    public static final int BEM_REJOIN_KICK = 233;

    /**
     * MONITOR
     */
    public static final int MON_INDEX = 63744; // 63999
    public static final int MON_ROOM = 63745; // 63999
    public static final int MON_TABLE_DETAIL = 63746; // 63999
    public static final int MON_FIND_USER = 63747; // 63999
    public static final int MON_KICK_USER = 63748;
    public static final int MON_SEND_MESSAGE = 63749;
    public static final int MON_SEND_MESSAGE_TABLE = 63750;
    public static final int MON_SEND_MESSAGE_ROOM = 63751;
    public static final int MON_SEND_MESSAGE_ALL = 63752;
    public static final int MON_ROOM_DETAIL = 63753;
    public static final int MON_SHOW_ALL_USER = 63754;
    public static final int MON_KICK_USER_IN_ROOM = 63755;
    public static final int MON_RELOAD_CONFIG = 63756;
    public static final int MON_RESET_TABLE = 63757;
    public static final int MON_START_ZOMBIE_GAME = 63758;
    public static final int MON_END_ZOMBIE_GAME = 63759;
    public static final int MON_UPDATE_KOIN = 63760;
    public static final int MON_RELOAD_GUILD_ICON = 63761;
    public static final int MON_START_TOURNAMENT = 63762;
    public static final int MON_VMS_ONLINE = 63763;
    public static final int MON_ERROR_USER = 63764;
    public static final int MON_ERROR_ALL = 63765;
    public static final int MON_RELOAD_BLOCK_WORD = 63766;
    public static final int MON_UPDATE_ROOM = 63767;
    public static final int MON_LIST_IP = 63768;
    public static final int MON_SHUT_DOWN = 63769;
    public static final int MON_MSG_SLIDER = 63770;
    public static final int QUA_ONLINE = 20005;


    // Kick newUser
    // Xem tat ca newUser trong room
    // Gui message cho tat ca newUser
    /**
     * ***********************************
     */
    protected ByteArrayOutputStream body = new ByteArrayOutputStream(1024);
    protected int service = UNKNOW;
    private String mAddress = "";
    protected Channel channel;
    protected byte[] srcRequest;
    protected AbstractMessage response;
    protected JSONObject json, params;
    protected boolean isMonitor = false;

    public boolean isMonitor() {
        return isMonitor;
    }

    public ByteArrayOutputStream getBody() {
        return this.body;
    }

    public int getService() {
        return service;
    }

    public void setService(int service) {
        this.service = service;
    }

    /**
     * @return the mAddress
     */
    public String getAddress() {
        return mAddress;
    }

    public void setAddress(String aAddress) {
        this.mAddress = aAddress;
    }

    public void setChannel(Channel channel) {
        this.channel = channel;
    }

    public byte[] getSrcRequest() {
        return srcRequest;
    }

    public void setSrcRequest(byte[] srcRequest) {
        this.srcRequest = srcRequest;
    }

    public AbstractMessage getResponse() {
        return response;
    }

    public void setResponse(AbstractMessage response) {
        this.response = response;
    }

}
