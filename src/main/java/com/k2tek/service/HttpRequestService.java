package com.k2tek.service;

import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.StringTokenizer;

import org.slf4j.Logger;

import com.k2tek.Xerver;
import com.k2tek.common.slib_Logger;

public class HttpRequestService extends BaseServiceMessage {

    public static Logger getLogger() {
        return slib_Logger.root();
    }
    private static final String URL_PREFIX = "url";
    private static final String METHOD_PREFIX = "method";
    private static final String REQUEST_PREFIX = "req";
    private static final String PARAM_PREFIX = "par";
    private static final String QUICKTIMEOU_PARAM = "timeout";
    //private static final String CACHE_PARAM = "cache";
    private String url;
    private String method = "GET";
    private String request = "";
    private Hashtable params = new Hashtable();
    private boolean shortTimeOut = false;

    /**
     * url:URL method:METHOD req:REQUEST par:PARAMS
     * 
     * @param srcRequest
     */
    public HttpRequestService(byte[] srcRequest, String addr) {
        setAddress(addr);
        String strRequest = new String(srcRequest);
        // debug("--------- Request:" + strRequest);
        StringTokenizer token = new StringTokenizer(strRequest, "\n");
        String key;
        String con;
        String element;
        int index;
        while (token.hasMoreTokens()) {
            element = token.nextToken();
            index = element.indexOf(':');
            if (index != -1) {
                key = element.substring(0, index);
                con = element.substring(index + 1);
                if (URL_PREFIX.equals(key)) {
                    this.url = con;
                } else if (REQUEST_PREFIX.equals(key)) {
                    this.request = con;
                } else if (METHOD_PREFIX.equals(key)) {
                    this.method = con;
                } else if (PARAM_PREFIX.equals(key)) {
                    int indexi = con.indexOf(':');
                    if (indexi > 0) {
                        String key2 = con.substring(0, indexi);
                        String con2 = con.substring(indexi + 1);
                        params.put(key2, con2);
                    }
                } else if (QUICKTIMEOU_PARAM.equals(key)) {
                    shortTimeOut = true;
                }
                /* else if (CACHE_PARAM.equals(key)) {
                cacheEnabled = false;
                }*/
            }

        }

        // add additional client ip to param
		/*if (this.url != null)
        this.url += "&cip=" + this.getAddress();
        else
        this.url = "cip=" + this.getAddress();*/

        getContent();
    }

    private boolean isValidURL() {
        if (this.url == null || !this.url.toLowerCase().startsWith("http://")) {
            return false;
        }

        return true;
    }

    private void getContent() {
        HttpURLConnection mCon = null;
        if (!isValidURL()) {
            getLogger().error("Invalid URL: " + this.url);
            return;
        }

        //getLogger().info(this.url + "\t" + this.request);

        try {
            URL mUrl = new URL(this.url);
            mCon = (HttpURLConnection) mUrl.openConnection();

            //if (shortTimeOut) debug("DUNG SHORT TIMEOUT=====================");

            mCon.setConnectTimeout(Xerver.REMOTE_CONNECT_TIMEOUT);
            mCon.setReadTimeout(shortTimeOut ? Xerver.REMOTE_TIMEOUT_SHORT : Xerver.REMOTE_TIMEOUT);
            mCon.setRequestMethod(this.method);



            if (params != null && params.size() > 0) {
                Enumeration elm = params.keys();
                while (elm.hasMoreElements()) {
                    String key = (String) elm.nextElement();
                    mCon.setRequestProperty(key, params.get(key).toString());
                }
            }

            // send request
            if (this.request != null && !"".equals(this.request)) {
                mCon.setDoOutput(true);
                OutputStreamWriter out = new OutputStreamWriter(mCon.getOutputStream());
                out.write(this.request);
                out.close();
            }

            // get data
            InputStream reader = mCon.getInputStream();
            byte[] buff = new byte[10240];
            int read = 0;
            while ((read = reader.read(buff)) != -1) {
                body.write(buff, 0, read);
            }
            if (body.size() == 0) {
                body.write(-1);// avoid null data returned
            }
//            this.service = HTTP_REQUEST;
        } catch (Exception e) {
            getLogger().error("Error " + this.url + "\t" + e.toString());
        } finally {
            try {
                mCon.disconnect();
            } catch (Exception ignored) {
            }
        }
    }
}
