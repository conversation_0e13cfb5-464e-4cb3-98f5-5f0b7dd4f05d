/*
 * 2008 nvqhome.net
 */
package com.k2tek.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class slib_Logger {

	public static Logger root() {
		Logger logger = LoggerFactory.getLogger("BEM");
		return logger;
	}

	public static Logger money() {
		Logger logger = LoggerFactory.getLogger("MONEY");
		return logger;
	}

	public static Logger act() {
		Logger logger = LoggerFactory.getLogger("ACTIVITY");
		return logger;
	}

	public static Logger quay() {
		Logger logger = LoggerFactory.getLogger("QUAY");
		return logger;
	}

	public static Logger userAPI() {
		Logger logger = LoggerFactory.getLogger("USER_API");
		return logger;
	}

}
