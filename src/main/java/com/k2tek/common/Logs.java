package com.k2tek.common;

import com.bem.dao.mapping.LogErrorEntity;
import com.bem.util.Util;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;

/**
 * Created by vieth_000 on 6/19/2017.
 */
public class Logs {
    static HashSet<String> mError = new HashSet<>();
    static String curDate = "";

    public static void error(String msg) {
        error(msg, true, 1);
    }

    public static void error(String msg, int serverId) {
        error(msg, true, serverId);
    }

    public static void error(String msg, boolean mail, int serverId) {
        slib_Logger.root().error(msg);
        if (mail && serverId < 100) {
            String tmpDate = DateTime.getDateyyyyMMdd(new Date());
            if (!tmpDate.equals(curDate)) {
                curDate = tmpDate;
                mError.clear();
            }
            if (!mError.contains(msg)) {
                mError.add(msg);
                save(new LogErrorEntity(msg, serverId));
            }
        }
    }

    public static void debug(String msg) {
        slib_Logger.root().debug(msg);
    }

    public static void info(String msg) {
        slib_Logger.root().info(msg);
    }

    public static void api(String msg) {
        slib_Logger.userAPI().info(msg);
    }

    public static void warn(String msg) {
        slib_Logger.root().warn(msg);
    }

    public static void save(Object obj) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            session.save(obj);
            session.getTransaction().commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            slib_Logger.root().error(Util.exToString(ex));
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
    }
}
