//package com.k2tek.codec;
//
//import com.k2tek.Config;
//import com.k2tek.XerverHandler;
//
//public class XerverPipelineFactory implements ChannelPipelineFactory {
//
//	private final Timer timer;
//	private final OrderedMemoryAwareThreadPoolExecutor eventExecutor;
//
//	public XerverPipelineFactory(Timer timer, OrderedMemoryAwareThreadPoolExecutor eventExecutor) {
//		this.timer = timer;
//		this.eventExecutor = eventExecutor;
//	}
//
//	public ChannelPipeline getPipeline() throws Exception {
//		// Create a default pipeline implementation.
//		ChannelPipeline pipeline = Channels.pipeline();
//
//		// Here is where we handle accept timeouts
//		// bootstrap.setParentHandler(new ParentHandler(idleTimer, 0, 0, 10));
//
//		// Add the text line codec combination first,
//		pipeline.addLast("decoder", new RequestDecoder());
//
//		// Here is where we handle read/write timeouts on child channels
//		int idleTime = Config.getServerIdleTime();
//		pipeline.addLast("idleHandler", new IdleStateHandler(timer, idleTime, 0, 0));
//
//		// Insert OrderedMemoryAwareThreadPoolExecutor before your blocking handler
//		pipeline.addLast("executor", new ExecutionHandler(eventExecutor));
//
//		// and then business logic.
//		pipeline.addLast("handler", new XerverHandler());
//
//		return pipeline;
//
//	}
//}
