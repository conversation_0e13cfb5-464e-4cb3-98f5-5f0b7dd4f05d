package com.k2tek.codec;

import com.k2tek.Constans;
import com.k2tek.service.BaseServiceMessage;

public class RequestMessage {

	private String magic;
	private int service;
	private byte[] body;
	private String mAddress = "127.0.0.1";

	public RequestMessage(String magic, int service, byte[] body, String address) {
		this.magic = magic;
		this.service = service;
		this.body = body;
		mAddress = address;
	}

	@Override
	public String toString() {
		return String.format("MAGIC:%s, SERVICE:%3d, BODY:%s", magic, service, body);
	}

	public byte[] getBody() {
		return body;
	}

	public void setBody(byte[] body) {
		this.body = body;
	}

	public String getMagic() {
		return magic;
	}

	public void setMagic(String magic) {
		this.magic = magic;
	}

	public int getService() {
		return service;
	}

	public void setService(int service) {
		this.service = service;
	}

	/**
	 * @return the mAddress
	 */
	public String getAddress() {
		return mAddress;
	}
}
