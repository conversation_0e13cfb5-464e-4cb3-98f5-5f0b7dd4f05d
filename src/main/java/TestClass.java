import com.bem.config.CfgCommon;
import com.bem.dao.mapping.BattleUserEntity;
import com.bem.dao.mapping.MonthlyCardEntity;
import com.bem.object.clanbattle.ClanSummary;
import com.bem.util.Util;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.k2tek.Config;
import com.k2tek.common.Logs;
import com.proto.GGProto;
import grep.database.Database2;
import grep.database.HibernateUtil;
import grep.helper.DateTime;
import grep.helper.Filer;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

public class TestClass {

    int[] abc = new int[4];
    static int count = 0;

    public static void main(String[] args) throws Exception {
        var values = Filer.realAllFile("d:/Localization.csv");
        for (String value : values) {
            String[] splits = value.split(",");
            if (splits.length > 4) {
                System.out.println(splits[0]);
                System.out.println(splits[1]);
                System.out.println(splits[2]);
                System.out.println(splits[3]);
                System.out.println(splits[4]);
                System.out.println("----------");
            }
        }

    }

    public void test() {
        String data = new String(Filer.readFile("d:/thongke.log"));
        Map<Integer, ClanSummary> mClanSummary = new Gson().fromJson(data, new TypeToken<Map<Integer, ClanSummary>>() {
        }.getType());
    }

    public MonthlyCardEntity[] dbGetMonthlyCard(String username) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query50 = session.createSQLQuery("select * from boom.monthly_card where server_id=123 and username='nhdang1990' and type=50000 order by id desc limit 1");
            MonthlyCardEntity test = (MonthlyCardEntity) query50.uniqueResult();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(Util.exToString(ex));
        } finally {
            session.close();
        }
        return null;
    }

    public BattleUserEntity isFriend() {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select u.id,u.battle_id,u.user_id,u.receive,u.has_action,d.data,u.bonus_ex,u.date_created from battle_user u join battle_data d where u" +
                    ".battle_id=d.id");
            return (BattleUserEntity) query.addEntity(BattleUserEntity.class).uniqueResult();
        } catch (Exception he) {
            he.printStackTrace();
        } finally {
            session.close();
        }
        return null;
    }

    public static void readFile1(String filename, String parse) throws Exception {
        List<String> lines = Files.readAllLines(Paths.get(filename));
        lines.remove(0);
        Map<String, String> mBomb = new HashMap<>();
        Map<String, String> mAcc = new HashMap<>();

        for (String line : lines) {
            while (line.contains("  ")) line = line.replaceAll("  ", " ");
            Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(line.substring(0, line.indexOf("u") - 1));
            Date fromDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2017-09-01 17:00:00");
            Date toDate =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2017-09-01 18:00:00");
            if (date.after(fromDate)) {
                if (line.contains(parse)) {
                    if (line.contains("upgrade_accesory") && line.contains("Bomb")) {
                        line = line.substring(line.indexOf("u") + 1);
                        line = line.substring(line.indexOf("{"));
                        line = line.replaceAll(" ", "");
                        String bombLevel = line.substring(line.indexOf("BombLevel:"));
                        bombLevel = bombLevel.substring(bombLevel.indexOf(":") + 1);
                        bombLevel = bombLevel.substring(0, bombLevel.indexOf("\""));
                        String bombId = line.substring(line.indexOf("accessoryId:"));
                        bombId = bombId.substring(bombId.indexOf(":") + 1);
                        bombId = bombId.substring(0, bombId.indexOf(";"));
//                        if (!mAcc.containsKey(bombId))
                        mAcc.put(bombId, bombLevel);

                        //142851 upgrade_accesory upgrade_accesory: {"desc":"accessoryId:98657; accessoryImage: 12; BombLevel: 49"}
                    } else if (line.contains("upgrade_bomb") && line.contains("Bomb")) {
                        line = line.substring(line.indexOf("u") + 1);
                        line = line.substring(line.indexOf("{"));
                        line = line.replaceAll(" ", "");
                        String bombLevel = line.substring(line.indexOf("BombLevel:"));
                        bombLevel = bombLevel.substring(bombLevel.indexOf(":") + 1);
                        bombLevel = bombLevel.substring(0, bombLevel.indexOf(";"));
                        String bombId = line.substring(line.indexOf("BombId:"));
                        bombId = bombId.substring(bombId.indexOf(":") + 1);
                        bombId = bombId.substring(0, bombId.indexOf(";"));
//                        System.out.println(line);
//                        System.out.println(bombId);
//                        System.out.println(bombLevel);
//                        if (!mBomb.containsKey(bombId))
                        mBomb.put(bombId, bombLevel);
                        //142851 upgrade_bomb upgrade_bomb: {"desc":"BombId:280572; BombImage: 8; BombLevel: 8; materialId: 16: number: 40; "}
//                    Integer userId = new Integer(line.substring(0, line.indexOf(" ")));
//                    if (!m.containsKey(userId)) m.put(userId, 0);
//                    m.put(userId, m.get(userId) + 1);
//                    break;
                    }
                }
            }
//            String[] s = line.split(" ");
//            String tmp = s[4];
//            tmp = tmp.substring(0, tmp.lastIndexOf(":"));
//            tmp = tmp.substring(tmp.lastIndexOf(":") + 1);
////            System.out.println(tmp);
//            if (!m.containsKey(tmp)) {
//                m.put(tmp, 0);
//            }
//            m.put(tmp, m.get(tmp) + 1);
        }
//        List<Integer> s1 = Arrays.asList(145026,
//                144888,
//                144985,
//                144966,
//                11669,
//                142183,
//                144941,
//                103329,
//                136446,
//                109069);
//        List<Integer> s2 = Arrays.asList(142036,
//                132710,
//                144961,
//                104524,
//                144804,
//                145307,
//                144635,
//                145236,
//                145249,
//                144860,
//                145356);
//
        System.out.println("==============================" + parse);
//        System.out.println("access");
        mBomb.forEach((key, value) -> {
//            System.out.println(key + " " + value);
            String sql = String.format("update user_bomb set level=%s where id=%s;", value, key);
            System.out.println(sql);
        });
//        System.out.println("bomb");
        mAcc.forEach((key, value) -> {
//            System.out.println(key + " " + value);
            String sql = String.format("update user_accessories set level=%s where id=%s;", value, key);
            System.out.println(sql);
        });
    }

    public static void readFile(String filename) throws Exception {
        List<String> lines = Files.readAllLines(Paths.get(filename));
        lines.remove(0);
        Map<Integer, Integer> m = new HashMap<>();

        for (String line : lines) {
            while (line.contains("  ")) line = line.replaceAll("  ", " ");
//            System.out.println(line);
            Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(line.substring(0, line.indexOf("u") - 1));
            Date fromDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2017-09-01 17:00:00");
            Date toDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2017-09-01 18:00:00");
            if (date.after(fromDate) && date.before(toDate)) {
                if (line.contains("achievement|105")) {
                    line = line.substring(line.indexOf("u") + 1);
//                    System.out.println(line);
                    Integer userId = new Integer(line.substring(0, line.indexOf(" ")));
                    if (!m.containsKey(userId)) m.put(userId, 0);
                    m.put(userId, m.get(userId) + 1);
//                break;
                }
            }
//            String[] s = line.split(" ");
//            String tmp = s[4];
//            tmp = tmp.substring(0, tmp.lastIndexOf(":"));
//            tmp = tmp.substring(tmp.lastIndexOf(":") + 1);
////            System.out.println(tmp);
//            if (!m.containsKey(tmp)) {
//                m.put(tmp, 0);
//            }
//            m.put(tmp, m.get(tmp) + 1);
        }
        List<Integer> s1 = Arrays.asList(145026,
                144888,
                144985,
                144966,
                11669,
                142183,
                144941,
                103329,
                136446,
                109069);
        List<Integer> s2 = Arrays.asList(142036,
                132710,
                144961,
                104524,
                144804,
                145307,
                144635,
                145236,
                145249,
                144860,
                145356);

        m.forEach((key, value) -> {
            if (value > 22 && !s2.contains(key)) {
                System.out.println(key);
            }
        });

    }


    static int test = 0;

    public static void runDB() throws Exception {
        Config.load(Config.getConfigPath() + "config.xml");
        Calendar ca = Calendar.getInstance();
        String hours = String.valueOf(ca.get(Calendar.HOUR_OF_DAY));
        String dateCreated = DateTime.getDateyyyyMMddCross(ca.getTime());
        String online = Database2.getUniqueColumn(CfgCommon.mainDb + "cache_ccu", Arrays.asList("date_created", dateCreated, "hours", hours), "online");
        System.out.println(online);
//        Xerver.initGameConfig();
//        String username = "congatrong";
//        AuthDAO aDAO = new AuthDAO();
//        AuthUserEntity authUser = aDAO.getUserByUsername(username);
//        System.out.println(authUser);
//        UserEntity uEntity = aDAO.getLoginUserEntity(authUser);
//        System.out.println(uEntity);
//        UserInfo user = new UserInfo(null, authUser, uEntity);
//        System.out.println(user.loadInventory());
    }

    public int[] getabc() {
        return abc;
    }

    public class PlayerPoint implements Serializable {
        public long id, lastSearch;
        public int point, winStraight, bonusPoint, clanId;
        public String name, clanName;

        public PlayerPoint(int value) {
            this.point = value;
        }

        public PlayerPoint(long id, String name, int clanId, String clanName) {
            this.id = id;
            this.clanId = clanId;
            this.name = name;
            this.clanName = clanName;
            this.lastSearch = System.currentTimeMillis();
        }

        public GGProto.CommonVector.Builder toProto(int index) {
            GGProto.CommonVector.Builder tmp = GGProto.CommonVector.newBuilder();
            tmp.addANumber(point);
            tmp.addANumber(id);
            tmp.addAString(String.valueOf(index));
            tmp.addAString(name);
            return tmp;
        }
    }

}
