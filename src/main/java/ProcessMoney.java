import grep.database.HibernateUtil;
import net.sf.json.JSONObject;
import org.hibernate.SQLQuery;
import org.hibernate.Session;

import java.io.*;
import java.util.*;

public class ProcessMoney {
    public static int[] stat = new int[8];

    public static void main(String[] args) {
        readFile();
    }

    public static void test(String s) {
        s = "";
    }

    public static void listFile() {
        // Directory path here
        String path = "D:/CDRSENT/";

        String files;
        File folder = new File(path);
        File[] listOfFiles = folder.listFiles();

        for (int i = 0; i < listOfFiles.length; i++) {
            // if (listOfFiles[i].isFile()) {
            files = listOfFiles[i].getName();
            String newPath = path + files + "/";
            File subFolder = new File(newPath);
            File[] subFile = subFolder.listFiles();
            for (File file : subFile) {
                readFile(file);
            }
        }
    }

    public static void readFile(File file) {
        try {
            // Open the file that is the first
            // command line parameter
            FileInputStream fstream = new FileInputStream(file);
            // Get the object of DataInputStream
            DataInputStream in = new DataInputStream(fstream);
            BufferedReader br = new BufferedReader(new InputStreamReader(in));
            String strLine;
            // Read File Line By Line
            while ((strLine = br.readLine()) != null) {
                System.out.println(strLine);
                String[] s = strLine.split(":");
                Integer index = Integer.parseInt(s[1].substring(3, 4));
                stat[index]++;
            }
            // Close the input stream
            in.close();
        } catch (Exception e) {// Catch exception if any
            System.err.println("Error: " + e.getMessage());
        }
    }

    public static void readFile() {
        try {
            // Open the file that is the first
            // command line parameter
            FileInputStream fstream = new FileInputStream("d:/error.txt");
            // Get the object of DataInputStream
            DataInputStream in = new DataInputStream(fstream);
            BufferedReader br = new BufferedReader(new InputStreamReader(in));
            String strLine;
            // Read File Line By Line
            int index = 0;
            String o = "";
            while ((strLine = br.readLine()) != null) {
                String test = strLine.substring(strLine.indexOf("u") + 1);
                String[] tmp = test.split(" ");
//                System.out.println(tmp[0] + " " + tmp[3]);
                JSONObject obj = JSONObject.fromObject(tmp[3]);
                String sql = String.format("update user set gold=gold-%s where id=%s;", obj.getLong("addValue"), tmp[0]);
                System.out.println(sql);
            }
            System.out.println(o);
            // Close the input stream
            in.close();
        } catch (Exception e) {// Catch exception if any
            e.printStackTrace();
        }
    }

    public static String getString(String str) {
        if (str.length() == 1) {
            return "0" + str;
        }
        return str;
    }

    // '2tay1em90','qn88','tiphutinhduc','fb100004361942345','hoamyrung'
    // 'lenhhoxung79','dungcoi145','hoaithu179','linhquy990','fb100005383565769','hoamy88','heocuteohd','cattleya','nongtrai','betuong','bcnssjxsazx','zjxjpy','tramhuongviet','fb100005367302477','tramjsss','qn88','dongmon','kuphongkute','anxinanmay5379','hoangcute88','koten6789','fufjvhhjihvvg','fb100004630093500'

    public static void process() {
        Map<String, String> mUsername = new HashMap<String, String>();
        long total = 0, hour = 0;
        for (int i = 1; i <= 17; i++) {
            try {
                // Open the file that is the first
                // command line parameter
                FileInputStream fstream = new FileInputStream("d:/chuot.txt");
                // Get the object of DataInputStream
                DataInputStream in = new DataInputStream(fstream);
                BufferedReader br = new BufferedReader(new InputStreamReader(in));
                String strLine;
                // Read File Line By Line
                long ba = 69263850 + 500000;
                while ((strLine = br.readLine()) != null) {
                    // Print the content on the console
                    if (strLine.contains("Add")) {
                        String tmp = strLine;
                        String money = strLine.substring(strLine.lastIndexOf(" ") + 1);
                        // total += Long.parseLong(money);
                        strLine = strLine.substring(0, strLine.lastIndexOf(" "));
                        String balance = strLine.substring(strLine.lastIndexOf(" ") + 1);
                        // strLine = strLine.substring(0, strLine.lastIndexOf(" "));
                        // String username = strLine.substring(strLine.lastIndexOf(" ") + 1);
                        // mUsername.put(username, balance);

                        if (Long.parseLong(balance) != Long.parseLong(money) + ba) {
                            System.out.println(tmp);
                            break;
                        } else {
                            ba = Long.parseLong(balance);
                        }

                        // if (Long.parseLong(money) > 15000) {
                        // System.out.println(money);
                        // System.out.println(tmp);
                        // break;
                        // }
                    }
                }

                in.close();
            } catch (Exception e) {// Catch exception if any
                e.printStackTrace();
            }
            break;
        }

        // System.out.println(total);
        // System.out.println(mUsername.size());

        // try {
        // Thread.sleep(5000);
        // } catch (InterruptedException e) {
        // // TODO Auto-generated catch block
        // e.printStackTrace();
        // }

        // Object[] obj = mUsername.keySet().toArray();
        // for (Object o : obj) {
        // String s = (String) o;
        // int fileBalance = Integer.parseInt(mUsername.get(s));
        // int dbBalance = getUserBalance(s);
        // System.out.println(s + " -> " + fileBalance + " -> " + dbBalance);
        // try {
        // Thread.sleep(5000);
        // } catch (InterruptedException e) {
        // // TODO Auto-generated catch block
        // e.printStackTrace();
        // }
        // if (fileBalance > dbBalance) {
        // update(s, fileBalance);
        // }
        // }
    }

    public static void update(String username, int balance) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            session.beginTransaction();
            SQLQuery query = session.createSQLQuery("update auth_user set koin=? where username=?");
            query.setInteger(0, balance);
            query.setString(1, username);
            query.executeUpdate();
            session.getTransaction().commit();
        } catch (Exception he) {
            he.printStackTrace();
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
    }

    public static int getUserBalance(String username) {
        Session session = null;
        try {
            session = HibernateUtil.getSessionFactory().openSession();
            SQLQuery query = session.createSQLQuery("select koin from auth_user where username=?");
            query.setString(0, username);
            return Integer.parseInt(query.uniqueResult().toString());
        } catch (Exception he) {
            he.printStackTrace();
        } finally {
            try {
                if (session != null) {
                    session.close();
                }
            } catch (Exception ex) {
            }
        }
        return 0;
    }

    public static void checkFee() {
        String filename = "d:/money.log.2016-09-19";
        List<String> keys = Arrays.asList("UNKNOWN", "SHARECARD", "GETGIFTCODESHARE", "CATTE_", "ADDSAFEBOX", "TRANSFERKOIN_DECREASE"
                , "ADDBALANCETOUR", "Hethongchanle", "Hethong", "BAUCUA_", "XITO_", "FREEKOIN_MOREGAME", "XOCDIA_"
                , "SICBO_", "BONUSSHAREFANPAGE", "BONUSLIKEFANPAGE", "INVITE_FRIENDS_FACEBOOKNEW_"
                , "FREEKOIN_FACEBOOK_", "BONUSCOMMENTS", "CHAN_", "POKER_", "BACAYCH_", "INVITE_BONUS_", "TLMN_"
                , "TLMNNEW_", "PHOM_", "BACAY_", "LIENG_", "SAM_", "TLMNDC_", "BACAYNEW_", "FACEBOOK_FIRST_"
                , "TLMB_", "DAILY_BONUS", "EXP_MISSION", "EVENT_", "MAUBINH", "buy_");
        Map<String, Long> map = new HashMap<String, Long>();
        for (String key : keys) {
            map.put(key, 0L);
        }
        try {
            FileInputStream fstream = new FileInputStream(filename);
            DataInputStream in = new DataInputStream(fstream);
            BufferedReader br = new BufferedReader(new InputStreamReader(in));
            String strLine;
            while ((strLine = br.readLine()) != null) {
                if (strLine.contains("Remove")) {
                    String[] tmp = strLine.split(" -> ");
                    if (tmp.length >= 2) {
                        long money = Long.parseLong(tmp[1].substring(tmp[1].lastIndexOf(" ") + 1));
                        if (strLine.contains("Hethongchanle")) {
                            map.put("Hethongchanle", map.get("Hethongchanle") + money);
                        } else if (strLine.contains("Hethong")) {
                            map.put("Hethong", map.get("Hethong") + money);
                        } else {
                            boolean found = false;
                            for (String key : keys) {
                                if (strLine.contains(key)) {
                                    map.put(key, map.get(key) + money);
                                    found = true;
                                    break;
                                }
                            }
                            if (!found) {
                                System.out.println(strLine);
                                map.put("UNKNOWN", map.get("UNKNOWN") + money);
                            }
                        }
                    }
                }
            }
            in.close();
            Iterator<String> iter = map.keySet().iterator();
            while (iter.hasNext()) {
                String key = iter.next();
                System.out.println(key + " " + map.get(key));
            }
        } catch (Exception e) {// Catch exception if any
            e.printStackTrace();
        }
    }
}
