import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp;
import com.google.api.client.extensions.jetty.auth.oauth2.LocalServerReceiver;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;

import java.io.*;
import java.security.GeneralSecurityException;
import java.util.Arrays;
import java.util.List;

public class GoogleIAP {
    private static final String APPLICATION_NAME = "Google Sheets API Java Quickstart";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    private static final String TOKENS_DIRECTORY_PATH = "tokens";

    /**
     * Global instance of the scopes required by this quickstart.
     * If modifying these scopes, delete your previously saved tokens/ folder.
     */
    private static final List<String> SCOPES = Arrays.asList("https://www.googleapis.com/auth/androidpublisher");
    private static final String CREDENTIALS_FILE_PATH = "bomb.json";

    /**
     * Creates an authorized Credential object.
     *
     * @param HTTP_TRANSPORT The network HTTP Transport.
     * @return An authorized Credential object.
     * @throws IOException If the credentials.json file cannot be found.
     */
    private static Credential getCredentials(final NetHttpTransport HTTP_TRANSPORT) throws IOException {
        // Load client secrets.
        File file = new File(CREDENTIALS_FILE_PATH);
        InputStream in = new FileInputStream(file);// GoogleSheet.class.getResourceAsStream(CREDENTIALS_FILE_PATH);
        if (in == null) {
            throw new FileNotFoundException("Resource not found: " + CREDENTIALS_FILE_PATH);
        }
        GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));

        // Build flow and trigger user authorization request.
        GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
                HTTP_TRANSPORT, JSON_FACTORY, clientSecrets, SCOPES)
                .setDataStoreFactory(new FileDataStoreFactory(new File(TOKENS_DIRECTORY_PATH)))
                .setAccessType("offline")
                .build();
        LocalServerReceiver receiver = new LocalServerReceiver.Builder().setPort(8888).build();
        return new AuthorizationCodeInstalledApp(flow, receiver).authorize("user");
    }

    // TODO: Another wait to connect to Google Sheets

/*    public static Sheets createSheetsService() throws IOException, GeneralSecurityException {
        HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
        JsonFactory jsonFactory = JacksonFactory.getDefaultInstance();

        // TODO: Change placeholder below to generate authentication credentials. See
        // https://developers.google.com/sheets/quickstart/java#step_3_set_up_the_sample
        //
        // Authorize using one of the following scopes:
        //   "https://www.googleapis.com/auth/drive"
        //   "https://www.googleapis.com/auth/drive.file"
        //   "https://www.googleapis.com/auth/spreadsheets"

        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
        return new Sheets.Builder(httpTransport, jsonFactory, getCredentials(HTTP_TRANSPORT))
                .setApplicationName("Google-SheetsSample/0.1")
                .build();
    }*/

    public static final String spreadsheetId = "1yW_Aklg8qxecHXAJo4HHH2wF12WNxRogs9dRewExTrg";
    public static final List<String> tables = List.of("dson_main.config_help", "dson_main.config_language", "dson_main.config_res_language");
    public static final List<String> sheetRanges = List.of("config_help!A:L", "config_language!A:L", "config_res_language!A:L");

    public static void main(String... args) throws IOException, GeneralSecurityException {
        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();

        AndroidPublisher publisher = new AndroidPublisher.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(HTTP_TRANSPORT))
                .setApplicationName(APPLICATION_NAME).build();
        publisher.purchases().products().consume("com.MetaBomb.BoomB", "pack.diamond.6", "fddehidodjifonpgppogobfp.AO-J1Oz9OhAHS1iFFJtBGLML-PP996otZV_sWxTjzvY2060hhVyQoSVN6spGN3w80FMaWuoW97oxI6JBebKAHJDuptfSZ82PMw").execute();

        //        DBJPA.init("localtestsea");
        //        for (int i = 0; i < tables.size(); i++) {
        //            String table = tables.get(i);
        //            String sheetRange = sheetRanges.get(i);
        //            System.out.println("table = " + table);
        //            // Build a new authorized API client service.
        //            final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
        //
        //            // 1Y2XcXEMIpjfplYnMGJX_qKGf9vbSN9-X4WG4Jnj-pLY
        //            final String range = sheetRange; // sheetname!col1:col2
        //            Sheets service = new Sheets.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(HTTP_TRANSPORT))
        //                    .setApplicationName(APPLICATION_NAME)
        //                    .build();
        //            ValueRange response = service.spreadsheets().values()
        //                    .get(spreadsheetId, range)
        //                    .execute();
        //            List<List<Object>> values = response.getValues();
        //            if (values == null || values.isEmpty()) {
        //                System.out.println("No data found.");
        //            } else {
        //                //                LangProcess.builder().values(values).table(table).build().process();
        //                SuperCustomLangProcess.builder().values(values).table(table).build().process();
        //                //                CustomLangProcess.builder().values(values).table(table).build().process();
        //            }
        //        }
    }
}