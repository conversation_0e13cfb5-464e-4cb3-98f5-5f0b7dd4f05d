import com.google.gson.JsonObject;
import com.proto.GGProto;
import grep.helper.Filer;
import grep.helper.GsonUtil;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Ellipse2D;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Calendar;
import java.util.Hashtable;
import java.util.Map;

public class CP {

    int id = 0;

    public CP(int id) {
        this.id = id;
    }

    public static String cps = "AKN,ALV,B9X,CAU,COL,D4G,DAD,DPX,DWG,EGNS,G3X,G9X,GBG,GDP," + "HDY,HEO,HIE,HNN,HOR,HQA,HSM,HSV,HUW,HVT,HYK,ISV,IVI,KNH,KS9X,KUY," + "L9X,LAL,LNQ,M4U,MHP,MKX,MMG,MNGS,MVC,NHN,NTY,NVD,OLC,QTR,RUA,SVT,SVX," + "T2H,TG9X,TGT,TH1X,THM,THN,THN,TNN,TNP,TRN,TSU,TT9,TTV,TTX," + "V3X,V4G,V9X,VCH,VHA,VHP,VIET,VINA,VJV,VND,VNL,VODOI,VPR,VTL," + "W1X,W8X,WA4G,WBM,WBY,WCN,WHN,WNL,WSV,WTA,WTG,WVT,XKK,YEU,YMB,YOH,YOTEL,YWAP,Z9X,ZHX";
    public static Map<tete, String> m = new Hashtable<tete, String>();

    public static BufferedImage makeRoundedCorner(BufferedImage image, int cornerRadius) {
        int w = image.getWidth();
        int h = image.getHeight();
        BufferedImage output = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);

        Graphics2D g2 = output.createGraphics();

        // This is what we want, but it only does hard-clipping, i.e. aliasing
        // g2.setClip(new RoundRectangle2D ...)

        // so instead fake soft-clipping by first drawing the desired clip shape
        // in fully opaque white with antialiasing enabled...
        g2.setComposite(AlphaComposite.Src);
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.setColor(Color.WHITE);
        g2.fill(new Ellipse2D.Float(0, 0, w, h));

        // ... then compositing the image on top,
        // using the white shape from above as alpha source
        g2.setComposite(AlphaComposite.SrcAtop);
        g2.drawImage(image, 0, 0, null);

        g2.dispose();

        return output;
    }

    public static void main(String[] args) throws IOException {

        String data = "      {" +
                "      \"Payload\":\"{\\\"json\\\":\\\"{\\\\\\\"orderId\\\\\\\":\\\\\\\"GPA.3370-4772-5589-89088\\\\\\\",\\\\\\\"packageName\\\\\\\":\\\\\\\"com.MetaBomb.BoomB\\\\\\\",\\\\\\\"productId\\\\\\\":\\\\\\\"pack.diamond.6\\\\\\\",\\\\\\\"purchaseTime\\\\\\\":1714801823237,\\\\\\\"purchaseState\\\\\\\":0,\\\\\\\"purchaseToken\\\\\\\":\\\\\\\"fddehidodjifonpgppogobfp.AO-J1Oz9OhAHS1iFFJtBGLML-PP996otZV_sWxTjzvY2060hhVyQoSVN6spGN3w80FMaWuoW97oxI6JBebKAHJDuptfSZ82PMw\\\\\\\",\\\\\\\"quantity\\\\\\\":1,\\\\\\\"acknowledged\\\\\\\":false}\\\",\\\"signature\\\":\\\"CtB7ghCKEZgg0Q6GKq8oA1lQVt5vgTHALyDmEVhqgQ1M7xKRG2lRfacLGXZ9iSd35KqHP3HQfXkaOWZ4czFmg0W8IIsscbWDHTsw0AEAAdUGUWQ6wCGyhoCBQNMDoVaZNoVQeAfnYyopn462uP60rnBTL+QaFmFovpi72yoArwt1/fKD2uyK9JYLAlTQZiw8k1OOFQ9yFDKo3EA8agwQ02XrENYSI7pZSCNPQK3l1ttWyXLrG1kt4ESewydm0W8o//qUhBdtf1wolkJcz1mj/vLNHv3T5XnfzSLd/qvABZYhJ83yMW+lwej8lOz3h+1///xnQAqH2MNpnL3BEGl2fQ==\\\",\\\"skuDetails\\\":[\\\"{\\\\\\\"productId\\\\\\\":\\\\\\\"pack.diamond.6\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"inapp\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Diamond Pack 6 (com.MetaBomb.BoomB (unreviewed))\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"Diamond Pack 6\\\\\\\",\\\\\\\"iconUrl\\\\\\\":\\\\\\\"https:\\\\\\\\/\\\\\\\\/lh3.googleusercontent.com\\\\\\\\/btIgzqAzlb3OYTag94wYOyO-exTx4qkGZFfwopOydrJBWn4YbgdpNb2otjmBSRsgTA0\\\\\\\",\\\\\\\"description\\\\\\\":\\\\\\\"Diamond Pack 6\\\\\\\",\\\\\\\"price\\\\\\\":\\\\\\\"19,66\\\\u00a0US$\\\\\\\",\\\\\\\"price_amount_micros\\\\\\\":19656784,\\\\\\\"price_currency_code\\\\\\\":\\\\\\\"USD\\\\\\\",\\\\\\\"skuDetailsToken\\\\\\\":\\\\\\\"AEuhp4LXmQhyFlZQ53bWsqLjCBEr-S3e_A4-fSlw2dT2T0pT4IfF2V8kcP8GTPCYnTY=\\\\\\\"}\\\"]}\",\n" +
                "     \"Store\":\"GooglePlay\",\n" +
                "      \"TransactionID\":\"fddehidodjifonpgppogobfp.AO-J1Oz9OhAHS1iFFJtBGLML-PP996otZV_sWxTjzvY2060hhVyQoSVN6spGN3w80FMaWuoW97oxI6JBebKAHJDuptfSZ82PMw\"\n" +
                "      }";

        JsonObject obj = GsonUtil.parseJsonObject(data);
        String reqId = obj.get("TransactionID").getAsString();
        String reqData = GsonUtil.parseJsonObject(obj.get("Payload").getAsString()).get("json").getAsString();
        System.out.println(reqId);
        System.out.println("reqData = " + reqData);

        JsonObject objData = GsonUtil.parseJsonObject(reqData);
        String packageName = objData.get("packageName").getAsString();
        String productId = objData.get("productId").getAsString();
        String purchaseToken = objData.get("purchaseToken").getAsString();
        String verifyResult = Filer.getHttpContent("https://androidpublisher.googleapis.com/androidpublisher/v3/applications/%s/purchases/products/%s/tokens/%s"
                .formatted(packageName, productId, purchaseToken));
        System.out.println("verifyResult = " + verifyResult);
    }

    public static class tete {
        String a;
        String b;

        public tete(String a, String b) {
            super();
            this.a = a;
            this.b = b;
        }

    }
}
