<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserEventEntity" table="user_event">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserEventEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="eventId" column="event_id"/>
            <key-property name="eventIndex" column="event_index"/>
            <key-property name="serverId" column="server_id"/>
        </composite-id>
        <property name="info" column="info"/>
        <property name="value" column="value"/>
        <property name="sendBonus" column="send_bonus"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>