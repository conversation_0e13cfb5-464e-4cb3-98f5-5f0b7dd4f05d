<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserMaterialEntity" table="user_material">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserMaterialEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="materialId" column="material_id"/>
        </composite-id>
        <property name="number" column="number"/>
    </class>
</hibernate-mapping>