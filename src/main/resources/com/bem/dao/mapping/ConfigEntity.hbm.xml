<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.ConfigEntity" table="config" schema="boom">
        <id name="key" column="key"/>
        <property name="value" column="value"/>
    </class>
</hibernate-mapping>