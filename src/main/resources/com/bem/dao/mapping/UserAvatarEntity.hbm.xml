<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserAvatarEntity" table="user_avatar">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserAvatarEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="avatarId" column="avatar_id"/>
        </composite-id>
        <property name="level" column="level"/>
        <property name="fragment" column="fragment"/>
    </class>
</hibernate-mapping>