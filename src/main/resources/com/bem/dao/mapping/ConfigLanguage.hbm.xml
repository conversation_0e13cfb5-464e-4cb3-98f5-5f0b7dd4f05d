<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- Generated Jan 9, 2013 3:15:14 PM by Hibernate Tools 3.4.0.CR1 -->
<hibernate-mapping>
	<class name="com.bem.dao.mapping.ConfigLanguage" table="config_language" catalog="boom">
		<id name="k" type="string">
			<column name="k" length="200" not-null="true" />
		</id>
		<property name="vi" type="string">
			<column name="vi" length="1000"/>
		</property>
		<property name="en" type="string">
			<column name="en" length="1000" />
		</property>
	</class>
</hibernate-mapping>
