<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserFriendEntity" table="user_friend">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserFriendEntityPK">
            <key-property name="user1" column="user1"/>
            <key-property name="user2" column="user2"/>
        </composite-id>
        <property name="fstatus" column="fstatus"/>
        <property name="wifeStatus" column="wife_status"/>
        <property name="point" column="point_friend"/>

    </class>
</hibernate-mapping>