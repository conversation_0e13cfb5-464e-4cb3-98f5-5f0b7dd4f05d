<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserUfoEntity" table="user_ufo">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserUfoEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="ufoId" column="ufo_id"/>
        </composite-id>
        <property name="atk" column="atk"/>
        <property name="hp" column="hp"/>
    </class>
</hibernate-mapping>