<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.SystemMessageEntity" table="system_message" schema="boom">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="enable" column="enable"/>
        <property name="title" column="title"/>
        <property name="content" column="content"/>
        <property name="os" column="os"/>
        <property name="cp" column="cp"/>
        <property name="version" column="version"/>
        <property name="server" column="server"/>
        <property name="type" column="type"/>
        <property name="actionType" column="action_type"/>
        <property name="actionData" column="action_data"/>
        <property name="dateBegin" column="date_begin"/>
        <property name="dateEnd" column="date_end"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>