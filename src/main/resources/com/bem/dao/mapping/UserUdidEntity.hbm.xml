<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserUdidEntity" table="user_udid" schema="boom">
        <id name="id" column="id">
            <generator class="identity" />
        </id>
        <property name="udid" column="udid"/>
        <property name="username" column="username"/>
    </class>
</hibernate-mapping>