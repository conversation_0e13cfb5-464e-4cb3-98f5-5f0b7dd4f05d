<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- Generated Jul 29, 2014 3:03:45 PM by Hibernate Tools 3.4.0.CR1 -->
<hibernate-mapping>
    <class name="com.bem.dao.mapping.Jackpot" table="jackpot">
        <id name="serverId" type="int">
            <column name="server_id"/>
            <generator class="assigned"/>
        </id>
        <property name="pot" type="long">
            <column name="pot" not-null="true"/>
        </property>
        <property name="payout" type="long">
            <column name="payout" not-null="true"/>
        </property>
        <property name="history" type="string">
            <column name="history" length="5000"/>
        </property>
        <property name="minReceive" type="int">
            <column name="min_receive" not-null="true"/>
        </property>
        <property name="maxDay" type="int">
            <column name="max_day" not-null="true"/>
        </property>
        <property name="lastJackpot" type="timestamp">
            <column name="last_jackpot" length="19" not-null="true"/>
        </property>
    </class>
</hibernate-mapping>