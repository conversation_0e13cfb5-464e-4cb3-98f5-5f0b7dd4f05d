<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.MonthlyCardReceivedEntity" table="monthly_card_received" schema="boom">
        <id name="id" column="id">
            <generator class="identity" />
        </id>
        <property name="serverId" column="server_id"/>
        <property name="userId" column="user_id"/>
        <property name="username" column="username"/>
        <property name="cardType" column="card_type"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>