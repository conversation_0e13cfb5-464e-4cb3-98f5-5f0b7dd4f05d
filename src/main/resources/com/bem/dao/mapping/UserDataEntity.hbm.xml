<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserDataEntity" table="user_data">
        <id name="userId" column="user_id"/>
        <property name="dataInt" column="data_int"/>
        <property name="shopSpecial" column="shop_special"/>
        <property name="energy" column="energy"/>
        <property name="attendance" column="attendance"/>
        <property name="rankTrophy" column="rank_trophy"/>
        <property name="mapLevel" column="map_level"/>
        <property name="mapAward" column="map_award"/>
        <property name="moneys" column="moneys"/>
        <property name="missionInviteFb" column="mission_invite_fb"/>
    </class>
</hibernate-mapping>