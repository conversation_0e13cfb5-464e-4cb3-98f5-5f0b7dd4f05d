<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.LogEventEntity" table="log_event">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="serverId" column="server_id"/>
        <property name="eventId" column="event_id"/>
        <property name="eventIndex" column="event_index"/>
        <property name="eventYear" column="event_year"/>
        <property name="data" column="data"/>
        <property name="info" column="info"/>
        <property name="dateModified" column="date_modified"/>
    </class>
</hibernate-mapping>