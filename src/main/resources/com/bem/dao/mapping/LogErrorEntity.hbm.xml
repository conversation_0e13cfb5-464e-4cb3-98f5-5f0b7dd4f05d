<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.LogErrorEntity" table="log_error" schema="boom">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="serverId" column="server_id"/>
        <property name="message" column="message"/>
        <property name="notify" column="notify"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>