<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.ShopItemEntity" table="shop_item" schema="boom">
        <id name="id" column="id"/>
        <property name="name" column="name"/>
        <property name="desc" column="desc"/>
        <property name="priceType" column="price_type"/>
        <property name="price" column="price"/>
        <property name="numberAllow" column="number_allow"/>
        <property name="mode" column="mode"/>
        <property name="isEnable" column="is_enable"/>
        <property name="unlockLevel" column="unlock_level"/>
    </class>
</hibernate-mapping>