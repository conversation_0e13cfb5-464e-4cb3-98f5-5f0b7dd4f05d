<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.ClanBattleBonusEntity" table="clan_battle_bonus">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="clanId" column="clan_id"/>
        <property name="message" column="message"/>
        <property name="bonus" column="bonus"/>
        <property name="number" column="number"/>
        <property name="receiver" column="receiver"/>
        <property name="dateModified" column="date_modified"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>