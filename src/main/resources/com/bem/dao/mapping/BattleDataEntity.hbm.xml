<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.BattleDataEntity" table="battle_data">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="dateCreated" column="date_created"/>
        <property name="gameType" column="game_type"/>
        <property name="data" column="data"/>
    </class>
</hibernate-mapping>