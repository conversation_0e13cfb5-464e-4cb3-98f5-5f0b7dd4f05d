<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserSymbolEntity" table="user_symbol">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserSymbolEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="symbolId" column="symbol_id"/>
        </composite-id>
    </class>
</hibernate-mapping>