<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserHeroEntity" table="user_hero">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserHeroEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="heroId" column="hero_id"/>
        </composite-id>
        <property name="exp" column="exp"/>
        <property name="level" column="level"/>
    </class>
</hibernate-mapping>