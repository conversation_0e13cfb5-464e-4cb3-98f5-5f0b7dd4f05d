<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserPetFoodEntity" table="user_pet_food">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserPetFoodEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="foodId" column="food_id"/>
        </composite-id>
        <property name="number" column="number"/>
    </class>
</hibernate-mapping>