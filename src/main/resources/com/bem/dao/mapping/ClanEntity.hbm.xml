<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.ClanEntity" table="clan">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="name" column="name"/>
        <property name="serverId" column="server_id"/>
        <property name="avatar" column="avatar"/>
        <property name="slogan" column="slogan"/>
        <property name="exp" column="exp"/>
        <property name="trophy" column="trophy"/>
        <property name="level" column="level"/>
        <property name="joinRule" column="join_rule"/>
        <property name="joinTrophy" column="join_trophy"/>
        <property name="member" column="member"/>
        <property name="chat" column="chat"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>