<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.topTrophyDaily" table="top_trophy_daily">
        <id name="id" type="int">
            <column name="id"/>
            <generator class="assigned"/>
        </id>
        <property name="oldId" type="int">
            <column name="old_id" not-null= "true"/>
        </property>
        <property name="trophy" type="long">
            <column name="trophy" not-null="true"/>
        </property>
        <property name="oldTrophy" type="long">
            <column name="old_trophy" not-null="true"/>
        </property>
        <property name="username">
            <column name="username" sql-type="varchar" length="50" />
        </property>
        <property name="clan">
            <column name="clan" sql-type="varchar" length="50" />
        </property>
        <property name="screenName">
            <column name="screen_name" sql-type="varchar" length="50" />
        </property>
        <property name="userId" type="int">
            <column name="user_id" not-null= "true"/>
        </property>
        <property name="createdOn" type="timestamp">
            <column name="created_on" length="19" not-null="true"/>
        </property>
    </class>
</hibernate-mapping>