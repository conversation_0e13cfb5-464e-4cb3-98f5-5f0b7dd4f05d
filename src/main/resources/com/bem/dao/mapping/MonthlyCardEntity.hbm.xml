<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.MonthlyCardEntity" table="monthly_card" schema="boom">
        <id name="id" column="id"/>
        <property name="cardId" column="card_id"/>
        <property name="iapId" column="iap_id"/>
        <property name="fromTime" column="from_time"/>
        <property name="toTime" column="to_time"/>
        <property name="username" column="username"/>
        <property name="serverId" column="server_id"/>
        <property name="type" column="type"/>
    </class>
</hibernate-mapping>