<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserPetEntity" table="user_pet">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserPetEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="petId" column="pet_id"/>
        </composite-id>
        <property name="level" column="level"/>
        <property name="exp" column="exp"/>
        <property name="star" column="star"/>
        <property name="tienhoalv" column="tienhoalv"/>
        <property name="fragment" column="fragment"/>
        <property name="energy" column="energy"/>
    </class>
</hibernate-mapping>