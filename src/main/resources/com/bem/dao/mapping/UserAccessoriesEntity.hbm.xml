<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserAccessoriesEntity" table="user_accessories">
    <id name="id" column="id">
        <generator class="identity" />
    </id>
    <property name="userId" column="user_id"/>
    <property name="accessoriesId" column="accessories_id"/>
    <property name="level" column="level"/>
    </class>
</hibernate-mapping>