<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserItemEntity" table="user_item">
        <composite-id mapped="true" class="com.bem.dao.mapping.UserItemEntityPK">
            <key-property name="userId" column="user_id"/>
            <key-property name="itemId" column="item_id"/>
        </composite-id>
        <property name="number" column="number"/>
    </class>
</hibernate-mapping>