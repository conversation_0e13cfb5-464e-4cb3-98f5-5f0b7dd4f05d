<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserEntity" table="user">
        <id name="id" column="id"/>
        <property name="mainId" column="main_id"/>
        <property name="server" column="server"/>

        <property name="username" column="username"/>
        <property name="name" column="name"/>
        <property name="clan" column="clan"/>
        <property name="clanPosition" column="clan_position"/>
        <property name="facebook" column="facebook"/>
        <property name="level" column="level"/>
        <property name="avatar" column="avatar"/>
        <property name="battleItem" column="battle_item"/>
        <property name="star" column="star"/>
        <property name="starMap" column="star_map"/>
        <property name="gold" column="gold"/>
        <property name="medal" column="medal"/>
        <property name="diemdanh" column="diemdanh"/>
        <property name="win" column="win"/>
        <property name="loose" column="loose"/>
        <property name="trophy" column="trophy"/>
        <property name="gem" column="gem"/>
        <property name="gemNap" column="gem_nap"/>
        <property name="getEventNap" column="get_event_nap"/>
        <property name="eventLienthang" column="event_lienthang"/>
        <property name="exp" column="exp"/>
        <property name="vip" column="vip"/>
        <property name="numberBossTop" column="number_boss_top"/>
        <!--<property name="money" column="money"/>-->
        <property name="atomic" column="atomic"/>
        <property name="notify" column="notify"/>
        <property name="settings" column="settings"/>
        <property name="cp" column="cp"/>
        <property name="logout" column="logout"/>
        <property name="topBossCur" column="top_boss_cur"/>
        <property name="topBossMax" column="top_boss_max"/>
        <property name="udidLogin" column="udid_login"/>
        <property name="udidGroup" column="udid_group"/>
        <property name="achievementReceive" column="achievement_receive"/>
        <property name="achievementStatus" column="achievement_status"/>
        <property name="lastLogin" column="last_login"/>
        <property name="dateCreated" column="date_created"/>
        <property name="lockChat" column="lock_chat"/>

    </class>
</hibernate-mapping>