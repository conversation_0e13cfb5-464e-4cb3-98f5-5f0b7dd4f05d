<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserMessageEntity" table="user_message">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="userId" column="user_id"/>
        <property name="title" column="title"/>
        <property name="message" column="message"/>
        <property name="bonus" column="bonus"/>
        <!--<property name="bonusMsg" column="bonus_msg"/>-->
        <property name="receive" column="receive"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>