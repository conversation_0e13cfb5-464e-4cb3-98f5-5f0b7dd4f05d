<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.BattleUserEntity" table="battle_user">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="battleId" column="battle_id"/>
        <property name="userId" column="user_id"/>
        <property name="receive" column="receive"/>
        <property name="hasAction" column="has_action"/>
        <property name="bonusEx" column="bonus_ex"/>
        <property name="bonusBattleEx" column="bonus_battle_ex"/>
        <property name="data" column="data"/>
        <property name="gameType" column="game_type"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>