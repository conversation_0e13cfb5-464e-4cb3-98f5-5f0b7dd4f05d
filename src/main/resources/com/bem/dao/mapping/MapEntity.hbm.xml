<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.MapEntity" table="map" schema="boom">
        <id name="id" column="id"/>
        <property name="data" column="data"/>
        <property name="dataMonster" column="data_monster"/>
        <property name="exp" column="exp"/>
        <property name="gold" column="gold"/>
        <property name="gem" column="gem"/>
        <property name="dropItem" column="drop_item"/>
        <property name="effect" column="effect"/>
        <property name="energy" column="energy"/>
        <property name="timesAtk" column="times_atk"/>
        <property name="unlockLevel" column="unlock_level"/>
    </class>
</hibernate-mapping>
