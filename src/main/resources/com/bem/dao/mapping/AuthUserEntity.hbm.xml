<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.AuthUserEntity" table="auth_user" schema="boom">
        <id name="id" column="id">
            <generator class="identity" />
        </id>
        <property name="createdOn" column="created_on"/>
        <property name="username" column="username"/>
        <property name="facebook" column="facebook"/>
        <property name="mobile" column="mobile"/>
        <property name="hpass" column="hpass"/>
        <property name="cp" column="cp"/>
        <property name="subcp" column="subcp"/>
        <property name="os" column="os"/>
        <property name="osVersion" column="os_version"/>
        <property name="lastLogin" column="last_login"/>
        <property name="udid" column="udid"/>
        <property name="version" column="version"/>
        <property name="email" column="email"/>
        <property name="serverIds" column="server_ids"/>
    </class>
</hibernate-mapping>