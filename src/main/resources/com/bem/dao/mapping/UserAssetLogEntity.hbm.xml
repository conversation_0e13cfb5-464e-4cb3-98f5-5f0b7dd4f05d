<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="com.bem.dao.mapping.UserAssetLogEntity" table="user_asset_log">
        <id name="id" column="id">
            <generator class="identity"/>
        </id>
        <property name="userId" column="user_id"/>
        <property name="data" column="data"/>
        <property name="k" column="k"/>
        <property name="dateCreated" column="date_created"/>
    </class>
</hibernate-mapping>