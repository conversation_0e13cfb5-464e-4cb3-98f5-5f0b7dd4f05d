<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-configuration PUBLIC "-//Hibernate/Hibernate Configuration DTD 3.0//EN" "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
    <session-factory>
        <property name="hibernate.dialect">org.hibernate.dialect.MySQLDialect</property>

        <!-- Database connection settings -->
        <property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
        <property name="hibernate.connection.url">*****************************/</property>

        <!--<property name="hibernate.connection.useUnicode">true</property>
        <property name="hibernate.connection.characterEncoding">UTF-8</property>
        <property name="hibernate.connection.charSet">UTF-8</property>-->

        <property name="hibernate.physical_naming_strategy">com.bem.util.CustomANamingStrategy</property>

        <!-- Echo all executed SQL to stdout -->
        <property name="hibernate.show_sql">false</property>
        <!-- <property name="hibernate.query.factory_class">org.hibernate.hql.classic.ClassicQueryTranslatorFactory</property> -->

        <!-- Disable the second-level cache -->
        <property name="cache.provider_class">org.hibernate.cache.NoCacheRegionFactoryAvailableException</property>

        <!-- Use the C3P0 connection pool provider -->
        <property name="hibernate.c3p0.max_size">100</property>
        <property name="hibernate.c3p0.min_size">5</property>
        <property name="hibernate.c3p0.timeout">60</property>
        <property name="hibernate.c3p0.max_statements">0</property>
        <property name="hibernate.c3p0.idle_test_period">60</property>
        <property name="hibernate.c3p0.acquire_increment">5</property>
        <property name="hibernate.c3p0.preferredTestQuery">select 1;</property>

        <!--		&lt;!&ndash; Mapping Files &ndash;&gt;-->
        <!--		<mapping resource="com/bem/dao/mapping/MonthlyCardEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/MonthlyCardReceivedEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/AuthUserEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserUdidEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/MapEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/ShopItemEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserAvatarEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserFriendEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/ConfigEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/ConfigLanguage.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/UserMessageEntity.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/UserDataEntity.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/UserBombEntity.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/UserHeroEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserItemEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserMaterialEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserPetEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserPetFoodEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserIconEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/FeedbackEntity.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/ClanEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserAccessoriesEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserSymbolEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserUfoEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserStoneUfoEntity.hbm.xml"/>-->

        <!--		<mapping resource="com/bem/dao/mapping/LotteryEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/SystemMessageEntity.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/LogEventEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/UserEventEntity.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/UserAssetLogEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/BattleDataEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/BattleUserEntity.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/LogErrorEntity.hbm.xml"/>-->
        <!--		<mapping resource="com/bem/dao/mapping/Jackpot.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/ClanBattleBonusEntity.hbm.xml"/>-->
        <!--        <mapping resource="com/bem/dao/mapping/ConfigLocalEntity.hbm.xml"/>-->
        <!-- Mapping Files -->
    </session-factory>
</hibernate-configuration>
