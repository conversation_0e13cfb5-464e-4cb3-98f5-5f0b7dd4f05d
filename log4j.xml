<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/" debug="null" reset="false" threshold="null">

    <appender class="org.apache.log4j.ConsoleAppender" name="STDOUT">
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss} - [%M](%C{1}:%L) - %m%n"/>
        </layout>
    </appender>

    <appender class="org.apache.log4j.DailyRollingFileAppender" name="ZOMBIE">
        <param name="File" value="logs/zombie.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="INFO"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <logger name="ZOMBIE">
        <appender-ref ref="ZOMBIE"/>
    </logger>

    <appender class="org.apache.log4j.RollingFileAppender" name="QUAY">
        <param name="File" value="logs/quay.log"/>
        <param name="Append" value="true"/>
        <param name="MaxFileSize" value="10MB"/>
        <param name="MaxBackupIndex" value="10"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="INFO"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <logger name="QUAY">
        <appender-ref ref="QUAY"/>
    </logger>

    <appender class="org.apache.log4j.RollingFileAppender" name="DEBUG">
        <param name="File" value="logs/debug.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="DEBUG"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <appender class="org.apache.log4j.RollingFileAppender" name="INFO">
        <param name="File" value="logs/info.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="INFO"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <appender class="org.apache.log4j.RollingFileAppender" name="WARN">
        <param name="File" value="logs/warn.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="WARN"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <appender class="org.apache.log4j.RollingFileAppender" name="ERROR">
        <param name="File" value="logs/error.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="ERROR"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <logger name="BEM">
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="WARN"/>
        <appender-ref ref="ERROR"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <appender class="org.apache.log4j.DailyRollingFileAppender" name="MONEY_INFO">
        <param name="File" value="logs/money.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="INFO"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <appender class="org.apache.log4j.RollingFileAppender" name="MONEY_ERROR">
        <param name="File" value="logs/moneyError.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="ERROR"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <logger name="MONEY">
        <appender-ref ref="MONEY_INFO"/>
        <appender-ref ref="MONEY_ERROR"/>
    </logger>

    <appender class="org.apache.log4j.RollingFileAppender" name="USER_API_INFO">
        <param name="File" value="logs/userAPI.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="INFO"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <appender class="org.apache.log4j.RollingFileAppender" name="USER_API_ERROR">
        <param name="File" value="logs/userAPIError.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="ERROR"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <logger name="USER_API">
        <appender-ref ref="USER_API_INFO"/>
        <appender-ref ref="USER_API_ERROR"/>
    </logger>

    <appender class="org.apache.log4j.DailyRollingFileAppender" name="ACTIVITY">
        <param name="File" value="logs/act.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss}\t%m%n"/>
        </layout>

        <filter class="org.apache.log4j.varia.LevelMatchFilter">
            <param name="LevelToMatch" value="INFO"/>
            <param name="AcceptOnMatch" value="true"/>
        </filter>
        <filter class="org.apache.log4j.varia.DenyAllFilter"/>
    </appender>

    <logger name="ACTIVITY">
        <appender-ref ref="ACTIVITY"/>
        <appender-ref ref="STDOUT"/>
    </logger>

</log4j:configuration>
