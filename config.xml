<?xml version="1.0" encoding="UTF-8"?>
<config>
    <server>
        <!-- Server settings -->
        <id>10</id>
        <port>6661</port>
        <welcome>HTTP Socket Proxy version 1.0</welcome>
        <!-- second -->
        <idle>60</idle>
        <!-- milisecond -->
        <remoteConnectTimeout>60000</remoteConnectTimeout>
        <remoteTimeout>30000</remoteTimeout>
        <remoteTimeoutShort>3000</remoteTimeoutShort>
        <user>zyuo|</user>
        <pass>coukA?xB=ylBZyuo|%</pass>
<!--        <user>dsonserver</user>-->
<!--        <pass>wO5icApi7O134uQInOJekutAw4F83O</pass>-->
        <db>boomss</db>
        <dbMain>boom</dbMain>
        <host>local.db:3306</host>
    </server>
    <memcache>
        <host>local.db</host>
        <port>11211</port><!-- 11211 -->
    </memcache>
    <redis>
        <host>local.db</host>
        <port>0</port>
    </redis>
    <!--
        Client
    -->
    <client>
        <maxClients>100000</maxClients>
        <timeout>300</timeout>
        <!-- delimiter can be: unix, win, mac, nul -->
        <lineDelimiter>win</lineDelimiter>
    </client>

    <!--
    Cache
    -->
    <cache>
        <!-- num of cache items -->
        <maxcache>10000</maxcache>
        <!-- second: 1/2 day-->
        <time>43200</time>
    </cache>
    <monitor>
        <koin>true</koin>
        <useronline>false</useronline>
        <debug>true</debug>
    </monitor>
</config>
